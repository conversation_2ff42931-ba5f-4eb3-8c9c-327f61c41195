---
title: 常见问题
lang: zh-CN
---
# 常见问题
### 为什么不是 Ant Design Pro Vue ？
[Ant Design Pro Vue](https://github.com/vueComponent/ant-design-vue-pro) 是 [Ant Design Pro](https://github.com/ant-design/ant-design-pro) 的 Vue 版本，其中项目结构、组件、
布局和使用方法等基本与 Ant Design Pro 的 react 版本保持一致。如果你比较熟悉 react 版，或者你已经在使用它，这确实是一个不错的选择。 

[Vue Antd Admin](https://github.com/iczer/vue-antd-admin) 同样实现了 Ant Design Pro 的所有功能。与此同时，我们还根据 Vue 的特性，对 Ant Design Pro 的一些组件和布局作出了相应的修改及优化，同时不影响保持与 Ant Design Pro 的一致。 

另外，我们还在添加一些 Ant Design Pro 没有的功能，比如全局动画、多页签模式等。  

如果你想使用 Ant Design Pro，但又觉得它缺乏一些你想要的功能，不妨看看 [Vue Antd Admin](https://github.com/iczer/vue-antd-admin)，我们会认真考虑每个用户的需求。  

因此，如果你有一些不错的想法和建议，欢迎随时和我们交流，很可能你的想法就在我们下一个版本中实现。

### 如何使用 Vue Antd Admin ？
请阅读文档 [开始使用](./use.md)。有任何疑问，欢迎在 github 上给我们提交 [issue](https://github.com/iczer/vue-antd-admin/issues/new)。

### 是否支持国际化 ？
Vue Antd Admin 引入了 vue-i18n 支持。因此你可以使用 vue-i18n 的特性对项目做国际化修改，详细请查看 [国际化](../advance/i18n.md)
