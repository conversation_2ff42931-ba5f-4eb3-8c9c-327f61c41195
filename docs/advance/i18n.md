---
title: 国际化
lang: zn-CN
---
# 国际化
vue-antd-admin 采用 [vue-i18n](https://kazupon.github.io/vue-i18n/) 插件来实现国际化，该项目已经内置并且加载好了基础配置。可以直接上手使用。

> 如果你还没有看快速入门，请先移步查看: [页面 -> i18n国际化配置](../develop/page.html#i18n国际化配置)


## 菜单和路由

### 默认情况
如果你没有对菜单进行国际化配置，admin 默认会从路由数据中提取数据作为国际化配置。route.name 作为中文语言，route.path 作为英文语言。  
国际化提取函数定义在 `@/utils/i18n.js` 文件中，会在路由加载时调用，如下：
```js
/**
 * 从路由提取国际化数据
 * @param i18n
 * @param routes
 */
function mergeI18nFromRoutes(i18n, routes) {
  formatFullPath(routes)
  const CN = generateI18n(new Object(), routes, 'name')
  const US = generateI18n(new Object(), routes, 'path')
  i18n.mergeLocaleMessage('CN', CN)
  i18n.mergeLocaleMessage('US', US)
  const messages = routesI18n.messages
  Object.keys(messages).forEach(lang => {
    i18n.mergeLocaleMessage(lang, messages[lang])
  })
}
```
### 自定义
如果你想自定义菜单国际化数据，可在 `@/router/i18n.js` 文件中配置。我们以路由的 path 作为 key（嵌套path 的写法也会被解析），name 作为 国际化语言的值。    
假设你有一个路由的配置如下：
```js
[{
  path: 'parent',
  ...
  children: [{
    path: 'self',
    ...
  }]
}]

or 

[{
  path: 'other',
  ...
  children: [{
    path: '/parent/self',   // 在国际化配置中 key 会解析为 parent.self
    ...
  }]
}]
```
那么你需要在 `@/router/i18n.js` 中这样配置：
```jsx
 messages: {
    CN: {
      parent: {
        name: '父級菜單',
        self: {name: '菜單名'},
    },
    US: {
      parent: {
        name: 'parent menu',
        self: {name: 'menu name'},
    },
    HK: {
      parent: {
        name: '父級菜單',
        self: {name: '菜單名'},
    },
```

## 添加语言

首先在 `@/layouts/header/AdminHeader.vue` ，新增一门语言 (多个同理)。

```vue {15}
<template>
  ...
</template>
<script>
...
export default {
  ...
  data() {
    return {
      langList: [
        {key: 'CN', name: '简体中文', alias: '简体'},
        {key: 'HK', name: '繁體中文', alias: '繁體'},
        {key: 'US', name: 'English', alias: 'English'},
        // 新增一个语言选项, key是i18n的索引，name是菜单显示名称
        {key: 'JP', name: 'Japanese', alias: 'Japanese'}
      ],
      searchActive: false
    }
  },
}
</script>
```

> TIP: 后续开发建议把这里改成动态配置的方式！

然后开始往 `@/router/i18n.js` 和 `@/pages/你的页面/i18n.js` 里面分别添加上语言的翻译。

```vue {12,13,14}
module.exports = {
    messages: {
        CN: {
            home: {name: '首页'},
        },
        US: {
            home: {name: 'home'},
        },
        HK: {
            home: {name: '首頁'},
        },
        JP: {
            home: {name: '最初のページ'},
        },
    }
}
```

> Notice: 更多用法请移步到 [vue-i18n](https://kazupon.github.io/vue-i18n/) 。