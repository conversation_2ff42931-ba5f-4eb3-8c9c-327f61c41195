import {SSO_LOGIN, GET_TOKEN, GET_MENUS} from '@/services/api/user'
import {request, METHOD, removeAuthorization} from '@/utils/request'
import {encrypt} from "../utils/authority-utils";
import {setAuthorization} from "../utils/request";
import {loadRoutes, parseMenu} from "../utils/routerUtil";

/**
 * 登录服务
 * @param name 账户名
 * @param password 账户密码
 * @returns {Promise<AxiosResponse<T>>}
 */
export async function login(store) {
    let userCode;
    //开发环境写死用户
    if (process.env.NODE_ENV === 'development') {
        // userCode = encrypt('10006294')
        // userCode = encrypt('yc90092988')
        userCode = encrypt('10050518')
    } else {
        //1、单点登录获取用户编号
        let ssoRes = await ssoLogin()
        //加密
        userCode = encrypt(ssoRes.data.data)
    }
    //通过用户编号获取token，aes加密
    //获取接口调用凭证
    let res = await getToken(userCode)
    let {employeeCode, employeeName, orgName,fullOrgInfo} = res.data.ycUser
    let token = res.data.access_token.split('.')[1].replace(/-/ig, '+').replace(/_/ig, '/')

    //设置用户信息和token
    store.commit('account/setUser', {
        name: employeeName,
        code: employeeCode,
        position: orgName,
        fullOrgInfo:fullOrgInfo
    })

    setAuthorization({
        token: res.data.access_token,
        refresh_token: res.data.refresh_token,
        expireAt: res.data.expires_in
    })


    // 获取路由配置
    getRoutesConfig(employeeCode).then(result => {
        let menu = parseMenu(result.data.table)
        loadRoutes([{router: 'root', children: menu}])
        //获取按钮权限
        let buttonInfo=[]
        menu.forEach(item=>{
            if(item.children.length>0){
                if(item.menuName==="基础数据"&&item.menuCode==="setting"){
                    item.children.forEach(temp=>{
                        if(temp.children.length>0){
                            temp.children.forEach(linshi=>{
                                buttonInfo.push(temp.menuCode);
                            })
                        }
                    })
                }else{
                    item.children.forEach(temp=>{
                        buttonInfo.push(temp.menuCode);
                    })
                }
            }
        })

        let tokenInfo = JSON.parse(window.atob(token))
        if(buttonInfo.length>0){
            buttonInfo.forEach(item=>{
               tokenInfo.authorities.push(item)
            })
        }
        store.commit('account/setPermissions',tokenInfo.authorities)

    })

}

export async function ssoLogin() {
    return request(SSO_LOGIN, METHOD.GET)
}

export async function getToken(code) {
    return request(GET_TOKEN, METHOD.GET, {'grant_type': 'yc_cloud', 'scope': 'all', code}, {
        auth: {
            username: 'sinosoft-xsgs',
            password: 'sinosoft-xsgs.2021'
        }
    })
}

export async function refreshToken(token) {
    return request(GET_TOKEN, METHOD.GET, {
        'grant_type': 'refresh_token',
        'refresh_token': token,
        client_id: 'sinosoft-xsgs',
        client_secret: 'sinosoft-xsgs.2021'
    })
}

export async function getMenu() {
    return request(GET_MENUS, METHOD.POST)
}

export async function getRoutesConfig(userCode) {
    return request(GET_MENUS, METHOD.POST, {memberCode: userCode, roleCategory: 'SALES-COMPANY-PLATFORM'})
}

/**
 * 退出登录
 */
export function logout() {
    localStorage.removeItem(process.env.VUE_APP_ROUTES_KEY)
    localStorage.removeItem(process.env.VUE_APP_PERMISSIONS_KEY)
    localStorage.removeItem(process.env.VUE_APP_ROLES_KEY)
    removeAuthorization()
}

export default {
    login,
    logout,
    getRoutesConfig
}
