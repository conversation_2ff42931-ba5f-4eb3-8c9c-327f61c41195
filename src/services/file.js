import {doAction, METHOD, request} from "../utils/request";
import {GET_OBS_SIGNED_URL} from "./api/file";

export async function getObsUploadUrl(objectname, fileType, bucketname) {
    return request(GET_OBS_SIGNED_URL, METHOD.PUT, {}, {
        params: {bucketname, objectname},
        headers: {'Content-Type': fileType}
    })
}

export async function getObsDownloadUrl(objectname, bucketname = process.env.VUE_APP_BUCKET_NAME) {
    return request(GET_OBS_SIGNED_URL, METHOD.GET, {}, {
        params: {bucketname, objectname}
    })
}

export async function uploadObsFile(file, path, field, bucketname = process.env.VUE_APP_BUCKET_NAME) {
    const time = new Date().getTime();
    const index = file.name.lastIndexOf('.');
    const finalName = file.name.substring(0, index) + '_' + time + file.name.substring(index);
    const res = await getObsUploadUrl(path + finalName, file.type, bucketname);
    const signedUrl = res.data.data
    if (!signedUrl) {
        alert('获取url签名失败！');
    }
    await doAction('PUT', signedUrl, file, {'Content-Type': file.type});
    return {
        uid: time,
        name: file.name,
        finalName,
        size: file.size,
        path,
        field,
        url: signedUrl
    }
}
