import {GET_ALL_ORG,GET_FND_EMPLOYEES,GET_YC_USER} from '@/services/api/user'
import {request, METHOD} from '@/utils/request'

// 查询组织树(用于选择人员组件)
export const queryAllOrgApi = (params) => {
    return request(GET_ALL_ORG, METHOD.POST, params);
};

// 公用员工查询接口
export const getFndEmployeesApi = (value) => {
    let config = {
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        }
    };
    let params = {};
    params['employeeName'] = value;
    params['page'] = 1;
    params['pageSize'] = 20;
    return request(GET_YC_USER, METHOD.POST, params, config);
};
