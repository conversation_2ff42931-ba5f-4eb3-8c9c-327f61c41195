//跨域代理前缀
// const API_PROXY_PREFIX='/api'
// const BASE_URL = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_API_BASE_URL : API_PROXY_PREFIX
const BASE_URL = process.env.VUE_APP_API_BASE_URL
module.exports = {
    CLIENT_PAGE: `${BASE_URL}/sys/client/page`,
    DELETE_CLIENT: `${BASE_URL}/sys/client/`,
    SUBMIT_CLIENT: `${BASE_URL}/sys/client`,

    DICT_PAGE: `${BASE_URL}/sys/dict/page`,
    DELETE_DICT: `${BASE_URL}/sys/dict/`,
    SUBMIT_DICT: `${BASE_URL}/sys/dict`,

    DICT_ITEM_PAGE: `${BASE_URL}/sys/dict/item/page`,
    DELETE_DICT_ITEM: `${BASE_URL}/sys/dict/item/`,
    SUBMIT_DICT_ITEM: `${BASE_URL}/sys/dict/item`,

    GET_DICT_ITEMS: `${BASE_URL}/sys/dict/`
}
