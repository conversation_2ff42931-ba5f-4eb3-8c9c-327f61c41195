//跨域代理前缀
// const API_PROXY_PREFIX='/api'
// const BASE_URL = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_API_BASE_URL : API_PROXY_PREFIX
const BASE_URL = process.env.VUE_APP_API_BASE_URL
module.exports = {
    UPLOAD_EQUIPMENT: `${BASE_URL}/wx-fire-check/equipment/uploadEquipment`,
    DOWNLOAD_EQUIPMENT: `${BASE_URL}/wx-fire-check/equipment/downloadEquipment`,
    EQUIPMENT_PAGE: `${BASE_URL}/wx-fire-check/equipment/page`,
    DELETE_EQUIPMENT: `${BASE_URL}/wx-fire-check/equipment/`,
    SUBMIT_EQUIPMENT: `${BASE_URL}/wx-fire-check/equipment`,

    CHECK_LOG_PAGE: `${BASE_URL}/wx-fire-check/check-log/page`,
    DELETE_CHECK_LOG: `${BASE_URL}/wx-fire-check/check-log/`,
}
