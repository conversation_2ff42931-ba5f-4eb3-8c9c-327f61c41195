//跨域代理前缀
// const API_PROXY_PREFIX='/api'
// const BASE_URL = process.env.NODE_ENV === 'production' ? process.env.VUE_APP_API_BASE_URL : API_PROXY_PREFIX
const ZT = process.env.VUE_APP_API_ZT
const MPAAS = process.env.VUE_APP_API_MPAAS
module.exports = {

    SSO_LOGIN: `http://${window.location.host}/ssologin/service/ssologinuser`,
    GET_TOKEN: `${ZT}/auth/oauth/token`,
    GET_MENUS: `${MPAAS}gateway/uc-user-service/menu/queryTreeMenusByUsers`,
    GET_YC_USER:`${MPAAS}gateway/uc-user-service/user/queryValidUsers`,
    GET_ALL_ORG:`${MPAAS}gateway/uc-user-service/organization/queryAllOrg`,
    GET_FND_EMPLOYEES:`${MPAAS}gateway/bc-utils-service/pick/getFndEmployees`,

}
