import 'babel-polyfill';
import Vue from 'vue'
import App from './App.vue'
import {initRouter} from './router'
import './theme/index.less'
import Antd from 'ant-design-vue'
import Viser from 'viser-vue'
import Print from 'vue-print-nb'
import { Collapse, CollapseItem } from 'vant';
// import '@/mock'
import store from './store'
import 'animate.css/source/animate.css'
import Plugins from '@/plugins'
import {initI18n} from '@/utils/i18n'
import bootstrap from '@/bootstrap'
import moment from 'moment';
import 'moment/locale/zh-cn'
import 'xe-utils'
import '@/utils/dragModal'
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import '@/assets/font/font.css'
import 'vant/lib/index.css'
import { NumberAdd,Subtr,onRowClick,isEmpty,cutList,copy}  from '@/utils/util';
import { coverAuthColumn, getUserAuth,getPfList}  from '@/utils/authUtil';
import { checkName,addWaterMark}  from '@/utils/setWatermark';
import './directives/dragModal'

import VXETablePluginExportXLSX from 'vxe-table-plugin-export-xlsx'
VXETable.use(VXETablePluginExportXLSX)

const router = initRouter(store.state.setting.asyncRoutes)
const i18n = initI18n('CN', 'US')

Vue.prototype.$moment = moment;
Vue.prototype.NumberAdd=NumberAdd;
Vue.prototype.onRowClick=onRowClick;
Vue.prototype.checkName=checkName;
Vue.prototype.addWaterMark=addWaterMark;
Vue.prototype.Subtr=Subtr;
Vue.prototype.isEmpty=isEmpty;
Vue.prototype.cutList=cutList;
Vue.prototype.copy=copy;
Vue.prototype.coverAuthColumn=coverAuthColumn;
Vue.prototype.getUserAuth=getUserAuth;
Vue.prototype.getPfList=getPfList;

Vue.use(Antd)
Vue.config.productionTip = false
Vue.use(Viser)
Vue.use(Plugins)
Vue.use(VXETable)
Vue.use(Print);
Vue.use(Collapse);
Vue.use(CollapseItem);

bootstrap({router, store, i18n, message: Vue.prototype.$message})


new Vue({
    router,
    store,
    i18n,
    render: h => h(App),
}).$mount('#app')
