.week-mode{
  overflow: hidden;
  filter: invert(80%);
}
.beauty-scroll{
  scrollbar-color: @primary-color @primary-2;
  scrollbar-width: thin;
  -ms-overflow-style:none;
  position: relative;
  &::-webkit-scrollbar{
    width: 3px;
    height: 1px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 3px;
    background: @primary-color;
  }
  &::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 1px rgba(0,0,0,0);
    border-radius: 3px;
    background: @primary-3;
  }
}
.split-right{
  &:not(:last-child) {
    border-right: 1px solid rgba(98, 98, 98, 0.2);
  }
}
.disabled{
  cursor: not-allowed;
  color: @disabled-color;
  pointer-events: none;
}

.resize-table-th {
  position: relative;
  .table-draggable-handle {
    height: 100% !important;
    bottom: 0;
    left: auto !important;
    right: -5px;
    cursor: col-resize;
    touch-action: none;
  }
}
.xsgs-textarea {
  border-color: #d9d9d9;
  border-radius: 4px;
  line-height: 18px;
  font-size: 16px;
  float:left;
  width: 100%;
  min-height: 33px;
}
