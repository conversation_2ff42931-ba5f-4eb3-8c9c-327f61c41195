
.ant-table-thead{
  tr{
    th{
      &.ant-table-column-has-actions{
        &.ant-table-column-has-sorters:hover{
          background-color: @background-color-base;
        }
        &.ant-table-column-has-filters{
          &:hover{
            .anticon-filter, .anticon-filter:hover{
              background-color: @background-color-base;
            }
          }
          .anticon-filter.ant-table-filter-open{
            background-color: @background-color-base;
          }
        }
      }
    }
  }
}

.ant-table-thead >tr >th{
  background-color:@shadow-color;
}



.ant-table-tbody tr:nth-child(2n){
  background: rgba(#e6fdff);
}

.ant-table-thead tr:nth-child(1){
  background:rgba(0,0,0,0.2) ;
}



.ant-form >table >tbody >tr >td {
  border: 1px solid #464646;
}

.ant-table-thead >tr >th{
  border: 1px solid #464646;
}

.ant-table-tbody >tr >td{
  border: 1px solid #464646;
}
.ant-table-fixed .ant-table-row-hover {
  background: #fcce10 !important;
}
.ant-table-fixed .ant-table-row-hover > td {
  background: #fcce10 !important;
}
.ant-table .ant-table-tbody .ant-table-row .row--current {
  background-color: #fcce10 !important;
}
.ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-thead > tr > th,
.ant-table-small > .ant-table-content > .ant-table-header > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td,
.ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table > .ant-table-tbody > tr > td {
  padding: 2px 2px;
}
