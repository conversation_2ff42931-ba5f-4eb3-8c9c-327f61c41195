<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="名称">
              <a-input v-model="searchForm.name"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch">查询</a-button>
              <a-button type="primary" @click="dialog=true">新增</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="middle"
          :customRow="onRowClick"
          :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="$router.push('/sys/dict_item/'+record.type)">字典项</a>
                    <a-divider type="vertical"/>
                    <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
        <a-form-model-item label="类型" prop="type">
          <a-input v-model="form.type"/>
        </a-form-model-item>
        <a-form-model-item label="名称" prop="name">
          <a-input v-model="form.name"/>
        </a-form-model-item>
        <a-form-model-item label="描述" prop="description">
          <a-textarea v-model="form.description" :rows="4"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {METHOD, request} from "../../../utils/request";
import {hasAuth} from "@/utils/authority-utils";
import {DELETE_DICT, DICT_PAGE, SUBMIT_DICT} from "@/services/api/sys";

export default {
  name: "dict.vue",
  data() {
    return {
      hasAuth,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '类型', dataIndex: 'type'},
        {title: '名称', dataIndex: 'name'},
        {title: '描述', dataIndex: 'description'},
        {title: '创建时间', dataIndex: 'createTime', width: 200},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        type: [{required: true, message: '类型不能为空', trigger: 'blur'}],
        name: [{required: true, message: '名称不能为空', trigger: 'blur'}],
        description: [{required: true, message: '描述不能为空', trigger: 'blur'}],
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(DICT_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.name} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_DICT + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_DICT, METHOD.POST, this.form)
              .then(() => {
                this.getData()
                this.$message.info('提交成功')
                this.closeForm()
              })
        }
      })
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },
  }
}
</script>

<style scoped>

</style>
