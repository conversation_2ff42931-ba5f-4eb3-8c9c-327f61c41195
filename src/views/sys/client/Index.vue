<template>
  <div>
    <a-card>
      <a-form-model
          ref="searchForm"
          :model="searchForm"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
      >
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="应用ID">
              <a-input v-model="searchForm.clientId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item>
              <a-button
                  type="primary"
                  style="margin: 0 8px"
                  @click="handleSearch"
              >查询
              </a-button
              >
              <a-button type="primary" @click="dialog = true">新增</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="clientId"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="middle"
          :customRow="onRowClick"
          :loading="loading"
      >
        <span slot="types" slot-scope="types">
          <a-tag v-for="tag in types.split(',')" :key="tag">
            {{ tag.toUpperCase() }}
          </a-tag>
        </span>
        <span slot="action" slot-scope="record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical"/>
          <a @click="handleDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id ? '编辑' : '新增'"
        :visible="dialog"
        width="70%"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm"
    >
      <a-form-model
          ref="form"
          :model="form"
          :rules="rules"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
      >
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="ID" prop="clientId">
              <a-input :disabled="form.createTime" v-model="form.clientId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="密钥" prop="clientSecret">
              <a-input v-model="form.clientSecret" type="password"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="令牌秒数" prop="accessTokenValidity">
              <a-input-number v-model="form.accessTokenValidity"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="刷新秒数" prop="refreshTokenValidity">
              <a-input-number v-model="form.refreshTokenValidity"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="权限范围" prop="scope">
              <a-input v-model="form.scope"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="自动授权" prop="autoapprove">
              <a-radio-group v-model="form.autoapprove" button-style="solid">
                <a-radio-button value="true">是</a-radio-button>
                <a-radio-button value="false">否</a-radio-button>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="华为云endpoint"
            prop="hwEndpoint"
        >
          <a-input v-model="form.hwEndpoint"/>
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="华为云AK" prop="hwAk">
              <a-input v-model="form.hwAk"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="华为云SK" prop="hwSk">
              <a-input v-model="form.hwSk" type="password"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="阿里云endpoint"
            prop="hwEndpoint"
        >
          <a-input v-model="form.aliyunEndpoint"/>
        </a-form-model-item>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="阿里云accessId" prop="aliyunAccessId">
              <a-input v-model="form.aliyunAccessId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="阿里云accessKey" prop="aliyunAccessKey">
              <a-input v-model="form.aliyunAccessKey" type="password"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="SAP用户" prop="sapUser">
              <a-input v-model="form.sapUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="SAP密码" prop="sapPwd">
              <a-input v-model="form.sapPwd" type="password"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="资源集合"
            prop="resourceIds"
        >
          <a-input v-model="form.resourceIds"/>
        </a-form-model-item>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="授权模式"
            prop="authorizedGrantTypes"
        >
          <a-select
              v-model="form.authorizedGrantTypes"
              mode="tags"
              style="width: 100%"
          >
            <a-select-option key="client_credentials">客户端模式</a-select-option>
            <a-select-option key="password">密码模式</a-select-option>
            <a-select-option key="authorization_code">授权码模式</a-select-option>
            <a-select-option key="refresh_token">刷新模式</a-select-option>
            <a-select-option key="implicit">简化模式</a-select-option>
            <a-select-option key="work_wx">企业微信模式</a-select-option>
            <a-select-option key="yc_cloud">玉柴云模式</a-select-option>
            <a-select-option key="mini_wx">小程序模式</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="权限"
            prop="authorities"
        >
          <a-input v-model="form.authorities"/>
        </a-form-model-item>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="回调地址"
            prop="webServerRedirectUri"
        >
          <a-input v-model="form.webServerRedirectUri"/>
        </a-form-model-item>
        <a-form-model-item
            :labelCol="{ span: 4 }"
            :wrapperCol="{ span: 20 }"
            label="附加说明"
            prop="additionalInformation"
        >
          <a-textarea v-model="form.additionalInformation" :rows="4"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {METHOD, request} from "../../../utils/request";
import {CLIENT_PAGE, DELETE_CLIENT, SUBMIT_CLIENT} from "@/services/api/sys";

export default {
  name: "client.vue",
  data() {
    return {
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: "10",
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ["10", "20", "30", "40", "50"],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) => this.onSizeChange(current, size), // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: "应用ID", dataIndex: "clientId", width: 110},
        {title: "权限范围", dataIndex: "scope", width: 110},
        {
          title: "授权模式",
          dataIndex: "authorizedGrantTypes",
          scopedSlots: {customRender: "types"},
          width: 500,
        },
        {title: "回调地址", dataIndex: "webServerRedirectUri", width: 200},
        {title: "令牌过期秒数", dataIndex: "accessTokenValidity", width: 150},
        {
          title: "刷新令牌过期秒数",
          dataIndex: "refreshTokenValidity",
          width: 150,
        },
        {title: "自动授权", dataIndex: "autoapprove", width: 110},
        {title: "创建时间", dataIndex: "createTime", width: 200},
        {
          title: "操作",
          fixed: "right",
          width: 110,
          scopedSlots: {customRender: "action"},
        },
      ],
      rules: {
        clientId: [
          {required: true, message: "应用ID不能为空", trigger: "blur"},
        ],
        clientSecret: [
          {required: true, message: "应用密钥不能为空", trigger: "blur"},
        ],
        scope: [
          {required: true, message: "权限范围不能为空", trigger: "blur"},
        ],
        authorizedGrantTypes: [
          {required: true, message: "授权模式不能为空", trigger: "blur"},
        ],
        webServerRedirectUri: [
          {required: true, message: "回调地址不能为空", trigger: "blur"},
        ],
        accessTokenValidity: [
          {required: true, message: "令牌过期秒数不能为空", trigger: "blur"},
        ],
        refreshTokenValidity: [
          {
            required: true,
            message: "刷新令牌过期秒数不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loading = true;
      let {current, size} = this.pagination;
      request(CLIENT_PAGE, METHOD.GET, {
        ...this.searchForm,
        current,
        size,
      }).then((res) => {
        const {records, total} = res.data.data;
        this.loading = false;
        this.data = records;
        this.pagination.total = parseInt(total);
      });
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.clientId} ？`,
        onOk: () => {
          this.loading = true;
          request(DELETE_CLIENT + record.clientId, METHOD.DELETE)
              .then(() => {
                this.$message.info("删除成功");
                this.getData();
              })
              .catch(() => {
                this.loading = false;
              });
        },
      });
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          request(SUBMIT_CLIENT, METHOD.POST, {
            ...this.form,
            authorizedGrantTypes: this.form.authorizedGrantTypes.join(","),
          }).then(() => {
            this.getData();
            this.$message.info("提交成功");
            this.closeForm();
          });
        }
      });
    },
    handleSearch() {
      this.pagination.current = 1;
      this.getData();
    },
    handleEdit(record) {
      this.dialog = true;
      this.form = {
        ...record,
        authorizedGrantTypes: record.authorizedGrantTypes.split(","),
      };
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },
    closeForm() {
      this.dialog = false;
      this.$refs.form.resetFields();
      this.form = {};
    },
    tableChange(pagination) {
      this.pagination = {...this.pagination, current: pagination.current};
      this.getData();
    },
  },
};
</script>

<style scoped>
</style>
