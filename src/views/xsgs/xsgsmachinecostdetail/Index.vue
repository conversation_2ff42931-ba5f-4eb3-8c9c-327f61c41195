<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="时间范围" /></td>
            <td style="width: 450px">
              <a-form-model-item >
                <a-form-item  :style="{ display: 'inline-block', width: 'calc(50% - 11px)' }">
                  <a-date-picker style="width: 100%" value-format="YYYY/MM"  format="YYYY/MM" v-model="searchForm.saleTimeBegin" />
                </a-form-item>
                <span :style="{ display: 'inline-block', width: '12px', textAlign: 'center', margin: 'auto 5px auto 5px', }">到</span>
                <a-form-item :style="{ display: 'inline-block', width: 'calc(50% - 11px)' }">
                  <a-date-picker style="width: 100%"  value-format="YYYY/MM" format="YYYY/MM"  v-model="searchForm.saleTimeEnd" />
                </a-form-item>
              </a-form-model-item>
            </td>
            <td><a-form-model-item label="状态机" /></td>
            <td>
              <a-input v-model="searchForm.saleTimeEnd">
                <template #addonAfter>
                  <a-upload
                      v-model="fileLists"
                      :fileList="fileList"
                      name="file"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      @change="handleUploadExcel">
                    <a-button type="primary">
                      批导
                    </a-button>
                  </a-upload>
                </template>
              </a-input>
            </td>
            <td><a-button type="primary" style="float: right" @click="handleSearch"><a-icon type="search"/>查询</a-button></td>
            <td><a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button></td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload
                    name="file"
                    :action="UPLOAD_XSGSMATERIALCOST"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
          <template slot="createTime" slot-scope="text, record">
            {{dateFormat(record.createTime)}}
          </template>
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        width="40%"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
          <a-row class="form-row">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="年度" prop="year">
                <a-input v-model="form.year"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="状态机" prop="platformName">
                <a-input v-model="form.platformName"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="24" :md="24" :sm="24">
              <a-form-model-item label="基准成本" prop="baseCost">
                <a-input v-model="form.baseCost"/>
              </a-form-model-item>
            </a-col>
          </a-row>
            <a-row class="form-row">
              <a-col :lg="24" :md="24" :sm="24">
                <a-form-model-item label="实际成本" prop="materialCost">
                  <a-input v-model="form.materialCost"/>
                </a-form-model-item>
              </a-col>

          </a-row>

      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSMATERIALCOST_PAGE,DELETE_XSGSMATERIALCOST,SUBMIT_XSGSMATERIALCOST,DOWNLOAD_XSGSMATERIALCOST,UPLOAD_XSGSMATERIALCOST,DELETE_XSGSMATERIALCOST_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import {dateFormat} from "@/utils/dateUtil";
import Cookie from 'js-cookie'
export default {
  name: "xsgsmachinecostdetail.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSMATERIALCOST,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', dataIndex: 'year'},
        {title: '状态机', dataIndex: 'platformName'},
        {title: '基准成本', dataIndex: 'baseCost'},
        {title: '实际成本', dataIndex: 'materialCost'},
        {title: '更新时间', dataIndex: 'createTime', scopedSlots: {customRender: 'createTime'}},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
        baseCost: [{required: false, message: '基准成本不能为空', trigger: 'blur'}],
        materialCost: [{required: false, message: '实际成本不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
        isDelete: [{required: false, message: '删除标志不能为空', trigger: 'blur'}],
      },
      fileList: [],
      fileLists: undefined
    }
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSMATERIALCOST_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSMATERIALCOST_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                  .then(() => {
                    this.$message.info('删除成功')
                    this.getData()
                  }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.platformName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSMATERIALCOST + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSMATERIALCOST, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      let fileList = [...info.fileList]; // 复制当前文件列表到新变量
      console.log("上传成功",fileList,this.fileLists)
      // this.loading = true
      // if (info.file.status !== 'uploading') {
      //   this.getData()
      // }
      // if (info.file.status === 'done') {
      //   this.$message.success(`${info.file.name} 导入成功`);
      // } else if (info.file.status === 'error') {
      //   this.$message.error(`${info.file.name} 导入失败`);
      // }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          let option = []
          if (searchForm.year != null) {
            option.year = searchForm.year
          }
          if (searchForm.machineTypes != null && searchForm.machineTypes != "") {
            option.machineTypes = searchForm.machineTypes.split((','));
          }
          exportExcel(DOWNLOAD_XSGSMATERIALCOST,{...option},'材料成本表.xlsx')
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    dateFormat(date) {
      return dateFormat(date, 'YYYY-MM-DD HH:mm:ss');
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

>>>.ant-input-group-addon {
  padding: inherit;
  border: aliceblue;
}

.ant-btn {
  border-radius: inherit
}
</style>
