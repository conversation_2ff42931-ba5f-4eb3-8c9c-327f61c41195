<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="用户id">
              <a-input v-model="searchForm.userId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="子表头">
              <a-input v-model="searchForm.columns"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="父表头">
              <a-input v-model="searchForm.parentColumns"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-upload
                    name="file"
                    :action="UPLOAD_XSGSREPORTTHEME"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="middle"
          :customRow="onRowClick"
          :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="用户id" prop="userId">
                <a-input v-model="form.userId"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="子表头" prop="columns">
                <a-input v-model="form.columns"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="父表头" prop="parentColumns">
                <a-input v-model="form.parentColumns"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSREPORTTHEME_PAGE,DELETE_XSGSREPORTTHEME,SUBMIT_XSGSREPORTTHEME,DOWNLOAD_XSGSREPORTTHEME,UPLOAD_XSGSREPORTTHEME} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgsreporttheme.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSREPORTTHEME,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '用户id', dataIndex: 'userId'},
        {title: '子表头', dataIndex: 'columns'},
        {title: '父表头', dataIndex: 'parentColumns'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        userId: [{required: false, message: '用户id不能为空', trigger: 'blur'}],
        columns: [{required: false, message: '子表头不能为空', trigger: 'blur'}],
        parentColumns: [{required: false, message: '父表头不能为空', trigger: 'blur'}],
      }
    }
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSREPORTTHEME_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.name} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSREPORTTHEME + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSREPORTTHEME, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          window.open(decodeURI(DOWNLOAD_XSGSREPORTTHEME))
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },
  }
}
</script>

<style scoped>

</style>
