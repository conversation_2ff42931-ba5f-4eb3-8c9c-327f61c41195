<template>
  <div>
    <a-card>
      <a-form ref="searchForm" :label-col="{span:6}" :wrapper-col="{span:18}">
        <a-row>
          <a-col span="8">
            <a-form-item label='角色名称'>
              <a-input v-model="searchForm.roleName" placeholder="请输入角色名称"/>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-row>
        <a-col :span="24" style="margin-bottom: 5px;margin-top: 10px">
				<span style="float: left">
					<a-button type="primary" style="margin-right: 8px" icon="plus" @click="handleAdd">新增</a-button>
					<a-button type="danger" icon="delete" @click="handleDel">删除</a-button>
				</span>
          <span style="float: right">
					<a-button type="primary" icon="search" @click="getData">查询</a-button>
				</span>
        </a-col>
      </a-row>
      <a-table rowKey="id"
               bordered
               size="small"
               :loading="spinning"
               :pagination="pagination"
               :data-source="data"
               :customRow="onRowClick"
               :row-selection="rowSelection"
               :columns="columns">
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="action" slot-scope="text, record">
          <div>
					<span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                @click="authConfig(record)">权限配置</span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="regionConfig(record)">大区配置</span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="plateConfig(record)">板块配置</span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="userConfig(record)">用户配置</span>

            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="editData(record)">修改</span>
            <span v-if="record.roleCode != 'ADMIN_ROLE'"
                  :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="delData(record)">删除</span>
          </div>
        </template>
      </a-table>
      <a-modal v-drag-modal v-model="showDialog" :title="roleForm.title" @ok="saveData" :loading="subLoading"
               @cancel="showDialog = false">
        <a-spin :spinning="subLoading">
          <a-form-model ref="roleForm" :model="roleForm" :rules="rules" :label-col="{span: 4}"
                        :wrapper-col="{span: 20}">
            <a-row>
              <a-col :span="24">
                <a-form-model-item label="角色编号" prop="roleCode">
                  <a-input :disabled="roleForm.id" :maxLength="30" v-model="roleForm.roleCode"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-model-item label="角色名称" prop="roleName">
                  <a-input :maxLength="50" v-model="roleForm.roleName"></a-input>
                </a-form-model-item>
              </a-col>
            </a-row>
          </a-form-model>
        </a-spin>
      </a-modal>
      <a-modal v-drag-modal v-model="showAuthDialog" title="权限配置" @ok="saveAuthData" :loading="subLoading"
               @cancel="showAuthDialog = false">
        <a-spin :spinning="subLoading">
          <a-form-model :model="roleForm"
                        :wrapper-col="{span: 20}">
            <a-tree v-model="roleForm.checkedKeys"
                    checkable
                    :blockNode="true"
                    :defaultExpandedKeys="['origin']"
                    :tree-data="menuTree"
                    :selectable="false"
                    @check="checkAction"/>
          </a-form-model>
        </a-spin>
      </a-modal>
      <a-modal v-drag-modal v-model="showRegionDialog" title="大区配置" @ok="saveRegionData" :loading="subLoading"
               @cancel="showRegionDialog = false">
        <a-spin :spinning="subLoading">
          <a-form-model :model="regionForm"
                        :wrapper-col="{span: 20}">
            <a-tree v-model="regionForm.checkedKeys"
                    checkable
                    :blockNode="true"
                    :defaultExpandedKeys="['全部']"
                    :tree-data="menuTrees"
                    :selectable="false"/>
          </a-form-model>
        </a-spin>
      </a-modal>
      <a-modal v-drag-modal v-model="showPlateDialog" title="板块配置" @ok="savePlateData" :loading="subLoading"
               @cancel="showPlateDialog = false">
        <a-spin :spinning="subLoading">
          <a-form-model :model="plateForm"
                        :wrapper-col="{span: 20}">
            <a-tree v-model="plateForm.checkedKeys"
                    checkable
                    :blockNode="true"
                    :defaultExpandedKeys="['全部']"
                    :tree-data="plateMenuTree"
                    :selectable="false"/>
          </a-form-model>
        </a-spin>
      </a-modal>
      <!-- 开始选人组件 -->
      <YcChooseMember
          ref="chooseMember"
          @member-modal-ok="memberModalOkTo($event)"
          @member-modal-cancel="canCel"
      ></YcChooseMember>
    </a-card>

  </div>
</template>

<script>
import {
  LIST_XSGSAUTHROLE_PAGE,
  SUBMIT_XSGSAUTHROLE,
  GET_XSGSAUTHROLE_ID,
  DELETE_XSGSAUTHROLE_IDS,
  GET_XSGSAUTHLIST_TREE2,
  SUBMIT_XSGSAUTHROLE_AUTH,
  SAVE_ROLE_USER,
  GET_XSGSAUTHREGION_TREE,
  GET_XSGSAUTHREGION_ID,
  SUBMIT_XSGSAUTHREGION_Region,
  GET_XSGSAUTHPLATE_ID,
  SUBMIT_XSGSAUTHPLATE_Plate,
  GET_XSGSAUTHPLATE_TREE
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import YcChooseMember from "../../../components/chooseMember/YcChooseMember";

export default {
  neme: 'roleList',
  components: {
    YcChooseMember
  },
  data() {
    return {
      searchForm: {
        roleName: null
      },
      roleForm: {
        title: "新增角色",
        roleName: null,
        roleCode: null,
        checkedKeys: [],
        userList: [],
      },
      roleCheckedKeys: [],
      checkedKeysss: [],
      regionForm: {
        checkedKeys: [],
      },
      plateForm: {
        checkedKeys: [],
      },
      rules: {
        roleCode: [{
          required: true,
          message: "角色编号不能为空",
          trigger: 'blur'
        },
          {
            pattern: new RegExp(/^[0-9a-zA-Z_]{1,}$/, "g"),
            message: "角色编号只允许包含数字、字母和下划线",
            trigger: 'blur'
          },
        ],
        roleName: [{
          required: true,
          message: "角色名称不能为空",
          trigger: 'blur'
        }],
      },
      spinning: false,
      subLoading: false,
      showDialog: false,
      showAuthDialog: false,
      showRegionDialog: false,
      showPlateDialog: false,
      selectedRowKeys: [],
      defExpandedKeys: [],
      selectedRows: [],
      menuTree: [],
      menuTrees: [],
      plateMenuTree: [],
      autoExpandParent: false,
      data: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ['10', '15', '20', '100'],
        showTotal: (total) => `共 ${total} 条数`,
        onChange: (current, pageSize) => this.pageChange(current, pageSize),
        onShowSizeChange: (current, pageSize) =>
            this.pageSizeChange(current, pageSize),
      },
      columns: [{
        dataIndex: 'num', title: '序号', key: 'num', width: 60, align: 'center', scopedSlots: {customRender: 'num'}
      },
        {dataIndex: "roleCode", title: "角色编号", width: 180},
        {dataIndex: "roleName", title: "角色名称", width: 220},
        {dataIndex: "userDisplayName", title: "用户名称", ellipsis: true},
        {dataIndex: "action", title: "操作", width: 250, align: "center", scopedSlots: {customRender: 'action'}},
      ],
      flatArr: [],//权限配置树所有父节点
    };
  },
  mounted() {
    this.getData();
    this.getTree();
    this.getTree1();
    this.getTree2();
  },
  computed: {
    rowSelection() {
      return {
        columnWidth: '50px',
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRows = selectedRows;
          this.selectedRowKeys = selectedRowKeys;
        },
      };
    },
  },
  watch: {
    "roleForm": {
      handler(newVal) {
        console.log("新的值", newVal.checkedKeys);
      },
      deep: true
    },
  },
  methods: {
    getData() {
      this.spinning = true;
      request(LIST_XSGSAUTHROLE_PAGE, METHOD.POST, {
        ...this.searchForm,
        ...this.pagination
      }).then(res => {
        this.spinning = false;
        let {
          records,
          total
        } = res.data.data;
        this.data = records;
        this.pagination.total = Number(total);
      });
    },
    getTree() {
      let that = this;
      this.subLoading = true;
      request(GET_XSGSAUTHLIST_TREE2, METHOD.GET, {}).then(res => {
        this.subLoading = false;
        that.menuTree = res.data.data.tree;
      });
    },
    getTree1() {
      let that = this;
      this.subLoading = true;
      request(GET_XSGSAUTHREGION_TREE, METHOD.GET, {}).then(res => {
        this.subLoading = false;
        that.menuTrees = res.data.data.tree;
      });
    },
    getTree2() {
      let that = this;
      this.subLoading = true;
      request(GET_XSGSAUTHPLATE_TREE, METHOD.GET, {}).then(res => {
        this.subLoading = false;
        that.plateMenuTree = res.data.data.tree;
      });
    },
    saveData() {
      let that = this;
      this.$refs.roleForm.validate((valid) => {
        if (valid) {
          this.subLoading = true;
          request(SUBMIT_XSGSAUTHROLE, METHOD.POST, {
            ...this.roleForm
          }).then(res => {
            that.subLoading = false;
            if (res.data.code == 0) {
              that.showDialog = false;
              that.$message.info(res.data.msg);
              that.getData();
            } else {
              that.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    saveAuthData() {
      let that = this;
      this.roleForm.checkedKeys = this.roleCheckedKeys
      this.subLoading = true;
      request(SUBMIT_XSGSAUTHROLE_AUTH, METHOD.POST, {
        ...this.roleForm
      }).then(res => {
        that.subLoading = false;
        if (res.data.code == 0) {
          that.showAuthDialog = false;
          that.$message.info("保存成功");
          that.getData();
        } else {
          that.$message.error(res.data.msg);
        }
      });
    },
    saveRegionData() {
      let that = this;
      this.subLoading = true;
      request(SUBMIT_XSGSAUTHREGION_Region, METHOD.POST, {
        ...this.regionForm
      }).then(res => {
        that.subLoading = false;
        if (res.data.code == 0) {
          that.showRegionDialog = false;
          that.$message.info("保存成功");
          that.getData();
        } else {
          that.$message.error(res.data.msg);
        }
      });
    },
    savePlateData() {
      let that = this;
      this.subLoading = true;
      request(SUBMIT_XSGSAUTHPLATE_Plate, METHOD.POST, {
        ...this.plateForm
      }).then(res => {
        that.subLoading = false;
        if (res.data.code == 0) {
          that.showPlateDialog = false;
          that.$message.info("保存成功");
          that.getData();
        } else {
          that.$message.error(res.data.msg);
        }
      });
    },
    handleAdd() {
      this.roleForm = {};
      this.roleForm = {
        title: "新增角色"
      };
      this.showDialog = true;
    },
    handleDel() {
      if (this.selectedRowKeys.length < 1) {
        this.$message.warning('没有选择要删除的数据！');
        return;
      }
      let ids = this.selectedRowKeys
      this.deleteDatas(ids);

    },
    editData(record) {
      let that = this;
      request(GET_XSGSAUTHROLE_ID, METHOD.GET, {id: record.id}).then(res => {
        that.roleForm = res.data.data;
        that.roleForm.title = '修改角色';
        this.showDialog = true;
        console.log("修改角色", res.data.data)
      });
    },
    editUser(record) {
      let userList = [...record.userList];
      let employeesData = [];
      this.$refs.chooseMember.defaultChoose = [];

      userList.forEach(item => {
        employeesData.push({
          employeeName: item.userName,
          employeeCode: item.userAccount,
          orgFullName: item.userDisplayName
        });
      });
      this.$refs.chooseMember.show(record.id, [...employeesData]);
    },
    authConfig(record) {
      let that = this;
      request(GET_XSGSAUTHROLE_ID, METHOD.GET, {id: record.id}).then(res => {
        that.roleForm = res.data.data;
        this.showAuthDialog = true;
        this.roleCheckedKeys = this.roleForm.checkedKeys
        this.flatTree(this.menuTree)
        //过滤掉父节点
        let list = []
        that.roleForm.checkedKeys.filter(a => {
          let cc = this.flatArr.filter(b => a == b)
          if (cc == 0) {
            list.push(a)
          }
        });
        that.roleForm.checkedKeys = list
      });
    },
    userConfig(record) {
      let userList = [...record.userList];
      let employeesData = [];
      this.$refs.chooseMember.defaultChoose = [];

      userList.forEach(item => {
        employeesData.push({
          employeeName: item.userName,
          employeeCode: item.userAccount,
          orgFullName: item.userDisplayName
        });
      });
      this.$refs.chooseMember.show(record.id, [...employeesData]);
    },
    regionConfig(record) {
      let that = this;
      request(GET_XSGSAUTHREGION_ID, METHOD.GET, {id: record.id}).then(res => {
        that.regionForm = res.data.data;
        this.showRegionDialog = true;
      });
    },
    plateConfig(record) {
      let that = this;
      request(GET_XSGSAUTHPLATE_ID, METHOD.GET, {id: record.id}).then(res => {
        that.plateForm = res.data.data;
        this.showPlateDialog = true;
      });
    },
    delData(record) {
      let id = record.id;
      let ids = [];
      ids.push(id);
      this.deleteDatas(ids);
    },
    deleteDatas(ids) {
      let that = this;
      this.$confirm({
        title: '删除',
        content: '是否确定删除？一旦删除，数据将无法恢复',
        onOk() {
          that.spinning = true;
          request(DELETE_XSGSAUTHROLE_IDS, METHOD.POST, [...ids]).then(res => {
            that.$message.info("删除成功");
            that.getData()
          });
        },
        onCancel() {
        },
      });

    },
    pageChange(current) {
      this.pagination.current = current;
      this.getData();
    },
    pageSizeChange(current, pageSize) {
      this.pagination.current = 1;
      this.pagination.pageSize = pageSize;
      this.getData();
    },
    canCel() {
    },
    memberModalOkTo(memberData) {
      let rowId = memberData.rowId;
      if (rowId) {
        let userList = [];
        let dataList = memberData.memberData;
        // if (this.isEmpty(dataList)) {
        // 	this.$message.error("至少选择一个用户");
        // 	return;
        // }
        dataList.forEach(item => {
          userList.push({
            userName: item.employeeName,
            userAccount: item.employeeCode,
            userDisplayName: item.orgFullName
          });
        });
        let records = this.data.filter(item => item.id == rowId);
        if (records.length > 0) {
          let role = records[0];
          role.userList = userList;
          this.loading = true;
          let that = this;
          request(SAVE_ROLE_USER, METHOD.POST, role).then((res) => {
            if (res.data.code == 0) {
              that.$message.info("保存成功");
              this.getData();
              this.loading = false;
            }
          });
        }

      }
    },
    /** 权限配置点击节点事件 **/
    checkAction(checkedKeys, e) {
      this.roleCheckedKeys = [...checkedKeys, ...e.halfCheckedKeys];
    },
    /** 获取树形控件父节点 **/
    flatTree(tree) {
      if (!tree || !tree.length) return [];
      let flatArr = [];
      for (let item of tree) {
        if (item.children != null && item.children.length > 0) {
          this.flatArr.push(item.key);
        }
        this.flatTree(item.children);
      }
      return flatArr;
    }
  }
};
</script>

<style>
</style>
