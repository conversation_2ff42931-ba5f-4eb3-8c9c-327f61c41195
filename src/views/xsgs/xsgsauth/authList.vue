<template>
    <div>
		<a-card>
        <a-form ref="searchForm" :label-col="{span:6}" :wrapper-col="{span:18}">
            <a-row>
                <a-col span="8">
                    <a-form-item label='名称'>
                        <a-input v-model="searchForm.authName" placeholder="请输入权限名称"/>
                    </a-form-item>
                </a-col>
				<a-col span="8">
					<a-form-item label='权限类型'>
						<a-select width="width:100px" v-model="searchForm.authType" style="min-width: 60px"
								  :allowClear="true" >
							<a-select-option value="MY">菜单页面</a-select-option>
							<a-select-option value="DF">权限类型</a-select-option>
							<a-select-option value="TF">表格字段</a-select-option>
							<a-select-option value="PF">页面功能</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="8" style="margin-bottom: 5px;margin-top: 10px">
					<span style="float: right">
						<a-button type="primary" icon="search" @click="getData">查询</a-button>
					</span>
				</a-col>
            </a-row>
        </a-form>
		<a-row>
			<a-col :span="6" style="padding-right: 5px;">
				<a-spin :spinning="spinning">
				<a-tree
					:blockNode="true"
					@select ="selectAuth"
					@expand ="expandAuth"
					:defaultExpandedKeys="defExpandedKeys"
					:expandedKeys="expandedKeys"
					:selectedKeys="selectedKeys"
					:tree-data="authTree"
				/>
				</a-spin>
			</a-col>
			<a-col :span="18" style="padding-left: 5px;">
				<a-row>
					<a-col :span="19" style="margin-bottom: 5px;margin-top: 10px">
						<span style="float: left">
							<a-button type="primary" icon="plus" @click="showForm()">新增</a-button>
							<a-upload
									  name="file"
									  :action="UPLOAD_XSGSAUTHLIST"
									  :headers="{'Authorization':Cookie.get('Authorization'),'isCover':isCover}"
									  :showUploadList="false"
									  style="margin-left: 5px"
									  @change="handleUploadExcel">
							  <a-button type="primary">
								<a-icon type="upload"/>
								导入
							  </a-button>
							</a-upload>
							<a-button type="primary" style="margin-left: 5px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>
							<a-checkbox style="margin-left: 5px" :checked="isCover" v-model="isCover">是否覆盖</a-checkbox>
						</span>

					</a-col>
				</a-row>
				<a-table
						:columns="columns"
						rowKey="id"
						:pagination="pagination"
						:data-source="data"
						size="small"
						bordered
						:customRow="onRowClick"
						:loading="spinning">
				    <template slot="num" slot-scope="text, record, index">
				        {{index+1}}
				    </template>
					<template slot="authType" slot-scope="text, record">
						{{coverAuthType(record.authType)}}
					</template>
					<template slot="action" slot-scope="text, record">
						<div v-if="record.userName != 'admin'">
							<span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
								@click="editData(record)">修改</span>
							<span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
								@click="delData(record)">删除</span>
						</div>
					</template>
				</a-table>
			</a-col>
		</a-row>
        <a-modal
                v-drag-modal
                v-model="showDialog"
                :title="modalTitle"
                @ok="saveData"
				width="50%"
                @cancel="showDialog = false"
        >
		<a-spin :spinning="modalSpinning">
            <a-form-model class="ant-advanced-search-form" ref="authForm" :model="authForm" :rules="rules" :label-col="{span: 6}" :wrapper-col="{span: 18}">
				<a-row>
					<a-col :span="12">
						<a-form-model-item label="权限编号" prop="authCode">
							<a-input :disabled="authForm.id"
									v-model="authForm.authCode"
							></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="权限名称" prop="authName">
							<a-input
									v-model="authForm.authName"
							></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="权限类型">
							<a-select width="width:100px" v-model="authForm.authType" style="min-width: 60px"
									  :allowClear="true" >
								<a-select-option value="MY">菜单页面</a-select-option>
								<a-select-option value="DF">权限类型</a-select-option>
								<a-select-option value="TF">表格字段</a-select-option>
								<a-select-option value="PF">页面功能</a-select-option>
							</a-select>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="排序">
							<a-input
									v-model="authForm.sort"
							></a-input>
						</a-form-model-item>
					</a-col>
					<a-col :span="12">
						<a-form-model-item label="父级权限">
							<a-input disabled
									v-model="authForm.parentName"
							></a-input>
						</a-form-model-item>
					</a-col>
				</a-row>

            </a-form-model>
			</a-spin>
        </a-modal>
		</a-card>
    </div>
</template>

<script>
	import { LIST_XSGSAUTHLIST_PAGE,
		GET_XSGSAUTHLIST_ID,
		SUBMIT_XSGSAUTHLIST,
		GET_XSGSAUTHLIST_TREE,
		DELETE_XSGSAUTHLIST_IDS,
		DOWNLOAD_XSGSAUTHLIST,
		UPLOAD_XSGSAUTHLIST
	} from "@/services/api/xsgs";
	import {METHOD, request,exportExcel} from "@/utils/request";
	import Cookie from "js-cookie";
    export default {
        neme: 'AuthList',
        data() {
            return {
				UPLOAD_XSGSAUTHLIST,
				Cookie,
				isCover:false,
                searchForm: {
                    authName: null,
					parentCode:null,
					authType:null
                },
                authForm:{
                    authCode:null,
					authName:null,
					authType:null,
					authDir:null,
					authLevel:null,
					enabled:null,
					isAuth:null,
					sort:null,
					parentCode:null,
					parentName:null
                },
				modalTitle:'新增权限',
				currentCode:null,
				currentName:null,
                spinning:false,
				modalSpinning:false,
                showDialog:false,
                data: [],
				rules: {
					authCode: [{
							required: true,
							message: "权限编号不能为空",
							trigger: 'blur'
						},
					],
					authName: [{
						required: true,
						message: "权限名称不能为空",
						trigger: 'blur'
					}],
				},
				originCode:"origin",
				defExpandedKeys:[],
				expandedKeys:[],
				selectedKeys:[],
				authTree:[],
				authList:[],
                pagination: {
                    current: 1,
                    pageSize: 50,
                    total: 0,
                    showSizeChanger: true,
                    pageSizeOptions: ['10', '15', '20', '50', '100'],
                    showTotal: (total) => `共 ${total} 条数`,
                    onChange: (current, pageSize) => this.pageChange(current, pageSize),
                    onShowSizeChange: (current, pageSize) =>
                        this.pageSizeChange(current, pageSize),
                },
                columns: [
					{dataIndex: 'num', title: '序号',  key: 'num', width: 60, align: 'center', scopedSlots: {customRender: 'num'}},
                    {dataIndex: "authCode", title: "编号", width: 230},
                    {dataIndex: "authName", title: "权限名称"},
					{dataIndex: "authType", title: "权限类型",width: 100,scopedSlots: {customRender: 'authType'}},
                    {dataIndex: "sort", title: "排序", width: 60},
					{dataIndex: "action",title: "操作",width: 120,align: "center",scopedSlots: {customRender: 'action'}
					},
                ],

            };
        },
		mounted() {
			this.getTree();
			this.getData();
		},
		methods:{
			getData(){
			    this.spinning = true;
				this.searchForm.parentCode = this.currentCode;
				this.pagination.size = this.pagination.pageSize;
                request(LIST_XSGSAUTHLIST_PAGE,METHOD.POST,{...this.searchForm, ...this.pagination}).then(res=>{
                    this.spinning = false;
                    let {
                    	records,
                    	total
                    } = res.data.data;
                    this.data = records;
                    this.pagination.total = Number(total);
                });
			},
			getTree(){
			    this.spinning = true;
				let that = this;
                request(GET_XSGSAUTHLIST_TREE,METHOD.GET,{}).then(res=>{
                    this.spinning = false;
                    console.log(res.data.data);
					that.authTree = res.data.data.tree;
					that.authList = res.data.data.list;

					if (that.expandedKeys.length == 0) {
						that.expandedKeys.push(this.originCode);
					}
					if (that.selectedKeys.length == 0) {
						that.selectedKeys.push(this.originCode);
					}

                });
			},
            saveData(){
				this.modalTitle="新增权限";
				let that = this;
				this.$refs.authForm.validate((valid) => {
					if (valid) {
						request(SUBMIT_XSGSAUTHLIST,METHOD.POST,{...this.authForm}).then(res=>{
						    this.modalSpinning = false;
						    if (res.data.code == 0) {
						    	that.showDialog = false;
						    	that.$message.info('保存成功');
						    	that.getData();
								that.getTree();
						    } else {
						    	that.$message.error(res.data.msg);
						    }
						});
					}
				});

			},
			editData(record){
				this.showDialog = true;
				this.modalTitle="编辑权限";
				let that = this;
				request(GET_XSGSAUTHLIST_ID, METHOD.GET, {id:record.id}).then(res => {
					that.authForm = res.data.data;
					that.authForm.parentName = that.authForm.parentCode?that.authForm.parentName:"根目录";
				});
			},
			delData(record){
				let ids = [record.id];
				this.delDataByIds(ids);
			},
			delDataByIds(ids){
				let that = this;
				this.$confirm({
					title: '删除',
					content: '是否确定删除？一旦删除，数据将无法恢复',
					onOk() {
						that.spinning = true;
						request(DELETE_XSGSAUTHLIST_IDS, METHOD.POST, [...ids]).then(res => {
							that.$message.info(res.data.msg);
							that.spinning = false;
							that.getData();
							that.getTree();
						});
					},
				 onCancel() {},
				});

			},
			selectAuth(selectedList, e){
				if (selectedList.length == 0) {
					return;
				}
				let auths = this.authList.filter(s=>s.authCode == selectedList[0]);
				if (auths.length > 0) {
					this.currentCode = auths[0].authCode;
					this.currentName = auths[0].authName;
				} else {
					this.currentCode = null;
					this.currentName = null;
				}
				this.getData();

				this.selectedKeys = selectedList;
			},
			expandAuth(expandKeys, e){
				this.expandedKeys = expandKeys;
			},
			showForm(){
				this.modalTitle="新增权限";
				this.authForm = {};
				this.authForm.parentCode = this.currentCode;
				this.authForm.parentName = this.currentCode?this.currentName:"根目录";
				this.authForm.authType = "PF";
				this.showDialog = true;

				// 设置权限目录
				let authDir = this.originCode;
				let authLevel = 1;
				if (this.currentCode) {
					let parentCode = this.currentCode;
					let auths = this.authList.filter(s=>s.authCode == parentCode);
					if (auths.length > 0) {
						let auth = auths[0];
						authDir = auth.authDir + "," + auth.authCode;
						authLevel = Number(auth.authLevel) + 1;
					}
				}
				this.authForm.authDir = authDir;
				this.authForm.authLevel = authLevel;
			},
			/** 上传 */
			handleUploadExcel(info) {
				this.spinning = true
				if (info.file.status !== 'uploading') {
					this.getData();
					this.getTree();
				}
				if (info.file.status === 'done') {
					this.spinning = false
					this.$message.success(`${info.file.name} 导入成功`);
					this.getData();
					this.getTree();
				} else if (info.file.status === 'error') {
					this.spinning = false
					this.$message.error(`${info.file.name} 导入失败`);
				}
			},
			handleExportExcel() {
				exportExcel(DOWNLOAD_XSGSAUTHLIST, {...this.searchForm}, "权限列表.xlsx");
			},
            pageChange(current) {
                this.pagination.current = current;
                this.getData();
            },
            pageSizeChange(current, pageSize) {
                this.pagination.current = 1;
                this.pagination.pageSize = pageSize;
                this.getData();
            },
			coverAuthType(type) {
				if (type == 'MY') {
					return "菜单页面";
				} else if (type == 'DF') {
					return "权限类型";
				} else if (type == 'TF') {
					return "表格字段";
				} else if (type == 'PF') {
					return "页面功能";
				}
			}
		}
    };
</script>

<style>
	.ant-tree-node-content-wrapper {
		text-align: left;
	}
</style>
