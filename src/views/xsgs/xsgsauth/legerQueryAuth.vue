<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <a-row>
                    <a-col span="8">
                        <a-form-model-item label="台账名称">
                            <a-select width="width:100px" v-model="searchForm.ledgerName" style="min-width: 60px"
                                      :allowClear="true" @change="changeLedgerName">
                                <a-select-option v-for="d in ledgerList" :key="d.ledgerName">
                                    {{ d.ledgerDesc}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col span="10">

                    </a-col>
                    <a-col span="6">

                    </a-col>
                </a-row>
            </a-form-model>

            <a-table
                    :columns="columns"
                    rowKey="ledgerName"
                    ref="table"
                    :data-source="data"
                    :scroll="{ x: 1000 }"
                    size="small"
                    bordered
                    :customRow="onRowClick"
                    :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{index+1}}
                </template>
                <span slot="action" slot-scope="text,record">
                     <a @click="handleEdit(record)">编辑</a>
                </span>

            </a-table>
        </a-card>
        <!-- 开始选人组件 -->
        <YcChooseMember
                ref="chooseMember"
                @member-modal-ok="memberModalOkTo($event)"
                @member-modal-cancel="canCel"
        ></YcChooseMember>

    </div>

</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {LEDGER_AUTH_LIST,SAVE_LEDGER_AUTH} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import YcChooseMember from "../../../components/chooseMember/YcChooseMember";

    export default {
        name: "nodeAppAuth.vue",
        components:{
            YcChooseMember
        },
        data() {
            return {
                hasAuth,
                loading: false,
                searchForm: {
                    ledgerName: null
                },
                ledgerList:[],
                data: [],
                allData:[],
                columns: [
                    {
                        title: '序号',
                        dataIndex: 'num',
                        key: 'num',
                        width: 50,
                        align: 'center',
                        fixed: true,
                        scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '台账名称', align: "center", dataIndex: 'ledgerDesc', width: 200},
                    {title: '备注', align: "center", dataIndex: 'remark', width: 250},
                    {title: '用户', align: "center",dataIndex: "userDisplayName"},
                    {title: '功能', align: "center", width: 100, scopedSlots: {customRender: 'action'}},

                ],
                rules: {},

            }
        },
        async mounted() {
            this.loading = true
            let res = await this.getData();
            this.loading = false
            let that = this;
            if (res && res.data && res.data.data) {
                that.data = res.data.data;
                that.allData = res.data.data;
                that.allData.forEach(item=>that.ledgerList.push(item));
            }
            this.checkName();
        },
        methods: {
            async getData() {
                return  request(LEDGER_AUTH_LIST, METHOD.GET);
            },
            handleEdit(record) {
                let userList = [...record.userList];
                let employeesData = [];
                this.$refs.chooseMember.defaultChoose = [];

                userList.forEach(item=>{
                    employeesData.push({
                        employeeName: item.userName,
                        employeeCode: item.userAccount,
                        orgFullName:item.userDisplayName
                    });
                });
                this.$refs.chooseMember.show(record.ledgerName, [...employeesData]);
            },
            changeLedgerName(value){
                if (!value) {
                    this.data = this.allData;
                } else {
                    this.data = this.allData.filter(item=>item.ledgerName == value);
                }
            },
            canCel() {},
            memberModalOkTo(memberData) {
                let rowId = memberData.rowId;
                if (rowId) {
                    let userList = [];
                    let dataList = memberData.memberData;
                    // if (this.isEmpty(dataList)) {
                    //     this.$message.error("至少选择一个用户");
                    //     return;
                    // }
                    dataList.forEach(item=>{
                        userList.push({
                            userName: item.employeeName,
                            userAccount: item.employeeCode,
                            userDisplayName:item.orgFullName
                        });
                    });
                    let records = this.data.filter(item=>item.ledgerName==rowId);
                    if (records.length > 0) {
                        let node = records[0];
                        node.userList = userList;
                        this.loading = true;
                        let that = this;
                        request(SAVE_LEDGER_AUTH, METHOD.POST, node).then((res)=>{
                            if (res.data.code == 0) {
                                that.$message.info("保存成功");
                                request(LEDGER_AUTH_LIST, METHOD.GET,).then((re)=>{
                                     if (re && re.data && re.data.data) {
                                        that.allData = re.data.data;
                                        that.nodeList = [];
                                        that.allData.forEach(item=>that.nodeList.push(item));
                                        that.changeLedgerName(that.searchForm.ledgerName);
                                         this.loading = false;
                                    }
                                });

                            }
                        });
                    }
                }
            },
        },

    }
</script>

<style scoped>
    .ant-input-number {
        width: 100%;
    }

    .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }

    /*/deep/  样式穿透
    */
    /*/deep/.ant-col-8{*/
    /*    display: block;*/
    /*    -webkit-box-sizing: border-box;*/
    /*    box-sizing: border-box;*/
    /*    width: 100%;*/
    /*}*/
    /deep/ .ant-form-item {
        text-align: right;
        margin: 0;
    }

    /deep/ .ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }

    /deep/ .customerLabel {
        width: 60%;
    }

    /deep/ .customerLabel .ant-form-item-label {
        width: 13.8%;
    }

    /deep/ #optionBudget :after {
        -webkit-appearance: none !important;
    }

    /deep/ #year::-webkit-outer-spin-button, #year::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        -moz-appearance: textfield;
    }

    /deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        -moz-appearance: textfield;
    }

    /deep/ .ant-input-number-handler-wrap {
        display: none;
    }
</style>
