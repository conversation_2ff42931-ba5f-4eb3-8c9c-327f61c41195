<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <a-row>
                    <a-col span="8">
                        <a-form-model-item label="审批环节">
                            <a-select width="width:100px" v-model="searchForm.nodeCode" style="min-width: 60px"
                                      :allowClear="true" @change="changeNode">
                                <a-select-option v-for="d in nodeList" :key="d.nodeCode">
                                    {{ d.nodeDisplayName}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col span="10">

                    </a-col>
                    <a-col span="6">

                    </a-col>
                </a-row>
            </a-form-model>
            <br>
            <a-table
                    :columns="columns"
                    rowKey="id"
                    ref="table"
                    :data-source="data"
                    :scroll="{ x: 1000 }"
                    size="small"
                    bordered
                    :customRow="onRowClick"
                    :pagination="false"
                    :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{index+1}}
                </template>
                <template slot="isOpinionReq" slot-scope="text, record">
                    <a-checkbox v-if="record.nodeCode != 'todo_receive'" :checked="record.isOpinionReq == 1" @change="changeIsOpinionReq($event, record)">简单意见</a-checkbox>
                    <a-checkbox v-if="record.nodeCode != 'todo_receive'" :checked="record.isContentReq == 1" @change="changeIsContentReq($event, record)">详细意见</a-checkbox>
                </template>

                <span slot="action" slot-scope="text,record">
                     <a @click="handleEdit(record)">编辑</a>
                </span>

            </a-table>
        </a-card>
        <!-- 开始选人组件 -->
        <YcChooseMember
                ref="chooseMember"
                @member-modal-ok="memberModalOkTo($event)"
                @member-modal-cancel="canCel"
        ></YcChooseMember>

    </div>

</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {LIST_FLOW_APPROVAL,SAVE_APPROVALS,UPDATE_IS_OPINION_REQ,UPDATE_IS_CONTENT_REQ,XSGS_REMIND} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import YcChooseMember from "../../../components/chooseMember/YcChooseMember";

    export default {
        name: "nodeAppAuth.vue",
        components:{
            YcChooseMember
        },
        data() {
            return {
                hasAuth,
                loading: false,
                searchForm: {
                    templateCode: null,
                    nodeCode:null
                },
                nodeList:[],
                allData:[],
                data: [],
                columns: [
                    {
                        title: '序号',
                        dataIndex: 'num',
                        key: 'num',
                        width: 50,
                        align: 'center',
                        fixed: true,
                        scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '审批环节', align: "center", dataIndex: 'nodeDisplayName', width: 200},
                    {title: '审批人', align: "center",dataIndex: "userDisplayName", scopedSlots: {customRender: 'approval'}},
                    {title: '审批意见是否必填', align: "center",dataIndex: "isOpinionReq", scopedSlots: {customRender: 'isOpinionReq'}},
                    {title: '功能', align: "center", width: 100, scopedSlots: {customRender: 'action'}},

                ],
                rules: {},

            }
        },
        async mounted() {
            let path = this.$route.path.split("/");
            this.searchForm.templateCode = path[path.length - 1];
            this.loading = true
            let res = await this.getData();
            this.loading = false
            let that = this;
            if (res && res.data && res.data.data) {
                res.data.data.forEach(item=>{
                    item.userDisplayName = that.coverApproval(item.approvalList);
                });
                that.data = res.data.data;
                that.allData = res.data.data;
                // 报价流程添加待办领用节点
                that.allData.forEach(item=>that.nodeList.push(item));



            }
            this.checkName();
        },
        methods: {
            async getData() {
                if (!this.searchForm.templateCode) {
                    this.$message.error("所属流程不正确");
                    return;
                }
                return  request(LIST_FLOW_APPROVAL, METHOD.GET, this.searchForm);
            },
            changeNode(value){
                if (!value) {
                    this.data = this.allData;
                } else {
                    this.data = this.allData.filter(item=>item.nodeCode == value);
                }
            },
            handleEdit(record) {
                let userList = [...record.approvalList];
                let employeesData = [];
                this.$refs.chooseMember.defaultChoose = [];

                userList.forEach(item=>{
                    employeesData.push({
                        employeeName: item.userName,
                        employeeCode: item.userAccount,
                        orgFullName:item.userDisplayName
                    });
                });
                this.$refs.chooseMember.show(record.id, [...employeesData]);
            },
            changeIsOpinionReq(e, d){
                if (e.target.checked == true) {
                    d['isOpinionReq'] = 1;
                } else {
                    d['isOpinionReq'] = 0;
                }
                request(UPDATE_IS_OPINION_REQ, METHOD.POST, {id:d.id, isOpinionReq:d['isOpinionReq']}).then((re)=>{

                });
            },
            changeIsContentReq(e, d){
                if (e.target.checked == true) {
                    d['isContentReq'] = 1;
                } else {
                    d['isContentReq'] = 0;
                }

                request(UPDATE_IS_CONTENT_REQ, METHOD.POST, {id:d.id, isContentReq:d['isContentReq']}).then((re)=>{

                });
            },
            canCel() {},
            memberModalOkTo(memberData) {
                let rowId = memberData.rowId;
                if (rowId) {
                    let approvalList = [];
                    let dataList = memberData.memberData;
                    if (this.isEmpty(dataList)) {
                        this.$message.error("至少选择一个用户");
                        return;
                    }
                    dataList.forEach(item=>{
                        approvalList.push({
                            userName: item.employeeName,
                            userAccount: item.employeeCode,
                            userDisplayName:item.orgFullName
                        });
                    });
                    let records = this.data.filter(item=>item.id==rowId);
                    if (records.length > 0) {
                        let node = records[0];
                        node.approvalList = approvalList;
                        this.loading = true;
                        let that = this;
                        request(SAVE_APPROVALS, METHOD.POST, node).then((res)=>{
                            if (res.data.code == 0) {
                                that.$message.info("保存成功");
                                request(LIST_FLOW_APPROVAL, METHOD.GET, {templateCode:that.searchForm.templateCode}).then((re)=>{
                                        if (re && re.data && re.data.data) {
                                            re.data.data.forEach(item=>{
                                                item.userDisplayName = that.coverApproval(item.approvalList);
                                            });
                                            that.allData = re.data.data;
                                            that.nodeList = [];
                                            that.allData.forEach(item=>that.nodeList.push(item));
                                            that.changeNode(that.searchForm.nodeCode);

                                    }
                                    this.loading = false;
                                });

                            }
                        });
                    }

                    // let that = this;
                    // this.data.forEach(item=>{
                    //     if (item.id == rowId) {
                    //         item.approvalList = approvalList;
                    //         item.userDisplayName = that.coverApproval(item.approvalList);
                    //     }
                    // });
                }
            },
            coverApproval(approvalList) {
                let userNames = "";
                approvalList.forEach(item => {
                    if (userNames.length == 0) {
                        userNames = item.userName;
                    } else {
                        userNames += "," + item.userName;
                    }
                });
                return userNames;

            }
        },

    }
</script>

<style scoped>
    .ant-input-number {
        width: 100%;
    }

    .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }

    /*/deep/  样式穿透
    */
    /*/deep/.ant-col-8{*/
    /*    display: block;*/
    /*    -webkit-box-sizing: border-box;*/
    /*    box-sizing: border-box;*/
    /*    width: 100%;*/
    /*}*/
    /deep/ .ant-form-item {
        text-align: right;
        margin: 0;
    }

    /deep/ .ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }

    /deep/ .customerLabel {
        width: 60%;
    }

    /deep/ .customerLabel .ant-form-item-label {
        width: 13.8%;
    }

    /deep/ #optionBudget :after {
        -webkit-appearance: none !important;
    }

    /deep/ #year::-webkit-outer-spin-button, #year::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        -moz-appearance: textfield;
    }

    /deep/ input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        -moz-appearance: textfield;
    }

    /deep/ .ant-input-number-handler-wrap {
        display: none;
    }
</style>
