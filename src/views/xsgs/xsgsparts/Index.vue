<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="配置名称"/></td>
            <td> <a-input v-model="searchForm.partsName"/></td>
            <td><a-form-model-item label="类型"/></td>
            <td>
              <a-select  style="width: 100%;min-width: 150px" v-model="searchForm.subType" >
                <a-select-option value="选型件">选型件</a-select-option>
                <a-select-option value="选装件">选装件</a-select-option>
              </a-select>
            </td>
            <td><a-button type="primary" style="float: right" @click="handleSearch" ><a-icon type="search"/>查询</a-button></td>
            <td><a-button type="primary" style="margin: 0 8px" @click="resetQuery" ><a-icon type="reload"/>重置</a-button></td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('PARTS_ORDER_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('PARTS_ORDER_DELETE')"  type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('PARTS_ORDER_IMPORT')"
                    name="file"
                    :action="UPLOAD_XSGSPARTS"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('PARTS_ORDER_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_ORDER_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PARTS_ORDER_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PARTS_ORDER_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="配置名称" prop="partsName">
                <a-input v-model="form.partsName"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="类型" prop="subType">
                <a-select  style="width: 100%" v-model="form.subType" >
                  <a-select-option value="选型件">选型件</a-select-option>
                  <a-select-option value="选装件">选装件</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="顺序" prop="seqNum">
                <a-input v-model="form.seqNum"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>
<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSPARTS_PAGE,DELETE_XSGSPARTS,SUBMIT_XSGSPARTS,DOWNLOAD_XSGSPARTS,UPLOAD_XSGSPARTS,DELETE_XSGSPARTS_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgsparts.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTS,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,PARTS_ORDER",
      PF_LIST:[],
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      selectedRowKeys:[],
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '配置名称', dataIndex: 'partsName'},
        {title: '类型', dataIndex: 'subType'},
        {title: '顺序', dataIndex: 'seqNum'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        id: [{required: false, message: 'id不能为空', trigger: 'blur'}],
        partsName: [{required: false, message: '配置名称不能为空', trigger: 'blur'}],
        subType: [{required: false, message: '类型不能为空', trigger: 'blur'}],
        seqNum: [{required: false, message: '顺序不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSPARTS_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
   onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
   },
    deleteSelectedIds(){
          if(this.selectedRowKeys.length<=0){
              this.$message.info('请至少选择一条记录！')
              return
          }
          this.$confirm({
              content: `是否确认删除选择的数据？`,
              onOk: () => {
                  this.loading = true
                  request(DELETE_XSGSPARTS_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                      .then(() => {
                          this.$message.info('删除成功')
                          this.getData()
                      }).catch(() => {
                      this.loading = false
                  })
              }
          });

      },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.name} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTS + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSPARTS, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          // let option = ""
          // if (searchForm.partsName != null) {
          //   option = "?partsName=" + searchForm.partsName
          // }
          //
          // if (searchForm.subType != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "subType=" + searchForm.subType
          // }
          // window.open(decodeURI(DOWNLOAD_XSGSPARTS+option))
          exportExcel(DOWNLOAD_XSGSPARTS, {...searchForm}, "选型选装列表排序.xlsx");
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },
  }
}
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
