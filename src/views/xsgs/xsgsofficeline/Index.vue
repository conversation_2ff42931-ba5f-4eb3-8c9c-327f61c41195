<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col span="4">
            <a-form-model-item label="板块">
              <a-select v-model="searchForm.plate">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="办事处">
              <a-select
                      v-model="searchForm.officeList"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :allowClear="true"
                      mode="multiple"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleOfficeSearch"
              >
                <a-select-option v-for="d in officeList" :key="d">
                  {{ d }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="8">
            <a-form-model-item label="客户">
              <a-select
                      v-model="searchForm.customerList"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :allowClear="true"
                      mode="multiple"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.customer">
                  {{ d.customer + '-' + d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item style="text-align: left" :wrapperCol="{span:24}">
              <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('OFFICE_LINE_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('OFFICE_LINE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload  v-if="checkPf('OFFICE_LINE_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSOFFICELINE"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button  v-if="checkPf('OFFICE_LINE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>
              <a-button  v-if="checkPf('OFFICE_LINE_USER_CONF')" type="primary" style="margin: 0 8px" @click="openUserConfig"><a-icon type="user"/>用户配置</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
              :columns="columns"
              rowKey="customer"
              :pagination="pagination"
              :data-source="data"
              :scroll="{ x: 1000 }"
              size="small"
              bordered
              :customRow="onRowClick"
              :row-selection="rowSelection"
              :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('OFFICE_LINE_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('OFFICE_LINE_DELETE')" type="vertical"/>
                  <a v-if="checkPf('OFFICE_LINE_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
            :title="form.office?'编辑':'新增'"
            :visible="dialog"
            v-drag-modal
            width="40%"
            :confirm-loading="confirmLoading"
            @ok="handleSubmit"
            @cancel="closeForm">
      <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-row>
          <a-col span="24">
            <a-form-model-item label="板块" prop="plate">
              <a-select v-model="form.plate">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="办事处" prop="office">
              <a-input v-model="form.office"/>
            </a-form-model-item>
          </a-col>
          <a-col span="24">
            <a-form-model-item label="客户号" prop="customer">
              <a-input v-model="form.customer" @blur="customerBlurs2"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="客户名称">
              <a-select
                      style="min-width: 250px"
                      v-model="form.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :allowClear="true"
                      :filter-option="false"
                      :not-found-content="null"
                      @dropdownVisibleChange="handleCustomerSearch2"
                      @search="handleCustomerSearch2"
                      @change="handleCustomerChange2"
              >
                <a-select-option v-for="d in customerList2" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="24">
            <a-form-model-item label="办事处/系统部">
              <a-select :value="form.osUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleOsUserSearch"
                        @search="handleOsUserSearch"
                        @change="handleOsUserChange"
              >
                <a-select-option v-for="d in osUserList" @click="selectedUser(d,'os')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="办事处/系统部正职">
              <a-select :value="form.osjUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleOsjUserSearch"
                        @search="handleOsjUserSearch"
                        @change="handleOsjUserChange"
              >
                <a-select-option v-for="d in osjUserList" @click="selectedUser(d,'osj')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="价值工程审核">
              <a-select :value="form.veUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleVeUserSearch"
                        @search="handleVeUserSearch"
                        @change="handleVeUserChange"
              >
                <a-select-option v-for="d in veUserList" @click="selectedUser(d,'ve')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="营销商务领导">
              <a-select :value="form.mbUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleMbUserSearch"
                        @search="handleMbUserSearch"
                        @change="handleMbUserChange"
              >
                <a-select-option v-for="d in mbUserList" @click="selectedUser(d,'mb')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="营销公司总监">
              <a-select :value="form.mlUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleMlUserSearch"
                        @search="handleMlUserSearch"
                        @change="handleMlUserChange"
              >
                <a-select-option v-for="d in mlUserList" @click="selectedUser(d,'ml')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="营销公司总经理">
              <a-select :value="form.mmUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleMmUserSearch"
                        @search="handleMmUserSearch"
                        @change="handleMmUserChange"
              >
                <a-select-option v-for="d in mmUserList" @click="selectedUser(d,'mm')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
            <a-form-model-item label="营销线领导">
              <a-select :value="form.mllUserDisplayName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        :allowClear='true'
                        @dropdownVisibleChange="handleMllUserSearch"
                        @search="handleMllUserSearch"
                        @change="handleMllUserChange"
              >
                <a-select-option v-for="d in mllUserList" @click="selectedUser(d,'mll')" :key="d.employeeCode" :label="d.employeeName">
                  {{ d.displayName }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
  import {hasAuth} from "@/utils/authority-utils";
  import {LIST_XSGSOFFICELINE_PAGE,LIST_XSGSCUSTOMER,UPLOAD_XSGSOFFICELINE,DOWNLOAD_XSGSOFFICELINE,GET_BY_OFFICE_CUSTOMER,SUBMIT_XSGSOFFICELINE_EDIT,OFFICE_LIST,DELETE_BY_OFFICE_CUSTOMER} from "@/services/api/xsgs";
  import {GET_YC_USER} from "@/services/api/user";
  import {METHOD, request,exportExcel} from "@/utils/request";
  import Cookie from 'js-cookie'
  export default {
    name: "xsgsofficeline.vue",
    data() {
      return {
        hasAuth,
        UPLOAD_XSGSOFFICELINE,
        Cookie,
        loading: false,
        dialog: false,
        advanced:false,
        // 功能权限关键字
        PF_FIELD:"BASE_DATA_MANAGER,CUSTOMER_DATA,OFFICE_LINE",
        PF_LIST:[],
        confirmLoading: false,
        searchForm: {
          customer:null,
          customerName:null,
          customerList:[],
          officeList:[]
        },
        customerList:[],
        customerList2:[],
        allCustomerList:[],
        officeList:[],
        allOfficeList:[],
        osUserList:[],
        osjUserList:[],
        veUserList:[],
        mbUserList:[],
        mlUserList:[],
        mmUserList:[],
        mllUserList:[],
        form: {
          customer:null,
          customerName:null,
          machineType:null,
          materialNo:null,
        },
        selectedRowKeys: [],
        selectedRows:[],
        pagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50','100'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                          total / this.pagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },
        data: [],
        columns: [
          {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
              customRender: 'num'
            }
          },
          {title: '办事处', align: 'center', dataIndex: 'office',width:130},
          {title: '板块', align: 'center', dataIndex: 'plate',width:60},
          {title: '客户号', align: 'center', dataIndex: 'customer',width:90},
          {title: '客户名称', dataIndex: 'customerName',width:220},
          {title: '办事处/系统部', align: 'center', dataIndex: 'osUserName',width:100,customCell:this.checkCustomerOs},
          {title: '办事处/系统部正职', align: 'center', dataIndex: 'osjUserName',width:150,customCell:this.checkCustomerOsj},
          {title: '价值工程审核', align: 'center', dataIndex: 'veUserName',width:100,customCell:this.checkCustomerVe},
          {title: '营销商务领导', align: 'center', dataIndex: 'mbUserName',width:100,customCell:this.checkCustomerMb},
          {title: '营销公司总监', align: 'center', dataIndex: 'mlUserName',width:100,customCell:this.checkCustomerMl},
          {title: '营销公司总经理', align: 'center', dataIndex: 'mmUserName',width:130,customCell:this.checkCustomerMm},
          {title: '营销线领导', align: 'center', dataIndex: 'mllUserName',width:100,customCell:this.checkCustomerMll},
          {title: '操作', fixed: 'right', align:"center", width: 135, scopedSlots: {customRender: 'action'}}
        ],

        rules: {
          customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
          plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
          office: [{required: true, message: '办事处不能为空', trigger: 'blur'}],

        },
      }
    },
    computed: {
      rowSelection() {
        return {
          onChange: (selectedRowKeys, selectedRows) => {
            this.selectedRowKeys = selectedRowKeys;
            this.selectedRows = selectedRows;
            // console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          },
          getCheckboxProps: record => ({
            props: {
              disabled: record.name === 'Disabled User', // Column configuration not to be checked
              name: record.name,
            },
          }),
        };
      },
    },
    async created() {
      // 获取页面功能权限
      await this.getUserAuth(this.PF_FIELD);
      this.PF_LIST = this.getPfList(this.PF_FIELD);
    },
    mounted() {
      this.getCustomerList();
      this.getOfficeList();
      this.getData()
      this.checkName()
    },
    methods: {
      checkPf(field) {
        return this.PF_LIST.findIndex(s=>s == field) != -1;
      },
      customerBlurs(){
          let customer = this.searchForm.customer;
          if (!customer) {
            this.searchForm.customerName = null;
          }
          let newData = this.customerList.filter(item => item.customer == customer);
          if (newData.length > 0) {
            this.searchForm.customerName = newData[0].name;
          } else {
            newData = this.allCustomerList.filter(item => item.customer == customer);
            if (newData.length > 0) {
              this.searchForm.customerName = newData[0].name;
            }
          }
      },

      handleCustomerSearch(value) {
        if (value) {
          this.customerList = this.allCustomerList.filter(s=>s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
        } else {
          this.customerList = this.allCustomerList;
        }

      },
      handleCustomerChange(value) {
        if (value) {
          this.customerList.forEach(item =>{
            if(item.name == value){
              this.searchForm.customer = item.customer
              this.searchForm.customerName = item.name
            }
          })
        } else {
          this.searchForm.customer = null
        }

      },
      handleOfficeSearch(value){
        if (value) {
          this.officeList = this.allOfficeList.filter(s=>s.indexOf(value) != -1);
        } else {
          this.officeList = this.allOfficeList;
        }
      },
      customerBlurs2(){
        let customer = this.form.customer;
        if (!customer) {
          this.form.customerName = null;
        }
        let newData = this.customerList2.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.form.customerName = newData[0].name;
          this.customerList2 = newData[0]
        } else {
          newData = this.allCustomerList.filter(item => item.customer == customer);
          if (newData.length > 0) {
            this.form.customerName = newData[0].name;
            this.customerList2 = newData[0]
          }
        }
      },

      handleCustomerSearch2(value) {
        if (value) {
          this.customerList2 = this.allCustomerList.filter(s=>s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
        } else {
          this.customerList2 = this.allCustomerList;
        }

      },
      handleCustomerChange2(value) {
        if (value) {
          this.customerList.forEach(item =>{
            if(item.name == value){
              this.form.customer = item.customer
              this.form.customerName = item.name
            }
          })
        } else {
          this.form.customer = null
        }
      },
      getCustomerList(){
        request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
          this.allCustomerList = res.data.data
          this.customerList = res.data.data
          this.customerList2 = res.data.data
        })
      },
      getOfficeList(){
        request(OFFICE_LIST, METHOD.GET, {}).then(res => {
          this.officeList = res.data.data
          this.allOfficeList = res.data.data
        })
      },
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys;
      },
      getData: function () {
        this.loading = true
        let {current, size} = this.pagination
        request(LIST_XSGSOFFICELINE_PAGE, METHOD.POST, {...this.searchForm, current, size})
                .then(res => {
                  const {records, total} = res.data.data
                  this.loading = false
                  this.data = records
                  this.pagination.total = parseInt(total)
                })
      },
      checkCustomerOs(record) {
        if (record.osIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerOsj(record) {
        if (record.osjIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerVe(record) {
        if (record.veIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerMb(record) {
        if (record.mbIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerMl(record) {
        if (record.mlIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerMm(record) {
        if (record.mmIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      checkCustomerMll(record) {
        if (record.mllIsConfirmed > 1) {
          return {style:{'background-color': 'rgb(255,210,177)',}}
        }
      },
      handleOsUserSearch(value) {
        this.osUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.osUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleOsUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.osUserName = null;
            this.form.osUser = null;
            this.form.osUserDisplayName = null;
          });

        }
      },
      handleOsjUserSearch(value) {
        this.osjUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.osjUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleOsjUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.osjUserName = null;
            this.form.osjUser = null;
            this.form.osjUserDisplayName = null;
          });

        }
      },
      handleVeUserSearch(value) {
        this.veUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.veUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleVeUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.veUserName = null;
            this.form.veUser = null;
            this.form.veUserDisplayName = null;
          });

        }
      },
      handleMbUserSearch(value) {
        this.mbUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.mbUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleMbUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.mbUserName = null;
            this.form.mbUser = null;
            this.form.mbUserDisplayName = null;
          });
        }
      },
      handleMlUserSearch(value) {
        this.mlUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.mlUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleMlUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.mlUserName = null;
            this.form.mlUser = null;
            this.form.mlUserDisplayName = null;
          });
        }
      },
      handleMmUserSearch(value) {
        this.mmUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.mmUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleMmUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.mmUserName = null;
            this.form.mmUser = null;
            this.form.mmUserDisplayName = null;
          });
        }
      },
      handleMllUserSearch(value) {
        this.mllUserList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.mllUserList = this.cutList(res.data.data,0,20);
                })
      },
      handleMllUserChange(value) {
        if (!value) {
          this.$nextTick(()=>{
            this.form.mllUserName = null;
            this.form.mllUser = null;
            this.form.mllUserDisplayName = null;
          });
        }
      },
      selectedUser(d, type) {
        this.$nextTick(()=>{
          this.form[type + 'UserName'] = d.employeeName;
          this.form[type + 'User'] = d.employeeCode;
          this.form[type + 'UserDisplayName'] = d.displayName;
        });
      },
      deleteSelectedIds(){
        if(this.selectedRows.length<=0){
          this.$message.info('请至少选择一条记录！')
          return
        }
        this.$confirm({
          content: `是否确认删除选择的数据？`,
          onOk: () => {
            let ids = this.selectedRows;
            this.doDelete(ids)
          }
        });

      },
      doDelete(ids) {
        this.loading = true
        request(DELETE_BY_OFFICE_CUSTOMER, METHOD.POST,[...ids])
                .then(() => {
                  this.$message.info('删除成功')
                  this.getData()
                }).catch(() => {
          this.loading = false
        })
      },
      handleDelete(record) {
        this.$confirm({
          content: `是否确认删除 ${record.customerName} ？`,
          onOk: () => {
            let ids = [];
            ids.push(record);
            this.doDelete(ids);
          }
        });
      },
      handleSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            request(SUBMIT_XSGSOFFICELINE_EDIT, METHOD.POST, this.form)
                    .then(() => {
                      this.getData()
                      this.$message.info('提交成功')
                      this.closeForm()
                    })
          }
        })
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.searchForm = {

        }
        this.handleSearch();
      },
      handleEdit(record) {
        this.dialog = true
        this.form = {...record}
        let that = this;
        request(GET_BY_OFFICE_CUSTOMER, METHOD.POST, {...this.form})
                .then(res => {
                  that.form = res.data.data;
                  let customer = that.form.customer;
                  let custoemrs = that.allCustomerList.filter(s=>s.customer == customer);
                  if (!that.isEmpty(custoemrs)) {
                    that.form.customerName = custoemrs[0].name;
                  }
                })
      },
      handleSearch() {
        this.pagination.current = 1
        this.getData()
      },
      closeForm() {
        this.dialog = false
        this.$refs.form.resetFields()
        this.form = {}
      },
      onPageChange(page, size) {
        this.pagination.current = page;
        this.pagination.size = size.toString();
        this.getData();
      },
      onSizeChange(current, size) {
        this.pagination.current = 1;
        this.pagination.size = size.toString();
        this.getData();
      },
      handleUpload(info) {
        this.loading = true
        if (info.file.status !== 'uploading') {
          this.getData()
        }
        if (info.file.status === 'done') {
          this.$message.success(`${info.file.name} 导入成功`);
          if(info.file.response.data.length>0){
            let mesage=''
            info.file.response.data.forEach(item=>{
              mesage +=item+"\n"
            })
            this.$warning({
              title: '导入失败提示',
              content: (
                      <div style="white-space: pre-wrap;">{mesage}</div>
            ),
                    width:445
          });
          }
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 导入失败`);
        }
      },
      /** 导出按钮操作 */
      handleExportExcel() {
        exportExcel(DOWNLOAD_XSGSOFFICELINE, {...this.searchForm}, "客户权限管理.xlsx")
      },
      openUserConfig(){
        this.$router.push({path: 'xsgs_user_config', props: true})
      }


    }
  }
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
