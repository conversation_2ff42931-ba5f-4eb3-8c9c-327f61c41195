<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="员工号"/></td>
            <td> <a-input v-model="searchForm.userId"/></td>
            <td><a-form-model-item label="名字"/></td>
            <td><a-input v-model="searchForm.userName"/></td>
            <td><a-button type="primary" style="float: right" @click="handleSearch" ><a-icon type="search"/>查询</a-button></td>
            <td><a-button type="primary" style="margin: 0 8px" @click="resetQuery" ><a-icon type="reload"/>重置</a-button></td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
              :columns="columns"
              rowKey="id"
              :pagination="pagination"
              :data-source="data"
              :scroll="{ x: 1000 }"
              size="middle"
              :customRow="onRowClick"
              :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
            :title="form.id?'编辑':'新增'"
            :visible="dialog"
            :confirm-loading="confirmLoading"
            width="50%"
            @ok="handleSubmit"
            @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="员工号" prop="userId">
              <a-input v-model="form.userId"/>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="名字" prop="userName">
              <a-input v-model="form.userName"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row class="form-row">

          <a-col :lg="24" :md="24" :sm="24">
            <a-tree
                    v-model="checkedKeys"
                    checkable
                    :expanded-keys="expandedKeys"
                    :auto-expand-parent="autoExpandParent"
                    :selected-keys="selectedKeys"
                    :tree-data="treeData"
                    @expand="onExpand"
                    @check="onCheck"
                    @select="onSelect"
            />
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
  const treeData =[
    {

      title: '基础数据', key: 'col_0',
      children: [
        {title: '客户号', key: 'customer',width: 80, align: 'center',scopedSlots: { customRender: 'customer' }},
        {
          title: '年度', key: 'year',width: 65, align: 'center'
        },
        {title: '合同号', key: 'contract',width: 65, align: 'center'},
        {
          title: '板块', key: 'plate',width: 65, align: 'center'
        },
        {
          title: '排放', key: 'blowoff',width: 65, align: 'center'
        },
        {
          title: '系列', key: 'series',width: 65, align: 'center'
        },
        {
          title: '产品型号', key: 'productModel',width: 100, align: 'center'
        },
        {
          title: '功率(PS)', key: 'ps',width: 65, align: 'center'
        },
        {
          title: '状态机', key: 'platformName',width: 80, align: 'center',slots: { default: 'action' }

        },
        {title: '品系1', key: 'type',width: 65, align: 'center'},
        {title: '品系2', key: 'strain1',width: 65, align: 'center'},
        {title: '品系3', key: 'strain2',width: 65, align: 'center'},
        {title: '品系4', key: 'strain3',width: 65, align: 'center'},
        {title: '前年销量', key: 'yearSale1',width: 65, align: 'center'},
        {title: '去年销量', key: 'yearSale2',width: 65, align: 'center'},
        {title: '今年销量', key: 'yearSale3',width: 65, align: 'center'},

      ]
    },
    {
      title: '上一年度价格信息',key:'lastYearTitle',
      children: [
        {title: '基本型开票价', key: 'synBasicTicketPrice',width: 80, align: 'center'},
        {title: '整机开票价', key: 'synUnitTicketPrice',width: 80, align: 'center'},
        {title: '基本型实际价格', key: 'synBasicNetPrice',width: 90, align: 'center'},
        {title: '整机实际价格', key: 'synUnitNetPrice',width: 80, align: 'center'},
        {title: '选配件合计', key: 'synOptionAmount',width: 80, align: 'center'},
        {title: '结算价', key: 'synSettlementPrice',width: 80, align: 'center'}
      ]
    },
    {
      title: '上一年度折让信息', align: 'center',key:'prevYearTitle',
      children: [
        {title: '阶梯折让',  align: 'center', key: 'prevJtZrId',
          children: [
          ]
        },
        {title: '非阶梯折让',  align: 'center', key: 'prevZrId',
          children: []
        },
        {title: '折让合计', key: 'synBalanceAmount',width: 80, align: 'center'},
      ]
    },
    {
      title: '本年度价格信息',  key:'thisYearTitle',
      children: [
        {title: '降价', key: 'reducePrice',width: 60, align: 'center'},
        {title: '基本型开票价', key: 'basicTicketPrice',width: 80, align: 'center'},
        {title: '整机开票价', key: 'unitTicketPrice',width: 80, align: 'center'},
        {title: '基本型实际价格', key: 'basicNetPrice',width: 90, align: 'center'},
        {title: '整机实际价格', key: 'unitNetPrice',width: 80, align: 'center'},
        /*            {title: '选配件合计', field: 'optionAmount',width: 165, align: 'center'},
                      {title: '折让合计', field: 'balanceAmount',width: 165, align: 'center'},*/
      ]
    },
    {
      title: '总部预算信息', key: 'zbBudget',
      children: [
        {title: '基本型预算', key: 'zbBasicBudgetBalance',width: 80, align: 'center'},
        {title: '选配件预算', key: 'zbChooseBudget',width: 80, align: 'center'},
        {title: '整机预算', key: 'zbBudgetActualPrice',width: 80, align: 'center'},
        {title: '预算空间', key: 'zbBudgetSpace',width: 80, align: 'center'},
        {title: '基本型预算结余', key: 'zbBasicBalance',width: 80, align: 'center'},
        {title: '整机预算结余', key: 'zbUnitBalance',width: 80, align: 'center'},

      ]


    },
    {
      title: '系统部预算信息', key: 'sysBudget',
      children: [
        {title: '基本型预算', key: 'sysBasicBudgetBalance',width: 80, align: 'center'},
        {title: '选配件预算', key: 'sysChooseBudget',width: 80, align: 'center'},
        {title: '整机预算', key: 'sysBudgetActualPrice',width: 80, align: 'center'},
        {title: '预算空间', key: 'sysBudgetSpace',width: 80, align: 'center'},
        {title: '基本型预算结余', key: 'sysBasicBalance',width: 90, align: 'center'},
        {title: '整机预算结余', key: 'sysUnitBalance',width: 80, align: 'center'},
      ]
    },
    {
      title: '边贡信息', key: 'bg',
      children: [
        {title: '材料成本', key: 'costOfMeterial',width: 80, align: 'center'},
        {title: '边际贡献', key: 'contribution',width: 80, align: 'center'},
        {title: '边际贡献率(%)', key: 'contributionPercent',width: 80, align: 'center'},
      ]
    },
    {title: '结算价信息',   key: 'settlementPriceId',
      children: [
        {title: '结算价', key: 'settlementPrice',width: 80, align: 'center'},
      ]
    },
    {title: '阶梯折让',    key: 'jtzrId',
      children: [
      ]
    },
    {title: '非阶梯折让',  key: 'zrId',
      children: []
    },
    {title: '折让总额',  align: 'center', key: 'balanceAmount',width: 80},
    {title: '选配件降价合计', key: 'optionReducePrice',width: 60, align: 'center'},
    {title: '选配件合计',  align: 'center', key: 'optionAmount',width: 80},
    {title: '选型件', key: 'basicList' , align: 'center',
      children: []
    },
    {title: '选装件', key: 'optionListColumns' , align: 'center',
      children: []
    },
    {title: '备注', key: 'remarks' , align: 'center',width: 80, ellipsis: true},
  ];
  console.log(treeData);

  import {hasAuth} from "@/utils/authority-utils";
  import {LIST_XSGSREPORTCOLUMNS_PAGE,DELETE_XSGSREPORTCOLUMNS,SUBMIT_XSGSREPORTCOLUMNS,DOWNLOAD_XSGSREPORTCOLUMNS,UPLOAD_XSGSREPORTCOLUMNS} from "@/services/api/xsgs";
  import {METHOD, request} from "@/utils/request";
  import Cookie from 'js-cookie'
  export default {
    name: "xsgsreportcolumns.vue",
    data() {
      return {
        hasAuth,
        UPLOAD_XSGSREPORTCOLUMNS,
        Cookie,
        loading: false,
        dialog: false,
        confirmLoading: false,
        searchForm: {},
        form: {},
        pagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                          total / this.pagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },
        data: [],
        columns: [
          {title: '员工号', dataIndex: 'userId'},
          {title: '名字', dataIndex: 'userName'},
          {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
        ],
        rules: {
          userId: [{required: true, message: '员工号不能为空', trigger: 'blur'}],
          field: [{required: false, message: '字段不能为空', trigger: 'blur'}],
          userName: [{required: true, message: '名字不能为空', trigger: 'blur'}],
        },
        expandedKeys: [],
        autoExpandParent: true,
        checkedKeys: [],
        selectedKeys: [],
        halfCheckedKeys: [],
        treeData,
      }
    },
    mounted() {
      this.getData()
      this.checkName()
    },
    methods: {
      getData: function () {
        this.loading = true
        let {current, size} = this.pagination
        request(LIST_XSGSREPORTCOLUMNS_PAGE, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
                  const {records, total} = res.data.data
                  this.loading = false
                  this.data = records
                  this.pagination.total = parseInt(total)
                })
      },
      handleDelete(record) {
        this.$confirm({
          content: `是否确认删除 ${record.name} ？`,
          onOk: () => {
            this.loading = true
            request(DELETE_XSGSREPORTCOLUMNS + record.id, METHOD.DELETE)
                    .then(() => {
                      this.$message.info('删除成功')
                      this.getData()
                    }).catch(() => {
              this.loading = false
            })
          }
        });
      },
      handleSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            this.form.checkedKeys = this.checkedKeys
            this.form.halfCheckeds = this.halfCheckedKeys
            request(SUBMIT_XSGSREPORTCOLUMNS, METHOD.POST, this.form)
                    .then(() => {
                      this.getData()
                      this.$message.info('提交成功')
                      this.closeForm()
                    })
          }
        })
      },
      /** 新增，打开对话框 */
      handleNew() {
        this.dialog = true
        this.checkedKeys=['type','platformName','ps','productModel','series','blowoff','plate','contract','year','customer','col_0']
      },
      /** 编辑，打开对话框 */
      handleEdit(record) {
        this.dialog = true
        this.form = {...record}
        let cols = eval(record.field)
        this.checkedKeys = cols
      },
      /** 处理查询 */
      handleSearch() {
        this.pagination.current = 1
        this.getData()
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.searchForm = {}
        this.handleSearch();
      },
      /** 关闭对话框，表单重置 */
      closeForm() {
        this.dialog = false
        this.$refs.form.resetFields()
        this.form = {}
      },


      onPageChange(page, size) {
        this.pagination.current = page;
        this.pagination.size = size;
        this.getData();
      },
      onSizeChange(current, size) {
        this.pagination.current = 1;
        this.pagination.size = size;
        this.getData();
      },
      onExpand(expandedKeys) {

        this.expandedKeys = expandedKeys;
        this.autoExpandParent = false;
      },
      onCheck(checkedKeys, info) {
        this.halfCheckedKeys = info.halfCheckedKeys
        this.checkedKeys = checkedKeys;
      },
      onSelect(selectedKeys, info) {
        this.selectedKeys = selectedKeys;
      },
    }
  }
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
