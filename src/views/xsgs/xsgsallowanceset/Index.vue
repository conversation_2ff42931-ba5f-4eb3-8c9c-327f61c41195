<template>
  <div>
    <a-card>
      <a-form-model   ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度"/></td>
            <td><a-input v-model="searchForm.year"/></td>

            <td><a-form-model-item label="客户号"/></td>
            <td><a-input v-model="searchForm.customer" @blur="customerBlurs" /></td>
            <td><a-form-model-item label="折让"/></td>
            <td>
              <a-select v-model="searchForm.seqName">
                <a-select-option v-for="d in zrList" :key="d.seqName">
                  {{ d.seqName }}
                </a-select-option>
              </a-select>

            </td>
          </tr>
          <tr>
            <td><a-form-model-item label="客户名称"/></td>
            <td>
              <a-select
                      v-model="searchForm.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="searchForm.customerName"/>-->
            </td>

            <td><a-form-model-item label="计算类型" property="allowanceType"/></td>
            <td><a-select v-model="searchForm.allowanceType">
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="结算价">结算价</a-select-option>
              <a-select-option value="金额">金额</a-select-option>
              <a-select-option value="阶梯金额">阶梯金额</a-select-option>
              <a-select-option value="百分比">百分比</a-select-option>
            </a-select></td>

            <td  colspan="2"><a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button></td>
          </tr>


          <a-col :md="!advanced && 6 || 24" :sm="24" >
             <span class="table-page-search-submitButtons">
                <div style="margin-top: 4px">

                </div>
            </span>
          </a-col>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('DISCOUNT_POLICY_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
              :columns="columns"
              rowKey="id"
              :pagination="pagination"
              :data-source="data"
              :scroll="{ x: 1000 }"
              size="small"
              :customRow="onRowClick"
              bordered
              :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('DISCOUNT_POLICY_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('DISCOUNT_POLICY_DELETE')" type="vertical"/>
                  <a v-if="checkPf('DISCOUNT_POLICY_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSALLOWANCESET_PAGE,DELETE_XSGSALLOWANCESET,SUBMIT_XSGSALLOWANCESET,DOWNLOAD_XSGSALLOWANCESET,UPLOAD_XSGSALLOWANCESET,DOWNLOADPRICE_XSGSALLOWANCESET,
  UPLOADPRICE_XSGSALLOWANCESET,LIST_XSGSCUSTOMER,LIST_ZR} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgsallowanceset.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSALLOWANCESET,
      UPLOADPRICE_XSGSALLOWANCESET,
      Cookie,
      loading: false,
      dialog: false,
      // 功能权限关键字
      PF_FIELD:"BUSINESS_POLICY_MANAGER,DISCOUNT_POLICY",
      PF_LIST:[],
      confirmLoading: false,
      searchForm: {
        customerName:null,
        customer:null,
      },
      customerList:[],
      form: {},
      // 高级搜索 展开/关闭
      advanced: false,
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],

      zrList: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', dataIndex: 'year', width: 100},
        {title: '客户号', dataIndex: 'customer', width: 100},
        {title: '客户名称',width:500, dataIndex: 'customerName'},
        {title: '计算类型', dataIndex: 'allowanceType'},
        {title: '折让名称', dataIndex: 'seqName'},
        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],

    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.listZr()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    listZr(){
      request(LIST_ZR, METHOD.GET, {})
              .then(res => {
                this.zrList =  res.data
              })

    },
    customerBlurs(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.searchForm.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.searchForm.customer!=null){
            if(!this.searchForm.customer.split(" ").join("").length == 0){
              this.searchForm.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item =>{
        if(item.name == value){
          this.searchForm.customer = item.customer
        }
      })
    },

    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSALLOWANCESET_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },


    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.seqName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSALLOWANCESET + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSALLOWANCESET, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.$router.push({path:'xsgs_allowance_set_detail/:id', props: true})
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.$router.push({path:'xsgs_allowance_set_detail/'+record.id, props: true})
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm= {
        customerName:null,
        customer:null,
      },
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否导出折让结算价模板?',
        onOk() {
          // window.open(decodeURI(DOWNLOADPRICE_XSGSALLOWANCESET))
          exportExcel(DOWNLOADPRICE_XSGSALLOWANCESET, {}, "折让结算价导入模板.xlsx")
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
