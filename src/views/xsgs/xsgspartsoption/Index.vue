<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>

            <td><a-form-model-item label="客户号" /></td>
            <td><a-input v-model="searchForm.customer"/></td>

            <td><a-form-model-item label="板块" /></td>
            <td>
              <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="排放" /></td>
            <td>
              <a-select v-model="blowoff" mode="multiple"  style="min-width: 150px">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="系列" /></td>
            <td>
              <a-select v-model="series"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zxl_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>


          </tr>
          <tr>
            <td><a-form-model-item label="产品型号[简化]" /></td>
            <td>
              <a-select v-model="productModel"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
             </td>

            <td><a-form-model-item label="品系" /></td>
            <td>
              <a-select v-model="type"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zpx_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="类型" /></td>
            <td>
              <a-select v-model="searchForm.mainType">
                <a-select-option value="选型件">选型件</a-select-option>
                <a-select-option value="选装件">选装件</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="配置名称"  /></td>
            <td><a-input v-model="searchForm.partsName"/></td>

            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('PARTS_OPTIONS_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('PARTS_OPTIONS_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('PARTS_OPTIONS_IMPORT')"
                    name="file"
                    :action="UPLOAD_XSGSPARTSOPTION"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('PARTS_OPTIONS_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_OPTIONS_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PARTS_OPTIONS_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PARTS_OPTIONS_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="客户号" prop="customer">
                <a-input v-model.trim="form.customer"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="板块" prop="platformName">
                <a-select v-model="form.platformName"  style="width: 100%">
                  <a-select-option value="卡车" >卡车</a-select-option>
                  <a-select-option value="客车" >客车</a-select-option>
                  <a-select-option value="新能源" >新能源</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>

          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="排放" prop="blowoff">
                <a-select v-model="form.blowoff"  style="width: 100%">
                  <a-select-option value="国6" >国6</a-select-option>
                  <a-select-option value="国5" >国5</a-select-option>
                  <a-select-option value="国4" >国4</a-select-option>
                  <a-select-option value="国3" >国3</a-select-option>
                  <a-select-option value="国2" >国2</a-select-option>
                  <a-select-option value="国1" >国1</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系列" prop="series">
                <a-input v-model="form.series"/>
              </a-form-model-item>
            </a-col>

          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="产品型号[简化]" prop="productModel">
                <a-input v-model="form.productModel" @change="changes"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="品系" prop="type">
                <a-input v-model="form.type"/>
              </a-form-model-item>
            </a-col>


          </a-row>

        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="类型" prop="mainType">
              <a-select  v-model="form.mainType">
                <a-select-option value="选型件">选型件</a-select-option>
                <a-select-option value="选装件">选装件</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="配置名称" prop="partsName">
              <a-input v-model="form.partsName"/>
            </a-form-model-item>
          </a-col>
        </a-row>





      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSPARTSOPTION_PAGE,DELETE_XSGSPARTSOPTION,GETSEAChLIST_XSGSPARTSOPTION,SUBMIT_XSGSPARTSOPTION,DOWNLOAD_XSGSPARTSOPTION,UPLOAD_XSGSPARTSOPTION,DELETE_XSGS_PARTS_OPTION_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgspartsoption.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSOPTION,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},

      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,PARTS_OPTIONS",
      PF_LIST:[],
      productModel:[],
      blowoff:[],
      plate:[],
      series:[],
      type:[],
      zcpxh_list:[], //产品型号
      zxl_list:[],//系列
      zpx_list:[],//品系


      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center',  scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '客户号', dataIndex: 'customer', width: 60},
        {title: '板块', dataIndex: 'platformName', width: 60},
        {title: '排放', dataIndex: 'blowoff', width: 60},
        {title: '系列', dataIndex: 'series', width: 60},
        {title: '产品型号[简化]', dataIndex: 'productModel', width: 100},
        {title: '品系', dataIndex: 'type', width: 60},
        {title: '类型', dataIndex: 'mainType', width: 60},
        {title: '配置名称', dataIndex: 'partsName', width: 100},
        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        id: [{required: false, message: 'id不能为空', trigger: 'blur'}],
        platformName: [{required: false, message: '板块不能为空', trigger: 'blur'}],
        blowoff: [{required: false, message: '排放不能为空', trigger: 'blur'}],
        series: [{required: false, message: '系列不能为空', trigger: 'blur'}],
        productModel: [{required: false, message: '产品型号[简化]不能为空', trigger: 'blur'}],
        type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
        partsName: [{required: false, message: '配置名称不能为空', trigger: 'blur'}],
        mainType: [{required: false, message: '类型不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
        isDelete: [{required: false, message: '删除标志不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
    this.getSearchList()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getSearchList(){
      request(GETSEAChLIST_XSGSPARTSOPTION, METHOD.GET)
              .then(res => {
                if(res.data.data.cpxh!=null){
                  this.zcpxh_list=res.data.data.cpxh
                }

                if(res.data.data.xl!=null){
                  this.zxl_list=res.data.data.xl
                }
                if(res.data.data.px!=null){
                  this.zpx_list=res.data.data.px
                }
              })
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      this.searchForm.productModels = this.productModel.join(",")
      this.searchForm.blowoffs = this.blowoff.join(",")
      this.searchForm.plates = this.plate.join(",")
      this.searchForm.seriess = this.series.join(",")
      this.searchForm.types = this.type.join(",")
      request(LIST_XSGSPARTSOPTION_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_PARTS_OPTION_LIST, METHOD.POST,{ids:this.selectedRowKeys})
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.partsName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSOPTION + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`系列不能为空`);
        return;
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSPARTSOPTION, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增 产品型号[简化] 输入回调 */
    changes(){
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`请先填写系列`);
        this.form.productModel = undefined
      }
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModel=[],
      this.blowoff=[],
      this.plate=[],
      this.series=[],
      this.type=[],

      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.searchForm.productModels = this.productModel
      this.searchForm.blowoffs = this.blowoff
      this.searchForm.plates = this.plate
      this.searchForm.seriess = this.series
      this.searchForm.types = this.type
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {

          // let option = ""
          // if (searchForm.platformName != null) {
          //   option = "?platformName=" + searchForm.platformName
          // }
          //
          // if (searchForm.blowoff != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "blowoff=" + searchForm.blowoff
          // }
          // if (searchForm.customer != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customer=" + searchForm.customer
          // }
          //
          // if (searchForm.series != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "series=" + searchForm.series
          // }
          //
          // if (searchForm.productModel != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "productModel=" + searchForm.productModel
          // }
          //
          // if (searchForm.type != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "type=" + searchForm.type
          // }
          // if (searchForm.mainType != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "mainType=" + searchForm.mainType
          // }
          // if (searchForm.partsName != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "partsName=" + searchForm.partsName
          // }

          // window.open(decodeURI(DOWNLOAD_XSGSPARTSOPTION + option))
          exportExcel(DOWNLOAD_XSGSPARTSOPTION, {...searchForm}, "选型选装清单.xlsx")


        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
