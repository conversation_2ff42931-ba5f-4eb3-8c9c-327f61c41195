<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col span="6">
            <a-form-model-item label="用户名">
              <a-input v-model="searchForm.userName"/>
            </a-form-model-item>
          </a-col>

          <a-col :span="8">
            <a-form-model-item style="text-align: left">
              <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-show="advanced">

        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('OFFICE_LINE_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('OFFICE_LINE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload  v-if="checkPf('OFFICE_LINE_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSUSERCONFIG"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button  v-if="checkPf('OFFICE_LINE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
              :columns="columns"
              rowKey="id"
              :pagination="pagination"
              :data-source="data"
              :scroll="{ x: 1000 }"
              size="small"
              bordered
              :customRow="onRowClick"
              :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('OFFICE_LINE_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
            :title="form.id?'编辑':'新增'"
            :visible="dialog"
            :confirm-loading="confirmLoading"
            @ok="handleSubmit"
            @cancel="closeForm">
      <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 5}" :wrapperCol="{span: 19}">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="用户名称">
                <a-select v-if="!isSetting" :value="form.userName"
                          show-search
                          :default-active-first-option="false"
                          :show-arrow="false"
                          :filter-option="false"
                          :not-found-content="null"
                          :allowClear='true'
                          @search="handleUserSearch"
                          @change="handleUserChange"
                >
                  <a-select-option v-for="d in userList" @click="selectedUser(d)" :key="d.employeeCode" :label="d.employeeName">
                    {{ d.displayName }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="用户工号">
                <a-input v-if="!isSetting" v-model="form.userAccount"/>
              </a-form-model-item>
              <a-form-model-item label="用户部门">
                <a-input v-if="!isSetting" v-model="form.userDisplayName"/>
              </a-form-model-item>
            </a-col>
          </a-row>
      </a-form-model>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
  import {hasAuth} from "@/utils/authority-utils";
  import {LIST_XSGSUSERCONFIG_PAGE,SUBMIT_XSGSUSERCONFIG,UPLOAD_XSGSUSERCONFIG,DELETE_XSGSUSERCONFIG_IDS,DOWNLOAD_XSGSUSERCONFIG} from "@/services/api/xsgs";
  import {METHOD, request,exportExcel} from "@/utils/request";
  import {GET_YC_USER} from "@/services/api/user";
  import Cookie from 'js-cookie'
  export default {
    name: "xsgsuserconfig.vue",
    data() {
      return {
        hasAuth,
        UPLOAD_XSGSUSERCONFIG,
        Cookie,
        loading: false,
        dialog: false,
        advanced:false,
        isSetting:false,
        // 功能权限关键字
        PF_FIELD:"BASE_DATA_MANAGER,CUSTOMER_DATA,OFFICE_LINE",
        PF_LIST:[],
        confirmLoading: false,
        searchForm: {
          userName:null,
        },
        form: {
          userAccount:null,
          userName:null,
          userDisplayName:null,
        },
        selectedRowKeys: [],
        userList:[],
        pagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                          total / this.pagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },
        data: [],
        columns: [
          {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
              customRender: 'num'
            }
          },
          {title: '用户账号', align: 'center', dataIndex: 'userAccount',width:120},
          {title: '用户名称', align: 'center', dataIndex: 'userName',width:150},
          {title: '用户部门',  dataIndex: 'userDisplayName'},
          {title: '操作', fixed: 'right', align:"center", width: 130, scopedSlots: {customRender: 'action'}}
        ],

        rules: {

        },
      }
    },
    async created() {
      // 获取页面功能权限
      await this.getUserAuth(this.PF_FIELD);
      this.PF_LIST = this.getPfList(this.PF_FIELD);
    },
    mounted() {
      this.getData()
      this.checkName()
    },
    methods: {
      checkPf(field) {
        return this.PF_LIST.findIndex(s=>s == field) != -1;
      },
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys;
      },
      getData: function () {
        this.loading = true
        let {current, size} = this.pagination
        request(LIST_XSGSUSERCONFIG_PAGE, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
                  const {records, total} = res.data.data
                  this.loading = false
                  this.data = records
                  this.pagination.total = parseInt(total)
                })
      },
      deleteSelectedIds(){
        if(this.selectedRowKeys.length<=0){
          this.$message.info('请至少选择一条记录！')
          return
        }
        this.$confirm({
          content: `是否确认删除选择的数据？`,
          onOk: () => {
            let ids = this.selectedRowKeys;
            this.doDelete(ids)
          }
        });

      },
      doDelete(ids) {
        this.loading = true
        request(DELETE_XSGSUSERCONFIG_IDS, METHOD.POST,[...ids])
                .then(() => {
                  this.$message.info('删除成功')
                  this.getData()
                }).catch(() => {
          this.loading = false
        })
      },
      handleDelete(record) {
        this.$confirm({
          content: `是否确认删除 ${record.userName} ？`,
          onOk: () => {
            let ids = [];
            ids.push(record.id);
            this.doDelete(ids);
          }
        });
      },
      handleSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            request(SUBMIT_XSGSUSERCONFIG, METHOD.POST, this.form)
                    .then(() => {
                      this.getData()
                      this.$message.info('提交成功')
                      this.closeForm()
                    })
          }
        })
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.searchForm = {

        }
        this.handleSearch();
      },
      handleExportExcel() {
        exportExcel(DOWNLOAD_XSGSUSERCONFIG, {...this.searchForm}, "用户配置.xlsx")
      },
      handleUserSearch(value) {
        this.userList = [];
        request(GET_YC_USER, METHOD.POST, {employeeName: value,page:1,pageSize:20})
                .then(res => {
                  this.userList = this.cutList(res.data.data,0,20);
                })
      },
      handleUserChange(value) {
        if (!value) {
          this.isSetting = true;
          this.$nextTick(()=>{
            this.form.userName = null;
            this.form.userAccount = null;
            this.form.userDisplayName = null;
            this.isSetting = false;
          });

        }
      },
      selectedUser(d) {
        this.isSetting = true;
        this.$nextTick(()=>{
          this.form.userName = d.employeeName;
          this.form.userAccount = d.employeeCode;
          this.form.userDisplayName = d.displayName;
          this.isSetting = false;
        });
      },
      handleEdit(record) {
        this.dialog = true
        this.form = {...record}
      },
      handleSearch() {
        this.pagination.current = 1
        this.getData()
      },
      closeForm() {
        this.dialog = false
        this.$refs.form.resetFields()
        this.form = {}
      },
      onPageChange(page, size) {
        this.pagination.current = page;
        this.pagination.size = size.toString();
        this.getData();
      },
      onSizeChange(current, size) {
        this.pagination.current = 1;
        this.pagination.size = size.toString();
        this.getData();
      },
      handleUpload(info) {
        this.loading = true
        if (info.file.status !== 'uploading') {
          this.getData()
        }
        if (info.file.status === 'done') {
          this.$message.success(`${info.file.name} 导入成功`);
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 导入失败`);
        }
      },



    }
  }
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
