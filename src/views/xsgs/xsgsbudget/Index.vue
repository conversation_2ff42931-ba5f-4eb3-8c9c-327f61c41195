<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
            <tr>
              <td><a-form-model-item label="年度" /></td>
              <td><a-input-number :min="0"  style="width: 100%" v-model="searchForm.year"/></td>

              <td><a-form-model-item label="客户号"/></td>
              <td><a-input v-model="searchForm.customer" @blur="customerBlurs"   /></td>

              <td><a-form-model-item label="客户名称"/></td>
              <td colspan="5">
                <a-select
                        style="width: 100%"
                        v-model="searchForm.customerName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        @search="handleCustomerSearch"
                        @change="handleCustomerChange"
                >
                  <a-select-option v-for="d in customerList" :key="d.name">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
<!--                <a-input v-model="searchForm.customerName"/>-->
              </td>

            </tr>
            <tr>
              <td><a-form-model-item label="状态机"/></td>
              <td>
                <a-select v-model="platformName"  mode="multiple"  style="min-width: 150px">
                  <a-select-option v-for="sty in zztj_list" :key="sty"  >
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>


              <td><a-form-model-item label="板块"/></td>
              <td>
                <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                  <a-select-option value="卡车" >卡车</a-select-option>
                  <a-select-option value="客车" >客车</a-select-option>
                  <a-select-option value="新能源" >新能源</a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="系列"/></td>
              <td>
                <a-select v-model="series"  mode="multiple"  style="min-width: 150px">
                  <a-select-option v-for="sty in zxl_list" :key="sty"  >
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="产品型号"/></td>
              <td colspan="3">
                <a-select v-model="productModel"  mode="multiple"  style="min-width: 150px">
                  <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

            </tr>
            <tr>
              <td><a-form-model-item label="功率（PS）"/></td>
              <td>
                <a-select v-model="ps"  mode="multiple"  style="min-width: 150px">
                  <a-select-option v-for="sty in zgl_list" :key="sty"  >
                    {{sty?sty.split('.')[0]:""}}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="排放"/></td>
              <td>
                <a-select v-model="blowoff" mode="multiple"  style="min-width: 150px">
                  <a-select-option value="国6" >国6</a-select-option>
                  <a-select-option value="国5" >国5</a-select-option>
                  <a-select-option value="国4" >国4</a-select-option>
                  <a-select-option value="国3" >国3</a-select-option>
                  <a-select-option value="国2" >国2</a-select-option>
                  <a-select-option value="国1" >国1</a-select-option>
                </a-select>
              </td>

              <td><a-form-model-item label="柴油/气体" /></td>
              <td><a-select v-model="searchForm.machineType" >
                  <a-select-option v-for="mty in machineType" :key="mty">
                    {{ mty }}
                  </a-select-option>
                </a-select></td>


              <td><a-form-model-item label="品系"/></td>
              <td>
                <a-select v-model="type"  mode="multiple"  style="min-width: 150px">
                  <a-select-option v-for="sty in zpx_list" :key="sty"  >
                    {{ sty }}
                  </a-select-option>
                </a-select>
              </td>

              <td><a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button></td>
              <td><a-button  type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button></td>
            </tr>

          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSBUDGET"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>


      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1500 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <template slot="ps" slot-scope="record" >
          {{record?record.split('.')[0]:""}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        width="60%"
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 9}" :wrapperCol="{span: 15}">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="年度" prop="year">
              <a-input-number v-model="form.year"   :min="0"  />
<!--              <a-input v-model="form.year"/>-->
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态机" prop="platformName">
              <a-input v-model="form.platformName"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="客户号" prop="customer">
              <a-input v-model="form.customer" @blur="customerBlursFrom"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="客户名称" prop="customerName">
<!--              <a-input v-model="form.customerName"/>-->
              <a-select
                      style="width: 100%"
                      v-model="form.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearchFrom"
                      @change="handleCustomerChangeFrom"
              >
                <a-select-option v-for="d in customerListFrom" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="板块" prop="plate">
              <a-select v-model="form.plate"  style="width: 100%">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系列" prop="series">
              <a-input v-model="form.series"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="产品型号" prop="productModel">
              <a-input v-model="form.productModel"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="功率（PS）" prop="ps">
              <a-input v-model="form.ps"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="排放" prop="blowoff">
              <a-select v-model="form.blowoff"  style="width: 100%">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="柴油/气体" prop="machineType">
              <a-select v-model="form.machineType" >
                <a-select-option v-for="mty in machineType" :key="mty">
                  {{ mty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="品系" prop="type">
              <a-input v-model="form.type"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="上年度整机净价" prop="actualPrice">
              <a-input-number v-model="form.actualPrice"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
              />
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="总部预算-基本型预算" prop="zbBasicBudget">
              <a-input-number v-model="form.zbBasicBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="总部预算-选型配件预算" prop="zbChooseBudget">
              <a-input-number v-model="form.zbChooseBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="总部预算-整机预算" prop="zbUnitBudget">
              <a-input-number v-model="form.zbUnitBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="总部预算-预算空间" prop="zbBudgetSpace">
              <a-input-number v-model="form.zbBudgetSpace"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="系统部预算-基本型预算" prop="xtbBasicBudget">
              <a-input-number v-model="form.xtbBasicBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系统部预算-选配件预算" prop="xtbChooseBudget">
              <a-input-number v-model="form.xtbChooseBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="系统部预算-整机预算" prop="xtbUnitBudget">
              <a-input-number v-model="form.xtbUnitBudget"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系统部预算-预算空间" prop="xtbBudgetSpace">
              <a-input-number v-model="form.xtbBudgetSpace"  :min="0"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
            </a-form-model-item>
          </a-col>
        </a-row>

      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSBUDGET_PAGE,GETSEACHLIST_XSGSBUDGET,LIST_XSGSCUSTOMER,DELETE_XSGSBUDGET,SUBMIT_XSGSBUDGET,UPLOAD_XSGSBUDGET,DOWNLOAD_XSGSBUDGET,DELETE_XSGS_BUGET_LIST} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
// import moment from 'moment';

export default {
  name: "xsgsbudget.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSBUDGET,
      Cookie,
      // 高级搜索 展开/关闭
      advanced: false,
      loading: false,
      dialog: false,
      machineType:['柴油','气体'],
      yearShowOne: false,
      confirmLoading: false,
      searchForm: {
        customerName:null,
        customer:null,
      },

      productModel:[],
      platformName:[],
      blowoff:[],
      plate:[],
      series:[],
      type:[],
      ps:[],

      zcpxh_list:[], //产品型号
      zztj_list:[],//状态机
      zxl_list:[],//系列
      zgl_list:[],//功率
      zpx_list:[],//品系

      customerList:[],
      customerListFrom:[],
      form: {
        customerName:null,
        customer:null,
      },
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
                `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                        total / this.pagination.size
                )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
                this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', width: 50, fixed: 'left', dataIndex: 'year'},
        {title: '客户号', width: 100, fixed: 'left', dataIndex: 'customer'},
        {title: '状态机', width: 100, fixed: 'left', dataIndex: 'platformName'},

        {title: '客户名称', width: 100, dataIndex: 'customerName'},
        {title: '板块', width: 80,  dataIndex: 'plate'},
        {title: '系列', width: 80, dataIndex: 'series'},
        {title: '产品型号', width: 100, dataIndex: 'productModel'},
        {title: '功率（PS）', width: 100, dataIndex: 'ps',scopedSlots: { customRender: 'ps' }},
        {title: '排放', width: 100, dataIndex: 'blowoff'},
        {title: '柴油/气体', width: 100, dataIndex: 'machineType'},
        {title: '品系', width: 100, dataIndex: 'type'},
        {title: '上年度整机净价', width: 80, dataIndex: 'actualPrice'},
        {
          title: '总部预算', align: 'center',
          children: [
            {
              title: '基本型预算',
              dataIndex: 'zbBasicBudget', width: 80,align: 'center',
            },
            {
              title: '选型配件预算',
              dataIndex: 'zbChooseBudget',width: 80, align: 'center',
            },
            {
              title: '整机预算',
              dataIndex: 'zbUnitBudget',width: 80, align: 'center',
            },
            {
              title: '预算空间',
              dataIndex: 'zbBudgetSpace',width: 80, align: 'center',
            }

          ]
        },
        {
          title: '系统部预算', align: 'center',
          children: [
            {
              title: '基本型预算',
              dataIndex: 'xtbBasicBudget', width: 80,align: 'center',
            },
            {
              title: '选型配件预算',
              dataIndex: 'xtbChooseBudget',width: 80, align: 'center',
            },
            {
              title: '整机预算',
              dataIndex: 'xtbUnitBudget',width: 80, align: 'center',
            },
            {
              title: '预算空间',
              dataIndex: 'xtbBudgetSpace',width: 80, align: 'center',
            }

          ]
        },
        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
        customer: [{required: false, message: '客户号不能为空', trigger: 'blur'}],
        customerName: [{required: false, message: '客户名称不能为空', trigger: 'blur'}],
        plate: [{required: false, message: '板块不能为空', trigger: 'blur'}],
        series: [{required: false, message: '系列不能为空', trigger: 'blur'}],
        productModel: [{required: false, message: '产品型号不能为空', trigger: 'blur'}],
        ps: [{required: false, message: '功率（PS）不能为空', trigger: 'blur'}],
        blowoff: [{required: false, message: '排放不能为空', trigger: 'blur'}],
        machineType: [{required: false, message: '柴油/气体不能为空', trigger: 'blur'}],
        type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
        actualPrice: [{required: false, message: '上年度整机净价不能为空', trigger: 'blur'}],
        zbBasicBudget: [{required: false, message: '总部预算-基本型预算不能为空', trigger: 'blur'}],
        zbChooseBudget: [{required: false, message: '总部预算-选型配件预算不能为空', trigger: 'blur'}],
        zbUnitBudget: [{required: false, message: '总部预算-整机预算不能为空', trigger: 'blur'}],
        zbBudgetSpace: [{required: false, message: '总部预算-预算空间不能为空', trigger: 'blur'}],
        xtbBasicBudget: [{required: false, message: '系统部预算-基本型预算不能为空', trigger: 'blur'}],
        xtbChooseBudget: [{required: false, message: '系统部预算-选配件预算不能为空', trigger: 'blur'}],
        xtbUnitBudget: [{required: false, message: '系统部预算-整机预算不能为空', trigger: 'blur'}],
        xtbBudgetSpace: [{required: false, message: '系统部预算-预算空间不能为空', trigger: 'blur'}],
      }
    }
  },
  mounted() {
    this.getData()
    this.checkName()
    this.getSearchList()
  },
  methods: {

    getSearchList(){
        request(GETSEACHLIST_XSGSBUDGET, METHOD.GET)
                .then(res => {
                  if(res.data.data.cpxh!=null){
                    this.zcpxh_list=res.data.data.cpxh
                  }
                  if(res.data.data.ztj!=null){
                    this.zztj_list=res.data.data.ztj
                  }
                  if(res.data.data.xl!=null){
                    this.zxl_list=res.data.data.xl
                  }
                  if(res.data.data.gl!=null){
                    this.zgl_list=res.data.data.gl
                  }
                  if(res.data.data.px!=null){
                    this.zpx_list=res.data.data.px
                  }
                })
    },


customerBlurs(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.searchForm.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.searchForm.customer!=null){
            if(!this.searchForm.customer.split(" ").join("").length == 0){
              this.searchForm.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },
    customerBlursFrom(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.form.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.form.customer!=null){
            if(!this.form.customer.split(" ").join("").length == 0){
              this.form.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item =>{
        if(item.name == value){
          this.searchForm.customer = item.customer
        }
      })
    },
    handleCustomerSearchFrom(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerListFrom = res.data.data
      })
    },
    handleCustomerChangeFrom(value) {
      this.customerListFrom.forEach(item =>{
        if(item.name == value){
          this.form.customer = item.customer
        }
      })
    },


    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination

      this.searchForm.productModels = this.productModel.join(",")
      this.searchForm.platformNames = this.platformName.join(",")
      this.searchForm.blowoffs = this.blowoff.join(",")
      this.searchForm.plates = this.plate.join(",")
      this.searchForm.seriess = this.series.join(",")
      this.searchForm.pss = this.ps.join(",")
      this.searchForm.types = this.type.join(",")

      request(LIST_XSGSBUDGET_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
                const {records, total} = res.data.data
                this.loading = false
                this.data = records
                this.pagination.total = parseInt(total)
              })
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }

      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_BUGET_LIST, METHOD.POST,{ids:this.selectedRowKeys})
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.platformName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSBUDGET + record.id, METHOD.DELETE)
                  .then(() => {
                    this.$message.info('删除成功')
                    this.getData()
                  }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSBUDGET, METHOD.POST, this.form)
                  .then(() => {
                    this.getData()
                    this.$message.info('提交成功')
                    this.closeForm()
                  })
        }
      })
    },
    // // 弹出日历和关闭日历的回调
    // openChangeOne(status) {
    //   if (status){
    //     this.yearShowOne = true
    //   }
    // },
    // // 得到年份选择器的值
    // panelChangeOne(value) {
    //   this.searchForm.year = moment(value._d).format('YYYY');
    //   this.yearShowOne =false;
    // },

    /** 重置按钮操作 */
    resetQuery() {
      this.productModel=[],
      this.platformName=[],
      this.blowoff=[],
      this.plate=[],
      this.series=[],
      this.ps=[],
      this.type=[],

      this.searchForm = {
        customerName:null,
        customer:null,
      }
      this.handleSearch();
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {
        customerName:null,
        customer:null,
      }
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData()
    },
    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk:() => {
          let option = ""
          if (searchForm.year != null) {
            option = "?year=" + searchForm.year
          }


          if (searchForm.customer != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "customer=" + searchForm.customer
          }

          if (searchForm.customerName != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "customerName=" + searchForm.customerName
          }

          if (searchForm.productModel != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "productModel=" + searchForm.productModel
          }
          if (searchForm.platformName != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "platformName=" + searchForm.platformName
          }

          if (searchForm.plate != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "plate=" + searchForm.plate
          }
          if (searchForm.series != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "series=" + searchForm.series
          }
          if (searchForm.ps != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "ps=" + searchForm.ps
          }
          if (searchForm.blowoff != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "blowoff=" + searchForm.blowoff
          }
          if (searchForm.machineType != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "machineType=" + searchForm.machineType
          }
          if (searchForm.type != null) {
            if (option != "") {
              option += "&"
            } else {
              option += "?"
            }
            option += "type=" + searchForm.type
          }
          window.open(decodeURI(DOWNLOAD_XSGSBUDGET + option))
        }
      })
    },


  }
}
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
