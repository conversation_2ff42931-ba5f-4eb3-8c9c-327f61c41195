<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>

            <td><a-form-model-item label="客户号" /></td>
            <td><a-input v-model="searchForm.customer"  @blur="customerBlurs" /></td>

            <td><a-form-model-item label="客户名称" /></td>
            <td>
              <a-select
                      style="min-width: 250px"
                      v-model="searchForm.name"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="searchForm.name"/>-->
            </td>

          </tr>
          <tr>
            <td><a-form-model-item label="客户简称" /></td>
            <td><a-input v-model="searchForm.customerName"/></td>

            <td><a-form-model-item label="客户类型"  /></td>
            <td>
              <a-select v-model="searchForm.customerType">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="主客户">主客户</a-select-option>
                <a-select-option value="副客户">副客户</a-select-option>
              </a-select>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('CUSTOMER_INFO_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('CUSTOMER_INFO_DELETE')" type="primary" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload v-if="checkPf('CUSTOMER_INFO_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSCUSTOMER"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button v-if="checkPf('CUSTOMER_INFO_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('CUSTOMER_INFO_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('CUSTOMER_INFO_DELETE')" type="vertical"/>
                  <a v-if="checkPf('CUSTOMER_INFO_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 5}" :wrapperCol="{span: 19}">
        <a-form-model-item label="客户号" prop="customer">
          <a-input v-model="form.customer"/>
        </a-form-model-item>
        <a-form-model-item label="客户名称" prop="name">
          <a-input v-model="form.name"/>
        </a-form-model-item>
        <a-form-model-item label="客户简称" prop="customerName">
          <a-input v-model="form.customerName"/>
        </a-form-model-item>
        <a-form-model-item label="客户类型" prop="customerType">
          <a-select v-model="form.customerType">
            <a-select-option value="主客户">主客户</a-select-option>
            <a-select-option value="副客户">副客户</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="关联主客户号" prop="customerJoin">
          <a-input v-model="form.customerJoin"/>
        </a-form-model-item>


      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSCUSTOMER_PAGE,DELETE_XSGSCUSTOMER,SUBMIT_XSGSCUSTOMER,LIST_XSGSCUSTOMER,UPLOAD_XSGSCUSTOMER,DOWNLOAD_XSGSCUSTOMER,DELETE_XSGS_CUSTOMER_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgscustomer.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSCUSTOMER,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,CUSTOMER_DATA,CUSTOMER_INFO",
      PF_LIST:[],
      searchForm: {
        name:null,
        customer:null,
      },
      customerList:[],
      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '客户号', dataIndex: 'customer', width:100},
        {title: '客户名称', dataIndex: 'name'},
        {title: '客户简称', dataIndex: 'customerName', width:180},
        {title: '客户类型', dataIndex: 'customerType', width:100},
        {title: '关联主客户号', dataIndex: 'customerJoin', width:120},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        customer: [{required: false, message: '客户号不能为空', trigger: 'blur'}],
        customerName: [{required: false, message: '客户简称不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    customerBlurs(){
      if(this.searchForm.customer) {
        request(LIST_XSGSCUSTOMER, METHOD.POST, {
          customer:this.searchForm.customer
        }).then(res => {
          if(res.data.data.length>0){
            this.$set(this.searchForm,"customerName",res.data.data[0].customerName)
            this.$set(this.searchForm,"name",res.data.data[0].name)
          }else{
            this.$message.info('此客户号没找到信息')
          }
        })
      } else {
        this.searchForm.customerName = null
      }
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item =>{
        if(item.name == value){
          this.searchForm.customer = item.customer
          this.searchForm.customerName = item.customerName
        }
      })
    },

    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSCUSTOMER_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_CUSTOMER_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                  .then(() => {
                    this.$message.info('删除成功')
                    this.getData()
                  }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.customerName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSCUSTOMER + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSCUSTOMER, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {
        name:null,
        customer:null,
      }
      this.handleSearch();
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk:() => {
          // let option = ""
          // if (searchForm.customer != null) {
          //   option = "?customer=" + searchForm.customer
          // }
          //
          // if (searchForm.customerName != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customerName=" + searchForm.customerName
          // }
          // if (searchForm.name != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "name=" + searchForm.name
          // }
          // if (searchForm.customerType != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customerType=" + searchForm.customerType
          // }
          //
          // window.open(decodeURI(DOWNLOAD_XSGSCUSTOMER + option))
          exportExcel(DOWNLOAD_XSGSCUSTOMER, {...searchForm}, "客户表.xlsx");
        }
      })
    },



  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
