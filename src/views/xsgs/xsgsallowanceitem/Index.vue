<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="价格主表ID">
              <a-input v-model="searchForm.masterId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="核算单号">
              <a-input v-model="searchForm.code"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="年度">
              <a-input v-model="searchForm.year"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="合同号">
              <a-input v-model="searchForm.contract"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="状态机">
              <a-input v-model="searchForm.platformName"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="年度销售预计">
              <a-input v-model="searchForm.yearExpectVolume"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="年度实际销售">
              <a-input v-model="searchForm.yearSalesVolume"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="基本型开票价">
              <a-input v-model="searchForm.basicTicketPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="整机开票价">
              <a-input v-model="searchForm.unitTicketPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="基本型净价">
              <a-input v-model="searchForm.basicNetPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="整机净价">
              <a-input v-model="searchForm.unitNetPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="基本型预算">
              <a-input v-model="searchForm.basicBudgetPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-选配件预算">
              <a-input v-model="searchForm.zbChooseBudget"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-整机预算净价">
              <a-input v-model="searchForm.zbBudgetActualPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-预算空间">
              <a-input v-model="searchForm.zbBudgetSpace"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-基本型预算结余">
              <a-input v-model="searchForm.zbBasicBalance"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-整机预算结余">
              <a-input v-model="searchForm.zbUnitBalance"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="总部-基本型预算">
              <a-input v-model="searchForm.zbBasicBudgetBalance"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="系统-选配件预算">
              <a-input v-model="searchForm.sysChooseBudget"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="系统-整机预算净价">
              <a-input v-model="searchForm.sysBudgetActualPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="系统-预算空间">
              <a-input v-model="searchForm.sysBudgetSapce"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="系统-基本型预算结余">
              <a-input v-model="searchForm.sysBasicBalance"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="系统-整机预算结余">
              <a-input v-model="searchForm.sysUnitBalance"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="材料成本">
              <a-input v-model="searchForm.costOfMeterial"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="边际贡献">
              <a-input v-model="searchForm.contribution"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="边际贡献率">
              <a-input v-model="searchForm.contributionPercent"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="序号">
              <a-input v-model="searchForm.sequenceNumber"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="结算价">
              <a-input v-model="searchForm.settlementPrice"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="折让金额">
              <a-input v-model="searchForm.balanceAmount"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="创建人">
              <a-input v-model="searchForm.createUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="创建时间">
              <a-input v-model="searchForm.createTime"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="更新人">
              <a-input v-model="searchForm.updateUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="更新时间">
              <a-input v-model="searchForm.updateTime"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-upload
                    name="file"
                    :action="UPLOAD_XSGSALLOWANCEITEM"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="价格主表ID" prop="masterId">
                <a-input v-model="form.masterId"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="核算单号" prop="code">
                <a-input v-model="form.code"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="年度" prop="year">
                <a-input v-model="form.year"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="合同号" prop="contract">
                <a-input v-model="form.contract"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="状态机" prop="platformName">
                <a-input v-model="form.platformName"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="年度销售预计" prop="yearExpectVolume">
                <a-input v-model="form.yearExpectVolume"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="年度实际销售" prop="yearSalesVolume">
                <a-input v-model="form.yearSalesVolume"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="基本型开票价" prop="basicTicketPrice">
                <a-input v-model="form.basicTicketPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="整机开票价" prop="unitTicketPrice">
                <a-input v-model="form.unitTicketPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="基本型净价" prop="basicNetPrice">
                <a-input v-model="form.basicNetPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="整机净价" prop="unitNetPrice">
                <a-input v-model="form.unitNetPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="基本型预算" prop="basicBudgetPrice">
                <a-input v-model="form.basicBudgetPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-选配件预算" prop="zbChooseBudget">
                <a-input v-model="form.zbChooseBudget"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-整机预算净价" prop="zbBudgetActualPrice">
                <a-input v-model="form.zbBudgetActualPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-预算空间" prop="zbBudgetSpace">
                <a-input v-model="form.zbBudgetSpace"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-基本型预算结余" prop="zbBasicBalance">
                <a-input v-model="form.zbBasicBalance"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-整机预算结余" prop="zbUnitBalance">
                <a-input v-model="form.zbUnitBalance"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="总部-基本型预算" prop="zbBasicBudgetBalance">
                <a-input v-model="form.zbBasicBudgetBalance"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系统-选配件预算" prop="sysChooseBudget">
                <a-input v-model="form.sysChooseBudget"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系统-整机预算净价" prop="sysBudgetActualPrice">
                <a-input v-model="form.sysBudgetActualPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系统-预算空间" prop="sysBudgetSapce">
                <a-input v-model="form.sysBudgetSapce"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系统-基本型预算结余" prop="sysBasicBalance">
                <a-input v-model="form.sysBasicBalance"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系统-整机预算结余" prop="sysUnitBalance">
                <a-input v-model="form.sysUnitBalance"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="材料成本" prop="costOfMeterial">
                <a-input v-model="form.costOfMeterial"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="边际贡献" prop="contribution">
                <a-input v-model="form.contribution"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="边际贡献率" prop="contributionPercent">
                <a-input v-model="form.contributionPercent"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="序号" prop="sequenceNumber">
                <a-input v-model="form.sequenceNumber"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="结算价" prop="settlementPrice">
                <a-input v-model="form.settlementPrice"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="折让金额" prop="balanceAmount">
                <a-input v-model="form.balanceAmount"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="创建人" prop="createUser">
                <a-input v-model="form.createUser"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="创建时间" prop="createTime">
                <a-input v-model="form.createTime"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="更新人" prop="updateUser">
                <a-input v-model="form.updateUser"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="更新时间" prop="updateTime">
                <a-input v-model="form.updateTime"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSALLOWANCEITEM_PAGE,DELETE_XSGSALLOWANCEITEM,SUBMIT_XSGSALLOWANCEITEM,DOWNLOAD_XSGSALLOWANCEITEM,UPLOAD_XSGSALLOWANCEITEM} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgsallowanceitem.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSALLOWANCEITEM,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '价格主表ID', dataIndex: 'masterId'},
        {title: '核算单号', dataIndex: 'code'},
        {title: '年度', dataIndex: 'year'},
        {title: '合同号', dataIndex: 'contract'},
        {title: '状态机', dataIndex: 'platformName'},
        {title: '年度销售预计', dataIndex: 'yearExpectVolume'},
        {title: '年度实际销售', dataIndex: 'yearSalesVolume'},
        {title: '基本型开票价', dataIndex: 'basicTicketPrice'},
        {title: '整机开票价', dataIndex: 'unitTicketPrice'},
        {title: '基本型净价', dataIndex: 'basicNetPrice'},
        {title: '整机净价', dataIndex: 'unitNetPrice'},
        {title: '基本型预算', dataIndex: 'basicBudgetPrice'},
        {title: '总部-选配件预算', dataIndex: 'zbChooseBudget'},
        {title: '总部-整机预算净价', dataIndex: 'zbBudgetActualPrice'},
        {title: '总部-预算空间', dataIndex: 'zbBudgetSpace'},
        {title: '总部-基本型预算结余', dataIndex: 'zbBasicBalance'},
        {title: '总部-整机预算结余', dataIndex: 'zbUnitBalance'},
        {title: '总部-基本型预算', dataIndex: 'zbBasicBudgetBalance'},
        {title: '系统-选配件预算', dataIndex: 'sysChooseBudget'},
        {title: '系统-整机预算净价', dataIndex: 'sysBudgetActualPrice'},
        {title: '系统-预算空间', dataIndex: 'sysBudgetSapce'},
        {title: '系统-基本型预算结余', dataIndex: 'sysBasicBalance'},
        {title: '系统-整机预算结余', dataIndex: 'sysUnitBalance'},
        {title: '材料成本', dataIndex: 'costOfMeterial'},
        {title: '边际贡献', dataIndex: 'contribution'},
        {title: '边际贡献率', dataIndex: 'contributionPercent'},
        {title: '序号', dataIndex: 'sequenceNumber'},
        {title: '结算价', dataIndex: 'settlementPrice'},
        {title: '折让金额', dataIndex: 'balanceAmount'},
        {title: '创建人', dataIndex: 'createUser'},
        {title: '创建时间', dataIndex: 'createTime'},
        {title: '更新人', dataIndex: 'updateUser'},
        {title: '更新时间', dataIndex: 'updateTime'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        masterId: [{required: false, message: '价格主表ID不能为空', trigger: 'blur'}],
        code: [{required: false, message: '核算单号不能为空', trigger: 'blur'}],
        year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        contract: [{required: false, message: '合同号不能为空', trigger: 'blur'}],
        platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
        yearExpectVolume: [{required: false, message: '年度销售预计不能为空', trigger: 'blur'}],
        yearSalesVolume: [{required: false, message: '年度实际销售不能为空', trigger: 'blur'}],
        basicTicketPrice: [{required: false, message: '基本型开票价不能为空', trigger: 'blur'}],
        unitTicketPrice: [{required: false, message: '整机开票价不能为空', trigger: 'blur'}],
        basicNetPrice: [{required: false, message: '基本型净价不能为空', trigger: 'blur'}],
        unitNetPrice: [{required: false, message: '整机净价不能为空', trigger: 'blur'}],
        basicBudgetPrice: [{required: false, message: '基本型预算不能为空', trigger: 'blur'}],
        zbChooseBudget: [{required: false, message: '总部-选配件预算不能为空', trigger: 'blur'}],
        zbBudgetActualPrice: [{required: false, message: '总部-整机预算净价不能为空', trigger: 'blur'}],
        zbBudgetSpace: [{required: false, message: '总部-预算空间不能为空', trigger: 'blur'}],
        zbBasicBalance: [{required: false, message: '总部-基本型预算结余不能为空', trigger: 'blur'}],
        zbUnitBalance: [{required: false, message: '总部-整机预算结余不能为空', trigger: 'blur'}],
        zbBasicBudgetBalance: [{required: false, message: '总部-基本型预算不能为空', trigger: 'blur'}],
        sysChooseBudget: [{required: false, message: '系统-选配件预算不能为空', trigger: 'blur'}],
        sysBudgetActualPrice: [{required: false, message: '系统-整机预算净价不能为空', trigger: 'blur'}],
        sysBudgetSapce: [{required: false, message: '系统-预算空间不能为空', trigger: 'blur'}],
        sysBasicBalance: [{required: false, message: '系统-基本型预算结余不能为空', trigger: 'blur'}],
        sysUnitBalance: [{required: false, message: '系统-整机预算结余不能为空', trigger: 'blur'}],
        costOfMeterial: [{required: false, message: '材料成本不能为空', trigger: 'blur'}],
        contribution: [{required: false, message: '边际贡献不能为空', trigger: 'blur'}],
        contributionPercent: [{required: false, message: '边际贡献率不能为空', trigger: 'blur'}],
        sequenceNumber: [{required: false, message: '序号不能为空', trigger: 'blur'}],
        settlementPrice: [{required: false, message: '结算价不能为空', trigger: 'blur'}],
        balanceAmount: [{required: false, message: '折让金额不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
      }
    }
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSALLOWANCEITEM_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.contract} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSALLOWANCEITEM + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSALLOWANCEITEM, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          window.open(decodeURI(DOWNLOAD_XSGSALLOWANCEITEM))
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>

</style>
