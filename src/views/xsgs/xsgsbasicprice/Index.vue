<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度"/></td>
            <td><a-input v-model="searchForm.year"/></td>

            <td><a-form-model-item label="客户号"/></td>
            <td><a-input v-model="searchForm.customer"  @blur="customerBlurs"/></td>

            <td><a-form-model-item label="客户名称"/></td>
            <td>
              <a-select
                      style="width: 100%"
                      v-model="searchForm.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="searchForm.customerName"/>-->
            </td>

          </tr>
          <tr>
            <td><a-form-model-item label="板块"/></td>
            <td>
              <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="系列"/></td>
            <td><a-input v-model="searchForm.series"/></td>

            <td><a-form-model-item label="品系"/></td>
            <td><a-input v-model="searchForm.type"/></td>

          </tr>
          <tr>
            <td><a-form-model-item label="产品型号"/></td>
            <td>
              <a-select v-model="productModel"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="产品型号[简称]"/></td>
            <td><a-input v-model="searchForm.productModelJh"/></td>

            <td><a-form-model-item label="价格"/></td>
            <td><a-input v-model="searchForm.actualPrice"/></td>
            
          </tr>
          <tr>
            <td colspan="5"></td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('BASIC_PRICE_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('BASIC_PRICE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('BASIC_PRICE_IMPORT')"
                name="file"
                :action="UPLOAD_XSGSBASICPRICE"
                :headers="{'Authorization':Cookie.get('Authorization')}"
                :showUploadList="false"
                style="margin: 0 8px"
                @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('BASIC_PRICE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <template slot="customer" slot-scope="text, record">
          {{record.customer == '' ? 'A00002' : text}}
        </template>
        <template slot="customerName" slot-scope="text, record">
          {{record.customer == '' ? '股司基本型' : text}}
        </template>
        <span slot="action" slot-scope="record">
          <a v-if="checkPf('BASIC_PRICE_EDIT')" @click="handleEdit(record)">编辑</a>
          <a-divider v-if="checkPf('BASIC_PRICE_DELETE')" type="vertical"/>
          <a v-if="checkPf('BASIC_PRICE_DELETE')" @click="handleDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 7}" :wrapperCol="{span: 17}">
        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="年度" prop="year">
              <a-input v-model="form.year"/>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="板块" prop="plate">
              <a-select  style="width: 100%" v-model="form.plate">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="客户" prop="customer">
              <a-input v-model="form.customer" @blur="customerBlursFrom" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="客户名称" prop="customerName">
              <!--                <a-input v-model="form.customer"/>-->
              <a-select
                      style="width: 100%"
                      v-model="form.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearchFrom"
                      @change="handleCustomerChangeFrom"
              >
                <a-select-option v-for="d in customerListFrom" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="品系" prop="type">
              <a-input v-model="form.type"/>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="系列" prop="series">
              <a-input v-model="form.series"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row class="form-row">
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="产品型号" prop="productModel">
              <a-input v-model="form.productModel"/>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="产品型号[简化]" prop="productModelJh">
              <a-input v-model="form.productModelJh"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="价格" prop="actualPrice">
              <a-input v-model="form.actualPrice"/>
            </a-form-model-item>
          </a-col>
        </a-row>

      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSBASICPRICE_PAGE,GETSEACHLIST_XSGSBASICPRICE,DELETE_XSGSBASICPRICE,SUBMIT_XSGSBASICPRICE,DOWNLOAD_XSGSBASICPRICE,UPLOAD_XSGSBASICPRICE,DELETE_XSGS_BASIC_PRICE_LIST,LIST_XSGSCUSTOMER} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'

export default {
  name: "xsgsbasicprice.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSBASICPRICE,
      Cookie,
      loading: false,
      dialog: false,
      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,BASIC_PRICE",
      PF_LIST:[],
      confirmLoading: false,
      searchForm: {
        customerName:null,
        customer:null,
      },
      plate:[],
      productModel:[],
      zcpxh_list:[],


      customerList:[],
      customerListFrom:[],
      form: {
        customerName:null,
        customer:null,
      },
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度',width:50, dataIndex: 'year'},
        {title: '客户',width:80, dataIndex: 'customer',scopedSlots: {
            customRender: 'customer'
        }},
        {title: '客户名称',dataIndex: 'customerName',scopedSlots: {
            customRender: 'customerName'
        }},
        {title: '板块',width:80, dataIndex: 'plate'},
        {title: '系列',width:80, dataIndex: 'series'},
        {title: '品系',width:80, dataIndex: 'type'},
        {title: '产品型号',width:120, dataIndex: 'productModel'},
        {title: '产品型号[简化]',width:120, dataIndex: 'productModelJh'},
        {title: '价格',width:80, dataIndex: 'actualPrice'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
        customerName: [{required: false, message: '客户名称不能为空', trigger: 'blur'}],
        productModel: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
        plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        actualPrice: [{required: true, message: '价格不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
        isDelete: [{required: false, message: '删除标志不能为空', trigger: 'blur'}],
        productModelJh: [{required: true, message: '产品型号[简化]不能为空', trigger: 'blur'}],
        series: [{required: true, message: '系列不能为空', trigger: 'blur'}]
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
    this.getSearchList()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
   getSearchList(){
      request(GETSEACHLIST_XSGSBASICPRICE, METHOD.GET)
              .then(res => {
                if(res.data.data!=null){
                  this.zcpxh_list=res.data.data
                }
              })
        },

    customerBlurs(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.searchForm.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.searchForm.customer!=null){
            if(!this.searchForm.customer.split(" ").join("").length == 0){
              this.searchForm.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },
    customerBlursFrom(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.form.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.form.customer!=null){
            if(!this.form.customer.split(" ").join("").length == 0){
              this.form.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item =>{
        if(item.name == value){
          this.searchForm.customer = item.customer
        }
      })
    },
    handleCustomerSearchFrom(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerListFrom = res.data.data
      })
    },
    handleCustomerChangeFrom(value) {
      this.customerListFrom.forEach(item =>{
        if(item.name == value){
          this.form.customer = item.customer
        }
      })
    },


    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      this.searchForm.productModels = this.productModel.join(",")
      this.searchForm.plates = this.plate.join(",")
      request(LIST_XSGSBASICPRICE_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_BASIC_PRICE_LIST, METHOD.POST,{ids:this.selectedRowKeys})
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.productModel} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSBASICPRICE + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          that.loading = true
          if (that.form.customer == 'A00002') {
            that.form.customer = "";
            that.form.customerName = "";
          }
          request(SUBMIT_XSGSBASICPRICE, METHOD.POST, that.form)
            .then(() => {
              that.getData()
              that.$message.info('提交成功')
              that.closeForm()
            });
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.plate=[],
      this.productModel=[],

      this.searchForm = {
        customerName:null,
        customer:null,
      }
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {
        customerName:null,
        customer:null,
      }
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.searchForm.productModels = this.productModel
      this.searchForm.plates = this.plate
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          // let option = ""
          // if (searchForm.year != null) {
          //   option = "?year=" + searchForm.year
          // }
          //
          // if (searchForm.customer != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customer=" + searchForm.customer
          // }
          //
          // if (searchForm.customerName != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customerName=" + searchForm.customerName
          // }
          // if (searchForm.productModel != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "productModel=" + searchForm.productModel
          // }
          // if (searchForm.plate != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "plate=" + searchForm.plate
          // }
          // if (searchForm.actualPrice != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "actualPrice=" + searchForm.actualPrice
          // }
          //
          // window.open(decodeURI(DOWNLOAD_XSGSBASICPRICE+option))
          exportExcel(DOWNLOAD_XSGSBASICPRICE, {...searchForm}, "基本型开票价.xlsx")

        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>