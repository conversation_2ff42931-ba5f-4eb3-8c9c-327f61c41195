<template>
  <div>
    <a-card>
      <div style="margin-bottom: 20px;font-size: 16px">
        <label>年度：</label>
        <label style="margin-right: 30px;">{{form.year}}</label>
        <label>客户号：</label>
        <label style="margin-right: 30px;">{{form.customer}}</label>
        <label>客户名称：</label>
        <label style="margin-right: 30px;">{{form.customerName}}</label>
      </div>

      <vxe-grid   highlight-hover-row
                  row-id="id"
                  ref="vxeTable"
                  @cell-menu="cellContextMenuEvent"
                  @menu-click="contextMenuClickEvent"
                  highlight-current-row
                  highlight-current-column
                  :checkbox-config="{ highlight: true,reserve: true, range: true}"
                  :seq-config="{startIndex: (gridOptions.pagerConfig.currentPage - 1) * gridOptions.pagerConfig.pageSize}"
                  stripe
                  v-bind="gridOptions"
                  :span-method="colspanMethod"
                  @page-change="handlePageChange">
        <template slot="ps" slot-scope="{ row  }" >
          {{row.ps?row.ps.split('.')[0]:""}}
        </template>
      </vxe-grid>

    </a-card>
    <a-modal
            title="详细"
            :visible="dialog"
            width="80%"
            @ok="dialog = false"
            @cancel="dialog = false">
      <a-form-model ref="detailForm" :labelCol="{span: 6}" :wrapperCol="{span: 18}" style="height: auto;font-size: 16px">
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="年度" prop="plates">
              <span>{{detailForm.year}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="客户" prop="plates">
              <span>{{detailForm.customer}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="客户名称" prop="plates">
              <span>{{detailForm.customerName}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="板块" prop="plates">
              <span>{{detailForm.plate}}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="4">
            <a-form-model-item label="排放" prop="plates">
              <span>{{detailForm.blowoff}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item label="系列" prop="plates">
              <span>{{detailForm.series}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="10">
            <a-form-model-item label="产品型号" prop="plates">
              <span>{{detailForm.productModel}}</span>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="功率" prop="plates">
              <span> {{detailForm.ps?detailForm.ps.split('.')[0]:""}}</span>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-table
                  :columns="detailColumns"
                  rowKey="id"
                  :pagination="false"
                  :data-source="detailData"
                  :scroll="{ x: 520 }"
                  size="small"
                  bordered
                  :customRow="onRowClick"
                  :loading="loading">
            <template slot="num" slot-scope="text, record, index">
              {{index+1}}
            </template>
            <template slot="subType" slot-scope="text, record">
              {{record.isSubtraction=='1'?(record.subType +'(减配)'):record.subType}}
            </template>
          </a-table>
        </a-row>
      </a-form-model>

    </a-modal>
  </div>
</template>

<script>
  import {hasAuth} from "@/utils/authority-utils";
  import {COMPARE_XSGSALLOWANCEITEM} from "@/services/api/xsgs";
  import {METHOD, request} from "@/utils/request";
  import tableDragResize  from '@/utils/tableDragResize'

  import Cookie from 'js-cookie'
  export default {
    name: "Manger.vue",
    mixins: [tableDragResize],
    data() {
      return {
        hasAuth,
        Cookie,
        loading: false,
        dialog: false,
        confirmLoading: false,
        searchForm: {
          id:this.$route.params.id,
        },
        detailForm:{},
        form: {},
        detailColumns:[
          {
            title: '序号',
            dataIndex: 'num',
            key: 'num',
            width: 50,
            align: 'center',
            fixed: true,
            scopedSlots: {
              customRender: 'num'
            }
          },
          {title: '类型', width:120, dataIndex: 'subType', scopedSlots: {customRender: 'subType'}},
          {title: '选配件名称', dataIndex: 'partsName'},
          {title: '规格', dataIndex: 'specs', width:120},
          {title: '供应商', dataIndex: 'supplierBak', width:120},
          {title: '上年售价', width:120, dataIndex: 'orgPrice'},
          {title: '本年售价', width:120, dataIndex: 'actualPrice'},
        ],
        detailData:[],
        gridOptions: {
          border: true,
          resizable: true,
          keepSource: true,
          showOverflow: true,
          loading: false,
          pagerConfig: {
            total: 0,
            currentPage: 1,
            pageSize: 10,
            pageSizes: [10, 20, 50],
            layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
            perfect: true
          },
          menuConfig:{
            body:{
              options:[
                [
                  { code: 'open', name: '展开', prefixIcon: 'fa fa-edit', visible: true, disabled: false },
                ],
              ]
            }
          },
          // 分组列头，通过 children 定义子列
          columns: [
            {title: '序号', align: 'center',type: 'seq', width: 50,fixed:'left' },
            {title: '板块', align: 'center', field: 'plate', minWidth: 48,fixed: 'left'},
            {title: '排放', align: 'center', field: 'blowoff', width: 50,fixed: 'left'},
            {title: '系列', align: 'center', field: 'series', width: 50,fixed: 'left'},
            {title: '产品型号', align: 'center', field: 'productModel', minWidth: 100,fixed: 'left'},
            {title: '功率(PS)', align: 'center', field: 'ps',minWidth: 40,fixed: 'left',slots: { default: 'ps' }},
            {title: '状态机', align: 'center', field: 'machineType',  minWidth: 80,fixed: 'left'},
            {title: '品系', align: 'center', field: 'type', minWidth: 48,fixed: 'left'},
            {title: '降价', align: 'center', field: 'optionReducePrice', width: 40,fixed: 'left'},
            {title: '上年选配件合计', align: 'center', field: 'synOptionAmount', width: 60,fixed: 'left'},
            {title: '本年选配件合计', align: 'center', field: 'optionAmount', width: 60,fixed: 'left'},
            {
              title: '选型件', field: 'xxColunms', align: 'center', headerClassName: 'col-xx',
              children: []
            },
            {
              title: '选装件', field: 'xzColunms', align: 'center', headerClassName: 'col-xz',
              children: []
            },
            {
              title: '减配', field: 'jpColunms', align: 'center', headerClassName: 'col-jp',
              children: []
            },
          ],
          data: []
        },

        data: [],
        rules: {

        }
      }
    },
    mounted() {
      this.getData()
      this.checkName();
    },
    methods: {
      colspanMethod ({ row, _rowIndex, column, visibleData }) {
        if (column.property != "machineType") {
          return
        }
        const fields = ['machineType']
        const cellValue = row[column.property]

        if (cellValue && fields.includes(column.property)) {
          const prevRow = visibleData[_rowIndex - 1]
          let nextRow = visibleData[_rowIndex + 1]
          if (prevRow && prevRow[column.property] === cellValue) {
            return {rowspan: 0, colspan: 0}
          } else {
            let countRowspan = 1
            while (nextRow && nextRow[column.property] === cellValue) {
              nextRow = visibleData[++countRowspan + _rowIndex]
            }
            if (countRowspan > 1) {
              return {rowspan: countRowspan, colspan: 1}
            }
          }
        }
      },
      handlePageChange ({ currentPage, pageSize }) {
        this.gridOptions.pagerConfig.currentPage = currentPage
        this.gridOptions.pagerConfig.pageSize = pageSize;
        this.getData()
      },

      getData () {
        this.gridOptions.loading = true
        let current= this.gridOptions.pagerConfig.currentPage
        let size = this.gridOptions.pagerConfig.pageSize

        request(COMPARE_XSGSALLOWANCEITEM, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
                  this.$nextTick(function () {
                    let xxColumns = res.data.data.xxColunms;
                    let xzColumns = res.data.data.xzColunms;
                    let jpColumns = res.data.data.jpColunms;
                    this.form = res.data.data.head;
                    let total = res.data.data.total;
                    let records = res.data.data.records;
                    let xxIndex = 0;
                    let xzIndex = 0;
                    let jpIndex = 0;
                    this.gridOptions.columns.forEach((item,index) => {
                      if (item.field == 'xxColunms') {
                        xxIndex = index;
                        if (xxColumns && xxColumns.length > 0) {
                          item.children = xxColumns
                        } else {
                          item.visible = false;
                        }
                      } else if (item.field == 'xzColunms') {
                        xzIndex = index;
                        if (xzColumns && xzColumns.length > 0) {
                          item.children = xzColumns
                        } else {
                          item.visible = false;
                        }
                      } else if (item.field == 'jpColunms') {
                        jpIndex = index;
                        if (jpColumns && jpColumns.length > 0) {
                          item.children = jpColumns
                        } else {
                          item.visible = false;
                        }
                      }
                    })

                    if (!jpColumns || jpColumns.length == 0) {
                      this.gridOptions.columns.splice(jpIndex, 1);
                    }
                    if (!xzColumns || xzColumns.length == 0) {
                      this.gridOptions.columns.splice(xzIndex, 1);
                    }
                    if (!xxColumns || xxColumns.length == 0) {
                      this.gridOptions.columns.splice(xxIndex, 1);
                    }
                    this.$refs.vxeTable.loadColumn(this.gridOptions.columns) //操作a组件
                    this.gridOptions.data = records;
                    this.gridOptions.loading = false
                    this.gridOptions.pagerConfig.total = parseInt(total)
                  })

                })
      },
      handleOpen(record) {
        this.detailForm = {...record}
        if (record.partList) {
          let xxData = record.partList.filter(item => item.isSubtraction=='0' && item.subType=='选型件');
          let xzData = record.partList.filter(item => item.isSubtraction=='0' && item.subType=='选装件');
          let jpData = record.partList.filter(item => item.isSubtraction=='1' );
          let data=[];
          data.push.apply( data, xxData);
          data.push.apply( data, xzData);
          data.push.apply( data, jpData);
          this.detailData = data;
        }

        this.dialog = true;
      },
      cellContextMenuEvent({ row }){

      },
      contextMenuClickEvent({ menu, row, column }){
        switch (menu.code) {
          case 'open':
            if (row && column) {
              this.handleOpen(row);
            }
            break
        }
      },
      /** 处理查询 */
      handleSearch() {
        this.gridOptions.pagerConfig.currentPage = 1
        this.getData();
      },

      /** 关闭对话框，表单重置 */
      closeForm() {
        this.dialog = false
        this.$refs.form.resetFields()
        this.form = {}
      },

    }
  }
</script>

<style scoped>
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }

  /deep/.col-blue {
    background-color: #2db7f5;
    color: #fff;
  }
  /deep/ .vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
    height: 0px;
  }

  /deep/ .vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 0px 0;
  }

  /deep/ .vxe-table--render-default.border--full .vxe-body--column, /deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/ .vxe-table--render-default.border--full .vxe-header--column {
    background-image: -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646)), -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646));
    background-image: linear-gradient(#464646, #464646), linear-gradient(#464646, #464646);
    background-repeat: no-repeat;
    background-size: 1px 100%, 100% 1px;
    background-position: 100% 0, 100% 100%;
  }

  /deep/ .vxe-table--render-default.border--default
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--full
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: #f8f8f9;
  }

  .vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), /deep/ .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 0px 0;

  }

  /deep/ .vxe-table--render-default .vxe-table--header-wrapper .vxe-table--header .vxe-header--row th .vxe-cell {
    padding: 0px 0;

  }

  /deep/ .vxe-table--render-default .vxe-table--body-wrapper .vxe-table--body .vxe-body--column .vxe-cell {
    padding: 0px 0;

  }

  /deep/ .vxe-pager.is--perfect {
    border: 0px solid #464646;
    border-top-width: 0;
    background-color: #fff;
  }


  /deep/.vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
    height: 0px;
  }
  /deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 0px 0;
  }
  /deep/.vxe-table--render-default.border--full .vxe-body--column,/deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/.vxe-table--render-default.border--full .vxe-header--column {
    background-image: -webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646)),-webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646));
    background-image: linear-gradient(#464646,#464646),linear-gradient(#464646,#464646);
    background-repeat: no-repeat;
    background-size: 1px 100%,100% 1px;
    background-position: 100% 0,100% 100%;
  }

  /deep/.vxe-table--render-default.border--default
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--full
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: #f8f8f9;
  }

  /deep/ .vxe-pager.is--perfect {
    border: 0px solid #464646;
    border-top-width: 0;
    background-color: #fff;
  }
  /deep/ .vxe-header--column{
    background-color: rgba(0, 0, 0, 0.2);
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--stripe{
    background-color: #e6fdff;
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--checked{
    background-color: #fff3e0;
  }
  /deep/.ant-col-16 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--hover{
    background-color: #fcce10;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--current{
    background-color: #fcce10;
  }
  /deep/.vxe-table--render-default .vxe-body--column.col--current{
    background-color: #fcce10;
  }
  /deep/.vxe-table--render-default .vxe-header--column.col--current{
    background-color: #fcce10;
  }
  /deep/ .col-jp {
    background-color: #c4ebad;

  }

  /deep/ .col-xx {
    background-color: #f2d643;

  }

  /deep/ .col-xz {
    background-color: #2ec7c9;

  }
  /deep/ .detailLabel{

  }
  /deep/ .ant-form-item{
    font-size: 18px;
  }
  /deep/ .ant-form-item-label label {
    font-size: 18px;
  }

  .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
    padding: 1px 1px;
    /*overflow-wrap: break-word;*/
  }
  /deep/ .ant-modal-content .ant-table {
    font-size: 16px;
  }
</style>
