<template>
    <div id="select_optionList">


        <!-- <table style="width: 100%; border: 1px solid #f0f0f0;">
        <tbody class="ant-table-tbody">
            <tr>
                <td colspan="5">本体配置</td>
            </tr>
            <tr>
                <td>名称</td>
                <td>规格</td>
                <td>系列</td>
                <td>形式</td>
                <td>供应商</td>
            </tr>
            <tr v-for="item in mainList" v-bind:key="item.id">
                <td>{{ item.partsName }}</td>
                <td>{{ item.specs }}</td>
                <td>{{ item.series }}</td>
                <td>{{ item.xs }}</td>
                <td>{{ item.supplierBak }}</td>
            </tr>
        </tbody>
        <tbody class="ant-table-tbody">
            <tr>
                <td colspan="5">后处理配置</td>
            </tr>
            <tr>
                <td>名称</td>
                <td>规格</td>
                <td>系列</td>
                <td>形式</td>
                <td>供应商</td>
            </tr>
            <tr v-for="item in proceList" v-bind:key="item.id">
                <td>{{ item.partsName }}</td>
                <td>{{ item.specs }}</td>
                <td>{{ item.series }}</td>
                <td>{{ item.xs }}</td>
                <td>{{ item.supplierBak }}</td>
            </tr>

           
        </tbody>
        <tbody class="ant-table-tbody">
            <tr v-if="this.childOption[4]">
                <td colspan="5">整机配置</td>
            </tr>
            <tr v-if="this.childOption[4]">
                <td>名称</td>
                <td>规格</td>
                <td>系列</td>
                <td>形式</td>
                <td>供应商</td>
            </tr>
            <tr v-for="item in zjList" v-bind:key="item.id">
                <td>{{ item.partsName }}</td>
                <td>{{ item.specs }}</td>
                <td>{{ item.series }}</td>
                <td>{{ item.xs }}</td>
                <td>{{ item.supplierBak }}</td>
            </tr>
        </tbody>
    </table> -->
        <div>
            <a-table :columns="columns1" :data-source="mainList" rowKey="id" :pagination="false" :scroll="{ y: 200 }">
            </a-table>
            <a-table style="margin: 40px 0px;" :columns="columns2" :data-source="proceList" rowKey="id"
                :pagination="false" :scroll="{ y: 200 }">
            </a-table>
            <a-table :columns="columns3" :data-source="zjList" rowKey="id" :pagination="false" :scroll="{ y: 200 }">
            </a-table>
        </div>
    </div>

</template>

<script>
import { LISTTYPE_XSGSPARTSSETTING, SELECTBASICLIST_XSGSALLOWANCEMAIN, SELECTBASICLISTNEWVERSION_XSGSALLOWANCEMAIN } from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import axios from "axios";
export default {
    name: "select_optionList",
    props: {
        childOption: Array,
        masterId: String,
    },
    data() {
        return {
            mainList: [],
            proceList: [],
            zjList: [],
            searchForm: {},
            proceForm: {},
            columns1: [
                {
                    title: '本体配置',
                    children: [
                        {
                            title: '名称',
                            dataIndex: 'partsName',
                            align: 'center',
                        },
                        {
                            title: '规格',
                            dataIndex: 'specs',
                            align: 'center',
                        },
                        {
                            title: '系列',
                            dataIndex: 'series',
                            align: 'center',
                        },
                        {
                            title: '形式',
                            dataIndex: 'xs',
                            align: 'center',
                        },
                        {
                            title: '供应商',
                            dataIndex: 'supplierBak',
                            align: 'center',
                        },
                    ]
                }

            ],
            columns2: [
                {
                    title: '后处理配置',
                    children: [
                        {
                            title: '名称',
                            dataIndex: 'partsName',
                            align: 'center',
                        },
                        {
                            title: '规格',
                            dataIndex: 'specs',
                            align: 'center',
                        },
                        {
                            title: '系列',
                            dataIndex: 'series',
                            align: 'center',
                        },
                        {
                            title: '形式',
                            dataIndex: 'xs',
                            align: 'center',
                        },
                        {
                            title: '供应商',
                            dataIndex: 'supplierBak',
                            align: 'center',
                        },
                    ]
                }

            ],
            columns3: [
                {
                    title: '整机配置',
                    children: [
                        {
                            title: '名称',
                            dataIndex: 'partsName',
                            align: 'center',
                        },
                        {
                            title: '规格',
                            dataIndex: 'specs',
                            align: 'center',
                        },
                        {
                            title: '系列',
                            dataIndex: 'series',
                            align: 'center',
                        },
                        {
                            title: '形式',
                            dataIndex: 'xs',
                            align: 'center',
                        },
                        {
                            title: '供应商',
                            dataIndex: 'supplierBak',
                            align: 'center',
                        },
                    ]
                }

            ]
        }
    },
    mounted() {
        this.checkName();
    },
    methods: {
        loadBasicData: function () {
            this.mainList = [];
            this.proceList = [];
            this.zjList = [];
            // 本体配置
            this.searchForm.platformName = this.childOption[0];
            this.searchForm.series = this.childOption[1];
            this.searchForm.customer = this.childOption[2];
            this.searchForm.productModel = this.childOption[3];
            this.searchForm.version = this.childOption[4];
            this.searchForm.year = this.childOption[5];
            this.searchForm.mainType = "本体配置";
            this.searchForm.blowoff = this.childOption[6];

            // 后处理配置、整机配置
            this.proceForm.platformName = this.childOption[0]
            this.proceForm.series = this.childOption[1]
            this.proceForm.customer = this.childOption[2]
            this.proceForm.productModel = this.childOption[3]
            this.proceForm.version = this.childOption[4]
            this.proceForm.year = this.childOption[5];
            this.proceForm.blowoff = this.childOption[6];

            let url = SELECTBASICLIST_XSGSALLOWANCEMAIN;
            if (this.childOption[4] === '新版') {
                url = SELECTBASICLISTNEWVERSION_XSGSALLOWANCEMAIN;
            }

            const _this = this;
            console.log(this.searchForm, this.proceForm);

            Promise.all([
                request(url, METHOD.POST, this.searchForm)
                    .catch(_ => { }),
                request(url, METHOD.POST, { ...this.proceForm, mainType: '后处理配置' })
                    .catch(_ => { }),
                request(url, METHOD.POST, { ...this.proceForm, mainType: '整机配置' })
                    .catch(_ => { })
            ]).then(res => {
                _this.mainList = res[0].data.data;
                _this.proceList = res[1].data.data;
                _this.zjList = res[2].data.data;

                // 提示空数组
                if (!_this.mainList.length) this.$message.warning('本体配置数据为空');
                if (!_this.proceList.length) this.$message.warning('后处理配置数据为空');
                if (!_this.zjList.length) this.$message.warning('整机配置数据为空');

            }).catch(err => {
                console.log('err =====> ', err)
            })
        },

        getLinshiBasicData: function () {
            this.searchForm.platformName = this.childOption[0]
            this.searchForm.series = this.childOption[1]
            this.searchForm.customer = this.childOption[2]
            this.searchForm.productModel = this.childOption[3]
            this.searchForm.version = this.childOption[4]
            this.searchForm.mainType = "本体配置";
            this.searchForm.blowoff = this.childOption[6];
            request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.searchForm)
                .then(res => {
                    this.mainList = res.data.data
                })
            //后处理配置
            this.proceForm.platformName = this.childOption[0]
            this.proceForm.series = this.childOption[1]
            this.proceForm.customer = this.childOption[2]
            this.proceForm.productModel = this.childOption[3]
            this.proceForm.version = this.childOption[4]
            this.proceForm.mainType = "后处理配置";
            this.proceForm.blowoff = this.childOption[6];
            request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.proceForm)
                .then(res => {
                    this.proceList = res.data.data
                    this.getZjList()
                })
        },
        /** 获取整机配置数据 **/
        getZjList() {
            //整机配置
            this.proceForm.platformName = this.childOption[0]
            this.proceForm.series = this.childOption[1]
            this.proceForm.customer = this.childOption[2]
            this.proceForm.productModel = this.childOption[3]
            this.proceForm.version = this.childOption[4]
            this.proceForm.mainType = "整机配置";
            this.proceForm.blowoff = this.childOption[6];
            request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.proceForm)
                .then(res => {
                    this.zjList = res.data.data
                })
        }
    }
}
</script>

<style lang="less">
#select_optionList {

    // :deep(.ant-table .ant-table-thead > tr > th) {
    //     padding: 4px !important;
    //     overflow-wrap: break-word !important;
    // }

    // :deep(.ant-table .ant-table-tbody > tr > td) {
    //     padding: 4px !important;
    //     overflow-wrap: break-word !important;
    // }
    .ant-table .ant-table-thead > tr > th {
        padding: 4px !important;
        overflow-wrap: break-word !important;
    }

    .ant-table .ant-table-tbody > tr > td {
        padding: 4px !important;
        overflow-wrap: break-word !important;
    }
}
</style>