<template>
    <div>

        <a-table :columns="optionalColumns" rowKey="id" :pagination="false" :data-source="optionalData"
            slot="expandedRowRender" :scroll="{ x: 520 }" size="small" bordered :customRow="onRowClick"
            :loading="optionalloading">
            <template slot="isCheck" slot-scope="text, record">
                <a-checkbox :checked="record.isCheck === '1' ? true : false" @change="cancel(record)">
                </a-checkbox>
            </template>
            <template slot="subType" slot-scope="text, record">
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.subType"
                    @change="e => handleChooseSubTypeChange(e, record)">
                    <a-select-option value="选型件">选型件</a-select-option>
                    <a-select-option value="选装件">选装件</a-select-option>
                </a-select>
            </template>
            <template slot="partsName" slot-scope="text, record">

                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.partsName"
                    @dropdownVisibleChange="onclickPartsName(record)" v-if="record.subType == '选型件'" showSearch
                    :filterOption="filterOption" @change="e => handleChoosePartChange(record)">
                    <a-select-option v-for="sty in xxListData" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.partsName"
                    @dropdownVisibleChange="onclickPartsName(record)" v-if="record.subType == '选装件'" showSearch
                    :filterOption="filterOption" @change="e => handleChoosePartChange(record)">
                    <a-select-option v-for="sty in xzListData" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>

            <template slot="series" slot-scope="text, record">
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.series"
                    v-if="record.partsName != '' && searchForm.dataType == '新版'"
                    @dropdownVisibleChange="onclickSeries(record)" @change="handleChooseSeriesChange(record)">
                    <a-select-option v-for="sty in seriesData" :key="sty">
                        {{ trimTrailingSlash(sty) }}
                    </a-select-option>
                </a-select>
            </template>

            <template slot="specs" slot-scope="text, record">
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.specs"
                    v-if="record.partsName != ''" @dropdownVisibleChange="onclickSpecs(record)"
                    @change="handleChooseSpecsChange(record)">
                    <a-select-option v-for="sty in AllListData" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>

            <template slot="xs" slot-scope="text, record">
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`" v-model="record.xs"
                    v-if="record.partsName != ''" @dropdownVisibleChange="onclickXs(record)"
                    @change="handleChooseXsChange(record)">
                    <a-select-option v-for="sty in xsData" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>

            <template slot="supplierBak" slot-scope="text, record">
                <a-select :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`"
                    v-model="record.supplierBak" v-if="record.partsName != ''"
                    @dropdownVisibleChange="onclickSupplierBak(record)"
                    @change="e => handleChooseSupplierBakChange(record)">
                    <a-select-option  key="">
                          &#12288;
                    </a-select-option>
                    <a-select-option v-for="sty in supplierBakListData" :key="`${sty.id}-${sty.supplier}`">
                        {{ sty.supplier }}
                    </a-select-option>
                </a-select>

            </template>

            <template slot="budgetedPrice" slot-scope="text, record">
                <a-input-number v-model="record.budgetedPrice"
                    :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" :step="0.01"
                    v-if="record.editable" :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`"
                    @blur="blursBudgetedPrice(record)"
                    @change="handleChange(record.budgetedPrice, record.id, record.budgetedPrice)" />
            </template>

            <template slot="actualPrice" slot-scope="text, record">
                <a-input-number v-model="record.actualPrice"
                    :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" :step="0.01"
                    v-if="record.editable" :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`"
                    @blur="blursActualPrice(record)"
                    @change="handleChange(record.actualPrice, record.id, record.actualPrice)" />
            </template>
            <template slot="valuePrice" slot-scope="text, record">
                <a-input-number v-model="record.valuePrice"
                    :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                    :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')" :precision="2" :step="0.01"
                    v-if="record.subType == '选型件'" :style="`width: 100%;${record.hasNoBasic == 1 ? 'color:red' : ''}`"
                    @blur="blursValuePrice()" @change="handleChange(record.valuePrice, record.id, record.valuePrice)" />
            </template>

            <template slot="action" slot-scope="text, record">
                <template v-if="record.editable">
                    <span>
                        <a @click="saveRow(record)">保存</a>
                    </span>
                </template>
            </template>


        </a-table>

        <div>
            <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus"
                @click="newMember">新增</a-button>
        </div>

    </div>

</template>

<script>
import {
    LIST_XSGSPARTSOPTIONNEW_VERSION,
    LIST_XSGSPARTSPRICE,
    LIST_XSGSALLOWANCEPARTS,
    getUuid,
    LIST_XSGSPARTSSETTING,
    LIST_XSGSPARTSOPTION, LIST_XSGSPARTSPRICENEWVERSION,
    XSGS_LISTNEWVERSION
} from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
export default {
    name: "edit_optional",

    props: {
        masterId: String,
        // optionSubtotal:Number,
        // chooseAmount:Number,
        searchForm: Object,
        year: Number,
        basicSubtotal: Number,
        productModelJh: String,
        optionList: Array,
    },
    data() {
        return {
            isBlurs: false,
            //选型选装配置
            optionalData: [],
            optionalloading: false,
            optionalForm: {
                masterId: null,
            },
            subType: null,
            subType2: null,
            subType3: null,
            subType4: null,
            subType5: null,
            subType6: null,
            subType7: null,
            subType8: null,
            partsName: null,
            partsName2: null,
            partsName3: null,
            partsName4: null,
            partsName5: null,
            partsName6: null,
            partsName7: null,
            partsName8: null,
            ids: [],
            isJump: true,
            isJump2: true,
            noInList: [],
            noPartsList: [],
            // linshiList:[],
            form: {},
            allForm: {},
            optionalPagination: {
                total: 0,
                current: 1,
                size: '10',
                showSizeChanger: true, // 是否可以改变 size
                showQuickJumper: true, // 是否可以快速跳转至某页
                pageSizeOptions: ['10', '20', '30', '40', '50'],
                showTotal: (total) =>
                    `共 ${total} 条 第${this.optionalPagination.current}/${Math.ceil(
                        total / this.optionalPagination.size
                    )}页`, // 显示总数
                onChange: (page, size) => this.onOptionalcPageChange(page, size), // 页码改变的回调
                onShowSizeChange: (current, size) =>
                    this.onOptionalcSizeChange(current, size) // 改变每页数量时更新显示
            },
            optionalColumns: [
                { title: '选中', dataIndex: 'isCheck', width: 50, scopedSlots: { customRender: 'isCheck' } },
                { title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }, width: 90 },
                { title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }, width: 200 },
                { title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }, width: 120 },
                { title: '供应商', dataIndex: 'supplierBak', scopedSlots: { customRender: 'supplierBak' }, width: 120 },
                { title: '指导售价', dataIndex: 'budgetedPrice', scopedSlots: { customRender: 'budgetedPrice' }, width: 90 },
                { title: '实际售价', dataIndex: 'actualPrice', scopedSlots: { customRender: 'actualPrice' }, width: 90 },
                { title: '价差', dataIndex: 'valuePrice', scopedSlots: { customRender: 'valuePrice' }, width: 90 },
                // {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
            ],
            optionalsColumns: [
                { title: '选中', dataIndex: 'isCheck', width: 50, scopedSlots: { customRender: 'isCheck' } },
                { title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }, width: 90 },
                { title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }, width: 200 },
                { title: '系列', dataIndex: 'series', scopedSlots: { customRender: 'series' }, width: 90 },
                { title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }, width: 120 },
                { title: '形式', dataIndex: 'xs', scopedSlots: { customRender: 'xs' }, width: 90 },
                { title: '供应商', dataIndex: 'supplierBak', scopedSlots: { customRender: 'supplierBak' }, width: 120 },
                { title: '指导售价', dataIndex: 'budgetedPrice', scopedSlots: { customRender: 'budgetedPrice' }, width: 90 },
                { title: '实际售价', dataIndex: 'actualPrice', scopedSlots: { customRender: 'actualPrice' }, width: 90 },
                { title: '价差', dataIndex: 'valuePrice', scopedSlots: { customRender: 'valuePrice' }, width: 90 },
                // {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
            ],
            xzListData: [],
            xxListData: [],
            specsListData: [],
            AllListData: [],
            supplierBakListData: [],
            seriesData: [],
            xsData: [],
        }
    },
    mounted() {
        this.checkName();
    },
    watch: {
        "searchForm": {
            handler(val) {
                if (val.dataType == '新版') {
                    this.optionalColumns = this.optionalsColumns

                } else if (val.dataType == '旧版') {
                    this.optionalColumns = this.optionalsColumns.filter(x => x.title != '系列' && x.title != '形式');
                    this.optionalData.forEach(item => {
                        item.series = null, item.xs = null
                    })
                    this.subType = null, this.subType2 = null, this.partsName = null, this.partsName2 = null
                    this.subType5 = null, this.subType6 = null, this.partsName5 = null, this.partsName6 = null
                }
            },
            deep: true
        },
    },
    methods: {
        initDataList(d) {
            let url = LIST_XSGSPARTSOPTION;
            const params = {
                series: this.searchForm.series,
                mainType: d.subType
            }

            if (this.searchForm.dataType === '新版') {
                url = LIST_XSGSPARTSOPTIONNEW_VERSION;
                params['year'] = this.searchForm.year;
                params['blowoff'] = this.searchForm.blowoff;
                params['type'] = this.searchForm.type;
                params['plate'] = this.searchForm.plate;
                params['productModelJh'] = this.productModelJh;
            } else {
                params['platformName'] = this.searchForm.plate;
            }

            request(url, METHOD.POST, { ...params })
                .then(res => {
                    const temp = [];
                    for (let i = 0; i < res.data.data.length; i++) {
                        if (temp.indexOf(res.data.data[i].partsName) === -1) {
                            temp.push(res.data.data[i].partsName);
                        }
                    }
                    if (d.subType === '选型件') {
                        this.xxListData = temp
                    } else {
                        this.xzListData = temp
                    }
                })
        },
        getOptionalcDataList() {
            return this.optionalData;
        },
        getIsBlurs() {
            return this.isBlurs;
        },
        setIsBlurs() {
            this.isBlurs = false;
        },
        //选型选装配置
        getOptionalcData: function () {
            if (this.masterId != null) {
                this.optionalloading = true
                this.optionalForm.subType = "subType"
                this.optionalForm.isSubtraction = "0";
                this.optionalForm.masterId = this.masterId;
                this.optionalData = []
                request(LIST_XSGSALLOWANCEPARTS, METHOD.POST, { ...this.optionalForm })
                    .then(res => {
                        this.optionalloading = false
                        if (this.searchForm.id === null) {
                            res.data.data.forEach(item => {
                                request(getUuid, METHOD.GET).then(res => {
                                    item.id = res.data.data
                                })
                                item.masterId = null
                            })
                            this.optionalData = res.data.data
                        } else {
                            this.optionalData = res.data.data
                        }
                        //默认进入编辑状态
                        const newData = [...this.optionalData];
                        for (let i = 0; i < this.optionalData.length; i++) {
                            const target = newData.filter(item => this.optionalData[i].id === item.id)[0];
                            if (target) {
                                target.editable = true;
                                this.optionalData = newData;
                            }
                        }

                    })
            } else {
                this.optionalData = []
                this.optionalPagination.total = parseInt(this.optionalData.length)
            }

        },
        //暂时list
        getoptionList() {
            this.optionalloading = false
            this.optionalData = this.optionList;
            console.log(this.optionalData);

            this.optionalPagination.total = parseInt(this.optionList.length)
            // 检查是否有不存在基本型配置的零部件
            if (!this.isEmpty(this.optionalData)) {
                let noList = this.optionalData.filter(s => s.hasNoBasic == 1);
                if (!this.isEmpty(noList)) {
                    let msg = "请注意，以下选型件尚未进行基本型配置：";
                    noList.forEach(item => {
                        msg += "\n" + item.partsName + "；"
                    })
                    msg += "\n请先进行基本型配置。";
                    this.$warning({
                        title: '基本型未配置',
                        onOk: () => {
                        },
                        content: (
                            <div style="white-space: pre-wrap;">{msg}</div>
                        ),
                        width: 500
                    });
                }
            }

            //默认进入编辑状态
            const newData = [...this.optionalData];
            for (let i = 0; i < newData.length; i++) {
                const target = newData.filter(item => newData[i].id === item.id)[0];
                if (target) {
                    target.editable = true;
                    this.optionalData = newData;
                }
            }
            console.log(this.optionalData);


        },
        //获取改变值
        handleChange(value, id, column) {
            const newData = [...this.optionalData]
            const target = newData.find(item => id === item.id)
            if (target) {
                target[column] = value
                this.optionalData = newData
            }

        },

        //选型选装配置
        onOptionalcPageChange(page, size) {
            this.optionalPagination.current = page;
            this.optionalPagination.size = size.toString();
        },
        onOptionalcSizeChange(current, size) {
            this.optionalPagination.current = 1;
            this.optionalPagination.size = size.toString();

        },
        //新增
        newMember() {
            request(getUuid, METHOD.GET).then(res => {

                const length = this.optionalPagination.total;
                const newData = [...this.optionalData]
                newData.push({
                    id: length === 0 ? res.data.data : res.data.data,
                    masterId: null,
                    isCheck: '1',
                    subType: '',
                    partsName: '',
                    specs: '',
                    supplierBak: '',
                    budgetedPrice: '',
                    standardPrice: '',
                    actualPrice: '',
                    valuePrice: '',
                    isSubtraction: '0',
                    series: '',
                    xs: '',
                })
                //默认进入编辑状态

                newData.forEach(item => {
                    item.editable = true;
                })
                const target = newData.filter(item => length === 0 ? res.data.data : res.data.data === item.id)[0];
                if (target) {
                    target.editable = true;
                    this.optionalData = newData;
                }
                this.optionalPagination.total = parseInt(newData.length);
                this.isBlurs = true;
            })
        },
        blursBudgetedPrice(record) {
            this.isBlurs = true;
            const newData = [...this.optionalData]
            const target = newData.find(item => record.id === item.id)
            if (target) {
                // target.standardPrice = record.budgetedPrice;
                this.optionalData = newData
            }
        },
        //更改价差
        blursValuePrice() {
            //当前页码的选装实际价格设置为0
            let optionalTotal = 0;
            //当前页码的选型件总价差设置为0
            let lectotypeDifTotal = 0;
            this.optionalData.forEach(item => {
                if (item.subType === '选型件') {
                    lectotypeDifTotal = this.NumberAdd(lectotypeDifTotal, item.valuePrice)
                }
                if (item.subType === '选装件') {
                    optionalTotal = this.NumberAdd(optionalTotal, item.actualPrice)
                }
            })
            //选型选装小计
            let xxxzTotal = this.NumberAdd(lectotypeDifTotal, optionalTotal);
            //选型选装件小计 = 选型件价差+选装件实际售价
            this.$emit('optionTotal', xxxzTotal)
            //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
            this.$emit('chooseAmountToal', this.NumberAdd(xxxzTotal, this.basicSubtotal))
            //不允许跳转
            this.isJump2 = false;
            this.isBlurs = true;
        },
        blursActualPrice(record) {
            // console.log(record);
            this.isBlurs = true;
            const newData = [...this.optionalData]
            newData.forEach(item => {
                item.editable = true;
            })
            const target = newData.find(item => record.id === item.id)
            //价差 =  实际价格-标准价格
            if (target) {
                if (record.standardPrice != null) {
                    target.valuePrice = this.Subtr(record.actualPrice, record.standardPrice);
                }
                this.optionalData = newData
            }

            //当前页码的选装实际价格设置为0
            let optionalTotal = 0;
            //当前页码的选型件总价差设置为0
            let lectotypeDifTotal = 0;
            newData.forEach(item => {
                if (item.subType === '选型件') {
                    lectotypeDifTotal = this.NumberAdd(lectotypeDifTotal, item.valuePrice)
                }
                if (item.subType === '选装件') {
                    optionalTotal = this.NumberAdd(optionalTotal, item.actualPrice)
                }
            })
            //选型选装小计
            let xxxzTotal = this.NumberAdd(lectotypeDifTotal, optionalTotal);
            //选型选装件小计 = 选型件价差+选装件实际售价
            this.$emit('optionTotal', xxxzTotal)
            //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
            this.$emit('chooseAmountToal', this.NumberAdd(xxxzTotal, this.basicSubtotal))
            //不允许跳转
            this.isJump2 = false;
        },

        //重新计算()
        Recalculate(newData, noPartData) {
            this.isBlurs = true;
            //当前页码的选装实际价格设置为0
            let optionalTotal = 0;
            //当前页码的选型件总价差设置为0
            let lectotypeDifTotal = 0;
            newData.forEach(item => {
                if (item.subType === '选型件') {
                    lectotypeDifTotal = this.NumberAdd(lectotypeDifTotal, item.valuePrice)
                }
                if (item.subType === '选装件') {
                    optionalTotal = this.NumberAdd(optionalTotal, item.actualPrice)
                }
            })

            //选型减配小计设置为0
            let lectotypeAcualTotal = 0;
            noPartData.forEach(item => {
                if (item.subType === '选型件') {
                    lectotypeAcualTotal = this.NumberAdd(lectotypeAcualTotal, item.actualPrice)
                }
            })
            //选型选装小计
            let xxxzTotal = this.NumberAdd(lectotypeDifTotal, optionalTotal);
            //选型选装件小计 = 选型件价差+选装件实际售价
            this.$emit('optionTotal', xxxzTotal)
            //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
            this.$emit('chooseAmountToal', this.NumberAdd(xxxzTotal, lectotypeAcualTotal))
            //选型减配小计
            this.$emit('lectotypeSubtotal', lectotypeAcualTotal)
        },
        //取消
        cancel(record) {
            this.isBlurs = true;
            const newData = [...this.optionalData]
            newData.forEach(item => {
                item.editable = true;
            })
            const target = newData.find(item => record.id === item.id)
            if (target) {
                let that = this;
                this.$confirm({
                    content: `是否取消？`,
                    onOk: () => {

                        if (target.subType === "选型件") {
                            // that.noPartsList=that.$parent.$parent.$parent.$parent.$parent.getNoPart();
                            that.$emit('getNoParts');
                            //选型减配=1
                            target.isSubtraction = "1";
                            // 减配还原标准售价
                            let standardPrice = target.standardPrice;
                            target.budgetedPrice = standardPrice;
                            target.actualPrice = standardPrice;
                            target.specs = target.standardSpecs;
                            target.valuePrice = null;
                            //指导售价不为空
                            if (target.budgetedPrice != null) {
                                if (target.budgetedPrice != 0) {
                                    target.budgetedPrice = "-" + target.budgetedPrice;
                                }
                            }
                            //实际售价不为空
                            if (target.actualPrice != null) {
                                if (target.actualPrice != 0) {
                                    target.actualPrice = "-" + target.actualPrice;
                                }
                            }
                            //在数组末端添加一组数据
                            that.noPartsList.push(target)
                            //传递list返回父组件
                            that.$emit('noPartsListNew', that.noPartsList)
                            // that.$parent.$parent.$parent.$parent.$parent.getChildrenPart();
                            //删除
                            const newDatas = newData.filter(item => item.id !== target.id)
                            that.optionalData = newDatas
                            //重新计算值
                            that.Recalculate(newDatas, that.noPartsList);
                        } else {
                            //直接删除
                            const newData1 = newData.filter(item => item.id !== target.id)
                            that.optionalData = newData1
                            that.$emit('getNoParts');
                            //重新计算值
                            that.Recalculate(newData1, that.noPartsList);
                        }
                        //更新total
                        that.optionalPagination.total = parseInt(that.Subtr(this.optionalPagination.total, "1"))


                    },
                    onCancel() {
                        target.isCheck = '1'
                    }
                });
            }

        },

        // 将输入的内容与显示的内容进行匹配
        filterOption(value, option) {
            return option.componentOptions.children[0].text.indexOf(value) >= 0
        },
        /** 点击获取系列 **/
        onclickSeries(record) {
            console.log(this.searchForm);
            //重新获取系列时清空规格、形式、供应商集合
            this.AllListData = []
            this.xs = []
            this.supplierBakListData = []
            this.isBlurs = true;
            //首次
            this.subType = record.subType
            this.partsName = record.partsName


            let url = LIST_XSGSPARTSPRICE;
            const params = {
                partsName: record.partsName,
                year: this.year
            }
            if (this.searchForm.dataType === '新版') {
                url = LIST_XSGSPARTSPRICENEWVERSION;
                params['customer'] = this.searchForm.customer
            }

            //   if (this.subType != this.subType2 || this.partsName != this.partsName2) {
            //点击当前获取相对应系列
            this.seriesData = []
            request(url, METHOD.POST, {
                partsName: record.partsName,
                year: this.year,
                version: this.searchForm.dataType == '新版' ? 1 : ''
            }).then(res => {
                if (res.data.data.length > 0) {
                    var temp = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                        if(res.data.data[i].series == null){
                            res.data.data[i].series = ''
                        }
                        if (res.data.data[i].series != null) {
                            
                            if (temp.indexOf(res.data.data[i].series) == -1) {
                                temp.push(res.data.data[i].series);
                            }
                        }
                    }
                    this.seriesData = temp
                    console.log(this.seriesData);

                } else {
                    this.seriesData = []
                }
                this.subType2 = record.subType
                this.partsName2 = record.partsName
            })
            //   }
        },
        //点击获取规格
        onclickSpecs(record) {
            //重新获取规格时清空供应商集合
            this.xsData = []
            this.supplierBakListData = []
            this.isBlurs = true;
            //首次
            this.subType3 = record.subType
            this.partsName3 = record.partsName
            // if(this.subType3!=this.subType4||this.partsName3!=this.partsName4) {

            let url = LIST_XSGSPARTSPRICE;
            const params = {
                partsName: record.partsName,
                year: this.year,
                series: record.series,
            }
            if (this.searchForm.dataType === '新版') {
                url = LIST_XSGSPARTSPRICENEWVERSION;
                params['customer'] = this.searchForm.customer
            }


            //点击当前获取相对应规格
            this.AllListData = []
            request(url, METHOD.POST, {
                partsName: record.partsName,
                year: this.year,
                series: record.series,
                version: this.searchForm.dataType == '新版' ? 1 : ''
            }).then(res => {
                if (res.data.data.length > 0) {
                    var temp = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                        if (res.data.data[i].specs != null) {
                            if (temp.indexOf(res.data.data[i].specs) == -1) {
                                temp.push(res.data.data[i].specs);
                            }
                        }
                    }
                    this.AllListData = temp
                } else {
                    this.AllListData = []
                }
                this.subType4 = record.subType
                this.partsName4 = record.partsName
            })
            // }
        },
        /** 点击获取形式 **/
        onclickXs(record) {
            //重新获取形式时清空供应商集合
            this.supplierBakListData = []
            this.isBlurs = true;
            //首次
            this.subType5 = record.subType
            this.partsName5 = record.partsName
            //   if (this.subType5 != this.subType6 || this.partsName5 != this.partsName6) {
            let url = LIST_XSGSPARTSPRICE;
            const params = {
                partsName: record.partsName,
                year: this.year,
                series: record.series,
                specs: record.specs,
                version: this.searchForm.dataType == '新版' ? 1 : ''
            }
            if (this.searchForm.dataType === '新版') {
                url = LIST_XSGSPARTSPRICENEWVERSION;
                params['customer'] = this.searchForm.customer;
            }
            //点击当前获取相对应系列
            this.seriesData = []
            request(url, METHOD.POST, { ...params }).then(res => {
                if (res.data.data.length > 0) {
                    var temp = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                        if (res.data.data[i].xs != null) {
                            if (temp.indexOf(res.data.data[i].xs) == -1) {
                                temp.push(res.data.data[i].xs);
                            }
                        }
                    }
                    this.xsData = temp
                } else {
                    this.xsData = []
                }
                this.subType6 = record.subType
                this.partsName6 = record.partsName
            })
            //   }
        },
        //点击获取供应商
        onclickSupplierBak(record) {
            this.isBlurs = true;
            //首次
            this.subType7 = record.subType
            this.partsName7 = record.partsName
            // if(this.subType7!=this.subType8||this.partsName7!=this.partsName8) {
            this.supplierBakListData = []
            let url = LIST_XSGSPARTSPRICE;
            const params = {
                // partsName: record.partsName,
                // specs: record.specs,
                ...record,
                year: this.year,
                version: this.searchForm.dataType == '新版' ? 1 : ''
            }
            params.supplier = ''
            if (this.searchForm.dataType === '新版') {
                url = LIST_XSGSPARTSPRICENEWVERSION;
                params['customer'] = this.searchForm.customer;
            }
            //点击当前获取相对应供应商
            request(url, METHOD.POST, {
                ...params
            }).then(res => {

                if (res.data.data.length === 1) {
                    if (res.data.data[0].supplier === null) {
                        this.supplierBakListData = []
                    } else {
                        this.supplierBakListData = res.data.data
                    }
                } else if (res.data.data.length > 1) {
                    this.supplierBakListData = []
                    let newData = []
                    res.data.data.forEach(item => {

                        if (item.supplier != null) {
                            newData.push(item)
                        }
                    })
                    // console.log(newData);

                    this.supplierBakListData = newData
                }
                this.subType8 = record.subType
                this.partsName8 = record.partsName

            })
            // }
        },
        //零部件点击事件
        onclickPartsName(d) {
            if (this.searchForm.machineType == null) {
                this.$message.info('请先输入新增状态机！')
                return
            }
            if (this.searchForm.plate == null) {
                this.$message.info('请先选择板块！')
                return
            }
            this.initDataList(d)
        },
        handleChooseSubTypeChange(e, record) {
            if (e) {
                record.subType = e;
            }

            this.isBlurs = true;
            //清空后面
            const newData = [...this.optionalData]
            const target = newData.find(item => record.id === item.id)
            if (target) {
                target.partsName = ''
                target.specs = ''
                target.standardSpecs = ''
                target.supplierBak = ''
                target.budgetedPrice = ''
                target.actualPrice = ''
                // target.standardPrice = ''
                target.valuePrice = ''
                target.series = ''
                target.xs = ''
                this.optionalData = newData
            }

        },
        /** 选择系列 **/
        handleChooseSeriesChange(record) {


            this.isBlurs = true;
            // const newData = [...this.optionalData]
            // const target = newData.find(item => record.id === item.id)
            // target.series = target && target.series ? target.series : ''
            // target.specs = ''
            // target.xs = ''
            // target.supplierBak =  ''
            let new_OR_old = this.searchForm.dataType == '新版' ? 'new' : 'old'
            if (new_OR_old == 'new') {
                this.newPriceGet(record)
            }
        },
        //选规格
        handleChoosePartChange(record) {
            this.isBlurs = true;
            const newData = [...this.optionalData]
            const target = newData.find(item => record.id === item.id)
            console.log(target, record);

            if (target) {
                target.specs = ''
                target.standardSpecs = ''
                target.supplierBak = ''
                target.budgetedPrice = ''
                target.actualPrice = ''
                // target.standardPrice = ''
                target.valuePrice = ''
                target.series = ''
                target.xs = ''
                this.optionalData = newData
            }
            //临时标准规格list
            if (record.subType == '选型件') {
                //根据当前选型配件去本体查询标准规格
                // request(LIST_XSGSPARTSSETTING, METHOD.POST, {
                //     platformName: this.searchForm.plate,
                //     // blowoff:this.searchForm.blowoff,
                //     series: this.searchForm.series,
                //     productModel: this.searchForm.productModelJh,
                //     partsName: target.partsName,
                // }).then(result => {

                //     if (result.data.data.length > 0) {
                //         //标准规格
                //         target.specs = result.data.data[0].specs;
                //         target.standardSpecs = result.data.data[0].specs;
                //         target.series = result.data.data[0].series;
                //         target.xs = result.data.data[0].xs;
                //         target.supplierBak = result.data.data[0].supplierBak;
                //         let url = LIST_XSGSPARTSPRICE;
                //         const params = {
                //             partsName: record.partsName,
                //             specs: target.specs,
                //             year: this.year,
                //             series: target.series,
                //             xs: target.xs,
                //             supplierBak: target.supplierBak,
                //         }
                //         if (this.searchForm.dataType === '新版') {
                //             url = LIST_XSGSPARTSPRICENEWVERSION;
                //             params['customer'] = this.searchForm.customer;
                //         }
                //         //大于0说明有标准规格，
                //         request(url, METHOD.POST, {
                //             partsName: record.partsName,
                //             specs: target.specs,
                //             year: this.year,
                //             series: target.series,
                //             xs: target.xs,
                //             supplierBak: target.supplierBak,
                //             version: this.searchForm.dataType == '新版' ? 1 : ''
                //         }).then(result1 => {

                //             if (result1.data.data.length > 0) {
                //                 target.budgetedPrice = result1.data.data[0].actualPrice
                //                 target.actualPrice = result1.data.data[0].actualPrice
                //                 target.standardPrice = result1.data.data[0].actualPrice
                //                 target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                //                 this.blursActualPrice(target);
                //             } else {
                //                 target.budgetedPrice = 0
                //                 target.actualPrice = 0
                //                 target.standardPrice = 0
                //                 target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                //                 this.blursActualPrice(target);
                //             }

                //         })
                //     } else {
                //         let url = LIST_XSGSPARTSPRICE;
                //         const params = {
                //             partsName: record.partsName,
                //             year: this.year
                //         }
                //         if (this.searchForm.dataType === '新版') {
                //             url = LIST_XSGSPARTSPRICENEWVERSION;
                //             params['customer'] = this.searchForm.customer;
                //         }
                //         //没有标准规格
                //         request(url, METHOD.POST,
                //             {
                //                 partsName: record.partsName,
                //                 year: this.year,
                //                 version: this.searchForm.dataType == '新版' ? 1 : ''
                //             }).then(res => {

                //                 if (res.data.data.length === 1 && res.data.data[0].specs === null && res.data.data[0].supplier === null) {
                //                     target.budgetedPrice = res.data.data[0].actualPrice
                //                     target.actualPrice = res.data.data[0].actualPrice
                //                     target.standardPrice = res.data.data[0].actualPrice
                //                     target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                //                     this.blursActualPrice(target);
                //                 } else if (res.data.data.length === 0) {
                //                     target.budgetedPrice = 0
                //                     target.actualPrice = 0
                //                     target.standardPrice = 0
                //                     target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                //                     this.blursActualPrice(target);
                //                 }
                //             })
                //     }

                //     this.$emit('deleteDoubleXx', target)
                // })
                let url = LIST_XSGSPARTSPRICE;
                const params = {
                    partsName: record.partsName,
                    supplierBak: record.supplierBak,
                    series: record.series,
                    xs: record.xs,
                    specs: record.specs,
                    year: this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : ''
                }
                if (this.searchForm.dataType === '新版') {
                    url = LIST_XSGSPARTSPRICENEWVERSION;
                    params['customer'] = this.searchForm.customer;
                }
                //没有标准规格
                request(url, METHOD.POST,
                    {
                        ...params
                    }).then(res => {

                        if (res.data.data.length === 1 && res.data.data[0].specs === null && res.data.data[0].supplier === null) {
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            // target.standardPrice = res.data.data[0].actualPrice
                            target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                            this.blursActualPrice(target);
                        } else if (res.data.data.length === 0) {
                            target.budgetedPrice = 0
                            target.actualPrice = 0
                            target.standardPrice = 0
                            target.valuePrice = this.Subtr(target.actualPrice, target.standardPrice);
                            this.blursActualPrice(target);
                        }
                    })
            } else if (record.subType == '选装件') {
                let url = LIST_XSGSPARTSPRICE;
                const params = {
                    partsName: record.partsName,
                    supplierBak: record.supplierBak,
                    series: record.series,
                    xs: record.xs,
                    specs: record.specs,
                    year: this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : ''
                }
                if (this.searchForm.dataType === '新版') {
                    url = LIST_XSGSPARTSPRICENEWVERSION;
                    params['customer'] = this.searchForm.customer;
                }
                request(url, METHOD.POST,
                    {
                        ...params
                    }).then(res => {
                        if (res.data.data.length === 1 && res.data.data[0].specs === null && res.data.data[0].supplier === null) {
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            this.blursActualPrice(target);
                        } else if (res.data.data.length === 0) {
                            target.budgetedPrice = 0
                            target.actualPrice = 0
                            target.standardPrice = 0
                            this.blursActualPrice(target);
                        }
                    })

            }
        },
        /** 选择系列 **/
        handleChooseXsChange(record) {
            this.isBlurs = true;
            // const newData = [...this.optionalData]
            // const target = newData.find(item => record.id === item.id)
            // if (target) {
            //     target.standardSpecs = ''
            //     target.supplierBak = ''
            //     target.budgetedPrice = ''
            //     target.actualPrice = ''
            //     // target.standardPrice = ''
            //     target.valuePrice = ''
            //     this.optionalData = newData
            // }
            let new_OR_old = this.searchForm.dataType == '新版' ? 'new' : 'old'
            if (new_OR_old == 'new') {
                this.newPriceGet(record)
            }
        },
        //选供应商
        handleChooseSpecsChange(record) {
            this.isBlurs = true;
            const newData = [...this.optionalData]
            const target = newData.find(item => record.id === item.id)
            // if (target) {
            //     target.supplierBak = ''
            //     target.budgetedPrice = ''
            //     target.actualPrice = ''
            //     target.valuePrice = ''
            //     this.optionalData = newData;
            // }
            let new_OR_old = this.searchForm.dataType == '新版' ? 'new' : 'old'
            if (new_OR_old == 'new') {
                this.newPriceGet(record)
            } else {
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                    partsName: target.partsName,
                    specs: target.specs,
                    year: this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : ''
                }).then(res => {
                    if (res.data.data.length > 0) {
                        if (res.data.data.length === 1 && res.data.data[0].supplier === null) {
                            if (target.subType == '选装件') {
                                if (target) {
                                    target.budgetedPrice = res.data.data[0].actualPrice
                                    target.actualPrice = res.data.data[0].actualPrice

                                    this.blursActualPrice(target);
                                }
                            } else if (target.subType == '选型件') {
                                if (target) {
                                    target.budgetedPrice = res.data.data[0].actualPrice
                                    target.actualPrice = res.data.data[0].actualPrice
                                    if (target.standardPrice === '' && target.masterId === null) {
                                        target.standardPrice = res.data.data[0].actualPrice;
                                    }
                                    this.blursActualPrice(target);
                                }
                            }
                        }
                        else if (res.data.data.length > 1) {
                            if (target.subType == '选装件') {
                                if (target) {
                                    res.data.data.forEach(item => {
                                        if (item.supplier == null) {
                                            target.budgetedPrice = item.actualPrice
                                            target.actualPrice = item.actualPrice
                                        }
                                    })
                                    this.blursActualPrice(target);
                                }
                            } else if (target.subType == '选型件') {
                                []
                                if (target) {
                                    res.data.data.forEach(item => {
                                        if (item.supplier == null) {
                                            target.budgetedPrice = item.actualPrice
                                            target.actualPrice = item.actualPrice
                                            if (target.standardPrice === '' && target.masterId === null) {
                                                target.standardPrice = item.actualPrice;
                                            }
                                        }
                                    })

                                    this.blursActualPrice(target);
                                }
                            }
                        }
                    }

                    //重新选取规格时供应商重新读取
                    this.subType4 = null
                    this.partsName4 = null
                })
                if (target) {
                    target.supplierBak = ''
                    target.budgetedPrice = ''
                    target.actualPrice = ''
                    target.valuePrice = ''
                    this.optionalData = newData;
                }
            }
        },
        //获取价格
        handleChooseSupplierBakChange(record) {
            this.isBlurs = true;
            let isFinish = 1
            let new_OR_old = this.searchForm.dataType == '新版' ? 'new' : 'old'
            if (new_OR_old == 'new') {
                this.newPriceGet(record,isFinish)
            } else {
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                    partsName: record.partsName,
                    specs: record.specs,
                    supplier: record.supplierBak.split('-')[1] ?? '',
                    year: this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : '',
                    
                }).then(res => {

                    if (res.data.data.length === 1) {
                        const newData = [...this.optionalData]
                        const target = newData.find(item => record.id === item.id)
                        this.optionalData = newData;
                        if (target.subType == '选装件') {
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            this.blursActualPrice(target);
                        } else if (target.subType == '选型件') {
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            if (target.standardPrice === '' && target.masterId === null) {
                                target.standardPrice = res.data.data[0].actualPrice;
                            }
                            this.blursActualPrice(target);
                        }
                    }

                })
            }
        },
        resetId() {
            this.optionalData.forEach(item => {
                request(getUuid, METHOD.GET).then(res => {
                    item.id = res.data.data
                })
                item.masterId = null
            })
        },
        setNoparts(noPartsList) {
            this.noPartsList = noPartsList;
        },
        newPriceGet(record,isFinish = 0) {
            console.log(record);
            
            const newData = [...this.optionalData]
            const target = newData.find(item => record.id === item.id)
            const supplier = record.supplierBak ? (record.supplierBak.includes('-') ? record.supplierBak.split('-')[1] : record.supplierBak) : '';
            record.supplier = supplier
            request(XSGS_LISTNEWVERSION, METHOD.POST, {
                year: this.year,
                customer: this.searchForm.customer,
                partsName: record.partsName,
                series: record.series,
                specs: record.specs,
                xs: record.xs,
                supplier: record.supplier,
                isFinish,
                // supplierBak: record.supplierBak ? record.supplierBak.split('-')[1] : '',
                // version: this.searchForm.dataType == '新版' ? 1 : ''
            }).then(res => {
                console.log(res);

                if (res.data.data.length == 1) {
                    if (target.subType == '选装件') {
                        target.budgetedPrice = res.data.data[0].actualPrice
                        target.actualPrice = res.data.data[0].actualPrice
                        this.blursActualPrice(target);
                    } else if (target.subType == '选型件') {
                        target.budgetedPrice = res.data.data[0].actualPrice
                        target.actualPrice = res.data.data[0].actualPrice
                        if (target.standardPrice === '' && target.masterId === null) {
                            target.standardPrice = res.data.data[0].actualPrice;
                        }
                        this.blursActualPrice(target);
                    }
                    if (target) {
                        this.optionalData = newData
                    }
                } else {
                    if (target) {
                        target.specs = target && target.specs ? target.specs : ''
                        target.standardSpecs = target && target.standardSpecs ? target.standardSpecs : ''
                        target.supplierBak = target && target.supplierBak ? target.supplierBak : ''
                        target.budgetedPrice = ''
                        target.actualPrice = ''
                        // target.standardPrice = ''
                        target.valuePrice = ''
                        target.xs = target && target.xs ? target.xs : ''
                        this.optionalData = newData
                    }
                }
            }).catch({
                if(target) {
                    target.specs = ''
                    target.standardSpecs = ''
                    target.supplierBak = ''
                    target.budgetedPrice = ''
                    target.actualPrice = ''
                    // target.standardPrice = ''
                    target.valuePrice = ''
                    target.xs = ''
                    this.optionalData = newData
                }
            })
        },
        trimTrailingSlash(str) {
            // console.log(str);
            
            return str.endsWith('/') ? str.slice(0, -1) : str;
        },
    }
}
</script>

<style scoped>
/*/deep/.ant-table-thead >tr >th{*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/

/*/deep/.ant-table-tbody >tr >td{*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/

/*/deep/.ant-form >table >tbody >tr >td {*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/
</style>
