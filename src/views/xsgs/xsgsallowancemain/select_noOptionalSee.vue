<template>
    <div>
        <a-table
                :columns="noOptionColumns"
                rowKey="id"
                :pagination="false"
                :data-source="noOptionData"
                :scroll="{ x: 1000 }"
                size="small"
                bordered
                :customRow="onRowClick"
                :loading="loading">
            <template slot="subType" slot-scope="text">
                {{text}}
            </template>

            <template slot="partsName" slot-scope="text" >
                {{text}}
            </template>

            <template slot="specs" slot-scope="text" >
                {{text}}
            </template>

            <template slot="supplierBak" slot-scope="text" >
                {{text}}
            </template>

            <template slot="budgetedPrice" slot-scope="text" >
                {{text}}
            </template>

            <template slot="actualPrice" slot-scope="text" >
                {{text}}
            </template>
        </a-table>
    </div>
</template>

<script>
    import {
        LIST_XSGSALLOWANCEPARTS,
        getUuid,} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "select_noOptional",
        props:{
            masterId:String,
            optionSubtotal:Number,
            searchForm:Object,
            year:Number,
            productModelJh:String,
        },
        data() {
            return {
                loading: false,
                noOptionForm: {
                    masterId:null,
                },
                //减配
                noOptionData: [],
                noOptionColumns: [
                    {title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }},
                    {title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }},
                    {title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }},
                    {title: '供应商', dataIndex: 'supplierBak',scopedSlots: { customRender: 'supplierBak' }},
                    {title: '指导售价', dataIndex: 'budgetedPrice',scopedSlots: { customRender: 'budgetedPrice' }},
                    {title: '实际售价', dataIndex: 'actualPrice',scopedSlots: { customRender: 'actualPrice' }},
                ],

            }
        },
        mounted() {
            this.checkName();
        },
        methods: {

            //选型减配
            getNoOptionData(){
                    this.loading = true
                    //条件
                    this.noOptionForm.isSubtraction = "1";
                    this.noOptionForm.masterId = this.masterId;
                    request(LIST_XSGSALLOWANCEPARTS, METHOD.POST, {...this.noOptionForm})
                        .then(res => {
                            this.loading = false
                            this.noOptionData = res.data.data
                        })
                },
                getNoOptionalcDataList(){
                    return this.noOptionData;
                }
        }
    }
</script>

<style scoped>

</style>
