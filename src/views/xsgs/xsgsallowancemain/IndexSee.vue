<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :rules="searchRules" :labelCol="{span: 8}" :wrapperCol="{span: 16}">

                <table style="width: 100%; border: 1px solid #f0f0f0;" >
                    <tbody class="ant-table">
                    <tr>
                        <td><a-form-model-item label="年份"  prop="year"/></td>
                        <td style="text-align: center">{{searchForm.year}}</td>
                        <td style="width: 90px"><a-form-model-item label="板块" prop="plate" /></td>
                        <td style="text-align: center">{{searchForm.plate}}</td>
                        <td><a-form-model-item label="客户" prop="customer"/></td>
                        <td style="text-align: center">{{searchForm.customer}}</td>
                        <td><a-form-model-item label="客户名称" prop="customerName"/></td>
                        <td colspan="3" style="text-align: center">{{searchForm.customerName}}</td>

                    </tr>

                    <tr>
                        <td colspan="10" style="text-align: center">
                            <strong>基本信息</strong>
                        </td>
                    </tr>

                    <tr>
                        <td><a-form-model-item label="参考状态机" prop="reference"/></td>
                        <td style="text-align: center">{{searchForm.reference}}</td>
                        <td><a-form-model-item label="整机开票价"  prop="unitAmountRefer"/></td>
                        <td style="text-align: center">{{searchForm.unitAmountRefer}}</td>
                        <td><a-form-model-item label="基本型开票价"  prop="basicAmountRefer"/></td>
                        <td style="text-align: center">{{searchForm.basicAmountRefer}}</td>
                        <td><a-form-model-item label="产品型号" prop="productModelRefer"/></td>
                        <td style="text-align: center">{{searchForm.productModelRefer}}</td>
                    </tr>

                    <tr>
                        <td colspan="10">
                            <a-divider></a-divider>
                        </td>
                    </tr>

                    <tr>
                        <td><a-form-model-item label="新增状态机" prop="machineType"/></td>
                        <td style="text-align: center">{{searchForm.machineType}}</td>
                        <td><a-form-model-item label="基本型指导价"  prop="basicGuidanceAmount"/></td>
                        <td style="text-align: center">{{searchForm.basicGuidanceAmount}}</td>
                        <td><a-form-model-item label="数据版本" prop="dataType"/></td>
                        <td style="text-align: center">{{searchForm.dataType}}</td>
                    </tr>
                    <tr>
                        <td><a-form-model-item label="排放"  prop="blowoff"/></td>
                        <td style="text-align: center">{{searchForm.blowoff}}</td>
                        <td><a-form-model-item label="系列"  prop="series"/></td>
                        <td style="text-align: center">{{searchForm.series}}</td>
                        <!--              <td><a-form-model-item label="产品型号[简化]"  prop="productModelJh"/></td>
                                      <td><a-input v-model="searchForm.productModelJh"/></td>-->
                        <td><a-form-model-item label="产品型号"  prop="productModel"/></td>
                        <td style="text-align: center">{{searchForm.productModel}}</td>
                        <td><a-form-model-item label="功率(PS)"  prop="ps"/></td>
                        <td style="text-align: center">{{searchForm.ps}}</td>

                    </tr>
                    <tr>
                        <td><a-form-model-item label="品系"  prop="type"/></td>
                        <td style="text-align: center">{{searchForm.type}}</td>
                        <td><a-form-model-item label="品系2"  prop="type"/></td>
                        <td style="text-align: center">{{searchForm.strain1}}</td>
                        <td><a-form-model-item label="品系3"  prop="type"/></td>
                        <td style="text-align: center">{{searchForm.strain2}}</td>
                        <td><a-form-model-item label="品系4"  prop="type"/></td>
                        <td style="text-align: center">{{searchForm.strain3}}</td>
                    </tr>
                    <tr>
                        <td><a-form-model-item label="基本型开票价"  prop="basicAmount"/></td>
                        <td style="text-align: center">{{searchForm.basicAmount}}</td>
                        <td colspan="2"> <a-checkbox  :checked="checked"   >特</a-checkbox></td>
                        <td><a-form-model-item label="选配件合计" prop="chooseAmount" /></td>
                        <td style="text-align: center">{{searchForm.chooseAmount}}</td>
                        <td><a-form-model-item label="整机开票价" prop="unitAmount" /></td>
                        <td  colspan="3"  style="text-align: center">{{searchForm.unitAmount}}</td>
                    </tr>

                    <tr>
                        <td><a-form-model-item label="选型选装件小计"  prop="optionSubtotal"/></td>
                        <td  colspan="2"  style="text-align: center">{{searchForm.optionSubtotal}}</td>
                        <td colspan="7"> <a-button type="primary" style="margin: 0 8px" @click="handleBasicView"><a-icon type="eye"/>查看基本型清单</a-button></td>
                    </tr>

                    </tbody>
                </table>


                <a-row style="margin: 24px;">
                    <a-col :span="24">
                        <a-form-model-item>
                            <!--                 <a-button  type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>刷新</a-button>-->
<!--                            <a-button type="primary" style="margin: 0 8px" @click="handleSave"><a-icon type="save"/>保存</a-button>-->
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">

                    </a-col>
                    <a-col :span="12">

                    </a-col>
                </a-row>

                <a-card title="选型选装清单">
                    <a-row>
                        <editOptionalSee :masterId="masterId" ref="editOptionalSee"></editOptionalSee>
                    </a-row>
                </a-card>
                <a-card title="选型减配小计">
                    <tr slot="extra" style="float: left">
                        <td   class="tdnumber" colspan="3"  style="text-align: center;width: 100px">{{searchForm.basicSubtotal}}</td>
                    </tr>
                    <a-row>
                        <selectNoOptionalSee :masterId="masterId"  ref="selectNoOptionalSee"></selectNoOptionalSee>
                    </a-row>
                </a-card>

                <table style="width: 100%; border: 1px solid #f0f0f0;" >
                    <tbody class="ant-table">
                    <tr>
                        <td><a-form-model-item label="备注信息" prop="remarks"/></td>
                        <td colspan="9" style="text-align: center">{{searchForm.remarks}}</td>
                    </tr>
                    <tr>
                        <td><a-form-model-item label="合同号" prop="businessAccountingNo"/></td>
                        <td  style="text-align: center">{{searchForm.businessAccountingNo}}</td>
                        <td><a-form-model-item label="创建人" prop="createUserName"/></td>
                        <td  style="text-align: center">{{searchForm.createUserName}}</td>
                        <td><a-form-model-item label="创建时间" prop="createTimeStr"/></td>
                        <td  style="text-align: center">{{searchForm.createTimeStr}}</td>
                        <td><a-form-model-item label="修改人" prop="updateUserName"/></td>
                        <td  style="text-align: center">{{searchForm.updateUserName}}</td>
                        <td><a-form-model-item label="修改时间" prop="updateTimeStr"/></td>
                        <td  style="text-align: center">{{searchForm.updateTimeStr}}</td>
                    </tr>
                    </tbody>
                </table>
            </a-form-model>
        </a-card>


        <a-card>
            <a-table
                    :columns="itemColumns"
                    rowKey="id"
                    bordered
                    :data-source="itemData"
                    :pagination="false"
                    :scroll="{ x: 1000 }"
                    size="small"
                    :customRow="onRowClick"
                    :loading="itemloading">
            </a-table>
        </a-card>

        <a-modal
                :title="'查看基本型清单'"
                :visible="lookBasicDialog"
                :confirm-loading="confirmLoading"
                @ok="handlelookBasicSubmit"
                @cancel="closelookBasicrForm">
            <a-form-model>
                <selectOptionList   :childOption="childOption"   :masterId="masterId"          ref="selectOptionList"></selectOptionList>
            </a-form-model>
        </a-modal>





    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {LIST_XSGSALLOWANCEMAIN_PAGE,
        GET_XSGSALLOWANCEMAIN_ID,
        UPLOAD_XSGSALLOWANCEMAIN,
        UPLOAD_XSGSALLOWANCEITEM,
        LIST_XSGSALLOWANCEITEM_PAGE,
        LIST_XSGSCUSTOMER,
        getTempData

    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import Cookie from 'js-cookie'
    import ARow from "ant-design-vue/es/grid/Row";
    import selectOptionList from "./select_optionList";
    import editOptionalSee from "./edit_optionalSee";
    import selectNoOptionalSee from "./select_noOptionalSee";
    import {b64_md5}  from "@/utils/md5";


    export default {
        name: "IndexSee.vue",
        components: {
            ARow,
            editOptionalSee,
            selectOptionList,
            selectNoOptionalSee,
        },
        // inject:["reload"],
        data() {
            return {
                hasAuth,
                UPLOAD_XSGSALLOWANCEMAIN,
                UPLOAD_XSGSALLOWANCEITEM,
                Cookie,
                masterId:null,
                loading: false,
                itemloading: false,
                isBlurs:false,
                checked:false,
                yearShowOne:false,
                settingDialog: false,
                machineType:null,
                lookBasicDialog: false,
                partOptionDialog: false,
                openFileDialog: false,
                itemDialog: false,
                confirmLoading: false,
                mainType:['本体','后处理'],
                subType:['标准选型','选型'],
                key:"1",
                searchForm: {
                    customer:null,
                    customerName:null,
                    year:null,
                    reference:null,
                    machineType:null,
                    productModel:null,
                    productModelJh:null,
                    blowoff:null,
                    plate:null,
                    series:null,
                    basicAmount:null,
                    isCheck:0,
                    chooseAmount:null,
                    optionSubtotal:null,
                    basicSubtotal:null,
                    unitAmount:null,
                    createTime:null,
                    updateTime:null,
                },
                searchPartForm: {
                    masterId:null,
                },
                noContainForm: {
                    masterId:null,
                },
                //子组件传值
                childOption:[],
                list:[],
                //选型选装清单
                optionList:[],
                //选型减配清单
                noPartsList:[],
                searchSettingForm: {},
                form: {},
                partDataForm:{
                    childProductModel:[],
                },
                searchFormAll:{
                },
                itemForm: {},
                settingForm: {
                    temId:null,
                    masterId:null,
                    year:null,
                    productModelJh:null,
                },

                basicAmount:null,
                //价格主表行数据
                itemPagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.itemPagination.current}/${Math.ceil(
                            total / this.itemPagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onItemPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onItemSizeChange(current, size) // 改变每页数量时更新显示
                },
                data: [],
                customerList: [],

                //价格主表行数据
                itemData: [],
                itemColumns: [
                    // {title: '价格主表ID',width: 165,align: 'center', dataIndex: 'masterId'},
                    {

                        title: '基础数据', align: 'center',
                        children: [
                            {title: '核算单号', dataIndex: 'masterId',width: 165, align: 'center'},
                            {title: '年度', dataIndex: 'year',width: 65, align: 'center'},
                            {title: '合同号', dataIndex: 'contract',width: 65, align: 'center'},
                            {title: '板块', dataIndex: 'plate',width: 65, align: 'center'},
                            {title: '排放', dataIndex: 'blowoff',width: 65, align: 'center'},
                            {title: '系列', dataIndex: 'series',width: 65, align: 'center'},
                            {title: '产品型号', dataIndex: 'productModel',width: 65, align: 'center'},
                            {title: '功率(PS)', dataIndex: 'ps',width: 65, align: 'center'},
                            {title: '状态机', dataIndex: 'platformName',width: 65, align: 'center'},
                            {title: '品系', dataIndex: 'type',width: 65, align: 'center'},

                        ]
                    },
                    {
                        title: '上一年度价格信息', align: 'center',dataIndex:'lastYearTitle',
                        children: [
                            {title: '基本型开票价', dataIndex: 'synBasicTicketPrice',width: 80, align: 'center'},
                            {title: '整机开票价', dataIndex: 'synUnitTicketPrice',width: 80, align: 'center'},
                            {title: '基本型实际价格', dataIndex: 'synBasicNetPrice',width: 90, align: 'center'},
                            {title: '整机实际价格', dataIndex: 'synUnitNetPrice',width: 80, align: 'center'},
                            {title: '选配件合计', dataIndex: 'synOptionAmount',width: 80, align: 'center'},
                            {title: '折让合计', dataIndex: 'synBalanceAmount',width: 80, align: 'center'},

                        ]
                    },
                    {
                        title: '本年度价格信息', align: 'center',dataIndex:'thisYearTitle',
                        children: [
                            {title: '基本型开票价', dataIndex: 'basicTicketPrice',width: 80, align: 'center'},
                            {title: '整机开票价', dataIndex: 'unitTicketPrice',width: 80, align: 'center'},
                            {title: '基本型实际价格', dataIndex: 'basicNetPrice',width: 90, align: 'center'},
                            {title: '整机实际价格', dataIndex: 'unitNetPrice',width: 80, align: 'center'},
                            /*            {title: '选配件合计', dataIndex: 'optionAmount',width: 165, align: 'center'},
                                          {title: '折让合计', dataIndex: 'balanceAmount',width: 165, align: 'center'},*/
                        ]
                    },
                    {
                        title: '总部预算信息', align: 'center',
                        children: [
                            {title: '基本型预算', dataIndex: 'zbBasicBudgetBalance',width: 80, align: 'center'},
                            {title: '选配件预算', dataIndex: 'zbChooseBudget',width: 80, align: 'center'},
                            {title: '整机预算', dataIndex: 'zbBudgetActualPrice',width: 80, align: 'center'},
                            {title: '预算空间', dataIndex: 'zbBudgetSpace',width: 80, align: 'center'},
                            {title: '基本型预算结余', dataIndex: 'zbBasicBalance',width: 80, align: 'center'},
                            {title: '整机预算结余', dataIndex: 'zbUnitBalance',width: 80, align: 'center'},

                        ]


                    },
                    {
                        title: '系统部预算信息', align: 'center',
                        children: [
                            {title: '基本型预算', dataIndex: 'sysBasicBudgetBalance',width: 80, align: 'center'},
                            {title: '选配件预算', dataIndex: 'sysChooseBudget',width: 80, align: 'center'},
                            {title: '整机预算', dataIndex: 'sysBudgetActualPrice',width: 80, align: 'center'},
                            {title: '预算空间', dataIndex: 'sysBudgetSpace',width: 80, align: 'center'},
                            {title: '基本型预算结余', dataIndex: 'sysBasicBalance',width: 90, align: 'center'},
                            {title: '整机预算结余', dataIndex: 'sysUnitBalance',width: 80, align: 'center'},
                        ]
                    },
                    {
                        title: '边贡信息', align: 'center',
                        children: [
                            {title: '材料成本', dataIndex: 'costOfMeterial',width: 80, align: 'center'},
                            {title: '边际贡献', dataIndex: 'contribution',width: 80, align: 'center'},
                            {title: '边际贡献率(%)', dataIndex: 'contributionPercent',width: 80, align: 'center'},
                        ]
                    },
                    {title: '阶梯折让',  align: 'center', dataIndex: 'jtzrId',
                        children: [
                        ]
                    },
                    {title: '非阶梯折让',  align: 'center', dataIndex: 'zrId',
                        children: []
                    },
                    {title: '折让总额',  align: 'center', dataIndex: 'balanceAmount',width: 80},
                    {title: '选配件合计',  align: 'center', dataIndex: 'optionAmount',width: 80},
                    {title: '选型件', dataIndex: 'basicList' , align: 'center',
                        children: []
                    },
                    {title: '选装件', dataIndex: 'optionListColumns' , align: 'center',
                        children: []
                    },
                ],


                settingColumns: [
                    {title: '产品型号', dataIndex: 'productModelJh'},
                    {title: '选配件名称', dataIndex: 'partsName'},
                    {title: '选配件图号', dataIndex: 'partsNo'},
                    {title: '规格', dataIndex: 'specs'},
                    {title: '供应商', dataIndex: 'supplierBak'},
                    {title: '本体/后处理', dataIndex: 'mainType'},
                    {title: '类型', dataIndex: 'subType'},
                    {title: '指导售价', dataIndex: 'budgetedPrice'},
                    {title: '实际售价', dataIndex: 'actualPrice'},
                    // {title: '类型(后台应用)', dataIndex: 'type'},
                    {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
                ],


                searchRules: {
                    customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
                    customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
                    year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
                    reference: [{required: false, message: '参考机型不能为空', trigger: 'blur'}],
                    machineType: [{required: true, message: '新增状态机型不能为空', trigger: 'blur'}],
                    productModel: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
                    productModelJh: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
                    blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
                    plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
                    series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
                    type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
                    ps: [{required: false, message: '功率(PS)不能为空', trigger: 'blur'}],
                    basicAmount: [{required: true, message: '基本型开票价不能为空', trigger: 'blur'}],
                    chooseAmount: [{required: true, message: '选配件合计不能为空', trigger: 'blur'}],
                    unitAmount: [{required: true, message: '整机开票价不能为空', trigger: 'blur'}],
                    optionSubtotal: [{required: false, message: '选型选装件小计不能为空', trigger: 'blur'}],
                    basicSubtotal: [{required: false, message: '基本型减配小计不能为空', trigger: 'blur'}],
                },

                itemRules: {
                    masterId: [{required: false, message: '价格主表ID不能为空', trigger: 'blur'}],
                    code: [{required: false, message: '核算单号不能为空', trigger: 'blur'}],
                    year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
                    contract: [{required: false, message: '合同号不能为空', trigger: 'blur'}],
                    platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
                    yearExpectVolume: [{required: false, message: '年度销售预计不能为空', trigger: 'blur'}],
                    yearSalesVolume: [{required: false, message: '年度实际销售不能为空', trigger: 'blur'}],
                    basicTicketPrice: [{required: false, message: '基本型开票价不能为空', trigger: 'blur'}],
                    unitTicketPrice: [{required: false, message: '整机开票价不能为空', trigger: 'blur'}],
                    basicNetPrice: [{required: false, message: '基本型净价不能为空', trigger: 'blur'}],
                    unitNetPrice: [{required: false, message: '整机净价不能为空', trigger: 'blur'}],
                    basicBudgetPrice: [{required: false, message: '基本型预算不能为空', trigger: 'blur'}],
                    zbChooseBudget: [{required: false, message: '总部-选配件预算不能为空', trigger: 'blur'}],
                    zbBudgetActualPrice: [{required: false, message: '总部-整机预算净价不能为空', trigger: 'blur'}],
                    zbBudgetSpace: [{required: false, message: '总部-预算空间不能为空', trigger: 'blur'}],
                    zbBasicBalance: [{required: false, message: '总部-基本型预算结余不能为空', trigger: 'blur'}],
                    zbUnitBalance: [{required: false, message: '总部-整机预算结余不能为空', trigger: 'blur'}],
                    zbBasicBudgetBalance: [{required: false, message: '总部-基本型预算不能为空', trigger: 'blur'}],
                    sysChooseBudget: [{required: false, message: '系统-选配件预算不能为空', trigger: 'blur'}],
                    sysBudgetActualPrice: [{required: false, message: '系统-整机预算净价不能为空', trigger: 'blur'}],
                    sysBudgetSapce: [{required: false, message: '系统-预算空间不能为空', trigger: 'blur'}],
                    sysBasicBalance: [{required: false, message: '系统-基本型预算结余不能为空', trigger: 'blur'}],
                    sysUnitBalance: [{required: false, message: '系统-整机预算结余不能为空', trigger: 'blur'}],
                    costOfMeterial: [{required: false, message: '材料成本不能为空', trigger: 'blur'}],
                    contribution: [{required: false, message: '边际贡献不能为空', trigger: 'blur'}],
                    contributionPercent: [{required: false, message: '边际贡献率不能为空', trigger: 'blur'}],
                    sequenceNumber: [{required: false, message: '序号不能为空', trigger: 'blur'}],
                    settlementPrice: [{required: false, message: '结算价不能为空', trigger: 'blur'}],
                    balanceAmount: [{required: false, message: '折让金额不能为空', trigger: 'blur'}],
                    createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
                    createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
                    updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
                    updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
                },
                childOptionalData:[],
                textarea:''

            }
        },

        mounted() {
            this.loading = false;
            if(":id"===this.$route.params.id){
                this.masterId =null
            }else{
                this.masterId =this.$route.params.id
                this.searchForm.id =this.$route.params.id;
            }
            this.getMainData(this.$route.params.id);
            this.checkName();
        },
        watch: {
          "searchForm.dataType": {
            deep: true,
            handler(val) {
              if(val == undefined || val == null){
                this.searchForm.dataType = '旧版'
              }
            }
          }
        },
        methods: {

            yearchanged(){
                this.itemColumns.forEach(item =>{
                    if(item.dataIndex == 'lastYearTitle'){
                        item.children.forEach(c =>{
                            if(c.dataIndex == 'synBasicTicketPrice'){
                                c.title = (this.searchForm.year-1)+"基本型开票价"
                            }else if(c.dataIndex == 'synUnitTicketPrice'){
                                c.title = (this.searchForm.year-1)+"整机开票价"
                            }else if(c.dataIndex == 'synBasicNetPrice'){
                                c.title = (this.searchForm.year-1)+"基本型实际价格"
                            }else if(c.dataIndex == 'synUnitNetPrice'){
                                c.title = (this.searchForm.year-1)+"整机实际价格"
                            }else if(c.dataIndex == 'synOptionAmount'){
                                c.title = (this.searchForm.year-1)+"选配件合计"
                            }else if(c.dataIndex == 'synBalanceAmount'){
                                c.title = (this.searchForm.year-1)+"折让合计"
                            }
                        })

                    }else if(item.dataIndex == 'thisYearTitle'){
                        item.children.forEach(c =>{
                            if(c.dataIndex == 'basicTicketPrice'){
                                c.title = (this.searchForm.year)+"基本型开票价";
                            }else if(c.dataIndex == 'unitTicketPrice'){
                                c.title = (this.searchForm.year)+"整机开票价"
                            }else if(c.dataIndex == 'basicNetPrice'){
                                c.title = (this.searchForm.year)+"基本型实际价格"
                            }else if(c.dataIndex == 'unitNetPrice'){
                                c.title = (this.searchForm.year)+"整机实际价格"
                            }
                        })
                    }
                })
            },

            getMainData:function(id){
                request(GET_XSGSALLOWANCEMAIN_ID, METHOD.GET, {id:id})
                    .then(res => {
                        if(res.data.data!=null){
                            this.searchPartForm.masterId =res.data.data.id;
                            this.noContainForm.masterId =res.data.data.id;
                            this.searchForm =res.data.data
                            if(res.data.data.isCheck===0){
                                this.checked =false
                            }else{
                                this.checked =true
                            }
                            //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                            this.$nextTick(()=>{
                                this.$refs.selectNoOptionalSee.getNoOptionData();
                                this.$refs.editOptionalSee.getOptionalcData();
                            })
                            this.yearchanged()
                            this.getItemData();
                            this.refreshItem()
                        }

                    })
            },
            getData: function () {
                this.loading = true
                let {current, size} = this.pagination
                request(LIST_XSGSALLOWANCEMAIN_PAGE, METHOD.GET, {...this.searchForm, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        this.loading = false
                        this.data = records
                        this.pagination.total = parseInt(total)
                    })
            },
            setItemDatas(records){
                let jts =[]
                let no_jts =[]
                records.forEach(item =>{
                    item.setDetails.forEach(key =>{
                        if('阶梯金额' != key.allowanceType){
                            if(!this.isExtis(no_jts,'ni_'+key.seqNum+'_seqNumber')){
                                no_jts.push({title: key.seqName, dataIndex: 'ni_'+key.seqNum+'_seqNumber',width: 80, align: 'center'})
                                no_jts.push({title: "类型", dataIndex: 'ni_'+key.seqNum+'_allowanceType',width: 80, align: 'center'})
                                no_jts.push({title: "备注", dataIndex: 'ni_'+key.seqNum+'_remarks',width: 80, align: 'center'})
                            }
                            item['ni_'+key.seqNum+'_seqNumber'] = key.allowanceAmount
                            let remarks =key.remarks
                            if(remarks == null){
                                remarks = ""
                            }

                            if(key.defaultRemarks){
                                item['ni_'+key.seqNum+'_remarks'] =key.defaultRemarks+ remarks
                            }else{
                                item['ni_'+key.seqNum+'_remarks'] = remarks
                            }


                            item['ni_'+key.seqNum+'_allowanceType'] = key.allowanceType
                        }else{
                            if(key.allowanceType =='阶梯金额'){
                                if(key.jtList!=null){
                                    key.jtList.forEach(i =>{
                                        if(!this.isExtis(jts,'i_'+i.seqNumber+'_seqNumber')){
                                            jts.push({title: i.seqName, dataIndex: 'i_'+i.seqNumber+'_seqNumber',width: 80, align: 'center'})
                                        }
                                        item['i_'+i.seqNumber+'_seqNumber'] = i.price
                                        // item['i_'+i.seqNumber+'_price'] = i.price
                                    })
                                    let remarks =key.remarks
                                    if(remarks == null){
                                        remarks = ""
                                    }
                                    if(key.defaultRemarks){
                                        item['jtzr_remarks'] = key.defaultRemarks+remarks
                                    }else{
                                        item['jtzr_remarks'] = remarks
                                    }
                                }


                            }
                        }


                    })

                })

                let xxColumns =[]
                let xzColumns =[]

                records.forEach(item =>{

                    this.optionalChanged(xxColumns,xzColumns,item.allowanceParts)

                    item.allowanceParts.forEach(key =>{
                        // 要区别选型 选装
                        if(key.subType==='选型件'){
                            if(key.isSubtraction==='0'){
                                if(key.valuePrice===0){
                                    if(key.specs===key.standardSpecs){
                                        // item[ b64_md5(b64_md5(key.id))] = key.valuePrice
                                        item[ b64_md5(b64_md5(key.id))] = null
                                    }else{
                                        item[ b64_md5(b64_md5(key.id))] = key.specs
                                    }

                                }else{
                                    item[ b64_md5(b64_md5(key.id))] = key.valuePrice
                                }

                            }else if(key.isSubtraction==='1'){
                                // item[ b64_md5(b64_md5(key.partsName))] = key.actualPrice
                                if(key.actualPrice===0){
                                    item[ b64_md5(b64_md5(key.id))] = '不带'
                                }else{
                                    item[ b64_md5(b64_md5(key.id))] = key.actualPrice
                                }

                            }
                        }else if(key.subType==='选装件'){
                            item[ b64_md5(b64_md5(key.id))] = key.actualPrice
                        }


                    })

                })

                if(no_jts.length ==0){
                    no_jts.push({title: '', dataIndex: "nijtzr_remarks",width: 80, align: 'center'})
                }
                jts.push({title: '备注', dataIndex: "jtzr_remarks",width: 80, align: 'center'})

                if(xxColumns.length == 0){
                    xxColumns.push({title: '', dataIndex: "xxColumns",width: 80, align: 'center'})
                }
                if(xzColumns.length == 0){
                    xzColumns.push({title: '', dataIndex: "xzColumns",width: 80, align: 'center'})
                }

                this.itemColumns.forEach(item =>{
                    if(item.dataIndex == 'basicList'){
                        item.children = xxColumns
                    }else if(item.dataIndex == 'optionListColumns'){
                        item.children = xzColumns
                    }else if(item.dataIndex == 'jtzrId'){
                        item.children = jts
                    }else if(item.dataIndex == 'zrId'){
                        item.children = no_jts
                    }


                })
            },

            getItemData: function () {
                this.itemloading = true
                let {current, size} = this.itemPagination
                request(LIST_XSGSALLOWANCEITEM_PAGE, METHOD.GET, {...this.searchPartForm, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        this.itemloading = false
                        this.itemPagination.total = parseInt(total)
                        this.setItemDatas(records)
                        this.itemData = records
                    })
            },

            isExtis(list,key){
                let flag = false;
                list.forEach(item =>{
                    if(item.dataIndex == key){
                        flag = true;
                    }
                })
                return flag

            },

            /** 打开基本型配置清单 */
            handleBasicView() {
              this.lookBasicDialog = true;
              if (this.searchForm.plate != null && this.searchForm.series != null) {
                this.childOption[0] = this.searchForm.plate
                this.childOption[1] = this.searchForm.series
                this.childOption[2] = this.searchForm.customer
                this.childOption[3] = this.searchForm.productModelJh
                this.childOption[4] = this.searchForm.dataType == '新版' ? "新版" : null
                //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                this.$nextTick(() => {
                  this.$refs.selectOptionList.getLinshiBasicData();
                })
              }
            },

            //关闭基本型清单对话框
            handlelookBasicSubmit(){
                this.lookBasicDialog =false;
            },
            //关闭基本型清单对话框
            closelookBasicrForm(){
                this.lookBasicDialog =false;
            },
            //刷新
            // resetQuery(){
            //    this.reload();
            // },
            optionalChanged(xxColumns,xzColumns,optionalData){
                optionalData.forEach(item =>{
                    if(item.subType =='选型件'){
                        if(!this.isDoubleColumns(xxColumns,item.partsName)){
                            xxColumns.push(   {title:item.partsName, dataIndex: b64_md5(b64_md5(item.id)),width: 165, align: 'center'})
                        }
                    }else if(item.subType =='选装件'){
                        if(!this.isDoubleColumns(xzColumns,item.partsName)){
                            xzColumns.push(   {title:item.partsName, dataIndex: b64_md5(b64_md5(item.id)),width: 165, align: 'center'})
                        }
                    }

                })



            },

            isDoubleColumns(columns,name){
                let flag = false;
                columns.forEach(item =>{
                    if(item.dataIndex == b64_md5(b64_md5(name))){
                        flag = true;
                    }
                })
                return flag
            },

            refreshItem(){

                request(getTempData, METHOD.POST,{... this.searchForm}).then(res => {
                    let data = res.data.data
                    this.$nextTick(()=>{
                        let parts =[]
                        let option = this.$refs.editOptionalSee.getOptionalcDataList()
                        option.forEach(item =>{
                            parts.push(item)
                        })
                        let notoption = this.$refs.selectNoOptionalSee.getNoOptionalcDataList()
                        notoption.forEach(item =>{
                            parts.push(item)
                        })
                        data[0].allowanceParts = parts
                        this.setItemDatas(data)
                        this.itemData = data
                    })



                })
            }
        }
    }
</script>

<style scoped>
    .ant-input-number{
        width: 100%;
    }
    td{
        border: 1px solid #f0f0f0;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }
    /*/deep/  样式穿透
    */
    /deep/.ant-col-8{
        display: block;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 100%;
    }
    /deep/.ant-card-head-title {
        flex: auto;
        width: 120px;
        overflow:inherit;
    }
    /deep/.ant-form-item {
        margin: 0;
    }
    /deep/.ant-card-extra {
        margin-right: 75%;
        padding: 0px 0;
    }
    .tdnumber>/deep/.ant-input-number{
        width: 180px;
    }
    /deep/.ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }
    .ant-divider-horizontal {
        height: 0.5px;
        margin: 0px 0;
    }
    .ant-divider {
        background: #000;
    }


</style>
