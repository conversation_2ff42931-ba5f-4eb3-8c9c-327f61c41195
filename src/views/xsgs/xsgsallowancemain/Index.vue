<template>
  <div>
    <a-card>
      <div class="header-button-area" :style="`width:${headerWidth};`">
        <a-button :disabled="isSaving" :loading="loading" type="primary" @click="handleSave">
          <a-icon type="save"/>
          保存
        </a-button>
      </div>
      <a-spin :spinning="isSaving">
        <a-form-model style="margin-top: 30px" ref="searchForm" :model="searchForm" :rules="searchRules"
                      :labelCol="{span: 8}" :wrapperCol="{span: 16}">

          <table style="width: 100%; border: 1px solid #f0f0f0;">
            <tbody class="ant-table">
            <tr>
              <td>
                <a-form-model-item label="年份" prop="year"/>
              </td>
              <td width="120px">
                <a-input-number :min="0" step="1" style="width: 100%" v-model="searchForm.year" @blur="yearchanged"/>
              </td>
              <td style="width: 120px">
                <a-form-model-item label="板块" prop="plate"/>
              </td>
              <td width="120px">
                <a-select style="width: 100%" v-model="searchForm.plate" @blur="platechanged">
                  <a-select-option value="卡车">卡车</a-select-option>
                  <a-select-option value="客车">客车</a-select-option>
                  <a-select-option value="新能源">新能源</a-select-option>
                </a-select>
              </td>
              <td>
                <a-form-model-item label="客户" prop="customer"/>
              </td>
              <td width="120px">
                <a-input v-model="searchForm.customer" @blur="customerchanged"/>
              </td>
              <td>
                <a-form-model-item label="客户名称" prop="customerName"/>
              </td>
              <td colspan="3">
                <a-select
                    v-model="searchForm.customerName"
                    show-search
                    placeholder=""
                    style="width: 280px"
                    :default-active-first-option="false"
                    :show-arrow="false"
                    :filter-option="false"
                    :not-found-content="null"
                    @search="handleCustomerSearch"
                    @change="handleCustomerChange"
                    @blur="customerNamechanged"
                >
                  <a-select-option v-for="d in customerList" :key="d.name">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </td>
            </tr>

            <tr>
              <td colspan="10" style="text-align: center">
                <strong>基本信息</strong>
              </td>
            </tr>


            <tr>
              <td>
                <a-form-model-item label="参考状态机" prop="reference"/>
              </td>
              <td>
                <a-input v-model.trim="searchForm.reference" @blur="blurs"/>
              </td>
              <td>
                <a-form-model-item label="整机开票价" prop="unitAmountRefer"/>
              </td>
              <td>
                <a-input-number v-model="searchForm.unitAmountRefer"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                />
              </td>
              <td>
                <a-form-model-item label="基本型开票价" prop="basicAmountRefer"/>
              </td>
              <td>
                <a-input-number v-model="searchForm.basicAmountRefer"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                />
              </td>
              <td>
                <a-form-model-item label="产品型号" prop="productModelRefer"/>
              </td>
              <td>
                <a-input style="width: 120px" v-model="searchForm.productModelRefer"/>
              </td>
            </tr>

            <tr>
              <td colspan="10">
                <a-divider></a-divider>
              </td>
            </tr>

            <tr>
              <td>
                <a-form-model-item label="新增状态机" prop="machineType"/>
              </td>
              <td>
                <a-input v-model.trim="searchForm.machineType" @blur="blursMachineType"/>
              </td>
              <td>
                <a-form-model-item label="基本型指导价" prop="basicGuidanceAmount"/>
              </td>
              <td>
                <a-input-number v-model="searchForm.basicGuidanceAmount" disabled
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                />

              </td>
              <td>
                <a-form-model-item label="客户物料" prop="materialNo"/>
              </td>
              <td>
                <a-input v-model.trim="searchForm.materialNo"/>
              </td>
              <td>
                <a-checkbox style="margin-left: 5px;color: rgba(0, 0, 0, 0.85);" :checked="artificialVer"
                            @change="onAtificialVerChange">人工定版
                </a-checkbox>
              </td>
              <td></td>
              <td>
                <a-form-model-item label="数据版本" prop="dataType"/>
              </td>
              <td style="width: 120px">
                <a-select style="width: 100%" v-model="searchForm.dataType" @change="dataTypeChanged">
                  <a-select-option value="旧版" >旧版</a-select-option>
                  <a-select-option value="新版" >新版</a-select-option>
                </a-select>
              </td>
            </tr>

            <tr>
              <td>
                <a-form-model-item label="排放" prop="blowoff"/>
              </td>
              <td>
                <a-input v-model="searchForm.blowoff" @blur="blowoffchanged"/>
              </td>
              <td>
                <a-form-model-item label="系列" prop="series"/>
              </td>
              <td>
                <a-input v-model="searchForm.series" @blur="serieschanged"/>
              </td>
              <td>
                <a-form-model-item label="产品型号" prop="productModel"/>
                <a-input v-show="false" v-model="searchForm.productModelJh"/>
              </td>
              <td>
                <a-input v-model="searchForm.productModel" @blur="productModelchanged"/>
              </td>
              <td>
                <a-form-model-item label="功率(PS)" prop="ps"/>
              </td>
              <td>
                <a-input style="width: 120px" v-model="searchForm.ps"/>
              </td>
            </tr>
            <tr>
              <td>
                <!-- <a-form-model-item label="品系1" prop="type"/> -->
                <a-form-model-item label="BU业务" prop="businessUnit"/>
              </td>
              <td>
                <a-input v-model="searchForm.businessUnit"/>  
              </td>
              <td>
                <!-- <a-form-model-item label="品系2" prop="type"/> -->
                <a-form-model-item label="品系" prop="productLine "/>
              </td>
              <td>
                <a-input v-model="searchForm.productLine "/>
              </td>
              <td>
                <!-- <a-form-model-item label="品系3" prop="type"/> -->
                <a-form-model-item label="新场景1" prop="newScenario1"/>
              </td>
              <td>
                <a-input v-model="searchForm.newScenario1"/>
              </td>
              <td>
                <!-- <a-form-model-item label="品系4" prop="type"/> -->
                <a-form-model-item label="新场景2" prop="newScenario2"/>
              </td>
              <td>
                <a-input style="width: 120px" v-model="searchForm.newScenario2"/>
              </td>
              <td>
                <!-- <a-form-model-item label="品系4" prop="type"/> -->
                <a-form-model-item label="新场景3" prop="newScenario3"/>
              </td>
              <td>
                <a-input style="width: 120px" v-model="searchForm.newScenario3"/>
              </td>
            </tr>
            <tr>
              <td>
                <a-form-model-item label="基本型开票价" prop="basicAmount"/>
              </td>
              <td>
                <a-input-number v-model="searchForm.basicAmount"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                @blur="basicAmountBlurs"
                />
              </td>
              <td>
                <a-checkbox :checked="checked" @change="onCheckChange">特</a-checkbox>
                <span style="float: right;">功率差：</span>
              </td>
              <td>
                <a-input-number v-model="searchForm.powerDiff"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                @blur="powerDiffBlurs"
                />
              </td>
              <td>
                <a-form-model-item label="选配件合计" prop="chooseAmount"/>
              </td>
              <td>
                <a-input-number v-model="searchForm.chooseAmount"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                @blur="chooseAmountBlurs"
                />
              </td>
              <td>
                <a-form-model-item label="整机开票价" prop="unitAmount"/>
              </td>
              <td colspan="3">
                <a-input-number v-model="searchForm.unitAmount"
                                :step="0.01"
                                style="width: 120px"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                @blur="unitAmountBlurs"
                />
              </td>
            </tr>

            <tr>
              <td>
                <a-form-model-item label="选型选装件小计" prop="optionSubtotal"/>
              </td>
              <td colspan="2">
                <a-input-number v-model="searchForm.optionSubtotal"
                                @blur="optionSubtotalBlurs"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
              </td>
              <td colspan="7">
                <a-button type="primary" style="margin: 0 8px" @click="handleBasicView">
                  <a-icon type="eye"/>
                  查看基本型清单
                </a-button>
              </td>
            </tr>
            </tbody>
          </table>
          <a-card title="选型选装清单">
            <a-row>
              <editOptional :masterId="masterId" :basicSubtotal="searchForm.basicSubtotal" :searchForm="searchForm"
                            :year="searchForm.year" :dataType="searchForm.dataType" @searchUpdate="getSearchUpdate"
                            @chooseAmountToal="getChooseAmoutToal"
                            @lectotypeSubtotal="getlectotypeSubtotal"
                            @optionTotal='getOptionTotal'
                            @deleteDoubleXx='deleteDoubleXx'
                            @noPartsListNew="setNoPartsListNew"
                            @getNoParts="getNoParts"
                            :optionList="optionList"
                            :productModelJh="searchForm.productModelJh" ref="editOptional"></editOptional>
            </a-row>
          </a-card>
          <a-card title="选型减配小计">
            <tr slot="extra" style="float: left">
              <td colspan="3" class="tdnumber">
                <a-input-number v-model="searchForm.basicSubtotal" disabled
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
              </td>
            </tr>
            <a-row>
              <selectNoOptional :masterId="masterId" :searchForm="searchForm"
                                :optionSubtotal="searchForm.optionSubtotal"
                                :year="searchForm.year"
                                @searchNoOptionUpdate="getSearchNoOptionUpdate"
                                :productModelJh="searchForm.productModelJh"
                                @chooseBasicAmountToal="getchooseBasicAmountToal"
                                @lectotypeSubtotal="getlectotypeSubtotal"
                                ref="selectNoOptional"></selectNoOptional>
            </a-row>
          </a-card>

          <table style="border: 1px solid #f0f0f0;">
            <tbody class="ant-table-tbody">
            <tr>
              <td>
                <a-form-model-item label="备注信息" prop="remarks"/>
              </td>
              <td colspan="9">
                <a-input type="textarea" :autoSize="{minRows: 3 }" aria-placeholder="请输入备注信息"
                         v-model="searchForm.remarks"/>
              </td>
            </tr>
            <tr>
              <td>
                <a-form-model-item label="商务核算编号" prop="id"/>
              </td>
              <td>
                <a-input v-model="searchForm.id"/>
              </td>
              <td>
                <a-form-model-item label="创建人" prop="createUserName"/>
              </td>
              <td>
                <a-input v-model="searchForm.createUserName" readOnly/>
              </td>
              <td>
                <a-form-model-item label="创建时间" prop="createTimeStr"/>
              </td>
              <td>
                <a-input v-model="searchForm.createTimeStr" readOnly/>
              </td>
              <td>
                <a-form-model-item label="修改人" prop="updateUserName"/>
              </td>
              <td>
                <a-input v-model="searchForm.updateUserName" readOnly/>
              </td>
              <td>
                <a-form-model-item label="修改时间" prop="updateTimeStr"/>
              </td>
              <td>
                <a-input v-model="searchForm.updateTimeStr" readOnly/>
              </td>
            </tr>
            </tbody>
          </table>
        </a-form-model>
      </a-spin>
    </a-card>

    <a-card v-if="false">
      <a-table
          :columns="itemColumns"
          rowKey="id"
          bordered
          :data-source="itemData"
          :pagination="false"
          :scroll="{ x: 1000 }"
          size="small"
          :customRow="onRowClick"
          :loading="itemloading">
        <template slot="ps" slot-scope="record">
          {{ record ? record.split('.')[0] : "" }}
        </template>
      </a-table>

    </a-card>

    <a-modal
        :title="'查看基本型清单'"
        :visible="lookBasicDialog"
        :confirm-loading="confirmLoading"
        @ok="handlelookBasicSubmit"
        @cancel="closelookBasicrForm"
        width="40%"
        >
      <a-form-model>
        <selectOptionList :childOption="childOption" :masterId="masterId" ref="selectOptionList"></selectOptionList>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSALLOWANCEMAIN_PAGE,
  GET_XSGSALLOWANCEMAIN_EDITID,
  SUBMIT_XSGSALLOWANCEMAINSAVE,
  SElECTLIST_XSGSALLOWANCEMAIN,
  DOWNLOAD_XSGSALLOWANCEMAIN,
  UPLOAD_XSGSALLOWANCEMAIN,
  SELECTOPTIONLIST_XSGSALLOWANCEMAIN,
  SELECTOPTIONLISTNEWVERSION_XSGSALLOWANCEMAIN,
  UPLOAD_XSGSALLOWANCEITEM,
  LIST_XSGSBASICPRICE,
  LIST_COLUMNS,
  ztjxx_action,
  LIST_XSGSCUSTOMER,
  getTempData,
  CHECK_MACHINE_TYPE,
  GET_BY_CUSTOMER_MACHINETYPE, LIST_XSGSBASIPRICENEWVERSION
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
import ARow from "ant-design-vue/es/grid/Row";
import selectOptionList from "./select_optionList";
import editOptional from "./edit_optional";
import selectNoOptional from "./select_noOptional";
import {b64_md5} from "@/utils/md5";


export default {
  name: "xsgsallowancemain.vue",
  components: {
    ARow,
    editOptional,
    selectOptionList,
    selectNoOptional,
  },
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSALLOWANCEMAIN,
      UPLOAD_XSGSALLOWANCEITEM,
      orgBackground: "",
      Cookie,
      isSaving: false,
      masterId: null,
      loading: false,
      headerWidth: null,
      itemloading: false,
      isBlurs: false,
      checked: false,
      artificialVer: false,
      yearShowOne: false,
      settingDialog: false,
      machineType: null,
      lookBasicDialog: false,
      partOptionDialog: false,
      openFileDialog: false,
      itemDialog: false,
      confirmLoading: false,
      mainType: ['本体', '后处理'],
      subType: ['标准选型', '选型'],
      key: "1",
      id: null,
      fullPath: null,
      parentPath: null,
      option: null,
      searchForm: {
        option: null,
        customer: null,
        customerName: null,
        year: null,
        reference: null,
        machineType: null,
        productModel: null,
        productModelJh: null,
        blowoff: null,
        plate: null,
        series: null,
        basicAmount: null,
        isCheck: 0,
        chooseAmount: null,
        optionSubtotal: null,
        basicSubtotal: null,
        unitAmount: null,
        createTime: null,
        updateTime: null,
        parentPath: null,
        unitAmountRefer: null,
        basicAmountRefer: null,
        productModelRefer: null,
        basicGuidanceAmount: null,
        isArtificialVer: null,
        strain1: null,
        strain2: null,
        strain3: null,
        materialNo: null,
        productLine:null,
        businessUnit:null,
        newScenario1:null,
        newScenario2:null,
        newScenario3:null,
        dataType: '新版', // 默认使用新版数据


      },
      searchPartForm: {
        masterId: null,
      },
      noContainForm: {
        masterId: null,
      },
      //子组件传值
      childOption: [],
      list: [],
      //选型选装清单
      optionList: [],
      //选型减配清单
      noPartsList: [],
      optionXXList: [],
      searchSettingForm: {},
      form: {},
      partDataForm: {
        childProductModel: [],
      },
      searchFormAll: {},
      itemForm: {},
      settingForm: {
        temId: null,
        masterId: null,
        year: null,
        productModelJh: null,
      },

      basicAmount: null,
      //价格主表行数据
      itemPagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.itemPagination.current}/${Math.ceil(
                total / this.itemPagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onItemPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onItemSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      customerList: [],

      //价格主表行数据
      itemData: [],
      itemColumns: [
        {

          title: '基础数据', align: 'center',
          children: [
            // {title: '核算单号', dataIndex: 'masterId',width: 165, align: 'center'},
            {title: '年度', dataIndex: 'year', width: 65, align: 'center'},
            {title: '合同号', dataIndex: 'contract', width: 65, align: 'center'},
            {title: '板块', dataIndex: 'plate', width: 65, align: 'center'},
            {title: '排放', dataIndex: 'blowoff', width: 65, align: 'center'},
            {title: '系列', dataIndex: 'series', width: 65, align: 'center'},
            {title: '产品型号', dataIndex: 'productModel', width: 65, align: 'center'},
            {title: '功率(PS)', dataIndex: 'ps', width: 65, align: 'center', scopedSlots: {customRender: 'ps'}},
            {title: '状态机', dataIndex: 'platformName', width: 65, align: 'center'},
            {title: '品系', dataIndex: 'type', width: 65, align: 'center'},
            {title: '数据版本', dataIndex: 'dataType', width: 65, align: 'center'},
          ]
        },
        {
          title: '上一年度价格信息', align: 'center', dataIndex: 'lastYearTitle',
          children: [
            {title: '基本型开票价', dataIndex: 'synBasicTicketPrice', width: 80, align: 'center'},
            {title: '整机开票价', dataIndex: 'synUnitTicketPrice', width: 80, align: 'center'},
            {title: '基本型实际价格', dataIndex: 'synBasicNetPrice', width: 90, align: 'center'},
            {title: '整机实际价格', dataIndex: 'synUnitNetPrice', width: 80, align: 'center'},
            {title: '选配件合计', dataIndex: 'synOptionAmount', width: 80, align: 'center'},
            {title: '折让合计', dataIndex: 'synBalanceAmount', width: 80, align: 'center'},

          ]
        },
        {
          title: '本年度价格信息', align: 'center', dataIndex: 'thisYearTitle',
          children: [
            {title: '基本型开票价', dataIndex: 'basicTicketPrice', width: 80, align: 'center'},
            {title: '整机开票价', dataIndex: 'unitTicketPrice', width: 80, align: 'center'},
            {title: '基本型实际价格', dataIndex: 'basicNetPrice', width: 90, align: 'center'},
            {title: '整机实际价格', dataIndex: 'unitNetPrice', width: 80, align: 'center'},
          ]
        },
        {
          title: '总部预算信息', align: 'center',
          children: [
            {title: '基本型预算', dataIndex: 'zbBasicBudgetBalance', width: 80, align: 'center'},
            {title: '选配件预算', dataIndex: 'zbChooseBudget', width: 80, align: 'center'},
            {title: '整机预算', dataIndex: 'zbBudgetActualPrice', width: 80, align: 'center'},
            {title: '预算空间', dataIndex: 'zbBudgetSpace', width: 80, align: 'center'},
            {title: '基本型预算结余', dataIndex: 'zbBasicBalance', width: 80, align: 'center'},
            {title: '整机预算结余', dataIndex: 'zbUnitBalance', width: 80, align: 'center'},

          ]


        },
        {
          title: '系统部预算信息', align: 'center',
          children: [
            {title: '基本型预算', dataIndex: 'sysBasicBudgetBalance', width: 80, align: 'center'},
            {title: '选配件预算', dataIndex: 'sysChooseBudget', width: 80, align: 'center'},
            {title: '整机预算', dataIndex: 'sysBudgetActualPrice', width: 80, align: 'center'},
            {title: '预算空间', dataIndex: 'sysBudgetSpace', width: 80, align: 'center'},
            {title: '基本型预算结余', dataIndex: 'sysBasicBalance', width: 90, align: 'center'},
            {title: '整机预算结余', dataIndex: 'sysUnitBalance', width: 80, align: 'center'},
          ]
        },
        {
          title: '边贡信息', align: 'center',
          children: [
            {title: '材料成本', dataIndex: 'costOfMeterial', width: 80, align: 'center'},
            {title: '边际贡献', dataIndex: 'contribution', width: 80, align: 'center'},
            {title: '边际贡献率(%)', dataIndex: 'contributionPercent', width: 80, align: 'center'},
          ]
        },
        {
          title: '阶梯折让', align: 'center', dataIndex: 'jtzrId',
          children: []
        },
        {
          title: '非阶梯折让', align: 'center', dataIndex: 'zrId',
          children: []
        },
        {title: '折让总额', align: 'center', dataIndex: 'balanceAmount', width: 80},
        {title: '选配件合计', align: 'center', dataIndex: 'optionAmount', width: 80},
        {
          title: '选型件', dataIndex: 'basicList', align: 'center',
          children: []
        },
        {
          title: '选装件', dataIndex: 'optionListColumns', align: 'center',
          children: []
        },
      ],


      settingColumns: [
        {title: '产品型号', dataIndex: 'productModelJh'},
        {title: '选配件名称', dataIndex: 'partsName'},
        {title: '选配件图号', dataIndex: 'partsNo'},
        {title: '规格', dataIndex: 'specs'},
        {title: '供应商', dataIndex: 'supplierBak'},
        {title: '本体/后处理', dataIndex: 'mainType'},
        {title: '类型', dataIndex: 'subType'},
        {title: '指导售价', dataIndex: 'budgetedPrice'},
        {title: '实际售价', dataIndex: 'actualPrice'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],


      searchRules: {
        customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
        customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        reference: [{required: false, message: '参考机型不能为空', trigger: 'blur'}],
        machineType: [{required: true, message: '新增状态机型不能为空', trigger: 'blur'}],
        productModel: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
        productModelJh: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
        blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
        plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
        type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
        ps: [{required: false, message: '功率(PS)不能为空', trigger: 'blur'}],
        basicAmount: [{required: true, message: '基本型开票价不能为空', trigger: 'blur'}],
        chooseAmount: [{required: true, message: '选配件合计不能为空', trigger: 'blur'}],
        unitAmount: [{required: true, message: '整机开票价不能为空', trigger: 'blur'}],
        optionSubtotal: [{required: false, message: '选型选装件小计不能为空', trigger: 'blur'}],
        basicSubtotal: [{required: false, message: '基本型减配小计不能为空', trigger: 'blur'}],
        dataType: [{required: false, message: '清单版本不能为空', trigger: 'blur'}],
      },

      itemRules: {
        masterId: [{required: false, message: '价格主表ID不能为空', trigger: 'blur'}],
        code: [{required: false, message: '核算单号不能为空', trigger: 'blur'}],
        year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        contract: [{required: false, message: '合同号不能为空', trigger: 'blur'}],
        platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
        yearExpectVolume: [{required: false, message: '年度销售预计不能为空', trigger: 'blur'}],
        yearSalesVolume: [{required: false, message: '年度实际销售不能为空', trigger: 'blur'}],
        basicTicketPrice: [{required: false, message: '基本型开票价不能为空', trigger: 'blur'}],
        unitTicketPrice: [{required: false, message: '整机开票价不能为空', trigger: 'blur'}],
        basicNetPrice: [{required: false, message: '基本型净价不能为空', trigger: 'blur'}],
        unitNetPrice: [{required: false, message: '整机净价不能为空', trigger: 'blur'}],
        basicBudgetPrice: [{required: false, message: '基本型预算不能为空', trigger: 'blur'}],
        zbChooseBudget: [{required: false, message: '总部-选配件预算不能为空', trigger: 'blur'}],
        zbBudgetActualPrice: [{required: false, message: '总部-整机预算净价不能为空', trigger: 'blur'}],
        zbBudgetSpace: [{required: false, message: '总部-预算空间不能为空', trigger: 'blur'}],
        zbBasicBalance: [{required: false, message: '总部-基本型预算结余不能为空', trigger: 'blur'}],
        zbUnitBalance: [{required: false, message: '总部-整机预算结余不能为空', trigger: 'blur'}],
        zbBasicBudgetBalance: [{required: false, message: '总部-基本型预算不能为空', trigger: 'blur'}],
        sysChooseBudget: [{required: false, message: '系统-选配件预算不能为空', trigger: 'blur'}],
        sysBudgetActualPrice: [{required: false, message: '系统-整机预算净价不能为空', trigger: 'blur'}],
        sysBudgetSapce: [{required: false, message: '系统-预算空间不能为空', trigger: 'blur'}],
        sysBasicBalance: [{required: false, message: '系统-基本型预算结余不能为空', trigger: 'blur'}],
        sysUnitBalance: [{required: false, message: '系统-整机预算结余不能为空', trigger: 'blur'}],
        costOfMeterial: [{required: false, message: '材料成本不能为空', trigger: 'blur'}],
        contribution: [{required: false, message: '边际贡献不能为空', trigger: 'blur'}],
        contributionPercent: [{required: false, message: '边际贡献率不能为空', trigger: 'blur'}],
        sequenceNumber: [{required: false, message: '序号不能为空', trigger: 'blur'}],
        settlementPrice: [{required: false, message: '结算价不能为空', trigger: 'blur'}],
        balanceAmount: [{required: false, message: '折让金额不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
      },
      childOptionalData: [],
      textarea: ''

    }
  },

  mounted() {
    this.loading = false;
    this.searchForm.option = this.$route.query.option;
    this.fullPath = this.$route.fullPath;
    this.parentPath = this.$route.query.parentPath;
    this.option = this.$route.query.option;
    this.searchForm.dataType = this.searchForm.dataType !== "新版" ? "旧版" : "新版"
    if(":id"===this.$route.params.id){
        this.masterId =null
        this.searchForm.id = null;
        this.id = null;
    }else{
        this.masterId =this.$route.params.id
        this.id = this.$route.params.id;
        this.searchForm.id =this.$route.params.id;
    }
    this.created();
    if (this.$route.query.reference) {
      this.searchForm.reference = this.$route.query.reference
      this.searchForm.customer = this.$route.query.customer
      this.searchForm.customerName = this.$route.query.customerName
      this.searchForm.plate = this.$route.query.plate
      this.$nextTick(() => {
        this.blurs();
      })

    }
    this.getMainData(this.$route.params.id);
    this.$nextTick(function () {
      setTimeout(() => {
        this.resizeHeader();
      }, 300)
    })
    window.addEventListener("resize", this.resizeHeader);
    // this.orgBackground= this.addWaterMark();
    this.checkName();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.resizeHeader);
  },
  watch: {
    "searchForm.dataType": {
      deep: true,
      handler(val, oVal) {
        if (val == null) {
          this.searchForm.dataType = '旧版';
        }
        if (val !== oVal) {
          // 当数据版本切换时，重新查询第二部分信息
          this.blursMachineType();
        }
      }
    }
  },
  methods: {
    resizeHeader() {
      this.headerWidth = this.$refs.searchForm.$el.clientWidth + 1 + 'px';
    },
    platechanged() {
      this.$refs.searchForm.validate(valid => {
      })
    },
    customerNamechanged() {
      this.$refs.searchForm.validate(valid => {
      })
    },
    customerchanged() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer: this.searchForm.customer
      }).then(res => {
        if (res.data.data.length > 0) {
          this.searchForm.customerName = res.data.data[0].name
          this.$refs.searchForm.validate(valid => {
          })
        } else {
          this.$message.info('此客户号没找到信息')
        }
      })

      },
      blowoffchanged(){
          this.$refs.searchForm.validate(valid => {})
      },
      serieschanged(){
          this.$refs.searchForm.validate(valid => {})
      },
      productModelchanged(){
          this.$refs.searchForm.validate(valid => {})
      },
      unitAmountBlurs(){
          this.$refs.searchForm.validate(valid => {})
      },
      dataTypeChanged(){
        // console.log(1111);
        this.$refs.searchForm.validate(valid => {
        })
        
        this.blursMachineType()
        
      },
      onCheckChange(e) {
          this.checked = e.target.checked;
          if(e.target.checked){
              this.searchForm.isCheck=1;
          }else{
              this.searchForm.isCheck=0;
          }
      },
    onAtificialVerChange(e) {
      this.artificialVer = e.target.checked;
      if (this.artificialVer) {
        this.searchForm.isArtificialVer = 1;
      } else {
        this.searchForm.isArtificialVer = 0;
      }
    },

    yearchanged() {
      this.itemColumns.forEach(item => {
        if (item.dataIndex == 'lastYearTitle') {
          item.children.forEach(c => {
            if (c.dataIndex == 'synBasicTicketPrice') {
              c.title = (this.searchForm.year - 1) + "基本型开票价"
            } else if (c.dataIndex == 'synUnitTicketPrice') {
              c.title = (this.searchForm.year - 1) + "整机开票价"
            } else if (c.dataIndex == 'synBasicNetPrice') {
              c.title = (this.searchForm.year - 1) + "基本型实际价格"
            } else if (c.dataIndex == 'synUnitNetPrice') {
              c.title = (this.searchForm.year - 1) + "整机实际价格"
            } else if (c.dataIndex == 'synOptionAmount') {
              c.title = (this.searchForm.year - 1) + "选配件合计"
            } else if (c.dataIndex == 'synBalanceAmount') {
              c.title = (this.searchForm.year - 1) + "折让合计"
            }
          })

        } else if (item.dataIndex == 'thisYearTitle') {
          item.children.forEach(c => {
            if (c.dataIndex == 'basicTicketPrice') {
              c.title = (this.searchForm.year) + "基本型开票价";
            } else if (c.dataIndex == 'unitTicketPrice') {
              c.title = (this.searchForm.year) + "整机开票价"
            } else if (c.dataIndex == 'basicNetPrice') {
              c.title = (this.searchForm.year) + "基本型实际价格"
            } else if (c.dataIndex == 'unitNetPrice') {
              c.title = (this.searchForm.year) + "整机实际价格"
            }
          })
        }
      })
    },
    //获取当前年份
    created: function () {
      var aData = new Date();
      this.searchForm.year = Number(this.$moment(aData).format('YYYY'));
      this.yearchanged();
    },
    mounted() {
      this.checkName();
    },
    getMainData: function (id) {

      request(GET_XSGSALLOWANCEMAIN_EDITID, METHOD.GET, {id: id})
          .then(res => {
            if (res.data.data != null) {
              this.searchPartForm.masterId = res.data.data.id;
              this.noContainForm.masterId = res.data.data.id;
              this.searchForm = res.data.data
              if (res.data.data.isCheck === 0) {
                this.checked = false
              } else {
                this.checked = true
              }
              if (res.data.data.isArtificialVer === 1) {
                this.artificialVer = true
              } else {
                this.artificialVer = false
              }

              //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
              this.$nextTick(() => {
                this.$refs.selectNoOptional.getNoOptionData();
                this.$refs.editOptional.getOptionalcData();
                this.getMaterialNo();
              })
              this.yearchanged()
              this.getItemData();
              if (this.$route.query.copyMachineType) {
                this.searchForm.id = null;
                this.id = null;
                this.$refs.selectNoOptional.resetId();
                this.$refs.editOptional.resetId();
              }
            }
          })
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSALLOWANCEMAIN_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    setItemDatas(records) {
      let jts = []
      let no_jts = []
      records.forEach(item => {
        if (item.setDetails === null) {
          return
        }
        item.setDetails.forEach(key => {
          if ('阶梯金额' != key.allowanceType) {
            if (!this.isExtis(no_jts, 'ni_' + key.seqNum + '_seqNumber')) {
              no_jts.push({
                title: key.seqName,
                dataIndex: 'ni_' + key.seqNum + '_seqNumber',
                width: 80,
                align: 'center'
              })
              no_jts.push({title: "类型", dataIndex: 'ni_' + key.seqNum + '_allowanceType', width: 80, align: 'center'})
              no_jts.push({title: "备注", dataIndex: 'ni_' + key.seqNum + '_remarks', width: 80, align: 'center'})
            }
            item['ni_' + key.seqNum + '_seqNumber'] = key.allowanceAmount
            let remarks = key.remarks
            if (remarks == null) {
              remarks = ""
            }

            if (key.defaultRemarks) {
              item['ni_' + key.seqNum + '_remarks'] = key.defaultRemarks + remarks
            } else {
              item['ni_' + key.seqNum + '_remarks'] = remarks
            }


            item['ni_' + key.seqNum + '_allowanceType'] = key.allowanceType
          } else {
            if (key.allowanceType == '阶梯金额') {
              if (key.jtList != null) {
                key.jtList.forEach(i => {
                  if (!this.isExtis(jts, 'i_' + i.seqNumber + '_seqNumber')) {
                    jts.push({
                      title: i.seqName,
                      dataIndex: 'i_' + i.seqNumber + '_seqNumber',
                      width: 80,
                      align: 'center'
                    })
                  }
                  item['i_' + i.seqNumber + '_seqNumber'] = i.price
                })
                let remarks = key.remarks
                if (remarks == null) {
                  remarks = ""
                }
                if (key.defaultRemarks) {
                  item['jtzr_remarks'] = key.defaultRemarks + remarks
                } else {
                  item['jtzr_remarks'] = remarks
                }
              }

            }
          }


        })

      })

      let xxColumns = []
      let xzColumns = []


      records.forEach(item => {

        this.optionalChanged(xxColumns, xzColumns, item.allowanceParts)
        if (item.allowanceParts === null) {
          return
        }
        item.allowanceParts.forEach(key => {

          // 要区别选型 选装
          if (key.subType === '选型件') {
            if (key.isSubtraction === '0') {
              if (key.valuePrice === 0) {
                if (key.specs === key.standardSpecs) {
                  //原来逻辑是默认为0
                  // item[b64_md5(b64_md5(key.id))] = key.valuePrice
                  //现在改为空值
                  item[b64_md5(b64_md5(key.id))] = null
                } else {
                  item[b64_md5(b64_md5(key.id))] = key.specs
                }

              } else {
                item[b64_md5(b64_md5(key.id))] = key.valuePrice
              }

            } else if (key.isSubtraction === '1') {
              if (key.actualPrice === 0) {
                item[b64_md5(b64_md5(key.id))] = '不带'
              } else {
                item[b64_md5(b64_md5(key.id))] = key.actualPrice
              }
            }
          } else if (key.subType === '选装件') {
            item[b64_md5(b64_md5(key.id))] = key.actualPrice
          }


        })

      })

      if (no_jts.length == 0) {
        no_jts.push({title: '', dataIndex: "nijtzr_remarks", width: 80, align: 'center'})
      }
      jts.push({title: '备注', dataIndex: "jtzr_remarks", width: 80, align: 'center'})

      if (xxColumns.length == 0) {
        xxColumns.push({title: '', dataIndex: "xxColumns", width: 80, align: 'center'})
      }
      if (xzColumns.length == 0) {
        xzColumns.push({title: '', dataIndex: "xzColumns", width: 80, align: 'center'})
      }

      this.itemColumns.forEach(item => {
        if (item.dataIndex == 'basicList') {
          item.children = xxColumns
        } else if (item.dataIndex == 'optionListColumns') {
          item.children = xzColumns
        } else if (item.dataIndex == 'jtzrId') {
          item.children = jts
        } else if (item.dataIndex == 'zrId') {
          item.children = no_jts
        }


      })
    },

    getItemData: function () {
    },
    getItemDataColumns() {
      request(LIST_COLUMNS, METHOD.POST, {...this.searchPartForm})
          .then(res => {
            let list = []
            res.data.data.forEach(item => {

              if (!this.isExtis(list, item)) {
                list.push(item)
              }
            })
            if (list.length == 0) {
              list.push({title: '', dataIndex: "fjtzr_null", width: 90, align: 'center'})
            }

          })
    },
    isExtis(list, key) {
      let flag = false;
      list.forEach(item => {
        if (item.dataIndex == key) {
          flag = true;
        }
      })
      return flag

    },

    handleSaveAction() {
      var that = this
      this.isSaving = true;
      that.$nextTick(() => {
        that.searchForm.partsList = that.$refs.editOptional.getOptionalcDataList()
        that.searchForm.noPartsList = that.$refs.selectNoOptional.getNoOptionalcDataList()
      })
      this.searchForm.id = this.id;
      this.searchForm.option = this.option;

      // that.searchForm.businessUnit = that.searchForm.businessUnit
      // that.searchForm.productLine = that.searchForm.productLine
      // that.searchForm.newScenario1 = that.searchForm.newScenario1
      // that.searchForm.newScenario2 = that.searchForm.newScenario2
      // that.searchForm.newScenario3 = that.searchForm.newScenario3

      // console.log(that.searchForm);
      
      debugger
      request(SUBMIT_XSGSALLOWANCEMAINSAVE, METHOD.POST, that.searchForm).then(res => {
        if (res.data.code == -1) {
          this.isSaving = false;
          that.$message.error(res.data.msg);
          return;
        }
        //获取主键ID
        that.masterId = res.data.data.id;
        that.searchPartForm.optionSubtotal = res.data.data.optionSubtotal;
        that.searchPartForm.masterId = res.data.data.id;
        that.noContainForm.masterId = res.data.data.id;
        that.searchForm = res.data.data;
        if (res.data.data.isCheck === 0) {
          this.checked = false
        } else {
          this.checked = true
        }
        that.$message.success('保存成功')
        this.isSaving = false;
        // 关闭页面
        that.$closePage(this.fullPath, this.parentPath);
      })

    },
    //保存
    handleSave() {
      var that = this

      that.$refs.searchForm.validate(valid => {
        if (valid) {
          this.$confirm({
            content: `是否保存当前数据？`,
            onOk: () => {
              that.handleSaveAction()
            },
          });
        }
      })


    },


    /** 打开基本型配置清单 */
    handleBasicView() {
      this.lookBasicDialog = true;
      if (this.searchForm.plate != null && this.searchForm.series != null) {
        this.childOption[0] = this.searchForm.plate
        this.childOption[1] = this.searchForm.series
        this.childOption[2] = this.searchForm.customer
        this.childOption[3] = this.searchForm.productModelJh
        this.childOption[4] = this.searchForm.dataType == '新版' ? "新版" : null
        this.childOption[5] = this.searchForm.year;
        this.childOption[6] = this.searchForm.blowoff;
        //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
        this.$nextTick(() => {
          this.$refs.selectOptionList.loadBasicData();
        })
      }
    },

    //更新页面计算信息
    getSearchUpdate(data) {
      this.searchForm = data
    },
    getSearchNoOptionUpdate(data) {
      this.searchForm = data
    },

    //计算选型选装小计
    getOptionTotal(data) {
      this.searchForm.optionSubtotal = Number(data);
    },

    // 选型组件获取减配列表
    getNoParts() {
      this.noPartsList = this.$refs.selectNoOptional.getNoOptionalcDataList();
      this.$refs.editOptional.setNoparts(this.noPartsList);
    },

    // 取消选型件后重新设置减配列表
    setNoPartsListNew(data) {
      this.noPartsList = data;
      this.$refs.selectNoOptional.getNoPartsListNewAll(this.noPartsList);
    },

    //选配件合计=选型选装件小计+选型减配小计
    getChooseAmoutToal(data) {
      this.searchForm.chooseAmount = Number(data);
      if (this.searchForm.basicAmount != null) {
        this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
        this.refreshItem()
      }


    },
    //计算选型减配小计
    getlectotypeSubtotal(data) {
      this.searchForm.basicSubtotal = Number(data);
      this.refreshItem()

    },
    //选配件合计=选型选装件小计+选型减配小计
    getchooseBasicAmountToal(data) {
      this.searchForm.chooseAmount = Number(data);
      if (this.searchForm.basicAmount != null) {
        this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
      }

    },

    //关闭基本型清单对话框
    handlelookBasicSubmit() {
      this.lookBasicDialog = false;
    },
    //关闭基本型清单对话框
    closelookBasicrForm() {
      this.lookBasicDialog = false;
    },
    /**选择参考*/
    blurs() {
      this.$refs.searchForm.validate(valid => {
      })
      this.machineType = this.searchForm.machineType
      this.basicAmount = this.searchForm.basicAmount
      this.searchFormAll.year = this.searchForm.year
      this.searchFormAll.reference = this.searchForm.reference
      this.searchFormAll.customer = this.searchForm.customer
      this.searchFormAll.plate = this.searchForm.plate
      if (this.searchForm.customer == null) {
        this.$message.info('请先选择客户')
        return
      }
      if (this.searchForm.plate == null) {
        this.$message.info('请先选择板块')
        return
      }
      this.$nextTick(() => {
        this.isBlurs = this.$refs.editOptional.getIsBlurs();
        if (this.isBlurs) {
          this.$message.info('此机型已修改，还未保存，请先保存!')
          return
        }
        this.loadReferenceData();
      })
    },
    //加载参考机型数据
    loadReferenceData() {
      if (this.searchForm.reference === null || this.searchForm.reference === '') {
        if (this.searchForm.machineType != null && this.searchForm.machineType != '') {
          this.blursMachineType();
        } else {
          this.$message.info('暂无参考机型')
          this.optionList = [];

          this.searchForm.basicAmount = 0;
          this.searchForm.chooseAmount = 0;
          this.searchForm.unitAmount = 0;
          this.searchForm.optionSubtotal = 0;
          this.searchForm.basicSubtotal = 0;

          this.searchForm.unitAmountRefer = 0;
          this.searchForm.basicAmountRefer = 0;
          this.searchForm.productModelRefer = null;
          this.masterId = null;
          //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
          this.$nextTick(() => {
            this.$refs.selectNoOptional.getNoOptionData();
            this.$refs.editOptional.getOptionalcData();
          })
          this.refreshItem()
        }
      } else {
        request(SElECTLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.searchFormAll)
            .then(res => {
              if (res.data.msg == 'false') {
                this.$message.info('暂无参考机型')
                this.searchForm.unitAmountRefer = 0;
                this.searchForm.basicAmountRefer = 0;
                this.searchForm.productModelRefer = null;
                this.masterId = "0";
                this.searchForm.optionSubtotal = 0;
                this.searchForm.chooseAmount = 0;
                this.searchForm.unitAmount = 0;
                this.searchForm.basicSubtotal = 0;
                this.searchForm.basicAmount = 0;
                //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                this.$nextTick(() => {
                  this.$refs.selectNoOptional.getNoOptionData();
                  this.$refs.editOptional.getOptionalcData();
                })
                this.refreshItem()
              } else {
                this.searchPartForm.masterId = res.data.data[0].id;
                this.masterId = res.data.data[0].id;
                this.noContainForm.masterId = res.data.data[0].id;
                if (res.data.data[0].isArtificialVer === 1) {
                  this.artificialVer = true
                  this.searchForm.isArtificialVer = 1;
                } else {
                  this.artificialVer = false
                  this.searchForm.isArtificialVer = 0;
                }
                if (this.searchForm.machineType === null || this.searchForm.machineType === '') {
                  // this.searchForm=res.data.data[0];
                  this.searchForm.optionSubtotal = res.data.data[0].optionSubtotal;
                  this.searchForm.chooseAmount = res.data.data[0].chooseAmount;
                  this.searchForm.unitAmount = res.data.data[0].unitAmount;
                  this.searchForm.basicSubtotal = res.data.data[0].basicSubtotal;
                  this.searchForm.basicAmount = res.data.data[0].basicAmount;
                  this.searchForm.unitAmountRefer = res.data.data[0].unitAmount;
                  this.searchForm.basicAmountRefer = res.data.data[0].basicAmount;
                  let data = {
                    "P_ZTJ": this.searchFormAll.reference
                  }
                  request(ztjxx_action, METHOD.POST, data).then(res => {

                    let returnData = res.data.data
                    let RETURN = returnData.RETURN
                    if (RETURN && RETURN.ZTYPE == 'S') {
                      let T_OUT = returnData.T_OUT[0];
                      this.searchForm.productModelRefer = T_OUT.ZCPXH
                    }
                  })


                  this.searchForm.machineType = null;
                  this.searchForm.blowoff = null;
                  this.searchForm.series = null;
                  this.searchForm.productModel = null;
                  this.searchForm.ps = null;
                  this.searchForm.basicGuidanceAmount = 0;
                  this.searchForm.productModelJh = null;
                } else {
                  //当新增状态机存在的时候，只带出相对应数据
                  this.searchForm.unitAmountRefer = res.data.data[0].unitAmount;
                  this.searchForm.basicAmountRefer = res.data.data[0].basicAmount;
                  this.searchForm.optionSubtotal = res.data.data[0].optionSubtotal;
                  this.searchForm.chooseAmount = res.data.data[0].chooseAmount;
                  this.searchForm.basicSubtotal = res.data.data[0].basicSubtotal;
                  this.searchForm.basicAmount = res.data.data[0].basicAmount;
                  let data1 = {
                    "P_ZTJ": this.searchFormAll.reference
                  }
                  request(ztjxx_action, METHOD.POST, data1).then(res => {
                    let returnData = res.data.data
                    let RETURN = returnData.RETURN
                    if (RETURN && RETURN.ZTYPE == 'S') {
                      let T_OUT = returnData.T_OUT[0];
                      this.searchForm.productModelRefer = T_OUT.ZCPXH
                    }
                  })

                }
                this.searchForm.id = null;
                this.searchForm.reference = this.searchFormAll.reference;

                //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                this.$nextTick(() => {
                  this.$refs.selectNoOptional.getNoOptionData();
                  this.$refs.editOptional.getOptionalcData();
                })
                this.basicAmountBlurs();
                this.getItemData();
              }
            })
      }
    },
    powerDiffBlurs() {
      //基本型开票价=参考基本型开票价+功率差
      this.searchForm.basicAmount = this.NumberAdd(this.searchForm.basicAmountRefer, this.searchForm.powerDiff);
      this.optionSubtotalBlurs();
    },
    //计算整机开票价
    basicAmountBlurs() {

      if (this.searchForm.chooseAmount != null) {
        this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
        this.refreshItem()
      }
    },
    //计算整机开票价
    chooseAmountBlurs() {
      this.$refs.searchForm.validate(valid => {
      })
      if (this.searchForm.basicAmount != null) {
        this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
        this.refreshItem()
      }
    },
    //计算整机开票价
    optionSubtotalBlurs() {
      if (this.searchForm.basicAmount != null && this.searchForm.chooseAmount != null) {
        //选配件合计=选型选装件小计+选型减配小计
        this.searchForm.chooseAmount = this.NumberAdd(this.searchForm.optionSubtotal, this.searchForm.basicSubtotal);
        //整机开票价=基本型开票价+选配件合计
        this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
        this.$refs.searchForm.validate(valid => {
        })
        this.refreshItem()
      }
    },

    /**  状态机变更加载数据 */
    blursMachineType() {
      if (this.searchForm.customer == null) {
        this.$message.info('请先输入客户名称！')
        return
      }
      if (this.searchForm.plate == null) {
        this.$message.info('请先输入板块！')
        return
      }
      // 校验状态机是否已经存在
      let that = this;
      // 添加状态机唯一性校验
      request(CHECK_MACHINE_TYPE, METHOD.POST, this.searchForm).then(res => {
         if (res.data.code == 0) {
            that.$nextTick(()=>{
              that.isBlurs=this.$refs.editOptional.getIsBlurs();
              if(that.isBlurs){
                that.$message.info('此机型已修改，还未保存，请先保存!')
                return
              }
              that.loadData();
              that.getMaterialNo();
            })
          } else {
            that.$message.error(res.data.msg);
          }
      }).catch(err =>{
        that.$message.error(err.msg);
      })

    },
      //加载数据
      loadData(){
          let data = {
              "P_ZTJ" :this.searchForm.machineType
          }
          request(ztjxx_action, METHOD.POST, data).then(res => {
              let returnData = res.data.data
              let RETURN = returnData.RETURN
              if (RETURN && RETURN.ZTYPE == 'S') {
                  let T_OUT = returnData.T_OUT[0];
                  // console.log(T_OUT);
                  
                  if(this.searchForm.reference!=null&&this.searchForm.reference!=''){
                      this.searchForm.productModel = T_OUT.ZCPXH;
                      this.searchForm.productModelJh = T_OUT.ZCPXHJ;
                      this.searchForm.ps = T_OUT.ZGL;
                      this.searchForm.series = T_OUT.ZCPZXL;
                      this.searchForm.blowoff = T_OUT.ZPF_TEXT;
                      this.searchForm.type = T_OUT.ZZPX;
                      this.searchForm.strain1 = T_OUT.ZYT1_TEXT;
                      this.searchForm.strain2 = T_OUT.ZYT2_TEXT;
                      this.searchForm.strain3 = T_OUT.ZYT3_TEXT;
                      // this.searchForm.plate = T_OUT.ZYWBK_TEXT;
                      this.searchForm.productLine = T_OUT.ZZPX;
                      this.searchForm.businessUnit = T_OUT.ZZBUYW;
                      this.searchForm.newScenario1 = T_OUT.ZZXCJ1;
                      this.searchForm.newScenario2 = T_OUT.ZZXCJ2;
                      this.searchForm.newScenario3 = T_OUT.ZZXCJ3;
                      this.selectBasicPrice2(this.searchForm.year,this.searchForm.customer,T_OUT.ZCPXH,this.searchForm.plate,T_OUT.ZCPXHJ,this.searchForm.dataType)
                  }else{
                      this.searchForm.type = T_OUT.ZZPX;
                      this.searchForm.productModel = T_OUT.ZCPXH;
                      this.searchForm.productModelJh = T_OUT.ZCPXHJ;
                      this.searchForm.ps = T_OUT.ZGL;
                      this.searchForm.series = T_OUT.ZCPZXL;
                      this.searchForm.blowoff = T_OUT.ZPF_TEXT;
                      this.searchForm.strain1 = T_OUT.ZYT1_TEXT;
                      this.searchForm.strain2 = T_OUT.ZYT2_TEXT;
                      this.searchForm.strain3 = T_OUT.ZYT3_TEXT;
                      // this.searchForm.plate = T_OUT.ZYWBK_TEXT;
                      this.searchForm.productLine = T_OUT.ZZPX;
                      this.searchForm.businessUnit = T_OUT.ZZBUYW;
                      this.searchForm.newScenario1 = T_OUT.ZZXCJ1;
                      this.searchForm.newScenario2 = T_OUT.ZZXCJ2;
                      this.searchForm.newScenario3 = T_OUT.ZZXCJ3;
                      this.selectBasicPrice()

                      if(this.searchForm.reference==null||this.searchForm.reference===''){
                          //如果参考机型为空的时候,带出选型list
                          request(SELECTOPTIONLIST_XSGSALLOWANCEMAIN, METHOD.POST, {
                            customer: this.searchForm.customer,
                            year: this.searchForm.year,
                            plate: this.searchForm.plate,
                            blowoff: this.searchForm.blowoff,
                            productModelJh: this.searchForm.productModelJh,
                            series: this.searchForm.series,
                            dataType: this.searchForm.dataType == '新版' ? 'new' : null,
                          }).then(res => {
                              this.optionList=res.data.data;
                              this.searchForm.optionSubtotal = 0;
                              this.searchForm.chooseAmount = 0;
                              this.searchForm.basicSubtotal = 0;
                              this.searchForm.unitAmount = 0;


                            this.searchForm.unitAmountRefer=0;
                            this.searchForm.basicAmountRefer=0;
                            this.searchForm.productModelRefer =null;
                              if(this.searchForm.basicAmount!=null){
                                  this.searchForm.unitAmount=this.NumberAdd(this.searchForm.basicAmount,this.searchForm.chooseAmount);
                              }
                              this.$nextTick(()=>{
                                  this.$refs.editOptional.getoptionList();
                                  this.$refs.selectNoOptional.getNoOptionList();
                                  this.refreshItem()
                              })
                          })
                      }
                  }


              }else{

                this.$message.info('暂无该机型信息')
                if(this.searchForm.reference!=null&&this.searchForm.reference!=''){
                  this.optionList= [];
                  this.searchForm.blowoff=null;
                  this.searchForm.series=null;
                  this.searchForm.productModel=null;
                  this.searchForm.ps=null;
                  this.searchForm.basicAmount=0;
                  this.searchForm.basicGuidanceAmount=0;
                  this.searchForm.productModelJh=null;
                  this.$nextTick(()=>{
                    this.$refs.selectNoOptional.getNoOptionData();
                    this.$refs.editOptional.getOptionalcData();
                  })
                }else{
                  this.optionList= [];
                  this.searchForm.blowoff=null;
                  this.searchForm.series=null;
                  this.searchForm.productModel=null;
                  this.searchForm.ps=null;
                  this.searchForm.basicAmount=0;
                  this.searchForm.chooseAmount=0;
                  this.searchForm.unitAmount=0;
                  this.searchForm.optionSubtotal=0;
                  this.searchForm.basicSubtotal=0;
                  this.searchForm.basicGuidanceAmount=0;
                  this.searchForm.productModelJh=null;
                  this.masterId=null;
                  this.$nextTick(()=>{
                    this.$refs.selectNoOptional.getNoOptionData();
                    this.$refs.editOptional.getOptionalcData();
                  })
                }
                 this.refreshItem()
              }
              this.$refs.searchForm.validate(valid => {})

          })
      },



    //基本型开票价
    selectBasicPrice(){

if(!(this.searchForm.year == null || this.searchForm.customer ==null || this.searchForm.productModel ==null)){
    //判断新旧数据 调整param
    let param = {
      year:this.searchForm.year,
      customer:this.searchForm.customer,
      productModel:this.searchForm.productModel,
      plate:this.searchForm.plate
    }
    if(this.searchForm.dataType == "新版"){
      // param.push({
      //   productModelJh: this.searchForm.productModelJh,
      //   isRead: 1
      // })
      param.productModelJh = this.searchForm.productModelJh;
      param.isRead = 1;
    }
    console.log('请求参数', param)
    request(LIST_XSGSBASICPRICE, METHOD.POST, {param}).then(res => {
        let data = res.data.data
        this.searchForm.basicAmount = 0;
        if(data.length >0 ){
            this.searchForm.basicGuidanceAmount = data[0].actualPrice
        }else{
            this.searchForm.basicGuidanceAmount = 0;
        }
    })
}
    },
    //基本型开票价
    selectBasicPrice2(year,customer,productModel,plate,productModelJh,dataType){
    if(!(year == null || customer ==null || productModel ==null)){
        //判断新旧数据 调整param
        let param = {
          year: year,
          customer: customer,
          productModel: productModel,
          plate: plate,
        }
        if (dataType == "新版") {
          // param.push({
          //   productModelJh: productModelJh,
          //   isRead: 1
          // })
          param.productModelJh = productModelJh;
          param.isRead = 1;
        }
        request(LIST_XSGSBASICPRICE, METHOD.POST, {param}).then(res => {

            let data = res.data.data
            if(data.length >0 ){
                this.searchForm.basicGuidanceAmount = data[0].actualPrice
            }else{
                this.searchForm.basicGuidanceAmount = 0;
            }
            this.checked =false;
            this.basicAmountBlurs()

        })
    }
    },

    /** 编辑主表价格行数据信息，打开对话框 */
    handleEdit(record) {
      this.itemDialog = true
      this.itemForm = {...record}
    },

    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },

    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },


    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          window.open(decodeURI(DOWNLOAD_XSGSALLOWANCEMAIN))
        },
        onCancel() {
        },
      });
    },


    //行数据
    onItemPageChange(page, size) {
      this.itemPagination.current = page;
      this.itemPagination.size = size.toString();
      this.getItemData();
    },
    onItemSizeChange(current, size) {
      this.itemPagination.current = 1;
      this.itemPagination.size = size.toString();
      this.getItemData();
    },
    optionalChanged(xxColumns, xzColumns, optionalData) {
      if (optionalData === null) {
        return
      }
      optionalData.forEach(item => {
        if (item.subType == '选型件') {
          if (!this.isDoubleColumns(xxColumns, item.partsName)) {
            xxColumns.push({title: item.partsName, dataIndex: b64_md5(b64_md5(item.id)), width: 165, align: 'center'})
          }
        } else if (item.subType == '选装件') {
          if (!this.isDoubleColumns(xzColumns, item.partsName)) {
            xzColumns.push({title: item.partsName, dataIndex: b64_md5(b64_md5(item.id)), width: 165, align: 'center'})
          }
        }

      })


    },

    isDoubleColumns(columns, name) {
      let flag = false;
      columns.forEach(item => {
        if (item.dataIndex == b64_md5(b64_md5(name))) {
          flag = true;
        }
      })
      return flag
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name: value
      }).then(res => {
        this.customerList = res.data.data
      })

    },
    handleCustomerChange(value) {
      this.customerList.forEach(item => {
        if (item.name == value) {
          this.searchForm.customer = item.customer
          this.selectBasicPrice()
        }
      })
    },

    deleteDoubleXx(records) {

      let newList = []
      let dataList = this.$refs.selectNoOptional.getNoOptionalcDataList();
      let flag = false
      dataList.forEach(item => {
        if (records.partsName == item.partsName) {
          flag = true
        } else {
          newList.push(item)
        }


      })
      let that = this;
      if (flag) {
        this.$confirm({
          content: `减配里面有相同的[` + records.partsName + "],是否删除掉",
          onOk: () => {
            that.$refs.selectNoOptional.setNoOptionalcDataList(newList)
            that.$refs.selectNoOptional.blursActualPrice()
          },
        });
      }
    },
    refreshItem() {

      request(getTempData, METHOD.POST, {...this.searchForm}).then(res => {
        let data = res.data.data
        this.$nextTick(() => {
          let parts = []
          let option = this.$refs.editOptional.getOptionalcDataList()
          option.forEach(item => {
            parts.push(item)
          })
          let notoption = this.$refs.selectNoOptional.getNoOptionalcDataList()
          notoption.forEach(item => {
            parts.push(item)
          })
          data[0].allowanceParts = parts
          this.setItemDatas(data)
          this.itemData = data
          this.$refs.searchForm.validate(valid => {
          })
        })


      })
    },
    getMaterialNo() {
      if (this.searchForm.machineType && this.searchForm.machineType && !this.searchForm.materialNo) {
        request(GET_BY_CUSTOMER_MACHINETYPE, METHOD.POST, {
          'customer': this.searchForm.customer,
          'machineType': this.searchForm.machineType
        }).then(res => {
          this.searchForm.materialNo = res.data.data;
        });
      }
    },
    
  }
}
</script>

<style scoped>
/deep/ .header-button-area {
  position: fixed;
  z-index: 999;
  height: 45px;
  background-color: #ffffff;
  line-height: 45px;
  margin-top: -25px;
  min-width: 700px;
}

/deep/ .header-button-area > button {
  margin-right: 8px;
}

.ant-input-number {
  width: 100%;
}

td {
  border: 1px solid #f0f0f0;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
*/
/deep/ .ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/ .ant-card-head-title {
  flex: auto;
  width: 120px;
  overflow: inherit;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-card-extra {
  margin-right: 75%;
  padding: 0px 0;
}

.tdnumber > /deep/ .ant-input-number {
  width: 180px;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

.ant-divider-horizontal {
  height: 0.5px;
  margin: 0px 0;
}

.ant-divider {
  background: #000;
}

</style>
