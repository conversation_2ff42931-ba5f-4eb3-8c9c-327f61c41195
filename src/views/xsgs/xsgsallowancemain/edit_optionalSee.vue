<template>
    <div>

        <a-table
                :columns="optionalColumns"
                rowKey="id"
                :pagination="false"
                :data-source="optionalData"
                slot="expandedRowRender"
                :scroll="{ x: 520 }"
                size="small"
                bordered
                :customRow="onRowClick"
                :loading="optionalloading">
            <template slot="isCheck" slot-scope="text, record">
                <a-checkbox  :checked="record.isCheck === '1' ? true:false ">
                </a-checkbox>
            </template>
            <template slot="subType" slot-scope="text">
               {{text}}
            </template>

            <template slot="partsName" slot-scope="text" >
                {{text}}
            </template>

            <template slot="specs" slot-scope="text"  >
                {{text}}
            </template>

            <template slot="supplierBak" slot-scope="text" >
               {{text}}
            </template>

            <template slot="budgetedPrice" slot-scope="text" >
               {{text}}
            </template>

            <template slot="actualPrice" slot-scope="text" >
              {{text}}
            </template>
            <template slot="valuePrice" slot-scope="text" >
              {{text}}
            </template>

        </a-table>

    </div>

</template>

<script>
    import {
        // LIST_XSGSALLOWANCEPARTS_PAGE,
        LIST_XSGSPARTSPRICE,
        LIST_XSGSALLOWANCEPARTS,
        getUuid,
        LIST_XSGSPARTSSETTING,
        LIST_XSGSPARTSOPTION} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "edit_optionalSee",

        props:{
            masterId:String,
        },
        data() {
            return {
                //选型选装配置
                optionalloading:false,
                optionalForm:{},
                optionalData: [],
                optionalColumns: [
                    {title: '选中', dataIndex: 'isCheck',width: 50, scopedSlots: { customRender: 'isCheck' }},
                    {title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }},
                    {title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }},
                    {title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }},
                    {title: '供应商', dataIndex: 'supplierBak',scopedSlots: { customRender: 'supplierBak' }},
                    {title: '指导售价', dataIndex: 'budgetedPrice',scopedSlots: { customRender: 'budgetedPrice' }},
                    {title: '实际售价', dataIndex: 'actualPrice',scopedSlots: { customRender: 'actualPrice' }},
                    {title: '价差', dataIndex: 'valuePrice',scopedSlots: { customRender: 'valuePrice' }},
                ],

            }
        },
        mounted() {
            this.checkName();
        },
        methods: {
            //选型选装配置
            getOptionalcData: function () {
                    this.optionalloading = true
                    this.optionalForm.subType="subType"
                    this.optionalForm.isSubtraction="0";
                    this.optionalForm.masterId=this.masterId;
                    request(LIST_XSGSALLOWANCEPARTS, METHOD.POST, {...this.optionalForm})
                        .then(res => {
                            this.optionalloading = false
                            this.optionalData = res.data.data
                    })
            },
            getOptionalcDataList(){
                return  this.optionalData;
            },
        }
    }
</script>

<style scoped>

</style>
