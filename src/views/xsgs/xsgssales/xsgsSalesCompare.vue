<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <a-row>
                    <a-col :span="4">
                        <a-form-model-item label="年度" class="year">
                            <a-input style="width: 100px" v-model="searchForm.saleYear" @blur="yearChanged"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="客户名称">
                            <a-select
                                    v-model="searchForm.customerList"
                                    show-search
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :not-found-content="null"
                                    mode="multiple"
                                    :allowClear='true'
                                    @search="handleCustomerSearch"
                                    @change="handleCustomerChange"
                            >
                                <a-select-option v-for="d in customerList" :key="d.customer">
                                    {{ d.customer + '-' + d.customerName }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item class="buttonArea">
                            <a-button type="primary" style="margin: 0 8px 0 0" @click="handleSearch">
                                <a-icon type="search"/>
                                查询
                            </a-button>
                            <a-button type="primary" @click="resetQuery">
                                <a-icon type="reload"/>
                                重置
                            </a-button>
                            <a @click="advanced?advanced=false:advanced=true" style="margin-left: 8px">
                                {{ advanced ? '收起' : '展开' }}
                                <a-icon :type="advanced ? 'up' : 'down'"/>
                            </a>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <template v-if="advanced">
                <a-row>
                    <a-col :span="6">
                        <a-form-model-item label="板块">
                            <a-select width="width:100px" v-model="searchForm.plateList" style="min-width: 60px"
                                      :allowClear="true"
                                      mode="multiple"
                                      @dropdownVisibleChange="getPlateList(searchForm)">
                                <a-select-option v-for="d in plateList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item label="排放">
                            <a-select width="width:100px" v-model="searchForm.blowoffList" style="min-width: 60px"
                                      :allowClear="true"
                                      mode="multiple"
                                      @dropdownVisibleChange="getBlowoffList(searchForm)">
                                <a-select-option v-for="d in blowoffList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item label="系列">
                            <a-select width="width:100px" v-model="searchForm.seriesList" style="min-width: 60px"
                                      :allowClear="true"
                                      mode="multiple"
                                      @dropdownVisibleChange="getSeriesList(searchForm)">
                                <a-select-option v-for="d in seriesList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">

                        <a-form-model-item label="品系1">
                            <a-select width="width:100px" v-model="searchForm.typeList" style="min-width: 60px"
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @dropdownVisibleChange="getTypeList(searchForm)">
                                <a-select-option v-for="d in typeList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                    <a-row>
                        <a-col :span="6">

                            <a-form-model-item label="品系2">
                                <a-select width="width:100px" v-model="searchForm.strain1List" style="min-width: 60px"
                                          :allowClear="true"
                                          mode="multiple"
                                          :filterOption="filterOption"
                                          @dropdownVisibleChange="getStrain1List(searchForm)">
                                    <a-select-option v-for="d in strain1List" :key="d">
                                        {{ d}}
                                    </a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-model-item label="品系3">
                                <a-select width="width:100px" v-model="searchForm.strain2List" style="min-width: 60px"
                                          :allowClear="true"
                                          mode="multiple"
                                          :filterOption="filterOption"
                                          @dropdownVisibleChange="getStrain2List(searchForm)">
                                    <a-select-option v-for="d in strain2List" :key="d">
                                        {{ d}}
                                    </a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                        <a-col :span="6">
                            <a-form-model-item label="品系4">
                                <a-select width="width:100px" v-model="searchForm.strain3List" style="min-width: 60px"
                                          :allowClear="true"
                                          mode="multiple"
                                          :filterOption="filterOption"
                                          @dropdownVisibleChange="getStrain3List(searchForm)">
                                    <a-select-option v-for="d in strain3List" :key="d">
                                        {{ d}}
                                    </a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                </template>
            </a-form-model>

            <vxe-grid highlight-hover-row
                      row-id="id"
                      ref="xTable"
                      show-footer
                      highlight-current-row
                      highlight-current-column
                      :checkbox-config="{ highlight: true,reserve: true, range: true}"
                      :seq-config="{startIndex: (gridOptions.pagerConfig.currentPage - 1) * gridOptions.pagerConfig.pageSize}"
                      stripe
                      v-bind="gridOptions"
                      @page-change="handlePageChange">
                <template #toolbar_buttons>
                    <a-button v-if="checkPf('BASE_COMPARE_EXPORT')" style="float: right;margin-right: 5px" type="primary" icon="download" @click="handleExportExcel">导出</a-button>
                </template>
                <template slot="customer" slot-scope="{ row  }">
                    {{coverCustomer(row.customer)}}
                </template>

            </vxe-grid>
        </a-card>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {
        LIST_XSGSSALES_SALES_PAGE,
        DOWNLOAD_XSGSSALES_COM,
        LIST_XSGSCUSTOMER,
        COMBO_PLATE,
        COMBO_BLOWOFF,
        COMBO_SERIES,
        COMBO_TYPE,
        COMBO_STRAIN1,
        COMBO_STRAIN2,
        COMBO_STRAIN3,
    } from "@/services/api/xsgs";
    import {METHOD, request, exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie'

    export default {
        name: "xsgsSales",
        data() {
            return {
                hasAuth,
                Cookie,
                advanced:false,
                loading: false,
                dialog: false,
                // 功能权限关键字
                PF_FIELD:"PRICE_LIBRARY_SEARCH,BASE_COMPARE",
                PF_LIST:[],
                confirmLoading: false,
                yearCount: 3,
                allCustomerList:[],
                customerList:[],
                customers:[],
                blowoffList:[],
                plateList:[],
                seriesList:[],
                typeList:[],
                strain1List:[],
                strain2List:[],
                strain3List:[],
                searchForm: {
                    saleYear:null,
                    saleTimeBegin:null,
                    saleTimeEnd:null,
                    blowoffList:[],
                    plateList:[],
                    seriesList:[],
                    typeList:[],
                    strain1List:[],
                    strain2List:[],
                    strain3List:[],
                    customerList:[]
                },
                form: {},
                activeKey: ["1"],
                gridOptions: {
                    border: true,
                    resizable: true,
                    keepSource: true,
                    showOverflow: true,
                    loading: false,
                    pagerConfig: {
                        total: 0,
                        currentPage: 1,
                        pageSize: 10,
                        pageSizes: [2,10, 20, 50, {label: '显示全部', value: -1}],
                        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
                        perfect: true
                    },
                    //导出参数
                    exportConfig: {
                        // 默认选中类型
                        type: 'xlsx',
                        filename: "基本型水平对比",
                        sheetName: "Sheet1",
                        //默认选中分组
                        isColgroup: true,
                        //默认选中样式
                        useStyle: true,
                        original: false,
                        // 局部自定义类型
                        types: ['xlsx'],
                        // 自定义数据量列表
                        modes: ['current'],

                    },
                    toolbarConfig: {
                        //放大
                        zoom: true,
                        //筛选框
                        custom: true,
                        slots: {
                            buttons: 'toolbar_buttons'
                        }
                    },
                    // 分组列头，通过 children 定义子列
                    columns: [],
                    data: []
                },
            }
        },
        async created() {
            // 获取页面功能权限
            await this.getUserAuth(this.PF_FIELD);
            this.PF_LIST = this.getPfList(this.PF_FIELD);
            this.initDate();
            this.initCustomer();
        },
        mounted() {
            this.yearChanged();
            this.getCustomerList();
            this.getData();
        },
        methods: {
            checkPf(field) {
                return this.PF_LIST.findIndex(s=>s == field) != -1;
            },
            getData: function () {
                this.yearChanged();
                this.gridOptions.loading = true
                let current= this.gridOptions.pagerConfig.currentPage
                let size = this.gridOptions.pagerConfig.pageSize
                let yearCount = this.yearCount;
                request(LIST_XSGSSALES_SALES_PAGE, METHOD.POST, {...this.searchForm, yearCount, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        if (records && records.length > 0) {
                            records.forEach(item=>{
                                item.customer = this.coverCustomer(item.customer);
                                item.ps = this.coverPs(item.ps);
                            })
                        }
                        this.gridOptions.loading = false
                        this.gridOptions.data = records
                        this.gridOptions.pagerConfig.total = parseInt(total)
                    })
            },
            getPlateList() {
                this.plateList = [];
                request(COMBO_PLATE, METHOD.POST, {}).then(res => {
                    this.plateList = res.data.data;
                })
            },
            getBlowoffList() {
                this.blowoffList = [];
                request(COMBO_BLOWOFF, METHOD.POST, {}).then(res => {
                    this.blowoffList = res.data.data;
                })
            },
            getSeriesList() {
                this.seriesList = [];
                request(COMBO_SERIES, METHOD.POST, {}).then(res => {
                    this.seriesList = res.data.data;
                })
            },
            getTypeList() {
                this.typeList = [];
                request(COMBO_TYPE, METHOD.POST, {}).then(res => {
                    this.typeList = res.data.data;
                })
            },
            getStrain1List() {
                this.strain1List = [];
                request(COMBO_STRAIN1, METHOD.POST, {}).then(res => {
                    this.strain1List = res.data.data;
                })
            },
            getStrain2List() {
                this.strain2List = [];
                request(COMBO_STRAIN2, METHOD.POST, {}).then(res => {
                    this.strain2List = res.data.data;
                })
            },
            getStrain3List() {
                this.strain3List = [];
                request(COMBO_STRAIN3, METHOD.POST, {}).then(res => {
                    this.strain3List = res.data.data;
                })
            },

            footerMethod({columns, data}){
                let footerData = [
                    columns.map((column, _columnIndex) => {
                        if (_columnIndex == 0) {
                            return '合计'
                        } else if (column.property.indexOf('yearSale') != -1) {
                            return this.sumNum(data, column.property)
                        }
                        return null
                    })
                ]
                return footerData
            },
            footerColspanMethod({$rowIndex, _columnIndex }){
                if ($rowIndex == 0) {
                    if (_columnIndex === 0) {
                        return {rowspan: 1, colspan: 4 };
                    } else if (_columnIndex < 4){
                        return {rowspan: 0, colspan: 0 };
                    }
                }

            },
            sumNum(list, field){
                let count = 0
                list.forEach(item => {
                    count += Number(item[field])
                })
                return count
            },
            doExport(options){
                console.log(options)
            },
            /** 处理查询 */
            handleSearch() {
                this.gridOptions.pagerConfig.current = 1
                this.getData()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.initDate();
                this.handleSearch();
            },
            /** 关闭对话框，表单重置 */
            closeForm() {
                this.dialog = false
                this.$refs.form.resetFields()
                this.form = {}
            },

            /** 导出按钮操作 */
            handleExportExcel() {
                let yearCount = this.yearCount;
                const searchForm ={...this.searchForm,yearCount};
                this.$confirm({
                    title: '提示',
                    content: '是否确认导出所有信息数据项?',
                    onOk:() => {
                        exportExcel(DOWNLOAD_XSGSSALES_COM,{...searchForm},'基本型水平对比数据.xlsx')
                    }
                })
            },
            handlePageChange ({ currentPage, pageSize }) {
                this.gridOptions.pagerConfig.currentPage = currentPage
                this.gridOptions.pagerConfig.pageSize = pageSize;
                this.getData()
            },
            yearChanged(){
                let year = this.searchForm.saleYear;
                if (!year) {
                    this.initDate();
                    year = this.searchForm.saleYear;
                }
                this.gridOptions.columns = [
                    {title: '序号', align: 'center',type: 'seq', width: 50,fixed:'left' },
                    {title: '板块', field: 'plate',align: 'center', width: 50},
                    {title: '排放', field: 'blowoff', align: 'center',width: 50},
                    {title: '系列', field: 'series',align: 'center', width: 80},
                    {title: '功率(PS)', field: 'ps', align: 'center',width: 40}
                ];
                this.gridOptions.columns.push({title: '品系1', field: 'type', align: 'center',width: 80})
                if (this.searchForm.strain1List.length > 0) {
                    this.gridOptions.columns.push({title: '品系2', field: 'strain1', align: 'center',width: 80})
                }
                if (this.searchForm.strain2List.length > 0) {
                    this.gridOptions.columns.push({title: '品系3', field: 'strain2', align: 'center',width: 80})
                }
                if (this.searchForm.strain3List.length > 0) {
                    this.gridOptions.columns.push({title: '品系4', field: 'strain3', align: 'center',width: 80})
                }
                this.gridOptions.columns.push({title: '客户', field: 'customer', align: 'center',width: 80, scopedSlots: {customRender: 'customer'}})

                for (let i = 1; i <= this.yearCount; i++) {
                    let item1 = {};
                    item1['field'] = 'sale' + i;
                    item1['title'] = (Number(year) - (this.yearCount - i)) + "年销量";
                    item1['width'] = 80;
                    item1['align'] = 'center';
                    this.gridOptions.columns.push(item1)
                    let item2 = {};
                    item2['field'] = 'basic_ticket_price' + i;
                    item2['title'] = (Number(year) - (this.yearCount - i)) + "年基本型开票价";
                    item2['width'] = 80;
                    item2['align'] = 'center';
                    this.gridOptions.columns.push(item2)
                    let item3 = {};
                    item3['field'] = 'basic_net_price' + i;
                    item3['title'] = (Number(year) - (this.yearCount - i)) + "年基本型实际价格";
                    item3['width'] = 80;
                    item3['align'] = 'center';
                    this.gridOptions.columns.push(item3)
                }
                this.$refs.xTable.loadColumn(this.gridOptions.columns)
            },

            getCustomerList(){
                request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
                    this.allCustomerList = res.data.data
                    this.customerList = res.data.data
                })
            },
            customerBlurs(){
                request(LIST_XSGSCUSTOMER, METHOD.POST, {
                    customer:this.searchForm.customer
                }).then(res => {
                    if(res.data.data.length>0){
                        if(this.searchForm.customer!=null){
                            if(!this.searchForm.customer.split(" ").join("").length == 0){
                                this.$set(this.searchForm,"customerName",res.data.data[0].name)
                            }
                        }
                    }else{
                        this.$message.info('此客户号没找到信息')
                    }
                })
            },
            handleCustomerSearch(value) {
                if (value) {
                    this.customerList = this.allCustomerList.filter(s=>s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
                } else {
                    this.customerList = this.allCustomerList;
                }
            },
            handleCustomerChange(value) {
                if (!value) {
                    this.customerList = this.allCustomerList;
                }
            },
            initDate(){
                var aData = new Date();
                this.searchForm.saleYear = Number(this.$moment(aData).format('YYYY'));
            },
            async initCustomer(){
                let res = await request(LIST_XSGSCUSTOMER, METHOD.POST, {});
                this.customers = res.data.data
            },
            coverCustomer(customer){
                let list = this.customers.filter(item=>item.customer==customer);
                if (list && list.length > 0) {
                    let cust = list[0];
                    return cust.customerName;
                }
            },
            coverPs(ps){
                return ps?ps.split('.')[0]:""
            },
            filterOption (value, option) {
                return option.componentOptions.children[0].text.indexOf(value) >= 0
            },
        }
    }
</script>

<style scoped>

    /deep/.ant-form-item {
        margin: 0;
    }
    /deep/.ant-form > table > tbody > tr > td {
        border: 1px solid;
    }

    /deep/.col-blue {
        background-color: #2db7f5;
        color: #fff;
    }

    /deep/ .vxe-pager.is--perfect {
        border: 0px solid #464646;
        border-top-width: 0;
        background-color: #fff;
    }
    /deep/ .vxe-header--column{
        background-color: rgba(0, 0, 0, 0.2);
    }
    /deep/  .vxe-table--render-default .vxe-body--row.row--stripe{
        background-color: #e6fdff;
    }
    /deep/.vxe-table--render-default .vxe-body--row.row--hover{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-body--row.row--current{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-body--column.col--current{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-header--column.col--current{
        background-color: #fcce10;
    }
    /deep/ .ant-form-item-label{
        width: 80px;
    }

    /deep/ .vxe-table--footer-wrapper{
        border-top: 1.5px solid;
    }
    /deep/ .year .ant-col-16{
        width: 33.3%;
    }
    /deep/ .customer .ant-col-16{
        width: 33.3%;
    }
    /deep/ .buttonArea .ant-col-16{
        width: 100%;
    }
    /deep/ .vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
        height: 0px;
    }

    /deep/ .vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
        padding: 0px 0;
    }

    /deep/ .vxe-table--render-default.border--full .vxe-body--column, /deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/ .vxe-table--render-default.border--full .vxe-header--column {
        background-image: -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646)), -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646));
        background-image: linear-gradient(#464646, #464646), linear-gradient(#464646, #464646);
        background-repeat: no-repeat;
        background-size: 1px 100%, 100% 1px;
        background-position: 100% 0, 100% 100%;
    }


    .vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), /deep/ .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
        padding: 0px 0;

    }

    /deep/ .vxe-table--render-default .vxe-table--header-wrapper .vxe-table--header .vxe-header--row th .vxe-cell {
        padding: 0px 0;

    }

    /deep/ .vxe-table--render-default .vxe-table--body-wrapper .vxe-table--body .vxe-body--column .vxe-cell {
        padding: 0px 0;

    }
</style>
