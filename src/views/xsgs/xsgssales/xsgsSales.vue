<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="年度">
                            <a-input v-model="searchForm.saleYear"  @blur="yearChanged"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-model-item label="客户">
                        <a-select
                            style="width: 100%;"
                            v-model="searchForm.customerList"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            mode="multiple"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear='true'
                            @search="handleCustomerSearch"
                            @change="handleCustomerChange"
                        >
                          <a-select-option v-for="d in customerList" :key="d.customer">
                            {{ d.customer + '-' +d.name }}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-model-item label="系列">
                        <a-select width="width:100px" v-model="searchForm.seriesList" style="min-width: 60px"
                                  :allowClear="true"
                                  mode="multiple"
                                  @dropdownVisibleChange="getSeriesList(searchForm)">
                          <a-select-option v-for="d in seriesList" :key="d">
                            {{ d}}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="状态机">
                            <a-input v-model="searchForm.machineType"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="时间范围">
                            <a-form-item  :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                                <a-date-picker style="width: 100%" value-format="MM-DD"  format="MM-DD" placeholder="" v-model="searchForm.saleTimeBegin" />
                            </a-form-item>
                            <span :style="{ display: 'inline-block', width: '12px', textAlign: 'center' }">~</span>
                            <a-form-item :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                                <a-date-picker style="width: 100%"  value-format="MM-DD" format="MM-DD" placeholder=""  v-model="searchForm.saleTimeEnd" />
                            </a-form-item>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                      <a-form-model-item label="排放">
                        <a-select width="width:100px" v-model="searchForm.blowoffList" style="min-width: 60px"
                                  :allowClear="true"
                                  mode="multiple"
                                  @dropdownVisibleChange="getBlowoffList(searchForm)">
                          <a-select-option v-for="d in blowoffList" :key="d">
                            {{ d}}
                          </a-select-option>
                        </a-select>
                      </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>

            <vxe-grid highlight-hover-row
                      row-id="id"
                      ref="xTable"
                      show-footer
                      highlight-current-row
                      highlight-current-column
                      :checkbox-config="{ highlight: true,reserve: true, range: true}"
                      :seq-config="{startIndex: (gridOptions.pagerConfig.currentPage - 1) * gridOptions.pagerConfig.pageSize}"
                      stripe
                      :span-method="colspanMethod"
                      v-bind="gridOptions"
                      @page-change="handlePageChange">
                <template #toolbar_buttons>
                    <a-button v-if="checkPf('SALES_SEARCH_EXPORT')" style="float: right;margin-right: 5px" type="primary" icon="download" @click="handleExportExcel">导出</a-button>
                    <a-col :span="8">
                      <a-form-model-item>
                        <a-button type="primary" style="margin: 0 8px" @click="handleSearch">
                          <a-icon type="search"/>
                          查询
                        </a-button>
                        <a-button type="primary" style="margin: 0 8px" @click="resetQuery">
                          <a-icon type="reload"/>
                          重置
                        </a-button>
                      </a-form-model-item>
                    </a-col>
                </template>
            </vxe-grid>
        </a-card>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {
        LIST_XSGSSALES_PAGE,
        DOWNLOAD_XSGSSALES,
        LIST_XSGSCUSTOMER,GET_BLOWOFF,GET_SERIES
    } from "@/services/api/xsgs";
    import {METHOD, request, exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie'

    export default {
        name: "xsgsSales",
        data() {
            return {
                hasAuth,
                Cookie,
                loading: false,
                dialog: false,
                // 功能权限关键字
                PF_FIELD:"PRICE_LIBRARY_SEARCH,SALES_SEARCH",
                PF_LIST:[],
                confirmLoading: false,
                yearCount: 3,
                customerList:[],
                allCustomerList:[],
                searchForm: {
                    saleYear:null,
                    saleTimeBegin:null,
                    saleTimeEnd:null,
                    blowoffList:[],
                    seriesList:[],
                },
                form: {},
                activeKey: ["1"],
                gridOptions: {
                    border: true,
                    resizable: true,
                    keepSource: true,
                    showOverflow: true,
                    loading: false,
                    pagerConfig: {
                        total: 0,
                        currentPage: 1,
                        pageSize: 10,
                        pageSizes: [10, 20, 50],
                        layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
                        perfect: true
                    },
                    toolbarConfig: {
                        //放大
                        zoom: true,
                        slots: {
                            buttons: 'toolbar_buttons'
                        }
                    },
                    // 分组列头，通过 children 定义子列
                    columns: [],
                    data: []
                },
                blowoffList:[],
                seriesList:[],
            }
        },
        async created() {
            // 获取页面功能权限
            await this.getUserAuth(this.PF_FIELD);
            this.PF_LIST = this.getPfList(this.PF_FIELD);

            this.initDate()
        },
        mounted() {
            this.yearChanged();
            this.getData();
            this.getCustomerList();
        },
        methods: {
            checkPf(field) {
                return this.PF_LIST.findIndex(s=>s == field) != -1;
            },
            getData: function () {
                this.yearChanged();
                this.gridOptions.loading = true
                let current= this.gridOptions.pagerConfig.currentPage
                let size = this.gridOptions.pagerConfig.pageSize
                let yearCount = this.yearCount;
                if (!this.searchForm.saleTimeBegin && !this.searchForm.saleTimeEnd) {
                    var aData = new Date();
                    this.searchForm.saleTimeBegin = this.$moment(aData).format('MM-DD');
                    this.searchForm.saleTimeEnd = this.$moment(aData).format('MM-DD');
                } else if (this.searchForm.saleTimeBegin && !this.searchForm.saleTimeEnd) {
                    this.searchForm.saleTimeEnd = this.searchForm.saleTimeBegin
                } else if (!this.searchForm.saleTimeBegin && this.searchForm.saleTimeEnd) {
                    this.searchForm.saleTimeBegin = this.searchForm.saleTimeEnd
                }
                request(LIST_XSGSSALES_PAGE, METHOD.POST, {...this.searchForm, yearCount, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        this.gridOptions.loading = false
                        this.gridOptions.data = records
                        this.gridOptions.pagerConfig.total = parseInt(total)
                    })
            },
            colspanMethod({row,$columnIndex,$rowIndex}){
                if (row['series'] == '合计') {
                    if ($columnIndex == 0) {
                        row['seqNum'] = '总合计';
                        return {rowspan: 1, colspan: 4 };
                    } else if ($columnIndex < 4) {
                        return {rowspan: 1, colspan: 0 };
                    }
                } else {
                    if ($columnIndex == 0) {
                        row['seqNum'] = $rowIndex + 1;
                    }
                    return {rowspan: 1, colspan: 1 };
                }
            },
            sumNum(list, field){
                let count = 0
                list.forEach(item => {
                    count += Number(item[field])
                })
                return count
            },
            doExport(options){
                console.log(options)
            },
            /** 处理查询 */
            handleSearch() {
                this.gridOptions.pagerConfig.current = 1
                this.getData()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.searchForm = {}
                this.initDate();
                this.handleSearch();
            },
            /** 关闭对话框，表单重置 */
            closeForm() {
                this.dialog = false
                this.$refs.form.resetFields()
                this.form = {}
            },

            /** 导出按钮操作 */
            handleExportExcel() {
                let yearCount = this.yearCount;
                const searchForm ={...this.searchForm,yearCount};
                this.$confirm({
                    title: '提示',
                    content: '是否确认导出所有信息数据项?',
                    onOk:() => {
                        exportExcel(DOWNLOAD_XSGSSALES,{...searchForm},'销量数据.xlsx')
                    }
                })
            },
            handlePageChange ({ currentPage, pageSize }) {
                this.gridOptions.pagerConfig.currentPage = currentPage
                this.gridOptions.pagerConfig.pageSize = pageSize;
                this.getData()
            },
            yearChanged(){
                let year = this.searchForm.saleYear;
                if (!year) {
                    var aData = new Date();
                    this.searchForm.saleYear = Number(this.$moment(aData).format('YYYY'));
                    year = this.searchForm.saleYear;
                }
                this.gridOptions.columns = [
                    {title: '序号', align: 'center',field: 'seqNum', width: 50,fixed:'left' },
                    {title: '系列', field: 'series', width: 150},
                    {title: '排放', field: 'blowoff', width: 150},
                    {title: '产品型号', field: 'productModel'},
                    {title: '状态机', field: 'machineType', width: 150},
                ];
                for (let i = 1; i <= this.yearCount; i++) {
                    let item = {};
                    item['field'] = 'yearSale' + i;
                    item['title'] = (Number(year) - (this.yearCount - i)) + "年销量";
                    item['width'] = 120;
                    this.gridOptions.columns.push(item)
                }
                this.$refs.xTable.loadColumn(this.gridOptions.columns)
            },
            getCustomerList() {
              request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
                const map = new Map()
                this.allCustomerList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1))
                this.customerList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1))
              })
            },
            handleCustomerSearch(value) {
              if (value) {
                this.customerList = this.allCustomerList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
              } else {
                this.customerList = this.allCustomerList;
              }
            },
            handleCustomerChange(value) {
              if (!value) {
                this.customerList = this.allCustomerList;
              }
            },
            initDate(){
                var aData = new Date();
                this.searchForm.saleYear = Number(this.$moment(aData).format('YYYY'));
                this.searchForm.saleTimeBegin = this.$moment(aData).format('MM-DD');
                this.searchForm.saleTimeEnd = this.$moment(aData).format('MM-DD');
                this.yearChanged();
            },
            /** 排放选择 **/
            getBlowoffList() {
              this.blowoffList = [];
              request(GET_BLOWOFF, METHOD.POST, {}).then(res => {
                this.blowoffList = res.data.data;
              })
            },
            /** 系列选择 **/
            getSeriesList() {
              this.seriesList = [];
              request(GET_SERIES, METHOD.POST, {}).then(res => {
                this.seriesList = res.data.data;
              })
            },
        }
    }
</script>

<style scoped>

    /deep/.ant-form-item {
        margin: 0;
    }
    /deep/.ant-form > table > tbody > tr > td {
        border: 1px solid;
    }

    /deep/.col-blue {
        background-color: #2db7f5;
        color: #fff;
    }

    /deep/.vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
        height: 0px;
    }
    /deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
        padding: 0px 0;
    }
    /deep/.vxe-table--render-default.border--full .vxe-body--column,/deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/.vxe-table--render-default.border--full .vxe-header--column {
        background-image: -webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646)),-webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646));
        background-image: linear-gradient(#464646,#464646),linear-gradient(#464646,#464646);
        background-repeat: no-repeat;
        background-size: 1px 100%,100% 1px;
        background-position: 100% 0,100% 100%;
    }

    /deep/.vxe-table--render-default.border--default
    .vxe-table--header-wrapper,
    .vxe-table--render-default.border--full
    .vxe-table--header-wrapper,
    .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
        background-color: #f8f8f9;
    }

    /deep/ .vxe-pager.is--perfect {
        border: 0px solid #464646;
        border-top-width: 0;
        background-color: #fff;
    }
    /deep/ .vxe-header--column{
        background-color: rgba(0, 0, 0, 0.2);
    }
    /deep/  .vxe-table--render-default .vxe-body--row.row--stripe{
        background-color: #e6fdff;
    }
    /deep/.vxe-table--render-default .vxe-body--row.row--hover{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-body--row.row--current{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-body--column.col--current{
        background-color: #fcce10;
    }
    /deep/.vxe-table--render-default .vxe-header--column.col--current{
        background-color: #fcce10;
    }
    /deep/ .ant-form-item-label{
        width: 80px;
    }

    /deep/ .vxe-table--footer-wrapper{
        border-top: 1.5px solid;
    }
</style>
