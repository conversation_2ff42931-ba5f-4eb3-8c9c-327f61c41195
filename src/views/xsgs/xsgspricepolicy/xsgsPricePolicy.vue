<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <table style="width: 100%; border: 1px solid #f0f0f0;" >
                    <tbody class="ant-table">
                    <tr>
                        <td><a-form-model-item label="计算年度" /></td>
                        <td><a-input v-model="searchForm.calcYear"/></td>
                        <td><a-form-model-item label="客户号" /></td>
                        <td><a-input v-model="searchForm.customerNo"  @blur="customerBlurs" /></td>
                        <td><a-form-model-item label="客户名称" /></td>
                        <td>
                            <a-select
                                    style="min-width: 250px"
                                    v-model="searchForm.customerName"
                                    show-search
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :allowClear='true'
                                    :not-found-content="null"
                                    @search="handleCustomerSearch"
                                    @change="handleCustomerChange"
                            >
                                <a-select-option v-for="d in customerList" :key="d.name">
                                    {{ d.name }}
                                </a-select-option>
                            </a-select>
                            <!--              <a-input v-model="searchForm.name"/>-->
                        </td>

                    </tr>
                    <tr>
                        <td><a-form-model-item label="政策名称" /></td>
                        <td colspan="2"><a-input v-model="searchForm.polityName"/></td>
                        <td colspan="3">
                            <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
                            <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <a-row>
                    <a-col :span="24">
                        <a-form-model-item>
                            <a-button v-if="checkPf('PRICE_POLICY_ADD')" type="primary" style="margin: 0 8px" @click="handleEdit"><a-icon type="plus"/>新增</a-button>
                            <!--                            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>-->

                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>
            <a-table
                    :columns="columns"
                    rowKey="id"
                    :pagination="pagination"
                    :data-source="data"
                    :scroll="{ x: 1000 }"
                    size="small"
                    bordered
                    :customRow="onRowClick"
                    :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{index+1}}
                </template>
                <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PRICE_POLICY_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PRICE_POLICY_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PRICE_POLICY_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
            </a-table>
        </a-card>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {LIST_XSGSPRICEPOLICY_PAGE,DELETE_XSGSPRICEPOLICY,DOWNLOAD_XSGSPRICEPOLICY,LIST_XSGSCUSTOMER} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import Cookie from 'js-cookie'
    export default {
        name: "xsgsPricePolicy.vue",
        data() {
            return {
                hasAuth,
                Cookie,
                loading: false,
                dialog: false,
                confirmLoading: false,
                // 功能权限关键字
                PF_FIELD:"BUSINESS_POLICY_MANAGER,PRICE_POLICY",
                PF_LIST:[],
                searchForm: {
                },
                customerList:[],
                form: {},
                pagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                            total / this.pagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onSizeChange(current, size) // 改变每页数量时更新显示
                },
                data: [],
                columns: [
                    {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '计算年度', dataIndex: 'calcYear', width:80},
                    {title: '客户号', dataIndex: 'customerNo', width:80},
                    {title: '客户名称', dataIndex: 'customerName'},
                    {title: '政策名称', dataIndex: 'polityName', width:250},
                    {title: '操作', fixed: 'right', width: 140, scopedSlots: {customRender: 'action'}}
                ],
                rules: {
                }
            }
        },
        async created() {
            // 获取页面功能权限
            await this.getUserAuth(this.PF_FIELD);
            this.PF_LIST = this.getPfList(this.PF_FIELD);
        },
        mounted() {
            this.getData()
            this.checkName()
        },
        methods: {
            checkPf(field) {
                return this.PF_LIST.findIndex(s=>s == field) != -1;
            },
            customerBlurs(){
                if(this.searchForm.customerNo) {
                    request(LIST_XSGSCUSTOMER, METHOD.POST, {
                        customer:this.searchForm.customerNo
                    }).then(res => {
                        if(res.data.data.length>0){
                            this.$set(this.searchForm,"customerName",res.data.data[0].name)
                        }else{
                            this.$message.info('此客户号没找到信息')
                        }
                    })
                } else {
                    this.searchForm.customerName = null
                }
            },
            handleCustomerSearch(value) {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {
                    name:value
                }).then(res => {
                    this.customerList = res.data.data
                })
            },
            handleCustomerChange(value) {
                this.customerList.forEach(item =>{
                    if(item.name == value){
                        this.searchForm.customerNo = item.customer
                        this.searchForm.customerName = item.name
                    }
                })
            },
            getData: function () {
                this.loading = true
                let {current, size} = this.pagination
                request(LIST_XSGSPRICEPOLICY_PAGE, METHOD.GET, {...this.searchForm, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        this.loading = false
                        this.data = records
                        this.pagination.total = parseInt(total)
                    })
            },
            handleDelete(record) {
                this.$confirm({
                    content: `是否确认删除政策【 ${record.polityName} 】？`,
                    onOk: () => {
                        this.loading = true
                        request(DELETE_XSGSPRICEPOLICY + record.id, METHOD.DELETE)
                            .then((res) => {
                                if (res.data.code==-1) {
                                    this.$message.error(res.data.msg);
                                } else {
                                    this.$message.info('删除成功')
                                }
                                this.getData()
                            }).catch(() => {
                            this.loading = false
                        })
                    }
                });
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.searchForm = {
                    customerName:null,
                    customerNo:null,
                }
                this.handleSearch();
            },
            handleEdit(record) {
                if (record.id) {
                    this.$router.push({path:'xsgs_price_policy_detail/' + record.id, props: true})
                } else {
                    this.$router.push({path:'xsgs_price_policy_detail/:id', props: true})
                }
            },
            handleSearch() {
                this.pagination.current = 1
                this.getData()
            },
            closeForm() {
                this.dialog = false
                this.$refs.form.resetFields()
                this.form = {}
            },
            onPageChange(page, size) {
                this.pagination.current = page;
                this.pagination.size = size.toString();
                this.getData();
            },
            onSizeChange(current, size) {
                this.pagination.current = 1;
                this.pagination.size = size.toString();
                this.getData();
            },

            /** 导出按钮操作 */
            handleExportExcel() {
                const searchForm =this.searchForm
                this.$confirm({
                    title: '提示',
                    content: '是否确认导出所有信息数据项?',
                    onOk:() => {
                        let option = ""
                        if (searchForm.customerNo != null) {
                            option = "?customerNo=" + searchForm.customerNo
                        }

                        if (searchForm.customerName != null) {
                            if (option != "") {
                                option += "&"
                            } else {
                                option += "?"
                            }
                            option += "customerName=" + searchForm.customerName
                        }
                        if (searchForm.calcYear != null) {
                            if (option != "") {
                                option += "&"
                            } else {
                                option += "?"
                            }
                            option += "calcYear=" + searchForm.calcYear
                        }

                        window.open(decodeURI(DOWNLOAD_XSGSPRICEPOLICY + option))
                    }
                })
            },



        }
    }
</script>

<style scoped>
    .ant-input-number{
        width: 100%;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }
    /*/deep/  样式穿透
    */
    /deep/.ant-col-8{
        display: block;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 100%;
    }
    /deep/.ant-form-item {
        margin: 0;
    }
    /deep/.ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }
</style>
