<template>
    <div>
        <a-table
                :columns="funcType=='F1'?func1Columns:func2Columns"
                rowKey="id"
                :pagination="false"
                :data-source="funcData"
                :scroll="{ x: 520 }"
                size="small"
                bordered
                :customRow="onRowClick"
                :loading="loading">
            <template slot="num" slot-scope="text, record, index">
                {{index+1}}
            </template>
            <template slot="plate" slot-scope="text, record">
                <a-select style="width: 100%" v-model="record.plate" :disabled="editAbled"
                          :allowClear="true"
                          @select="changedPlate(record)">
                    <a-select-option value="卡车">卡车</a-select-option>
                    <a-select-option value="客车">客车</a-select-option>
                </a-select>
            </template>
            <template slot="blowoff" slot-scope="text, record">
                <a-select style="width: 100%" v-model="record.blowoff" :disabled="editAbled"
                          :allowClear="true"
                          @select="changedBlowoff(record)"
                          @dropdownVisibleChange="getBlowoffList(record)">
                    <a-select-option v-for="d in blowoffList" :key="d">
                        {{ d}}
                    </a-select-option>
                </a-select>
            </template>
            <template slot="series" slot-scope="text, record">
                <a-select style="width: 100%" v-model="record.seriesArray"
                          @dropdownVisibleChange="onclickSeries(record)"
                          showSearch
                          :disabled="editAbled"
                          mode="multiple"
                          @select="changedSeries(record)"
                          :filterOption="filterOption">
                    <a-select-option v-for="sty in seriesList" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>
            <template slot="productModel" slot-scope="text, record">
                <a-select style="width: 100%" v-model="record.productModels"
                          @dropdownVisibleChange="onclickProductModel(record)"
                          showSearch
                          :disabled="editAbled"
                          mode="multiple"
                          :filterOption="filterOption">
                    <a-select-option v-for="sty in productModelList" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>
            <template slot="machineType" slot-scope="text, record">
                <a-select style="width: 100%" v-model="record.machineTypes"
                          @dropdownVisibleChange="onclickMachineType(record)"
                          showSearch
                          :disabled="editAbled"
                          mode="multiple"
                          :filterOption="filterOption">
                    <a-select-option v-for="sty in machineTypeList" :key="sty">
                        {{ sty }}
                    </a-select-option>
                </a-select>
            </template>
            <template slot="price" slot-scope="text, record">
                <a-input-number
                        v-model="record.price"
                        :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                        :precision="2"
                        :step="0.01"
                        :min="0"
                        :disabled="editAbled"
                        style="width: 100%"

                />
            </template>
            <template slot="action" slot-scope="record">
                <a-button type="primary" :disabled="editAbled" @click="handleDelete(record)">删除</a-button>

            </template>
        </a-table>

        <div>
            <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus" :disabled="editAbled"
                      @click="newMember">新增
            </a-button>
        </div>
        <div style="text-align: right;margin-top: 10px;" :loading="loading">
            <label style="float: left;font-weight: bold;font-size: 16px">{{caverState(funcState)}}</label>
            <label style="float: left;font-weight: bold;font-size: 16px">{{lastLog}}</label>
            <a-button :disabled="funcState !='FINISH' && funcState !='REVERROR'" style="margin-left: 10px" type="primary"
                      @click="handleCancel">
                撤销
            </a-button>
            <a-button :disabled="editAbled" style="margin-left: 10px" type="primary"
                      @click="handleSaveAndCalc">保存计算
            </a-button>
            <a-button style="margin-left: 10px" type="primary" @click="showLog">查看日志</a-button>
        </div>
    </div>

</template>

<script>
    import {
        LIST_XSGSPRICEPOLICYDETAIL,
        getUuid,
        COMBO_BLOWOFF,
        COMBO_SERIES,
        COMBO_PRODUCTMODEL,
        COMBO_MACHINETYPE,
        DELETE_XSGSPRICEPOLICYDETAIL
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";

    export default {
        name: "func1",
        props: {
            policyForm: Object,
            funcType: String
        },
        data() {
            return {
                funcData: [],
                loading: false,
                form: {},
                plateList: [],
                blowoffList: [],
                seriesList: [],
                editAbled:false,
                funcState: 'WAIT',
                lastLog: '',
                productModelList: [],
                machineTypeList:[],
                funcPagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.funcPagination.current}/${Math.ceil(
                            total / this.funcPagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onOptionalcPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onOptionalcSizeChange(current, size) // 改变每页数量时更新显示
                },
                func1Columns: [
                    {
                        title: '序号', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '板块', dataIndex: 'plate', width: 100, scopedSlots: {customRender: 'plate'}},
                    {title: '排放', dataIndex: 'blowoff', width: 100, scopedSlots: {customRender: 'blowoff'}},
                    {title: '系列', dataIndex: 'seriesArray', scopedSlots: {customRender: 'series'}},
                    {title: '产品型号', dataIndex: 'productModels', scopedSlots: {customRender: 'productModel'}},
                    {title: '状态机', dataIndex: 'machineTypes', scopedSlots: {customRender: 'machineType'}},
                    {title: '降价', dataIndex: 'price', width: 100, scopedSlots: {customRender: 'price'}},
                    {title: '功能', width: 100, align: 'center', scopedSlots: {customRender: 'action'}},
                ],
                func2Columns: [
                    {
                        title: '序号', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '板块', dataIndex: 'plate', width: 100, scopedSlots: {customRender: 'plate'}},
                    {title: '排放', dataIndex: 'blowoff', width: 100, scopedSlots: {customRender: 'blowoff'}},
                    {title: '系列', dataIndex: 'seriesArray', scopedSlots: {customRender: 'series'}},
                    {title: '产品型号', dataIndex: 'productModels', scopedSlots: {customRender: 'productModel'}},
                    {title: '状态机', dataIndex: 'machineTypes', scopedSlots: {customRender: 'machineType'}},
                    {title: '功能', width: 100, align: 'center', scopedSlots: {customRender: 'action'}},
                ],
            }
        },
        mounted() {
            this.checkName();
            this.setDisabled();
            this.getData();
        },
        created() {
        },
        methods: {
            getData() {
                if (this.policyForm.id != ":id") {
                    this.loading = true;
                    request(LIST_XSGSPRICEPOLICYDETAIL, METHOD.POST, {
                        policyId: this.policyForm.id,
                        funcType: this.funcType
                    }).then(res => {
                        this.funcData = res.data.data;
                        this.loading = false;
                    })
                }
            },
            changedPlate(record){
                this.seriesList = [];
                this.productModelList = [];
                this.machineTypeList = [];
                this.$set(record,"seriesArray",[])
                this.$set(record,"productModels",[])
                this.$set(record,"machineTypes",[])
            },
            getBlowoffList() {
                this.$nextTick(() => {
                    if (this.blowoffList.length == 0 && this.policyForm.calcYear) {
                        request(COMBO_BLOWOFF, METHOD.POST, {
                            customer:this.policyForm.customerNo,
                            year: this.policyForm.calcYear + ''
                        }).then(res => {
                            this.blowoffList = res.data.data;
                        })
                    }
                })
            },
            changedBlowoff(record){
                this.seriesList = [];
                this.productModelList = [];
                this.machineTypeList = [];
                this.$set(record,"seriesArray",[])
                this.$set(record,"productModels",[])
                this.$set(record,"machineTypes",[])

            },
            changedSeries(record){
                this.$set(record,"productModels",[])
                this.$set(record,"machineTypes",[])
                this.machineTypeList = [];
                this.productModelList = [];

            },
            changedProductModel(record){
                this.$set(record,"machineTypes",[])
                this.machineTypeList = [];

            },
            // 将输入的内容与显示的内容进行匹配
            filterOption(value, option) {
                return option.componentOptions.children[0].text.indexOf(value) >= 0
            },
            //点击获取系列
            onclickSeries(record) {
                this.seriesList = [];
                request(COMBO_SERIES, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    blowoff: record.blowoff,
                    plate: record.plate
                }).then(res => {
                    this.seriesList = res.data.data;
                })

            },
            onclickProductModel(record) {
                this.productModelList = [];
                request(COMBO_PRODUCTMODEL, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    blowoff: record.blowoff,
                    plate: record.plate,
                    series: record.seriesArray
                }).then(res => {
                    this.productModelList = res.data.data;
                })
            },
            onclickMachineType(record) {
                this.machineTypeList = [];
                request(COMBO_MACHINETYPE, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    blowoff: record.blowoff,
                    plate: record.plate,
                    series: record.seriesArray,
                    productModel: record.productModels
                }).then(res => {
                    this.machineTypeList = res.data.data;
                })
            },
            handleDelete(record) {
                //删除
                const newData = [...this.funcData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    let that = this;
                    this.$confirm({
                        content: `是否删除？`,
                        onOk: () => {
                            const newDatas = newData.filter(item => item.id !== target.id)
                            that.funcData = newDatas
                            this.loading = true
                            request(DELETE_XSGSPRICEPOLICYDETAIL + record.id, METHOD.DELETE)
                                .then(() => {
                                    this.$message.info('删除成功')
                                    this.loading = false
                                }).catch(() => {
                                this.loading = false
                            })
                        },
                        onCancel() {

                        }
                    });
                }
            },
            //新增
            newMember() {
                request(getUuid, METHOD.GET).then(res => {
                    const length = this.funcPagination.total;
                    const newData = [...this.funcData]
                    newData.push({
                        id: length === 0 ? res.data.data : res.data.data,
                        policyId: this.policyForm.id,
                        funcType: this.funcType,
                        blowoff: '',
                        price: null,
                        calState: 'WAIT',
                    })
                    //默认进入编辑状态

                    newData.forEach(item => {
                        item.editable = true;
                    })
                    const target = newData.filter(item => length === 0 ? res.data.data : res.data.data === item.id)[0];
                    if (target) {
                        target.editable = true;
                        this.funcData = newData;
                    }
                    this.funcPagination.total = parseInt(newData.length);
                })
            },
            handleCancel() {
                let that = this;
                this.$confirm({
                    content: `是否确认撤销上次计算数据？`,
                    onOk: () => {
                        that.loading = true;
                        that.funcState = 'REVOKEING';
                        that. setDisabled();
                        that.$emit('cancelCalc', this.funcType)
                    },
                    onCancel() {

                    }
                });
            },
            handleSaveAndCalc() {
                let that = this;
                this.$confirm({
                    content: `是否确认保存计算本次数据？`,
                    onOk: () => {
                        if (!that.checkPrice()) {
                            return;
                        }
                        let realData = this.funcData.filter(item => item.blowoff ||item.plate || (item.seriesArray && item.seriesArray.length > 0) || (item.productModels && item.productModels.length > 0)|| (item.machineTypes && item.machineTypes.length > 0))
                        if (realData && realData.length > 0) {
                            that.policyForm.funcData = realData;
                            that.policyForm.funcType = that.funcType
                            that.$emit('saveAndCalc', that.policyForm)
                        } else {
                            that.$message.error("请添加有效的计算条件")
                        }
                    },
                    onCancel() {

                    }
                });


            },
            checkPrice() {
                if (this.funcType == 'F2') {
                    return true;
                }
                // 验空
                let errData = this.funcData.filter((item, index) => {
                    if (!item.price || item.price == 0) {
                        item.sort = (index + 1);
                        return true;
                    }

                });
                if (errData && errData.length > 0) {
                    let mess = "";
                    errData.forEach((item, index) => {
                        mess += "第" + (item.sort) + "行\n\t";
                    });
                    this.$message.error("存在降价值为空的数据：\n\t" + mess)
                    return false;
                }
                return true;
            },
            showLog() {
                this.$emit('showLogs', this.funcType)
            },
            setFuncState(funcState, lastLog) {
                this.funcState = funcState;
                if (lastLog) {
                    if (funcState == 'RUNNING' || funcState == 'IMPORTING' || funcState == 'REVOKEING') {
                        this.lastLog = '（'+lastLog+'）';
                    } else {
                        this.lastLog = '';
                    }
                } else {
                    this.lastLog = '';
                }
                this. setDisabled();
            },
            showLoading() {
                this.loading = true;
            },
            closeLoading() {
                this.loading = false;
            },
            setDisabled(){
                this.editAbled = this.funcState != 'WAIT' && this.funcState != 'ERROR';
            },
            refreshForm(policyForm){
                this.policyForm = policyForm;
                this.policyForm.policyId = this.policyForm.id;
            },
            caverState(state) {
                if (state == 'WAIT') {
                    return '待计算';
                } else if (state == 'RUNNING') {
                    return '计算中...';
                } else if (state == 'IMPORTING') {
                    return '导入中...';
                } else if (state == 'REVOKEING') {
                    return '撤销中...';
                } else if (state == 'FINISH') {
                    return '计算完成';
                } else if (state == 'ERROR'){
                    return '计算错误';
                } else if (state == 'REVERROR'){
                    return '撤销错误';
                } else {
                    return '未知';
                }
            },
        }
    }
</script>

<style scoped>

    /*/deep/.ant-table-thead >tr >th{*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

    /*/deep/.ant-table-tbody >tr >td{*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

    /*/deep/.ant-form >table >tbody >tr >td {*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

</style>
