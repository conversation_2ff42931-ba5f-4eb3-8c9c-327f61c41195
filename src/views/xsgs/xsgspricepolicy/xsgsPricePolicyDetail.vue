<template>
    <div>
        <a-card>
            <a-form-model ref="policyForm" :model="policyForm" :rules="policyRules" :labelCol="{span: 8}"
                          :wrapperCol="{span: 16}">
                <table style="width: 100%; border: 1px solid #f0f0f0;">
                    <tbody class="ant-table">
                    <tr>
                        <td width="90">
                            <a-form-model-item label="计算年度" prop="calcYear"/>
                        </td>
                        <td style="width: 90px">
                            <!--                        <a-input-number :min="0" step="1"  v-model="policyForm.calcYear" />-->
                            <a-select
                                    v-model="policyForm.calcYear"
                                    show-search
                                    placeholder=""
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :not-found-content="null"
                                    @blur="checkField('calcYear')"
                            >
                                <a-select-option v-for="d in yearList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td width="90">
                            <a-form-model-item label="客户号" prop="customerNo"/>
                        </td>
                        <td style="width: 100px">
                            <a-input v-model="policyForm.customerNo" @blur="customerchanged"/>
                        </td>
                        <td width="90">
                            <a-form-model-item label="客户名称" prop="customerName"/>
                        </td>
                        <td style="width: 250px">
                            <a-select
                                    v-model="policyForm.customerName"
                                    show-search
                                    placeholder=""
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :not-found-content="null"
                                    @search="handleCustomerSearch"
                                    @change="handleCustomerChange"
                                    @blur="customerNamechanged"
                            >
                                <a-select-option v-for="d in customerList" :key="d.name">
                                    {{ d.name }}
                                </a-select-option>
                            </a-select>
                        </td>

                        <td width="90">
                            <a-form-model-item label="对标年度"/>
                        </td>
                        <td>{{policyForm.targetYear}}</td>
                    </tr>
                    <tr>
                        <td>
                            <a-form-model-item label="政策名称" prop="polityName"/>
                        </td>
                        <td colspan="5">
                            <a-input v-model="policyForm.polityName" @blur="checkField('polityName')"/>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </a-form-model>
            <a-tabs default-active-key="1">
                <a-tab-pane key="1" tab="功能1">
                    <a-row>
                        <func1 @saveAndCalc='saveAndCalc' @cancelCalc='cancelCalc' @showLogs='showLogs' :policyForm="policyForm"
                               funcType="F1" ref="func1"></func1>
                    </a-row>
                </a-tab-pane>
                <a-tab-pane key="2" tab="功能2" force-render>
                    <a-row>
                        <func2 @saveAndCalc='saveAndCalc' @cancelCalc='cancelCalc' @showLogs='showLogs' :policyForm="policyForm"
                               funcType="F2" ref="func2"></func2>
                    </a-row>
                </a-tab-pane>
                <a-tab-pane key="3" tab="功能3" force-render>
                    <p><label style="font-weight: bold;font-size: 16px">{{caverState(policyForm.funcState3)}}</label></p>
                    <p>提示：批量导入基本型开票价、整机开票价功能。针对于原则过于复杂的情况，需系统外计算后导入。</p>
                    <div style="margin-top: 50px;" :loading="loading">

                        <a-button style="margin-right: 20px" type="primary" @click="handleTemplate()">下载模板</a-button>
                        <a-upload
                                name="file"
                                accept=".xlsx"
                                :action="UPLOAD_XSGSPRICEPOLICY"
                                :data="policyForm"
                                :headers="{'Authorization':Cookie.get('Authorization')}"
                                :showUploadList="false"
                                :disabled="policyForm.funcState3 !='WAIT' && policyForm.funcState3 !='ERROR'"
                                style="margin: 0 8px"
                                @change="handleUpload">
                            <a-button :disabled="policyForm.funcState3 !='WAIT'" style="margin-right: 20px" type="primary">批量导入</a-button>
                        </a-upload>
                        <a-button :disabled="policyForm.funcState3 !='FINISH' && policyForm.funcState3 !='REVERROR'" @click="cancelImport" style="margin-right: 20px" type="primary">撤销导入</a-button>
                        <a-button style="margin-right: 20px" type="primary" @click="showLogs('F3')">查看日志</a-button>
                    </div>
                </a-tab-pane>
            </a-tabs>
        </a-card>
        <a-modal
                title="执行日志"
                :visible="dialog"
                width="80%"
                @ok="dialog = false"
                @cancel="dialog = false">
            <a-form-model ref="logsForm"  :labelCol="{span: 4}" :wrapperCol="{span: 20}" style="height: auto;">
                <a-row class="form-row">
                </a-row>
                <a-table
                        :columns="columns2"
                        rowKey="id"
                        :pagination="pagination2"
                        :data-source="data2"
                        size="middle"
                        :customRow="onRowClick"
                        :loading="loading">
                    <!-- 序号 -->
                    <template slot="num" slot-scope="text, record, index">
                        {{index+1}}
                    </template>
                    <template slot="createTime" slot-scope="text, record">
                      {{dateFormat(record.createTime)}}
                    </template>
                </a-table>
            </a-form-model>

        </a-modal>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {
        LIST_XSGSCUSTOMER,
        GET_XSGSPRICEPOLICY_ID,
        COMBO_YEAR,
        getUuid,
        SUBMIT_XSGSPRICEPOLICY,
        CALC_XSGSPRICEPOLICY,
        CANCEL_XSGSPRICEPOLICY,
        LIST_XSGSPRICEPOLICYLOG_PAGE,
        UPLOAD_XSGSPRICEPOLICY,
        TEMPLATE_XSGSPRICEPOLICYDETAIL,
        DOWNLOAD_XSGSPRICEPOLICYDETAIL
    } from "@/services/api/xsgs";
    import func1 from "./func";
    import func2 from "./func";
    import {METHOD, request,exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie'
    import {dateFormat} from "@/utils/dateUtil";

    export default {
        name: "xsgsPricePolicyDetail.vue",
        components: {
            func1,
            func2,
        },
        data() {
            return {
                hasAuth,
                Cookie,
                UPLOAD_XSGSPRICEPOLICY,
                loading: false,
                dialog: false,
                checkOk:false,
                yearList: [],
                policyForm: {
                    id: this.$route.params.id,
                    customerName: null,
                    customerNo: null,
                    calcYear: null,
                    polityName: null,
                    targetYear: null,
                    funcType: null,
                    funcState1: 'WAIT',
                    funcState2: 'WAIT',
                    funcState3: 'WAIT',
                    funcData: []
                },
                customerList: [],
                timeInterval: null,
                isTimer: false,
                form: {},
                key: null,
                policyRules: {
                    customerNo: [{required: true, message: '客户不能为空', trigger: 'blur'}],
                    customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
                    calcYear: [{required: true, message: '年度不能为空', trigger: 'blur'}],
                    polityName: [{required: true, message: '政策名称不能为空'}],
                },
                data2: [],
                pagination2: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.pagination2.current}/${Math.ceil(
                            total / this.pagination2.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onLogPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onLogSizeChange(current, size) // 改变每页数量时更新显示
                },
                columns2: [
                    {
                        title: '序号',
                        dataIndex: 'num',
                        key: 'num',
                        width: 50,
                        align: 'center',
                        fixed: true,
                        scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '操作人', dataIndex: 'createUser'},
                    {title: '内容', dataIndex: 'content'},
                    {title: '操作时间', dataIndex: 'createTime', scopedSlots: {customRender: 'createTime'}},
                ],
            }
        },
        created() {
            // 获取政策信息
            this.getData();
        },
        mounted() {
            // 获取年度列表
            this.getYearList();
        },
        destroyed() {
            clearInterval(this.timeInterval);
        },
        methods: {
            getData: function () {
                if (this.policyForm.id == ':id') {
                    // 新增时，获取政策id
                    request(getUuid, METHOD.GET).then(res => {
                        this.policyForm.id = res.data.data;
                        this.policyForm.calcYear = this.$moment(new Date()).format('YYYY');
                        this.policyForm.targetYear = Number(this.policyForm.calcYear) - 1;
                    })
                } else {
                    // 编辑时，获取政策信息
                    request(GET_XSGSPRICEPOLICY_ID, METHOD.GET, {id: this.policyForm.id})
                        .then(res => {
                            if (res.data.data) {
                                this.policyForm = res.data.data
                                this.policyForm.targetYear = Number(this.policyForm.calcYear) - 1;
                                this.$refs.func1.getBlowoffList();
                                this.$refs.func1.setFuncState(this.policyForm.funcState1);
                                this.$refs.func2.getBlowoffList();
                                this.$refs.func2.setFuncState(this.policyForm.funcState2);
                                // 如果存在执行中的状态，启动定时刷新
                                if (res.data.data.funcState1 == 'RUNNING'
                                    || res.data.data.funcState1 == 'REVOKEING'
                                    || res.data.data.funcState2 == 'RUNNING'
                                    || res.data.data.funcState2 == 'REVOKEING'
                                    || res.data.data.funcState3 == 'RUNNING'
                                    || res.data.data.funcState3 == 'REVOKEING'
                                    || res.data.data.funcState3 == 'IMPORTING') {
                                    this.startInterval();
                                }
                            }
                        })
                }
            },
            getLogData() {
                let {current, size} = this.pagination2;
                let searchForm = {
                    policyId: this.policyForm.id,
                    funcType: this.policyForm.funcType
                }
                this.data2 = [];
                this.pagination2.total = 0;
                request(LIST_XSGSPRICEPOLICYLOG_PAGE, METHOD.GET, {...searchForm, current, size})
                    .then(res => {
                        if (res.data.data) {
                            this.loading = false;
                            const {records, total} = res.data.data
                            this.data2 = records
                            this.pagination2.total = parseInt(total)
                        }
                    })
            },
            getYearList() {
                request(COMBO_YEAR, METHOD.POST, {})
                    .then(res => {
                        if (res.data.data) {
                            this.yearList = res.data.data;
                        }
                    })
            },
            formatTime(row, column) {
                const date = new Date(row[column.property])
                return date.getFullYear() + '年' +
                    date.getMonth() + '月' +
                    date.getDate() + '日 ' +
                    date.getHours()
                    + ':' + date.getMinutes()
            },
            customerNamechanged() {
                this.checkField('customerName');
            },
            handleCustomerSearch(value) {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {
                    name: value
                }).then(res => {
                    this.customerList = res.data.data
                })

            },
            handleCustomerChange(value) {
                this.customerList.forEach(item => {
                    if (item.name == value) {
                        this.policyForm.customerNo = item.customer
                    }
                })
            },
            customerchanged() {
                this.checkField('customerNo')
                if (this.policyForm.customerNo) {
                    request(LIST_XSGSCUSTOMER, METHOD.POST, {
                        customer: this.policyForm.customerNo
                    }).then(res => {
                        if (res.data.data.length > 0) {
                            this.policyForm.customerName = res.data.data[0].name
                            this.checkField('customerName')
                        } else {
                            this.$message.info('此客户号没找到信息')
                        }
                    })
                } else {
                    this.policyForm.customerName = null
                }
            },
            onLogPageChange(page, size) {
                this.pagination2.current = page;
                this.pagination2.size = size;
                this.getLogData();
            },
            onLogSizeChange(current, size) {
                this.pagination2.current = 1;
                this.pagination2.size = size;
                this.getLogData();
            },
            saveAndCalc(saveForm) {
                let that = this;
                const funcType = saveForm.funcType;
                this.$refs.policyForm.validate(valid => {
                    if (valid) {
                        if (funcType == 'F1') {
                            that.$refs.func1.setFuncState('RUNNING')
                            that.$refs.func1.showLoading();
                        } else if (funcType == 'F2') {
                            that.$refs.func2.setFuncState('RUNNING')
                            that.$refs.func2.showLoading();
                        }
                        request(SUBMIT_XSGSPRICEPOLICY, METHOD.POST, saveForm).then(res => {
                            that.policyForm = res.data.data;
                            if (funcType == 'F1') {
                                that.$refs.func1.getData();
                            } else if (funcType == 'F2') {
                                that.$refs.func2.getData();
                            }
                            // 计算
                            request(CALC_XSGSPRICEPOLICY, METHOD.POST, that.policyForm).then(res => {
                                if (funcType == 'F1') {
                                    that.$refs.func1.setFuncState(res.data.data.funcState1)
                                } else if (funcType == 'F2') {
                                    that.$refs.func2.setFuncState(res.data.data.funcState2)
                                }
                                that.$refs.func1.closeLoading();
                                that.$refs.func2.closeLoading();
                                that.$message.info('提交成功，正在计算中！')
                                that.startInterval();
                            });
                        })
                    }
                })
            },
            cancelImport(){
                let that = this;
                this.$confirm({
                    content: `是否确认撤销上次批量导入的数据？`,
                    onOk: () => {
                        that.cancelCalc('F3')
                    },
                    onCancel() {

                    }
                });
            },
            cancelCalc(funcType) {
                this.policyForm.funcType = funcType;
                if (funcType == 'F3') {
                    this.policyForm.funcState3 = 'REVOKEING'
                }
                request(CANCEL_XSGSPRICEPOLICY, METHOD.POST, this.policyForm).then(res => {
                    if (funcType == 'F1') {
                        this.$refs.func1.showLoading();
                        this.$refs.func1.setFuncState(res.data.data.funcState1)
                    } else if (funcType == 'F2') {
                        this.$refs.func2.showLoading();
                        this.$refs.func2.setFuncState(res.data.data.funcState2)
                    } else if (funcType == 'F3') {
                        this.policyForm.funcState3 = res.data.data.funcState3;
                    }
                    this.$refs.func1.closeLoading();
                    this.$refs.func2.closeLoading();
                    this.$message.info('提交成功，正在撤销中！')
                    this.startInterval();
                })
            },
            checkForm(){
                let that = this;
                this.$refs.policyForm.validate(valid => {
                    if (valid) {
                        this.policyForm.funcState3 = "RUNNING";
                        this.loading = true;
                    }
                    return valid;
                });
            },
            handleTemplate(){
                // 导出价格政策模板
                // window.open(TEMPLATE_XSGSPRICEPOLICYDETAIL)
                exportExcel(TEMPLATE_XSGSPRICEPOLICYDETAIL, {}, "价格政策条件导入模板.xlsx")
            },
            handleUpload(info){
                this.loading = true
                if (info.file.status !== 'uploading') {
                    this.getData()
                }
                if (info.file.status === 'done') {
                    this.loading = false;
                    this.$message.success(`${info.file.name} 导入成功`);
                    this.startInterval();
                } else if (info.file.status === 'error') {
                    this.$message.error(`${info.file.name} 导入失败`);
                }
            },
            showLogs(funcType){
                this.policyForm.funcType = funcType;
                this.dialog = true;
                this.getLogData()
            },
            startInterval() {
                if (this.isTimer) {
                    return;
                }
                this.isTimer = true;
                 let timeInterval = setInterval(() => {
                    request(GET_XSGSPRICEPOLICY_ID, METHOD.GET, {id: this.policyForm.id})
                        .then(res => {
                            if (res.data.data) {
                                console.log(res.data.data);
                                this.policyForm.funcState1 = res.data.data.funcState1;
                                this.policyForm.funcState2 = res.data.data.funcState2;
                                this.$refs.func1.setFuncState(res.data.data.funcState1)
                                this.$refs.func2.setFuncState(res.data.data.funcState2)
                                this.policyForm.funcState3 = res.data.data.funcState3;
                                if (res.data.data.funcState1 != 'RUNNING'
                                    && res.data.data.funcState1 != 'REVOKEING'
                                    && res.data.data.funcState2 != 'RUNNING'
                                    && res.data.data.funcState2 != 'REVOKEING'
                                    && res.data.data.funcState3 != 'RUNNING'
                                    && res.data.data.funcState3 != 'REVOKEING'
                                    && res.data.data.funcState3 != 'IMPORTING') {
                                    clearInterval(timeInterval);
                                    this.isTimer = false
                                    this.$message.info('操作完成');
                                }
                            }
                        }, res => {
                            clearInterval(timeInterval);
                            this.isTimer = false
                        })
                }, 1000);
                 this.timeInterval = timeInterval;
            },
            caverState(state) {
                if (state == 'WAIT') {
                    return '待计算';
                } else if (state == 'RUNNING') {
                    return '计算中...';
                } else if (state == 'IMPORTING') {
                    return '导入中...';
                } else if (state == 'REVOKEING') {
                    return '撤销中...';
                } else if (state == 'FINISH') {
                    return '计算完成';
                } else if (state == 'ERROR'){
                    return '计算错误';
                } else if (state == 'REVERROR'){
                    return '撤销错误';
                } else {
                    return '未知';
                }
            },
            checkField(field){
                this.$refs.policyForm.validateField(field, valid => {})
            },
            dateFormat(date) {
              if (date) {
                return dateFormat(date, 'YYYY-MM-DD HH:mm');
              }
              return "";
            },
        }
    }
</script>

<style scoped>
    .ant-input-number {
        width: 100%;
    }

    td {
        border: 1px solid #f0f0f0;
    }

    /deep/ .label_td {
        width: 90px;
    }

    .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }

    /*/deep/  样式穿透
    */
    /deep/ .ant-col-8 {
        display: block;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 100%;
    }

    /deep/ .ant-card-head-title {
        flex: auto;
        width: 120px;
        overflow: inherit;
    }

    /deep/ .ant-form-item {
        margin: 0;
    }

    /deep/ .ant-card-extra {
        margin-right: 75%;
        padding: 0px 0;
    }

    .tdnumber > /deep/ .ant-input-number {
        width: 180px;
    }

    /deep/ .ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }

    .ant-divider-horizontal {
        height: 0.5px;
        margin: 0px 0;
    }

    .ant-divider {
        background: #000;
    }
</style>
