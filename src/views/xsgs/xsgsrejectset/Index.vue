<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
          <tr>
            <td>
              <a-form-model-item label="年度"/>
            </td>
            <td>
              <a-input v-model="searchForm.year"/>
            </td>

            <td>
              <a-form-model-item label="客户号"/>
            </td>
            <td>
              <a-input v-model="searchForm.customer" @blur="customerBlurs"/>
            </td>

            <td>
              <a-form-model-item label="状态机"/>
            </td>
            <td>
              <a-input v-model="searchForm.platformName"/>
            </td>

            <td>
              <a-form-model-item label="业务板块"/>
            </td>
            <td>
              <a-select v-model="searchForm.plate" mode="multiple" style="min-width: 150px">
                <a-select-option value="卡车">卡车</a-select-option>
                <a-select-option value="客车">客车</a-select-option>
                <a-select-option value="新能源">新能源</a-select-option>
              </a-select>
            </td>
          </tr>
          <tr>
            <td>
              <a-form-model-item label="客户名称"/>
            </td>
            <td>
              <a-select
                  v-model="searchForm.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch"
                  @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </td>

            <td colspan="2">
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch">
                <a-icon type="search"/>
                查询
              </a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery">
                <a-icon type="reload"/>
                重置
              </a-button>
            </td>
          </tr>

          <a-col :md="!advanced && 6 || 24" :sm="24">
             <span class="table-page-search-submitButtons">
                <div style="margin-top: 4px">

                </div>
            </span>
          </a-col>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('DISCOUNT_POLICY_ADD')" type="primary" style="margin: 0 8px" @click="handleEdit">
              <a-icon type="plus"/>
              新增
            </a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          :customRow="onRowClick"
          bordered
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('DISCOUNT_POLICY_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('DISCOUNT_POLICY_DELETE')" type="vertical"/>
                  <a v-if="checkPf('DISCOUNT_POLICY_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 15}"
                    style="height: auto;">
        <a-row class="form-row">
          <a-col :span="12">
            <a-form-model-item label="年度" prop="year">
              <a-input style="width: 100%;" v-model="form.year"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="客户号" prop="customer">
              <a-input v-model="form.customer" @blur="customerBlursFrom"/>
            </a-form-model-item>
          </a-col>
          <a-col :lg="12" :md="12" :sm="24">
            <a-form-model-item label="客户名称" prop="customerName">
              <a-select
                  style="width: 100%"
                  v-model="form.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearchFrom"
                  @change="handleCustomerChangeFrom"
              >
                <a-select-option v-for="d in customerListFrom" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="业务板块" prop="plates">
              <a-select v-model="form.plates" mode="multiple">
                <a-select-option value="卡车">卡车</a-select-option>
                <a-select-option value="客车">客车</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="排放" prop="blowoffs">
              <a-select v-model="form.blowoffs" mode="multiple">
                <a-select-option value="国1">国1</a-select-option>
                <a-select-option value="国2">国2</a-select-option>
                <a-select-option value="国3">国3</a-select-option>
                <a-select-option value="国4">国4</a-select-option>
                <a-select-option value="国5">国5</a-select-option>
                <a-select-option value="国6">国6</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品子系列" prop="seriesArray">
              <a-select v-model="form.seriesArray" mode="multiple" @focus="onClickSeries('series')">
                <a-select-option v-for="sty in seriesListData" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品型号" prop="productModels">
              <a-select v-model="form.productModels" mode="multiple" @focus="onClickProductModel('productModel')">
                <a-select-option v-for="sty in zcpxh_list" :key="sty">
                  {{ sty }}
                </a-select-option>
              </a-select>
              <!--              <a-input v-model="form.productModel"/>-->
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品型号[简化]" prop="productModelJhs">
              <a-select v-model="form.productModelJhs" mode="multiple" @focus="onClickProductModelJH('productModelJh')">
                <a-select-option v-for="sty in zcpxhjh_list" :key="sty">
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="功率(PS)" prop="pses">
              <a-select v-model="form.pses" mode="multiple" @focus="onClickPs('ps')">
                <a-select-option v-for="sty in ps_list" :key="sty.split('.')[0]">
                  {{ sty ? sty.split('.')[0] : "" }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="状态机" prop="platformNames">
              <a-select v-model="form.platformNames" mode="multiple"
                        @dropdownVisibleChange="onClickPlatform"
                        @search="platformSearch">
                <a-select-option v-for="sty in platformname_list" :key="sty">
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="选配件">
              <a-select v-model="form.partsNames" mode="multiple"
                        @dropdownVisibleChange="onClickParts"
                        :allowClear="true"
                        @search="partsSearch">
                <a-select-option v-for="sty in partsNames_list" :key="sty">
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="规格">
              <a-select v-model="form.specses" mode="multiple"
                        @dropdownVisibleChange="onClickSpecs"
                        :allowClear="true"
                        @search="specsSearch">
                <a-select-option v-for="sty in specses_list" :key="sty">
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系1">
              <a-select v-model="form.types" style="min-width: 60px"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getTypeList(form)">
                <a-select-option v-for="d in typeList" :key="d">
                  {{ d }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系2">
              <a-select v-model="form.strain1List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain1List(form)">
                <a-select-option v-for="d in strain1List" :key="d">
                  {{ d }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系3">
              <a-select width="width:100px" v-model="form.strain2List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain2List(form)">
                <a-select-option v-for="d in strain2List" :key="d">
                  {{ d }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系4">
              <a-select v-model="form.strain3List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain3List(form)">
                <a-select-option v-for="d in strain3List" :key="d">
                  {{ d }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="金额" prop="amount">
              <a-input-number style="width: 100%; " v-model="form.amount"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="备注" prop="remarks">
              <textarea class="xsgs-textarea" style="height: 33px;" v-model="form.remarks"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSREJECTSET_PAGE,
  COMBO_SERIES,
  SUBMIT_XSGSREJECTSETDETAIL,
  DELETE_XSGSREJECTSET,
  UPLOAD_XSGSALLOWANCESET,
  DOWNLOADPRICE_XSGSALLOWANCESET,
  UPLOADPRICE_XSGSALLOWANCESET,
  LIST_XSGSCUSTOMER,
  LIST_ZR,wlxx_action,COMBO_MACHINETYPE,COMBO_PARTS,COMBO_SPECS,
  COMBO_TYPE,COMBO_STRAIN1,COMBO_STRAIN2,COMBO_STRAIN3
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'

export default {
  name: "xsgsrejectset.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSALLOWANCESET,
      UPLOADPRICE_XSGSALLOWANCESET,
      Cookie,
      loading: false,
      dialog: false,
      // 功能权限关键字
      PF_FIELD: "BUSINESS_POLICY_MANAGER,DISCOUNT_POLICY",
      PF_LIST: [],
      confirmLoading: false,
      searchForm: {
        customerName: null,
        customer: null,
      },
      customerList: [],
      form: {
        customerName:null,
        customer:null,
      },
      // 高级搜索 展开/关闭
      advanced: false,
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],

      zrList: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', dataIndex: 'year', width: 90},
        {title: '客户号', dataIndex: 'customer', width: 100},
        {title: '客户名称', width: 120, dataIndex: 'customerName', ellipsis:true},
        {title: '业务板块', width: 90, dataIndex: 'plate', ellipsis:true},
        {title: '排放', width: 90, dataIndex: 'blowoff', ellipsis:true},
        {title: '产品子系列', width: 90, dataIndex: 'series', ellipsis:true},
        {title: '产品型号', width: 90, dataIndex: 'productModel', ellipsis:true},
        {title: '产品型号[简化]', width: 120, dataIndex: 'productModelJh', ellipsis:true},
        {title: '功率(PS)', width: 90, dataIndex: 'ps', ellipsis:true},
        {title: '状态机', width: 90, dataIndex: 'platformName', ellipsis:true},
        {title: '选配件', width: 90, dataIndex: 'parts', ellipsis:true},
        {title: '选配件规格', width: 90, dataIndex: 'specs', ellipsis:true},
        {title: '品系1', width: 90, dataIndex: 'type', ellipsis:true},
        {title: '品系2', width: 90, dataIndex: 'strain1', ellipsis:true},
        {title: '品系3', width: 90, dataIndex: 'strain2', ellipsis:true},
        {title: '品系4', width: 90, dataIndex: 'strain3', ellipsis:true},
        {title: '金额', width: 90, dataIndex: 'amount'},
        {title: '备注', width: 90, dataIndex: 'remarks', ellipsis:true},
        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],
      customerListFrom:[],
      seriesList: [],
      zcpxh_list:[], //产品型号
      productModelDisabled:true,
      mainDataList: [],
      zzpx_list:[], //品系
      seriesListData:[],//子系列
      zcpxhjh_list:[],
      platformname_list:[],
      ps_list:[],
      partsNames:[],
      partsNames_list:[],
      specses:[],
      specses_list:[],
      typeList:[],
      strain1List:[],
      strain2List:[],
      strain3List:[],
      rules: {
        year: [{required: true, message: '年度必填', trigger: 'blur'}],
        customer: [{required: true, message: '客户号必填', trigger: 'blur'}],
        customerName: [{required: true, message: '客户名称必填', trigger: 'blur'}],
      },
      seriesDisabled:true,
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.listZr()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    listZr() {
      request(LIST_ZR, METHOD.GET, {})
          .then(res => {
            this.zrList = res.data
          })

    },
    customerBlurs() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer: this.searchForm.customer
      }).then(res => {
        if (res.data.data.length > 0) {
          if (this.searchForm.customer != null) {
            if (!this.searchForm.customer.split(" ").join("").length == 0) {
              this.searchForm.customerName = res.data.data[0].name
            }
          }
        } else {
          this.$message.info('此客户号没找到信息')
        }
      })
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name: value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item => {
        if (item.name == value) {
          this.searchForm.customer = item.customer
        }
      })
    },

    toggleAdvanced() {
      this.advanced = !this.advanced
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSREJECTSET_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },

    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除当前记录？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSREJECTSET + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      console.log(1122,this.form)
      let list = this.form
      if(list.plates !== undefined && list.plates.length > 0 ){
        list.plate = list.plates.join(',');
      }else {
        list.plate = null;
      }

      if(list.blowoffs!== undefined && list.blowoffs.length > 0 ){
        list.blowoff = list.blowoffs.join(',');
      }else {
        list.blowoff = null;
      }

      if( list.seriesArray!== undefined && list.seriesArray.length > 0){
        list.series = list.seriesArray.join(',');
      }else {
        list.series = null;
      }

      if(list.productModels!== undefined && list.productModels.length > 0){
        list.productModel = list.productModels.join(',');
      }else {
        list.productModel = null;
      }

      if(list.productModelJhs!== undefined && list.productModelJhs.length > 0){
        list.productModelJh = list.productModelJhs.join(',');
      }else {
        list.productModelJh = null;
      }

      if(list.pses!== undefined && list.pses.length > 0){
        list.ps = list.pses.join(',');
      }else {
        list.ps = null;
      }

      if(list.platformNames!== undefined && list.platformNames.length > 0){
        list.platformName = list.platformNames.join(',');
      }else {
        list.platformName = null;
      }

      if(list.partsNames!== undefined && list.partsNames.length > 0){
        list.parts = list.partsNames.join(',');
      }else {
        list.parts = null;
      }

      if(list.specses!== undefined && list.specses.length > 0){
        list.specs = list.specses.join(',');
      }else {
        list.specs = null;
      }

      if(list.types!== undefined && list.types.length > 0){
        list.type = list.types.join(',');
      }else {
        list.type = null;
      }

      if(list.strain1List!== undefined && list.strain1List.length > 0){
        list.strain1 = list.strain1List.join(',');
      }else {
        list.strain1 = null;
      }

      if(list.strain2List!== undefined && list.strain2List.length > 0){
        list.strain2 = list.strain2List.join(',');
      }else {
        list.strain2 = null;
      }

      if(list.strain3List!== undefined && list.strain3List.length > 0){
        list.strain3 = list.strain3List.join(',');
      }else {
        list.strain3 = null;
      }

      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSREJECTSETDETAIL, METHOD.POST, list)
              .then(() => {
                this.getData()
                this.$message.info('提交成功')
                this.closeForm()
              })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      console.log(12)
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {
        customerName: null,
        customer: null,
      },
          this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    /** 查询客户号 */
    customerBlursFrom(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.form.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.form.customer!=null){
            if(!this.form.customer.split(" ").join("").length == 0){
              this.form.customerName = res.data.data[0].name
              this.customerListFrom = res.data.data
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },
    handleCustomerSearchFrom(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerListFrom = res.data.data
      })
    },
    handleCustomerChangeFrom(value) {
      this.customerListFrom.forEach(item =>{
        if(item.name == value){
          this.form.customer = item.customer
        }
      })
    },
    //产品子系列
    onClickSeries(type){
      console.log(123456,this.seriesDisabled)
      //如果为true，则调用
      if(this.seriesDisabled){
        this.onclickPartsName(type);
        //同时禁用
        this.seriesDisabled=false;
        setTimeout(() => {
          this.seriesDisabled = true;//响应后延迟几秒回复正常；
        }, 1000)
      }
    },
    //产品型号
    onClickProductModel(type){
      //如果为true，则调用
      if(this.productModelDisabled){
        this.onclickPartsName(type);
        //同时禁用
        this.productModelDisabled=false;
        setTimeout(() => {
          this.productModelDisabled = true;//响应后延迟几秒回复正常；
        }, 1000)
      }

    },
    //查询所有数据
    onclickPartsName(type){
      let queryParams = {

      }
      console.log(1231,this.form)
      if(this.form.plates !== undefined && this.form.plates.length > 0){
        queryParams.IN_YWBKMS =[]
        this.form.plates.forEach(item =>{
          queryParams.IN_YWBKMS.push({ "VTEXT": item})
        })
      }else{
        queryParams.IN_YWBKMS =[]
        queryParams.IN_YWBKMS.push({ "VTEXT": '卡车'})
        queryParams.IN_YWBKMS.push({ "VTEXT": '客车'})
        queryParams.IN_YWBKMS.push({ "VTEXT": '新能源'})
      }

      if(this.form.blowoffs !== undefined && this.form.blowoffs.length > 0){
        queryParams.IN_PFMS =[]
        this.form.blowoffs.forEach(item =>{
          queryParams.IN_PFMS.push({ "VTEXT": item})
        })
      }


      if(this.form.seriesArray && this.form.seriesArray.length > 0){
        queryParams.IN_CPZXL =[]
        this.form.seriesArray.forEach(item =>{
          queryParams.IN_CPZXL.push({ "ZCPZXL": item})
        })
      }

      if(type =='series'){
        delete queryParams.IN_CPZXL
      }
      if(this.form.productModels && this.form.productModels.length > 0){
        queryParams.IN_CPXH =[]
        this.form.productModels.forEach(item =>{
          queryParams.IN_CPXH.push({ "ZCPXH": item})
        })
      }
      if(type =='productModel'){
        delete queryParams.IN_CPXH
      }
      if(this.form.productModelJhs && this.form.productModelJhs.length > 0){
        queryParams.IN_CPXHJ =[]
        this.form.productModelJhs.forEach(item =>{
          queryParams.IN_CPXHJ.push({ "ZCPXHJ": item})
        })
      }

      if(type =='productModelJh'){
        delete queryParams.IN_CPXHJ
      }
      if(this.form.pses && this.form.pses.length > 0){
        queryParams.IN_GL =[]
        console.log(5566,this.form,this.form.pses)
        this.form.pses.forEach(item =>{
          queryParams.IN_GL.push({ "ZGL": item})
        })
      }

      if(type =='ps'){
        delete queryParams.IN_GL
      }
      if(this.form.platformNames && this.form.platformNames.length > 0){
        queryParams.IN_ZTJ =[]
        this.form.pses.forEach(item =>{
          queryParams.IN_ZTJ.push({ "ZZTJ": item})
        })
      }
      if(type =='platformName'){
        delete queryParams.IN_ZTJ
      }
      console.log(456,this.form)
      request(wlxx_action, METHOD.POST, queryParams).then(res => {
        let returnData = res.data.data
        let RETURN = returnData.RETURN
        if (RETURN && RETURN.ZTYPE == 'S') {
          console.log(789,returnData.T_OUT)
          let ZCPZXL_LIST = [] //产品子系列
          let ZCPXH_LIST = [] //产品型号
          let ZCPXHJH_LIST = [] //产品型号[简化]
          let PS_LIST = [] //功率
          let PLATFORMNAME_LIST = [] //状态机


          let T_OUT = returnData.T_OUT
          T_OUT.forEach(item =>{
            if(!this.isdou(ZCPZXL_LIST,item.ZCPZXL) && item.ZCPZXL !== ""){
              ZCPZXL_LIST.push(item.ZCPZXL)
            }
            if(!this.isdou(ZCPXH_LIST,item.ZCPXH)){
              ZCPXH_LIST.push(item.ZCPXH)
            }
            if(!this.isdou(ZCPXHJH_LIST,item.ZCPXHJ)){
              ZCPXHJH_LIST.push(item.ZCPXHJ)
            }
            if(!this.isdou(PS_LIST,item.ZGL)){
              PS_LIST.push(item.ZGL)
            }
            if(!this.isdou(PLATFORMNAME_LIST,item.ZZTJ)){
              PLATFORMNAME_LIST.push(item.ZZTJ)
            }

          })
          console.log(741,ZCPZXL_LIST)
          this.seriesListData = ZCPZXL_LIST;
          this.zcpxh_list = ZCPXH_LIST;
          this.zcpxhjh_list = ZCPXHJH_LIST;
          this.ps_list = PS_LIST;
          this.platformname_list = PLATFORMNAME_LIST;
        }
      })
    },
    isdou(list,value){
      let flag = false
      list.forEach(item =>{
        if(item == value){
          flag = true
        }
      })
      return flag
    },
    /** 功率PS **/
    onClickPs(type){
      //如果为true，则调用
      if(this.PsDisabled){
        this.onclickPartsName(type);
        //同时禁用
        this.PsDisabled=false;
        setTimeout(() => {
          this.PsDisabled = true;//响应后延迟几秒回复正常；
        }, 1000)
      }
    },
    // 状态机
    onClickPlatform(){
      this.machineTypeList = [];
      this.platformname_list=[];
      request(COMBO_MACHINETYPE, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses
      }).then(res => {
        this.machineTypeList = res.data.data;
        this.platformname_list = this.cutList(this.machineTypeList,0,20);
      })
      // this.onclickPartsName('platformName');
    },
    platformSearch(value){
      if (!value) {
        this.platformname_list = this.cutList(this.machineTypeList,0,20);
      } else {
        this.platformname_list = this.cutList(this.machineTypeList.filter(s=>s.indexOf(value) != -1),0,20);
      }
    },
    specsSearch(value){
      if (!value) {
        this.specses_list = this.cutList(this.specses,0,20);
      } else {
        this.specses_list = this.cutList(this.specses.filter(s=>s.indexOf(value) != -1),0,20);
      }
    },
    // 选配件
    onClickParts(){
      console.log(555)
      this.partsNames = [];
      this.partsNames_list = [];
      request(COMBO_PARTS, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames
      }).then(res => {
        const map = new Map()
        this.partsNames = res.data.data.filter(key => !map.has(key) && map.set(key, 1));
        this.partsNames_list = this.cutList(this.partsNames, 0, 20);
        console.log(5588,this.partsNames,res.data.data)
      })
    },
    partsSearch(value){
      console.log(111)
      if (!value) {
        this.partsNames_list = this.cutList(this.partsNames,0,20);
      } else {
        this.partsNames_list = this.cutList(this.partsNames.filter(s=>s.indexOf(value) != -1),0,20);
      }
    },
    // 选配件
    onClickSpecs(){
      this.specses = [];
      this.specses_list = [];
      request(COMBO_SPECS, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames,
        partsNames:this.form.partsNames
      }).then(res => {
        this.specses = res.data.data;
        this.specses_list = this.cutList(this.specses, 0, 20);
      })
    },
    getTypeList() {
      this.typeList = [];
      request(COMBO_TYPE, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames,
      }).then(res => {
        this.typeList = res.data.data;
      })
    },
    getStrain1List() {
      this.strain1List = [];
      request(COMBO_STRAIN1, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames,
        type:this.form.typeList,
      }).then(res => {
        this.strain1List = res.data.data;
      })
    },
    getStrain2List() {
      this.strain2List = [];
      request(COMBO_STRAIN2, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames,
        type:this.form.typeList,
        starin1:this.form.strain1List,
      }).then(res => {
        this.strain2List = res.data.data;
      })
    },
    getStrain3List() {
      this.strain3List = [];
      request(COMBO_STRAIN3, METHOD.POST, {
        year:this.form.year + '',
        customer:this.form.customer,
        blowoffs: this.form.blowoffs,
        plates:this.form.plates,
        series:this.form.seriesArray,
        productModelJh:this.form.productModelJhs,
        ps:this.form.pses,
        machineType:this.form.platformNames,
        type:this.form.typeList,
        starin1:this.form.strain1List,
        starin2:this.form.strain2List
      }).then(res => {
        this.strain3List = res.data.data;
      })
    },
    //产品型号【简化】
    onClickProductModelJH(type){
      //如果为true，则调用
      if(this.productModelJHDisabled){
        this.onclickPartsName(type);
        //同时禁用
        this.productModelJHDisabled=false;
        setTimeout(() => {
          this.productModelJHDisabled = true;//响应后延迟几秒回复正常；
        }, 1000)
      }

    },
  }
}
</script>

<style scoped>
.ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
*/
/deep/ .ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
