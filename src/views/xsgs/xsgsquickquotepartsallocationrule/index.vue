<template>
  <div>
    <a-card>
      <div class="header-button-area">
        <a-button v-if="creatable" type="primary" @click="createRule"><a-icon type="save"/>新建</a-button>
      </div>
      <a-table
          style="margin-top: 10px"
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <template slot="action" slot-scope="text, record">
          <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                @click="editRule(record)">编辑</span>
          <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                @click="deleteRule(record)">删除</span>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  XSGSQUICKQUOTEPARTSALLOCATIONRULES
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
import * as _ from 'lodash';

export default {
  name: 'xsgs-quick-quote-parts-allocation-rule-index',
  data() {
    return {
      hasAuth,
      Cookie,
      loading: false,
      creatable: false,
      // 功能权限关键字
      PF_FIELD:"QUICK_QUOTE_MANAGER,QUICK_QUOTE_PARTS_ALLOCATION_RULE",
      PF_LIST:[],

      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num',  key: 'num',  width: 50,  align: 'center',  scopedSlots: {customRender: 'num'}},
        {title: '规则名称', dataIndex: 'ruleName',},
        {title: '规则内容', dataIndex: 'content', ellipsis: true},
        {dataIndex: "action", title: "操作", width: 250, align: "center", scopedSlots: {customRender: 'action'}},
      ],
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },

  mounted() {
    this.getData();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    createRule() {
        this.$router.push({path:'/quick_quote/xsgs_quick_quote_parts_allocation_rule_edit'})
    },
    editRule(record) {
      this.$router.push({path:'/quick_quote/xsgs_quick_quote_parts_allocation_rule_edit?id=' + record.id})
    },
    deleteRule(record) {
      const _this = this;
      this.$confirm({
        content: '是否删除当前规则？',
        onOk: () => {
          _this.loading = true;
          request(XSGSQUICKQUOTEPARTSALLOCATIONRULES, METHOD.DELETE, {id: record.id})
              .then(() => {
                _this.$message.success('删除成功');
                _.remove(_this.data, (item) => item.id === record.id);
                _this.getData();
              })
              .finally(() => {
                _this.loading = false;
              })
        },
      })
    },
    getData: function () {
      this.loading = true;
      const _this = this;
      const {current, size} = this.pagination;
      request(XSGSQUICKQUOTEPARTSALLOCATIONRULES, METHOD.GET, {current, size})
          .then(res => {
            const data = res.data.data;
            if (data && data.records && data.records.length) {
              _this.data = data.records;
            }
            if (!_this.data.length) {
              _this.creatable = true;
            }
          }).finally(() => {
            _this.loading = false;
      })

    },
    /** 关闭对话框，表单重置 */
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },

  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
/deep/.subTable {
  margin-left: 10px;
}
</style>
