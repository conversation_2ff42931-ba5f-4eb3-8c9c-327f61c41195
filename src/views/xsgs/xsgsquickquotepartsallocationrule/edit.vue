<template>
  <a-card>
    <a-spin :spinning="isSaving">
      <a-form-model ref="form" :model="form" :rules="formRules" :labelCol="{span: 4}" :wrapperCol="{span: 14}">
        <a-form-model-item label="规则名称" prop="ruleName">
          <a-input v-model="form.ruleName"/>
        </a-form-model-item>
        <a-form-model-item label="规则内容" prop="content">
          <a-input v-model="form.content" type="textarea"/>
        </a-form-model-item>
      </a-form-model>
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button type="primary" @click="onSubmit">
          保存
        </a-button>
      </a-form-model-item>
    </a-spin>
  </a-card>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  XSGSQUICKQUOTEPARTSALLOCATIONRULES
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'


export default {
  name: "name: 'xsgs-quick-quote-parts-allocation-rule-edit',",
  components: {},
  data() {
    return {
      hasAuth,
      Cookie,
      isSaving: false,
      id: null,
      fullPath: null,
      parentPath: null,
      option: null,
      form: {
        ruleName: '',
        content: ''
      },
      formRules: {
        ruleName: [{required: true, message: '规则名称不能为空', trigger: 'blur'}],
        content: [{required: true, message: '内容不能为空', trigger: 'blur'}],
      },

    }
  },

  mounted() {
    const query = this.$route.query;
    this.fullPath = this.$route.fullPath;
    this.parentPath = this.$route.query.parentPath;
    this.checkName();
    if (query.id) {
      this.id = query.id;
      this.getData();
    }
  },

  methods: {
    getData() {
      const _this = this;
      request(XSGSQUICKQUOTEPARTSALLOCATIONRULES + `/${this.id}`, METHOD.GET, {current: 1, page: 1})
          .then((res) => {
            if (res.data && res.data.data) {
              _this.form = res.data.data;
            }
          })
    },
    onSubmit() {
      const _this = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          _this.$confirm({
            content: '是否保存当前数据？',
            onOk: () => {
              _this.isSaving = true;
              let method = METHOD.POST
              if (this.id) {
                method = METHOD.PUT
              }
              request(XSGSQUICKQUOTEPARTSALLOCATIONRULES, method, this.form)
                  .then(() => {
                    _this.$message.success('保存成功')
                    // 关闭页面
                    _this.$closePage(this.fullPath, this.parentPath);
                  })
                  .finally(() => {
                    _this.isSaving = false;
                  })
            },
          })
        }
      })
    },

    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {
        ruleName: '',
        content: ''
      }
    },
  }
}
</script>

<style scoped>
/deep/ .header-button-area {
  position: fixed;
  z-index: 999;
  height: 45px;
  background-color: #ffffff;
  line-height: 45px;
  margin-top: -25px;
  min-width: 700px;
}

/deep/ .header-button-area > button {
  margin-right: 8px;
}

.ant-input-number {
  width: 100%;
}

td {
  border: 1px solid #f0f0f0;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
*/
/deep/ .ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/ .ant-card-head-title {
  flex: auto;
  width: 120px;
  overflow: inherit;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-card-extra {
  margin-right: 75%;
  padding: 0px 0;
}

.tdnumber > /deep/ .ant-input-number {
  width: 180px;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

.ant-divider-horizontal {
  height: 0.5px;
  margin: 0px 0;
}

.ant-divider {
  background: #000;
}

</style>
