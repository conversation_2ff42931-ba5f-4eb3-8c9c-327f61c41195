<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col span="4">
            <a-form-model-item label="板块">
              <a-select v-model="searchForm.plate"  style="min-width: 100px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="4">
            <a-form-model-item label="客户号">
              <a-input v-model="searchForm.customer" @blur="customerBlurs"/>
            </a-form-model-item>
          </a-col>
          <a-col span="8">
            <a-form-model-item label="客户名称">
              <a-select
                      style="min-width: 250px"
                      v-model="searchForm.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :allowClear="true"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item style="text-align: left">
              <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
              <a @click="advanced?advanced=false:advanced=true" style="margin-left: 8px">
                {{ advanced ? '收起' : '展开' }}
                <a-icon :type="advanced ? 'up' : 'down'"/>
              </a>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-show="advanced">
          <a-col span="6">
            <a-form-model-item label="TO/收件人">
              <a-input v-model="searchForm.recipient"/>
            </a-form-model-item>
          </a-col>
          <a-col span="8">
            <a-form-model-item label="FAX/收件人传真">
              <a-input v-model="searchForm.recipientFax"/>
            </a-form-model-item>
          </a-col>
          <a-col span="8">
            <a-form-model-item label="TEL/收件人电话">
              <a-input v-model="searchForm.recipientTel"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('QUOTE_LETTER_TEMPLATE_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('QUOTE_LETTER_TEMPLATE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload v-if="checkPf('QUOTE_LETTER_TEMPLATE_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSQUOTELETTERTEMP"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button v-if="checkPf('QUOTE_LETTER_TEMPLATE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
              :columns="columns"
              rowKey="id"
              :pagination="pagination"
              :data-source="data"
              :scroll="{ x: 1000 }"
              size="small"
              bordered
              :customRow="onRowClick"
              :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
              :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('QUOTE_LETTER_TEMPLATE_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('QUOTE_LETTER_TEMPLATE_DELETE')" type="vertical"/>
                  <a v-if="checkPf('QUOTE_LETTER_TEMPLATE_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
            :title="form.id?'编辑':'新增'"
            :visible="dialog"
            :confirm-loading="confirmLoading"
            @ok="handleSubmit"
            @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">

        <a-form-model-item label="板块" prop="plate">
          <a-select v-model="form.plate">
            <a-select-option value="卡车">卡车</a-select-option>
            <a-select-option value="客车">客车</a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="客户号" prop="customer">
<!--          <a-input v-model="form.customer" @blur="customerBlurs2"/>-->
          <a-select
                  v-model="form.customer"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :allowClear="true"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch2"
                  @change="handleCustomerChange2"
          >
            <a-select-option v-for="d in customerList2" :key="d.customer">
              {{ d.customer + '-' + d.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="客户名称" prop="customerName">
          <a-input v-model="form.customerName"/>
<!--          <a-select-->
<!--                  v-model="form.customerName"-->
<!--                  show-search-->
<!--                  :default-active-first-option="false"-->
<!--                  :show-arrow="false"-->
<!--                  :allowClear="true"-->
<!--                  :filter-option="false"-->
<!--                  :not-found-content="null"-->
<!--                  @search="handleCustomerSearch2"-->
<!--                  @change="handleCustomerChange2"-->
<!--          >-->
<!--            <a-select-option v-for="d in customerList2" :key="d.name">-->
<!--              {{ d.name }}-->
<!--            </a-select-option>-->
<!--          </a-select>-->
        </a-form-model-item>

        <a-form-model-item label="TO/收件人" prop="recipient">
          <a-input v-model="form.recipient"/>
        </a-form-model-item>

        <a-form-model-item label="FAX/收件人传真" prop="recipientFax">
          <a-input v-model="form.recipientFax"/>
        </a-form-model-item>

        <a-form-model-item label="TEL/收件人电话" prop="recipientTel">
          <a-input v-model="form.recipientTel"/>
        </a-form-model-item>

        <a-form-model-item label="FROM/发件人" prop="sender">
          <a-input v-model="form.sender"/>
        </a-form-model-item>

        <a-form-model-item label="TEL/发件人电话" prop="senderTel">
          <a-input v-model="form.senderTel"/>
        </a-form-model-item>

        <a-form-model-item label="备注" prop="remarkq">
          <a-input v-model="form.remarkq"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
  import {hasAuth} from "@/utils/authority-utils";
  import {LIST_XSGSQUOTELETTERTEMP_PAGE,SUBMIT_XSGSQUOTELETTERTEMP,LIST_XSGSCUSTOMER,UPLOAD_XSGSQUOTELETTERTEMP,DELETE_XSGSQUOTELETTERTEMP_BYIDS,DOWNLOAD_XSGSQUOTELETTERTEMP} from "@/services/api/xsgs";
  import {METHOD, request,exportExcel} from "@/utils/request";
  import Cookie from 'js-cookie'
  export default {
    name: "CustomerMaterial.vue",
    data() {
      return {
        hasAuth,
        UPLOAD_XSGSQUOTELETTERTEMP,
        Cookie,
        loading: false,
        dialog: false,
        advanced:false,
        confirmLoading: false,
        // 功能权限关键字
        PF_FIELD:"BASE_DATA_MANAGER,QUOTE_LETTER_TEMPLATE",
        PF_LIST:[],
        searchForm: {
          plate:null,
          customer:null,
          customerName:null,
          recipient:null,
          recipientFax:null,
          recipientTel:null,
          sender:null,
          senderTel:null,
        },
        customerList:[],
        customerList2:[],
        allCustomerList:[],
        form: {
          plate:null,
          customer:null,
          customerName:null,
          recipient:null,
          recipientFax:null,
          recipientTel:null,
          sender:null,
          senderTel:null,
          remarkq:null,
        },
        selectedRowKeys: [],
        pagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                          total / this.pagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },
        data: [],
        columns: [
          {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
              customRender: 'num'
            }
          },
          {title: '板块', dataIndex: 'plate',width:80},
          {title: '客户号', dataIndex: 'customer',width:100},
          {title: '客户名称', dataIndex: 'customerName',width:180},
          {title: 'TO/收件人', dataIndex: 'recipient',width:180},
          {title: 'FAX/收件人传真', dataIndex: 'recipientFax',width:120},
          {title: 'TEL/收件人电话', dataIndex: 'recipientTel',width:120},
          {title: 'FROM/发件人', dataIndex: 'sender',width:220, ellipsis:true},
          {title: 'TEL/发件人电话', dataIndex: 'senderTel',width:120},
          {title: '备注', dataIndex: 'remarkq',width:120},
          {title: '操作', fixed: 'right', align:"center", width: 165, scopedSlots: {customRender: 'action'}}
        ],

        rules: {
          plate: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
          customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
          customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
          recipient: [{required: true, message: 'TO/收件人不能为空', trigger: 'blur'}],
          recipientFax: [{required: true, message: 'FAX/收件人传真不能为空', trigger: 'blur'}],
          recipientTel: [{required: true, message: 'TEL/收件人电话不能为空', trigger: 'blur'}],
          sender: [{required: true, message: 'FROM/发件人不能为空', trigger: 'blur'}],
          senderTel: [{required: true, message: 'TEL/发件人电话不能为空', trigger: 'blur'}],
        },
      }
    },
    async created() {
      // 获取页面功能权限
      await this.getUserAuth(this.PF_FIELD);
      this.PF_LIST = this.getPfList(this.PF_FIELD);
    },
    mounted() {
      this.getCustomerList();
      this.getData()
      this.checkName()
    },
    methods: {
      checkPf(field) {
        return this.PF_LIST.findIndex(s=>s == field) != -1;
      },
      customerBlurs(){
        let customer = this.searchForm.customer;
        if (!customer) {
          this.searchForm.customerName = null;
        }
        let newData = this.customerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.searchForm.customerName = newData[0].name;
        } else {
          newData = this.allCustomerList.filter(item => item.customer == customer);
          if (newData.length > 0) {
            this.searchForm.customerName = newData[0].name;
          }
        }
      },

      handleCustomerSearch(value) {
        if (value) {
          this.customerList = this.allCustomerList.filter(s=>s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
        } else {
          this.customerList = this.allCustomerList;
        }

      },
      handleCustomerChange(value) {
        if (value) {
          this.allCustomerList.forEach(item =>{
            if(item.name == value){
              this.searchForm.customer = item.customer
              this.searchForm.customerName = item.name
            }
          })
        } else {
          this.searchForm.customer = null
        }

      },
      customerBlurs2(){
        let customer = this.form.customer;
        if (!customer) {
          this.form.customerName = null;
        }
        let newData = this.customerList2.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.$set(this.form,"customerName", newData[0].name)
        } else {
          newData = this.allCustomerList.filter(item => item.customer == customer);
          if (newData.length > 0) {
            this.$set(this.form,"customerName", newData[0].name)
          }
        }
      },

      handleCustomerSearch2(value) {
        if (value) {
          this.customerList2 = this.allCustomerList.filter(s=>s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
        } else {
          this.customerList2 = this.allCustomerList;
        }

      },
      handleCustomerChange2(value) {
        if (value) {
          this.allCustomerList.forEach(item =>{
            if(item.customer == value){
              this.form.customer = item.customer
              this.form.customerName = item.name
            }
          })
        } else {
          this.form.customer = null
          this.form.customerName = null
        }
      },
      getCustomerList(){
        request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
          this.allCustomerList = res.data.data
          this.customerList = res.data.data
          this.customerList2 = res.data.data
        })
      },
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys;
      },
      getData: function () {
        this.loading = true
        let {current, size} = this.pagination
        request(LIST_XSGSQUOTELETTERTEMP_PAGE, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
                  const {records, total} = res.data.data
                  this.loading = false
                  this.data = records
                  this.pagination.total = parseInt(total)
                })
      },
      deleteSelectedIds(){
        if(this.selectedRowKeys.length<=0){
          this.$message.info('请至少选择一条记录！')
          return
        }
        this.$confirm({
          content: `是否确认删除选择的数据？`,
          onOk: () => {
            let ids = this.selectedRowKeys;
            this.doDelete(ids)
          }
        });

      },
      doDelete(ids) {
        this.loading = true
        request(DELETE_XSGSQUOTELETTERTEMP_BYIDS, METHOD.POST,[...ids])
                .then(() => {
                  this.$message.info('删除成功')
                  this.getData()
                }).catch(() => {
          this.loading = false
        })
      },
      handleDelete(record) {
        this.$confirm({
          content: `是否确认删除 ${record.customerName} ？`,
          onOk: () => {
            let ids = [];
            ids.push(record.id);
            this.doDelete(ids);
          }
        });
      },
      handleSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            request(SUBMIT_XSGSQUOTELETTERTEMP, METHOD.POST, this.form)
                    .then(() => {
                      this.getData()
                      this.$message.info('提交成功')
                      this.closeForm()
                    })
          }
        })
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.searchForm = {

        }
        this.handleSearch();
      },
      handleEdit(record) {
        this.dialog = true
        this.form = {...record}
      },
      handleSearch() {
        this.pagination.current = 1
        this.getData()
      },
      closeForm() {
        this.dialog = false
        this.$refs.form.resetFields()
        this.form = {}
      },
      onPageChange(page, size) {
        this.pagination.current = page;
        this.pagination.size = size.toString();
        this.getData();
      },
      onSizeChange(current, size) {
        this.pagination.current = 1;
        this.pagination.size = size.toString();
        this.getData();
      },
      handleUpload(info) {
        this.loading = true
        if (info.file.status !== 'uploading') {
          this.getData()
        }
        if (info.file.status === 'done') {
          this.$message.success(`${info.file.name} 导入成功`);
        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 导入失败`);
        }
      },
      /** 导出按钮操作 */
      handleExportExcel() {
        exportExcel(DOWNLOAD_XSGSQUOTELETTERTEMP, {...this.searchForm}, "报价函模板信息.xlsx")
      },



    }
  }
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>
