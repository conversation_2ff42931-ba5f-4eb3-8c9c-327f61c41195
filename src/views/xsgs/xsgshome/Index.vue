<template>
    <iframe
        class="fr-iframe" v-if="env === 'QAS'" :style="`height:${height}px`"
        :src="genFrUrl($route.meta.frIdDev,$store.state.account.user.code)"
        frameborder="0"/>
    <iframe
        class="fr-iframe" v-else-if="env === 'PRD'" :style="`height:${height}px`"
        :src="genFrUrl($route.meta.frIdprod,$store.state.account.user.code)"
        frameborder="0"/>
</template>

<script>
import {genFrUrl} from "../../../utils/frUrlGen";

export default {
  name: "xsgshome.vue",
  data() {
    return {
      genFrUrl,
      env: "QAS",
      height: document.body.clientHeight - 10   //实时屏幕高度
    }
  },
  mounted() {
    this.getUrl();
    window.onresize = () => { //写在mounted中,onresize事件会在页面大小被调整时触发
      return (() => {
        window.screenHeight = document.body.clientHeight - 10;
        this.height = window.screenHeight;
      })();
    };
  },

  methods: {
    getUrl() {
      let url = window.location.href; //获取当前url
      //debugger;
      if (url.indexOf(".qas.") != -1) {
        this.env = "QAS"
      } else if (url.indexOf(".app.") != -1) {
        this.env = "PRD"
      }
    }
  }
}
</script>

<style scoped>
.fr-iframe {
  width: 100%;
  height: 100vh;
}
</style>