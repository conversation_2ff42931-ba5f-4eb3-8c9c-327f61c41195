<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>

            <td><a-form-model-item label="年度"/></td>
            <td><a-input v-model="searchForm.year"/></td>

            <td><a-form-model-item label="选配件名称" /></td>
            <td><a-input v-model="searchForm.partsName"/></td>

          </tr>
          <tr>
            <td><a-form-model-item label="选配件图号" /></td>
            <td><a-input v-model="searchForm.partsNo"/></td>

            <td><a-form-model-item label="规格" /></td>
            <td><a-input v-model="searchForm.specs"/></td>

            <td><a-form-model-item label="供应商名称" /></td>
            <td><a-input v-model="searchForm.supplier"/></td>
            <td>
                <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button></td>
              <td><a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
<!--        <a-row>-->
<!--          <a-col :span="6">-->
<!--            <a-form-model-item label="年度">-->
<!--              <a-date-picker-->
<!--                      mode="year"-->
<!--                      placeholder="请选择年份"-->
<!--                      format='YYYY'-->
<!--                      v-model="searchForm.year"-->
<!--                      style="width:100%"-->
<!--                      :open='yearShowOne'-->
<!--                      @openChange="openChangeOne"-->
<!--                      @panelChange="panelChangeOne"-->
<!--              />-->
<!--              <a-input v-model="searchForm.year"/>-->
<!--            </a-form-model-item>-->
<!--          </a-col>-->
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('PARTS_PRICE_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('PARTS_PRICE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
              <a-upload v-if="checkPf('PARTS_PRICE_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSPARTSPRICE"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button v-if="checkPf('PARTS_PRICE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>


      </a-form-model>




      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
          <template slot="num" slot-scope="text, record, index">
              {{index+1}}
          </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_PRICE_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PARTS_PRICE_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PARTS_PRICE_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>


    <a-modal
        width="50%"
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form"  :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="年度" prop="year">
<!--              <a-date-picker-->
<!--                      mode="year"-->
<!--                      placeholder="请选择年份"-->
<!--                      format='YYYY'-->
<!--                      :default-value="undefined"-->
<!--                      v-model="form.year"-->
<!--                      style="width:100%"-->
<!--                      :open='yearShowTwo'-->
<!--                      @change="onChange"-->
<!--                      @openChange="openChangeTwo($event)"-->
<!--                      @panelChange="panelChangeTwo($event)"-->
<!--              />-->
              <a-input v-model="form.year"/>
            </a-form-model-item>
          </a-col>
            <a-col :span="12">
                <a-form-model-item label="选配件名称" prop="partsName">
                    <a-input v-model="form.partsName"/>
                </a-form-model-item>
            </a-col>
        </a-row>
        <a-row>

            <a-col :span="12">
                <a-form-model-item label="选配件图号" prop="partsNo">
                  <a-input v-model="form.partsNo"/>
                </a-form-model-item>
            </a-col>
            <a-col :span="12">
                <a-form-model-item label="规格" prop="specs">
                    <a-input v-model="form.specs"/>
                </a-form-model-item>
            </a-col>
        </a-row>

        <a-row>

            <a-col :span="12">
                <a-form-model-item label="供应商名称" prop="supplier">
                    <a-input v-model="form.supplier"/>
                </a-form-model-item>
            </a-col>
        </a-row>
        <a-row>

            <a-col :span="12">
                <a-form-model-item label="指导售价" prop="actualPrice">
                  <a-input v-model="form.actualPrice"/>
                </a-form-model-item>
            </a-col>
            <a-col :span="12">
                <a-form-model-item label="预算价格" prop="budgetedPrice">
                    <a-input v-model="form.budgetedPrice"/>
                </a-form-model-item>
            </a-col>
        </a-row>


      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSPARTSPRICE_PAGE,DELETE_XSGSPARTSPRICE,SUBMIT_XSGSPARTSPRICE,UPLOAD_XSGSPARTSPRICE,DOWNLOAD_XSGSPARTSPRICE,DELETE_XSGS_PARTS_PRICE_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
// import moment from 'moment';

const supplierBak = ['国产', '国际品牌'];
export default {
  name: "xsgspartsprice.vue",
  data() {
    return {
      UPLOAD_XSGSPARTSPRICE,
      Cookie,
      hasAuth,
      loading: false,
      dialog: false,
      confirmLoading: false,
      yearShowOne: false,
      yearShowTwo: false,
        // 功能权限关键字
        PF_FIELD:"BASE_DATA_MANAGER,PARTS_PRICE",
        PF_LIST:[],
      supplierBak: supplierBak,
      searchForm: {},
      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
          {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                  customRender: 'num'
              }
          },
        {title: '年度', dataIndex: 'year',
            sorter: (a, b) => a.year - b.year,},
        {title: '选配件名称', dataIndex: 'partsName'},
        {title: '选配件图号', dataIndex: 'partsNo'},
        {title: '规格', dataIndex: 'specs'},
        {title: '供应商名称', dataIndex: 'supplier'},
        {title: '指导售价', dataIndex: 'actualPrice'},
        {title: '预算价格', dataIndex: 'budgetedPrice'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        partsName: [{required: false, message: '选配件名称不能为空', trigger: 'blur'}],
        partsNo: [{required: false, message: '选配件图号不能为空', trigger: 'blur'}],
        specs: [{required: false, message: '规格不能为空', trigger: 'blur'}],
        supplier: [{required: false, message: '供应商名称不能为空', trigger: 'blur'}],
        actualPrice: [{required: false, message: '指导售价不能为空', trigger: 'blur'}],
        budgetedPrice: [{required: false, message: '预算价格不能为空', trigger: 'blur'}],
      }
    }
  },
    async created() {
        // 获取页面功能权限
        await this.getUserAuth(this.PF_FIELD);
        this.PF_LIST = this.getPfList(this.PF_FIELD);
    },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
      checkPf(field) {
          return this.PF_LIST.findIndex(s=>s == field) != -1;
      },
    onSelectChange(selectedRowKeys) {
          this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSPARTSPRICE_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
      deleteSelectedIds(){
          if(this.selectedRowKeys.length<=0){
              this.$message.info('请至少选择一条记录！')
              return
          }
          this.$confirm({
              content: `是否确认删除选择的数据？`,
              onOk: () => {
                  this.loading = true
                  request(DELETE_XSGS_PARTS_PRICE_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                      .then(() => {
                          this.$message.info('删除成功')
                          this.getData()
                      }).catch(() => {
                      this.loading = false
                  })
              }
          });

      },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.partsName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSPRICE + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },

    handleSubmit() {
      console.log(this.form.year)
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true

          request(SUBMIT_XSGSPARTSPRICE, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    // // 弹出日历和关闭日历的回调
    // openChangeOne(status) {
    //   if (status){
    //     this.yearShowOne = true
    //   }
    // },
    // // 得到年份选择器的值
    // panelChangeOne(value) {
    //   this.searchForm.year = moment(value._d).format('YYYY');
    //   this.yearShowOne =false;
    // },

    // // 弹出日历和关闭日历的回调
    // openChangeTwo(status) {
    //   // console.log('status', status)
    //   if (status) {
    //     this.yearShowTwo = true
    //   //   window.console.log(index)
    //   //   // this.tableList[index].yearShow = true
    //   //   this.$set(this.data[index], 'yearShowTwo', true)
    //   }
    //
    // },
    //
    // // 得到年份选择器的值
    // panelChangeTwo(value) {
    //   this.form.year = moment(value._d).format('YYYY');
    //   this.yearShowTwo =false;
    // },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    handleImprot(){

    },

    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },

      /** 导出按钮操作 */
      handleExportExcel() {
          const searchForm =this.searchForm
          this.$confirm({
              title: '提示',
              content: '是否确认导出所有信息数据项?',
              onOk:() => {
                //   let option = ""
                //   if (searchForm.year != null) {
                //       option = "?year=" + searchForm.year
                //   }
                //
                //   if (searchForm.partsName != null) {
                //       if (option != "") {
                //           option += "&"
                //       } else {
                //           option += "?"
                //       }
                //       option += "partsName=" + searchForm.partsName
                //   }
                //
                //   if (searchForm.partsNo != null) {
                //       if (option != "") {
                //           option += "&"
                //       } else {
                //           option += "?"
                //       }
                //       option += "partsNo=" + searchForm.partsNo
                //   }
                //   if (searchForm.specs != null) {
                //       if (option != "") {
                //           option += "&"
                //       } else {
                //           option += "?"
                //       }
                //       option += "specs=" + searchForm.specs
                //   }
                // /*  if (searchForm.supplierBak != null) {
                //       if (option != "") {
                //           option += "&"
                //       } else {
                //           option += "?"
                //       }
                //       option += "supplierBak=" + searchForm.supplierBak
                //   }*/
                //   if (searchForm.supplier != null) {
                //       if (option != "") {
                //           option += "&"
                //       } else {
                //           option += "?"
                //       }
                //       option += "supplier=" + searchForm.supplier
                //   }
                //
                //   window.open(decodeURI(DOWNLOAD_XSGSPARTSPRICE + option))
                  exportExcel(DOWNLOAD_XSGSPARTSPRICE, {...searchForm}, "选配件价格列表.xlsx")
              }
          })
      },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
}
</style>
