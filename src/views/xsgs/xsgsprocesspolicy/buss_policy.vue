<template>
    <div>
        <table style="width: 100%" class="policyInfo">
            <tbody class="ant-table">
                <tr class="annexRow">
                    <th colspan="2">附件</th>
                    <td colspan="10" class="annexTd">
                        <FileUpload v-model="policyForm.annexList" path="xsgs/" field="bussPolicy" :multiple="true"
                            :disabled="isNotDrafted()" />
                    </td>
                </tr>
                <tr>
                    <th colspan="12">收益水平测算</th>
                </tr>

                <tr>
                    <th colspan="2">
                        <span v-if="isNotDrafted() && this.form.nodeInfo.nodeCode == 'value_engine_approval'"
                            style="color: red">*</span>
                        商务年度
                    </th>
                    <td colspan="2">
                        <a-input v-model="policyForm.businessYear" v-if="!isNotVE()" style="border: none;"
                            placeholder="商务年度" @blur="businessYearBlurs" />
                        <span v-if="isNotVE()">{{ policyForm.isCover == 1 ? '****' : policyForm.businessYear }}</span>
                    </td>
                    <th colspan="2">
                        <span style="color: red" v-if="isNotDrafted() && !isNotVE()">*</span>
                        客户号
                    </th>
                    <td colspan="2">
                        <a-select :style="`border:${formRule.customer.border}`" placeholder="请选择客户号" show-search
                            v-model="policyForm.customerList" mode="tags" @change="customerChange" v-if="!isNotVE()">
                            <a-select-option v-for="item in customerList" :key="item.id" :value="item.customer">
                                {{ item.customer }}
                            </a-select-option>
                        </a-select>
                        <!-- <a-input v-model="policyForm.customer" :style="`border:${formRule.customer.border}`"
                            v-if="!isNotVE()" @blur="customerBlurs" placeholder="请填写客户号" /> -->
                        <!--                       <a-input disabled v-if="isNotVE()" :value="policyForm.isCover==1?'****':policyForm.customer" />-->
                        <!-- <span v-if="isNotVE()">{{policyForm.isCover==1?'****':( Array.isArray(policyForm.customer) ? policyForm.customer.join(',') : policyForm.customer) }}</span> -->
                        <span v-if="isNotVE()">{{ policyForm.isCover == 1 ? '****' : policyForm.customer }}</span>
                    </td>
                    <th colspan="2">
                        <span style="color: red" v-if="isNotDrafted() && !isNotVE()">*</span>
                        客户名称
                    </th>
                    <td colspan="2">
                        <a-select show-search v-model="policyForm.customerNameList" placeholder="请选择客户"
                            v-if="!isNotVE()" mode="tags" @change="nameChange">
                            <a-select-option v-for="d in customerNameList" :key="d.id" :value="d.name">
                                {{ d.name }}
                            </a-select-option>
                        </a-select>
                        <!--                       <a-input disabled v-if="isNotVE()" :value="policyForm.isCover==1?'****':policyForm.customerName" />-->
                        <span v-if="isNotVE()">{{ policyForm.isCover == 1 ? '****' : policyForm.customerName }}</span>
                    </td>
                </tr>
                <tr>
                    <th colspan="2">
                        收益测算意见框
                    </th>
                    <td colspan="10">
                        <a-textarea v-if="!isNotVE()" auto-size v-model="policyForm.measurementOpinion"
                            style="border: none;" placeholder="收益测算意见框">
                            {{ policyForm.isCover == 1 ? '****' : policyForm.measurementOpinion }}
                        </a-textarea>
                        <span v-if="isNotVE()">{{ policyForm.isCover == 1 ? '****' : policyForm.measurementOpinion
                            }}</span>
                    </td>
                </tr>
                <tr>
                    <th colspan="6">测算信息</th>
                    <th colspan="5">参考信息</th>
                    <th rowspan="2" style="width: 14%">功能</th>
                </tr>
                <tr>

                    <th style="width: 6%">新增折让</th>
                    <th style="width: 6%">实际价格</th>
                    <th style="width: 12%">边贡率（实际价格）</th>
                    <th style="width: 6%">新增推广</th>
                    <th style="width: 4%">净价</th>
                    <th style="width: 9%">边贡率(净价)</th>
                    <th style="width: 5%">年度</th>
                    <th style="width: 9%">产品型号</th>
                    <th style="width: 9%">状态机</th>
                    <th style="width: 12%">边贡率（实际价格）</th>
                    <th style="width: 9%">边贡率（净价）</th>
                </tr>
                <tr v-for="d in policyForm.detailList" :key="d.id">
                    <td style="width: 6%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.newDiscount }}</td>
                    <td style="width: 6%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.budgetPrice }}</td>
                    <td style="width: 12%;text-align: center">{{ policyForm.isCover == 1 ? '**' : (d.borderRate ?
                            (d.borderRate +
                                '%') : '') }}</td>
                    <td style="width: 6%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.newPromote }}</td>
                    <td style="width: 4%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.netPrice }}</td>
                    <td style="width: 9%;text-align: center">
                        {{ policyForm.isCover == 1 ? '**' : (d.netBorderRate ? (d.netBorderRate + '%') : '') }}</td>
                    <td style="width: 5%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.calcYear }}</td>
                    <td style="width: 9%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.productModel }}</td>
                    <td style="width: 9%;text-align: center">{{ policyForm.isCover == 1 ? '**' : d.refMochineType }}
                    </td>
                    <td style="width: 12%;text-align: center">
                        {{ policyForm.isCover == 1 ? '**' : (d.refBorderRate ? (d.refBorderRate + '%') : '') }}</td>
                    <td style="width: 9%;text-align: center">
                        {{ policyForm.isCover == 1 ? '**' : (d.refNetBorderRate ? (d.refNetBorderRate + '%') : '') }}
                    </td>

                    <td style="width: 14%;padding-left: 2px;padding-right: 2px;text-align: center">
                        <a v-if="policyForm.isCover != 1" @click="viewPolicy(d)" style="margin: 0">查看详情</a>
                        <a-divider v-if="!isNotVE()" type="vertical" style="margin: 0 3px" />
                        <a v-if="!isNotVE()" @click="editPolicy(d)" style="margin: 0">编辑</a>
                        <a-divider v-if="!isNotVE()" type="vertical" style="margin: 0 3px" />
                        <a v-if="!isNotVE()" @click="deleteDetail(d.id)" style="margin: 0">删除</a>
                    </td>
                </tr>
                <tr v-if="!isNotVE()">
                    <td colspan="12">
                        <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus"
                            @click="showPolicyForm">新增
                        </a-button>
                    </td>
                </tr>

            </tbody>
        </table>
        <div class="bussPolicyFormModelStyle" ref="bussPolicyFormModel">
            <a-modal width="60%" :getContainer="() => $refs.bussPolicyFormModel" @cancel="showForm = false"
                @ok="saveInfo" :visible="showForm">
                <div slot="title">
                    <span>{{ policy.title }}</span>
                    <span style="float: right;margin-right: 50px;font-size: 14px">无税/(元/台) </span>
                </div>
                <a-spin :spinning="saving">
                    <a-form-model ref="policy" :model="policy" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                        <table style="width: 100%">
                            <tbody class="ant-table model-table">
                                <tr>
                                    <th style="text-align: left;padding-left: 5px" colspan="7">
                                        <span style="font-size: 16px">测算信息</span>
                                    </th>
                                </tr>
                                <tr>
                                    <th rowspan="2" style="width: 12%;text-align: center">价格类型</th>
                                    <th colspan="2" style="width: 24%;text-align: center">新增商务</th>
                                    <th rowspan="2" style="width: 12%;text-align: center">价格</th>
                                    <th rowspan="2" style="width: 12%;text-align: center">边贡率<br />(基准成本)</th>
                                    <th rowspan="2" style="width: 12%;text-align: center">边贡率<br />(实际成本)</th>
                                    <th rowspan="2" style="width: 28%;text-align: center">备注</th>
                                </tr>
                                <tr>
                                    <th style="width: 12%;text-align: center">类型</th>
                                    <th style="width: 12%;text-align: center">金额</th>
                                </tr>
                                <tr>
                                    <td style="width: 12%;text-align: center">实际价格</td>
                                    <td style="width: 12%;text-align: center">新增折让</td>
                                    <td style="width: 12%;text-align: center">
                                        <a-input v-if="!policy.isView" type="text" min="0.01"
                                            v-model="policy.newDiscount" @input="inputMoney(policy, 'newDiscount')"
                                            @blur="coverMoney(policy, 'newDiscount')" />
                                        <span v-if="policy.isView">{{ policy.newDiscount }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.budgetPrice }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.borderRate ? (policy.borderRate + "%") : "" }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.mtBorderRate ? (policy.mtBorderRate + "%") : "" }}</span>
                                    </td>
                                    <td rowspan="2" style="width: 28%;text-align: center">
                                        <span>实际价格=参考实际价格-新增折让</span>
                                        <span>净价=参考净价-新增折让-新增推广</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 12%;text-align: center">净价</td>
                                    <td style="width: 12%;text-align: center">新增推广</td>
                                    <td style="width: 12%;text-align: center">
                                        <a-input v-if="!policy.isView" type="text" min="0.01"
                                            v-model="policy.newPromote" @input="inputMoney(policy, 'newPromote')"
                                            @blur="coverMoney(policy, 'newPromote')" />
                                        <span v-if="policy.isView">{{ policy.newPromote }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.netPrice }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.netBorderRate ? (policy.netBorderRate + "%") : "" }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.mtNetBorderRate ? (policy.mtNetBorderRate + "%") : "" }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th>文本备注：</th>
                                    <td colspan="6">
                                        <textarea :readOnly="policy.isView" class="xsgs-textarea" style="height: 33px;"
                                            v-model="policy.remark" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <table style="width: 100%;margin-top: 5px">
                            <tbody class="ant-table model-table">
                                <tr>
                                    <th style="text-align: left;padding-left: 5px" colspan="8">
                                        <span style="font-size: 16px">参考信息</span>
                                    </th>
                                </tr>
                                <tr>
                                    <th style="width: 10%;text-align: center">年度</th>
                                    <th style="width: 16%;text-align: center">产品型号</th>
                                    <th colspan="2" style="width: 24%;text-align: center">状态机</th>
                                    <th style="width: 15%;text-align: center">品系1</th>
                                    <th style="width: 15%;text-align: center">上一年度采购量</th>
                                    <th style="width: 10%;text-align: center">基准成本</th>
                                    <th style="width: 10%;text-align: center">实际成本</th>
                                </tr>
                                <tr>
                                    <td style="width: 10%;text-align: center">
                                        <a-input v-if="!policy.isView" :class="formRule.calcYear.className"
                                            :allowClear='true' v-model="policy.calcYear"
                                            @blur="checkRequired2(policy, 'calcYear')" />
                                        <span v-if="policy.isView">{{ policy.calcYear }}</span>
                                    </td>
                                    <td style="width: 16%;text-align: center">
                                        <a-select v-if="!policy.isView" v-model="policy.productModel" show-search
                                            :class="formRule.productModel.className" :allowClear='true'
                                            :default-active-first-option="false" :show-arrow="false"
                                            :filter-option="false" :not-found-content="null"
                                            @blur="checkRequired2(policy, 'productModel')"
                                            @dropdownVisibleChange="productModelSearch()" @search="productModelSearch"
                                            @change="changeProductModel">
                                            <a-select-option v-for="str in productModelList" :key="str">
                                                {{ str }}
                                            </a-select-option>
                                        </a-select>
                                        <span v-if="policy.isView">{{ policy.productModel }}</span>
                                    </td>
                                    <td colspan="2" style="width: 24%;text-align: center">
                                        <a-select v-if="!policy.isView" v-model="policy.refMochineType" show-search
                                            :class="formRule.refMochineType.className" :allowClear='true'
                                            :default-active-first-option="false" :show-arrow="false"
                                            :filter-option="false" :not-found-content="null"
                                            @change="machineTypeBlues(policy, 'refMochineType')"
                                            @dropdownVisibleChange="machineTypeSearch()" @search="machineTypeSearch">
                                            <a-select-option v-for="str in machineTypeList" :key="str">
                                                {{ str }}
                                            </a-select-option>
                                        </a-select>
                                        <span v-if="policy.isView">{{ policy.refMochineType }}</span>
                                    </td>
                                    <td style="width: 15%;text-align: center"><span>{{ policy.refType }}</span></td>
                                    <td style="width: 15%;text-align: center"><span>{{ policy.refSales }}</span></td>
                                    <td style="width: 10%;text-align: center">
                                        <span>{{ policy.baseCost }}</span>
                                    </td>
                                    <td style="width: 10%;text-align: center">
                                        <span>{{ policy.materialCost }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width: 10%;text-align: center">价格类型</th>
                                    <th style="width: 16%;text-align: center">价格</th>
                                    <th style="width: 12%;text-align: center">边贡率<br />(基准成本)</th>
                                    <th style="width: 12%;text-align: center">边贡率<br />(实际成本)</th>
                                    <th style="width: 15%;text-align: center">全年主机厂<br />推广合计</th>
                                    <th colspan="3" style="width: 35%;text-align: center">备注</th>
                                </tr>
                                <tr>
                                    <th style="width: 10%;text-align: center">实际价格</th>
                                    <td style="width: 16%;text-align: center">
                                        <a-select v-if="!policy.isView" v-model="policy.refMochinePrice"
                                            :default-active-first-option="false" :show-arrow="false" :allowClear='true'
                                            :filter-option="false" :not-found-content="null"
                                            @change="changeRefPrice(policy)">
                                            <a-select-option v-for="p in refPriceList" :key="p.unitNetPrice"
                                                :value="p.unitNetPrice" @click="selectRefPrice(policy, p)">
                                                {{ p.unitNetPrice }}
                                            </a-select-option>
                                        </a-select>
                                        <span v-if="policy.isView">{{ policy.refMochinePrice }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.refBorderRate ? (policy.refBorderRate + '%') : '' }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.mtRefBorderRate ? (policy.mtRefBorderRate + '%') : '' }}</span>
                                    </td>
                                    <td rowspan="2" style="width: 15%;text-align: center">
                                        <a-input v-if="!policy.isView" type="text" min="0.01"
                                            v-model="policy.refPromote" @input="inputMoney(policy, 'refPromote')"
                                            @blur="coverMoney(policy, 'refPromote')" />
                                        <span v-if="policy.isView">{{ policy.refPromote }}</span>
                                    </td>
                                    <td colspan="3" rowspan="2" style="width: 35%;text-align: center">
                                        <span>净价=实际价格-全年主机厂推广合计</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width: 10%;text-align: center">净价</th>
                                    <td style="width: 16%;text-align: center">
                                        <span>{{ policy.refNetPrice }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.refNetBorderRate ? (policy.refNetBorderRate + '%') : ''
                                            }}</span>
                                    </td>
                                    <td style="width: 12%;text-align: center">
                                        <span>{{ policy.mtRefNetBorderRate ? (policy.mtRefNetBorderRate + '%') : ''
                                            }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <th style="width: 10%;">文本备注：</th>
                                    <td colspan="7" style="width: 90%">
                                        <textarea :readOnly="policy.isView" class="xsgs-textarea" style="height: 33px;"
                                            v-model="policy.refRemark" />
                                    </td>
                                </tr>


                            </tbody>
                        </table>
                        <!--                        <a-card title="基本信息">-->
                        <!--                            <a-row>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item label="年度">-->
                        <!--                                        <a-input v-if="!policy.isView" :class="formRule.calcYear.className" :allowClear='true' v-model="policy.calcYear" @blur="checkRequired2(policy, 'calcYear')"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.calcYear}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item label="产品型号">-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item label="状态机">-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item label="基准成本">-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item label="实际成本">-->
                        <!--                                        <a-input-->
                        <!--                                                type="text" min="0.01" v-model="policy.materialCost"-->
                        <!--                                                :allowClear='true'-->
                        <!--                                                v-if="!policy.isView"-->
                        <!--                                                @input="inputMoney(policy,'materialCost')"-->
                        <!--                                                @blur="coverMoney(policy,'materialCost')"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.materialCost}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                            </a-row>-->
                        <!--                        </a-card>-->
                        <!--                        <a-card title="参考信息">-->
                        <!--                            <a-row>-->
                        <!--                                <a-col span="8">-->
                        <!--                                    <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="实际价格">-->
                        <!--                                        <a-select v-if="!policy.isView"-->
                        <!--                                                  v-model="policy.refMochinePrice"-->
                        <!--                                                  :default-active-first-option="false"-->
                        <!--                                                  :show-arrow="false"-->
                        <!--                                                  :allowClear='true'-->
                        <!--                                                  :filter-option="false"-->
                        <!--                                                  :not-found-content="null"-->
                        <!--                                                  @change="changeRefPrice(policy)"-->
                        <!--                                                  @dropdownVisibleChange="getRefPriceList(policy)"-->
                        <!--                                        >-->
                        <!--                                            <a-select-option v-for="p in refPriceList" :key="p.unit_net_price" :value="p.unit_net_price"-->
                        <!--                                                             @click="selectRefPrice(policy, p)">-->
                        <!--                                                {{ p.unit_net_price}}-->
                        <!--                                            </a-select-option>-->
                        <!--                                        </a-select>-->
                        <!--                                        <span v-if="policy.isView">{{policy.refMochinePrice}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="7">-->
                        <!--                                    <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="边贡率(基准)">-->
                        <!--                                        <a-input v-if="!policy.isView" readOnly addon-after="%" v-model="policy.refBorderRate"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.refBorderRate?(policy.refBorderRate + '%'):''}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="7">-->
                        <!--                                    <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="边贡率(实际)">-->
                        <!--                                        <a-input v-if="!policy.isView" readOnly addon-after="%" v-model="policy.mtRefBorderRate"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.mtRefBorderRate?(policy.mtRefBorderRate + '%'):''}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="6">-->
                        <!--                                    <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="主机厂推广合计">-->
                        <!--                                        <a-input-->
                        <!--                                                v-if="!policy.isView"-->
                        <!--                                                type="text" min="0.01" v-model="policy.refPromote"-->
                        <!--                                                @input="inputMoney(policy,'refPromote')"-->
                        <!--                                                @blur="coverMoney(policy,'refPromote')"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.refPromote}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-row style="border-bottom: 1px solid ">-->

                        <!--                                </a-row>-->
                        <!--                                <a-col span="4">-->
                        <!--                                    <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="净价">-->
                        <!--                                        <a-input v-if="!policy.isView"  readOnly v-model="policy.refNetPrice"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.refNetPrice}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="7">-->
                        <!--                                    <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="净价边贡率(基准)">-->
                        <!--                                        <a-input v-if="!policy.isView" readOnly addon-after="%" v-model="policy.refNetBorderRate"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.refNetBorderRate?(policy.refNetBorderRate + '%'):''}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="7">-->
                        <!--                                    <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="净价边贡率(实际)">-->
                        <!--                                        <a-input v-if="!policy.isView" readOnly addon-after="%" v-model="policy.mtRefNetBorderRate"/>-->
                        <!--                                        <span v-if="policy.isView">{{policy.mtRefNetBorderRate?(policy.mtRefNetBorderRate + '%'):''}}</span>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                                <a-col span="6">-->
                        <!--                                    <a-form-model-item :labelCol="{span:6}" :wrapperCol="{span:18}" label="备注">-->
                        <!--                                        &lt;!&ndash;                                        <a-textarea :readOnly="policy.isView" style="height: 33px" v-model="policy.refRemark"/>&ndash;&gt;-->
                        <!--                                        <textarea :readOnly="policy.isView" style="height: 33px" class="xsgs-textarea" v-model="policy.refRemark"/>-->
                        <!--                                    </a-form-model-item>-->
                        <!--                                </a-col>-->
                        <!--                            </a-row>-->
                        <!--                        </a-card>-->
                        <!--                        <a-card title="测算信息">-->
                        <!--                            <a-col span="5">-->
                        <!--                                <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="新增折让">-->

                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="5">-->
                        <!--                                <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="实际价格">-->

                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="7">-->
                        <!--                                <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="边贡率(基准)">-->

                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="7">-->
                        <!--                                <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="边贡率(实际)">-->

                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="5">-->
                        <!--                                <a-form-model-item :labelCol="{span:12}" :wrapperCol="{span:12}" label="新增推广">-->
                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="5">-->
                        <!--                                <a-form-model-item :labelCol="{span:10}" :wrapperCol="{span:14}" label="净价">-->
                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="7">-->
                        <!--                                <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="净价边贡率(基准)">-->
                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="7">-->
                        <!--                                <a-form-model-item :labelCol="{span:14}" :wrapperCol="{span:10}" label="净价边贡率(实际)">-->
                        <!--                                    <a-input v-if="!policy.isView" readOnly addon-after="%" v-model="policy.mtNetBorderRate"/>-->
                        <!--                                    <span v-if="policy.isView">{{policy.mtNetBorderRate?(policy.mtNetBorderRate + '%'):''}}</span>-->
                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                            <a-col span="12">-->
                        <!--                                <a-form-model-item :labelCol="{span:5}" :wrapperCol="{span:19}" label="备注">-->
                        <!--                                    &lt;!&ndash;                                    <a-textarea :readOnly="policy.isView" style="height: 33px" v-model="policy.remark"/>&ndash;&gt;-->
                        <!--                                    <textarea :readOnly="policy.isView" class="xsgs-textarea" style="height: 33px;" v-model="policy.remark"/>-->
                        <!--                                </a-form-model-item>-->
                        <!--                            </a-col>-->
                        <!--                        </a-card>-->

                    </a-form-model>
                </a-spin>
            </a-modal>
        </div>
        <!-- 相关历史流程 -->
        <table style="width: 100%">
            <tbody class="ant-table">
                <tr>
                    <th style="text-align: center" colspan="2">历史流程</th>
                    <td style="width: 100%;" colspan="10">
                        <a-collapse v-model="activeKey">
                            <a-collapse-panel key="1" header="相关历史流程">
                                <div class="relevant_history" v-if="historyList.length > 0">
                                    <table style="width: 100%">
                                        <tbody class="ant-table">
                                            <tr>
                                                <th style="width: 10%">商务年度</th>
                                                <th style="width: 15%">流程编号</th>
                                                <th style="width: 15%">客户号</th>
                                                <th style="width: 7%">流程主题</th>
                                                <th style="width: 7%">操作</th>
                                            </tr>
                                            <tr v-for="d in historyList" :key="d.id">
                                                <td class="textTd" style="width: 10%;;text-align: center">
                                                    {{ d.businessYear }}</td>
                                                <td class="textTd" style="width: 15%;text-align: center">
                                                    {{ d.processNumber }}</td>
                                                <td class="textTd" style="width: 7%;text-align: center">
                                                    {{ d.customer }}</td>
                                                <td class="textTd" style="width: 48%;text-align: center">{{ d.title }}
                                                </td>
                                                <td class="textTd" style="width: 7%;text-align: center">
                                                    <a type="primary" @click="handleView(d)">查看</a>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div>
                                    <a-empty v-if="historyList.length == 0" description="暂无相关历史流程"
                                        style="margin: 20px;"> </a-empty>
                                </div>
                            </a-collapse-panel>
                        </a-collapse>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- <a-collapse v-model="activeKey">
            <a-collapse-panel key="1" header="相关历史流程">
                <div class="relevant_history" v-if="historyList.length > 0">
                    <table style="width: 100%">
                        <tbody class="ant-table">
                            <tr>
                                <th style="width: 10%">商务年度</th>
                                <th style="width: 15%">流程编号</th>
                                <th style="width: 15%">客户号</th>
                                <th style="width: 7%">流程主题</th>
                                <th style="width: 7%">操作</th>
                            </tr>
                            <tr v-for="d in historyList" :key="d.id">
                                <td class="textTd" style="width: 10%;;text-align: center">{{ d.businessYear }}</td>
                                <td class="textTd" style="width: 15%;text-align: center">{{ d.processNumber }}</td>
                                <td class="textTd" style="width: 7%;text-align: center">{{ d.customer }}</td>
                                <td class="textTd" style="width: 48%;text-align: center">{{ d.title }}</td>
                                <td class="textTd" style="width: 7%;text-align: center">
                                    <a-button type="primary" @click="handleView(d)">查看</a-button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <a-empty v-if="historyList.length == 0" description="暂无相关历史流程" style="margin: 20px;"> </a-empty>
                </div>
            </a-collapse-panel>
        </a-collapse> -->


    </div>

</template>

<script>
import {
    GET_XSGSPROCESSPOLICY_ID,
    LIST_XSGSCUSTOMER,
    COMBO_MACHINETYPE,
    GET_REF_PRICE_LIST,
    COMBO_PRODUCTMODEL,
    GET_MATERIAL_COST,
    GET_PRE_SALES, LIST_XSGSALLOWANCEITEM_PAGE,
    GET_PROCESS_PROCESSHISTORYQUERY
} from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import Cookie from 'js-cookie';
import FileUpload from "../../../components/upload/FileUpload";

export default {
    name: "buss_policy",
    props: {
        form: Object,
        userInfo: Object,
        isView: { type: Boolean, default: false },
    },
    components: {
        FileUpload
    },
    data() {
        return {
            Cookie,
            requiredBorder: "1px solid red !important",
            normalBorder: "1px solid #d9d9d9 !important",
            requiredClassName: "requiredClass",
            normalClassName: "normalClass",
            detailFontSize: 14,
            showForm: false,
            saving: false,
            getting: false,
            policyForm: {
                calcYear: null,
                customer: null,
                customerName: null,
                selectCustomer: [],
                checkResult: true,
                isCover: null,
                annexList: [],
                detailList: [],
                businessYear: null,
                measurementOpinion: null,
                customerList: [],
                customerNameList: [],
            },
            test: [],
            customerArray: [],
            policy: {
                title: '新增测算信息',
                isView: false,
                id: null,
                policyId: null,
                mainId: null,
                calcYear: null,
                customer: null,
                customerName: null,
                productModel: null,
                refMochineType: null,
                refMochinePrice: null,
                refBorderRate: null,
                refPromote: null,
                refNetPrice: null,
                refNetBorderRate: null,
                refRemark: null,
                newPolicy: null,
                newDiscount: null,
                newPromote: null,
                netPrice: null,
                netBorderRate: null,
                budgetPrice: null,
                borderRate: null,
                remark: null,
                baseCost: null,
                materialCost: null,
                mtRefBorderRate: null,
                mtRefNetBorderRate: null,
                mtBorderRate: null,
                mtNetBorderRate: null,
            },
            formRule: {},
            initRule: {
                calcYear: {
                    border: "1px solid #ffffff !important",
                    nodeCode: "value_engine_approval",
                    className: "normalClass",
                    msg: "请输入年度"
                },
                businessYear: {
                    border: "1px solid #d9d9d9 !important", nodeCode: "value_engine_approval", msg: "请输入"
                },
                customer: { border: "1px solid #d9d9d9 !important", nodeCode: "value_engine_approval", msg: "请输入客户" },
                customerName: {
                    border: "1px solid #d9d9d9 !important",
                    nodeCode: "value_engine_approval",
                    msg: "请选择客户"
                },
                series: { border: "1px solid #ffffff !important", nodeCode: "value_engine_approval", msg: "请输入系列" },
                productModel: {
                    border: "1px solid #ffffff !important", nodeCode: "value_engine_approval", msg: "请输入产品型号",
                    className: "normalClass"
                },
                refMochineType: {
                    border: "1px solid #ffffff !important",
                    nodeCode: "value_engine_approval",
                    msg: "请输入状态机",
                    targetType: 'SELECT'
                },
                newPolicy: { border: "1px solid #ffffff !important", nodeCode: "value_engine_approval", msg: "请输入新增政策" },
                forecastSales: { border: "1px solid #ffffff !important", nodeCode: "value_engine_approval", msg: "请输入预测销量" },
                baseCost: {
                    border: "1px solid #ffffff !important",
                    nodeCode: "value_engine_approval",
                    msg: "请输入基准成本"
                },
            },
            customerList: [],
            allCustomerList: [],
            machineTypeList: [],
            productModelList: [],
            refPriceList: [],
            nodeCode: '',
            historyList: [],
            activeKey: [],
            customerNameList: [],
        }
    },
    created() {
        this.formRule = this.copy(this.initRule);
        this.getCustomerList();
    },
    mounted() {
        // console.log(this.policyForm,this.form);
    },
    methods: {
        setNodeCode(code) {
            this.nodeCode = code
        },
        async initData(policyId, nodeCode,processId) {
            if (!policyId) {
                return;
            }
            await request(GET_XSGSPROCESSPOLICY_ID, METHOD.GET, { "id": policyId, "nodeCode": nodeCode,"processId":processId }).then(res => {
                if (res.data.data) {
                    this.policyForm = res.data.data;
                    if (!this.policyForm.customerName || this.policyForm.customerName.length == 0) {
                        this.policyForm.customerName = undefined;
                    }
                    if (!this.policyForm.customerList || this.policyForm.customerList.length == 0) {
                        this.policyForm.customerList = undefined;
                    }
                    if (this.policyForm.customer && this.policyForm.customer.length > 0 && this.policyForm.customerName && this.policyForm.customerName.length > 0) {
                        this.policyForm.customerNameList = this.policyForm.customerName.split('、');
                        this.policyForm.customerList = this.policyForm.customer.split('、');
                    }
                    let detailList = this.policyForm.detailList;
                    detailList.forEach(item => {
                        if (!item.refMochineType || item.refMochineType.length == 0) {
                            item.refMochineType = undefined;
                        }
                        if (!item.refMochinePrice || item.refMochinePrice.length == 0) {
                            item.refMochinePrice = undefined;
                        }
                    })

                }
            })
            // console.log(this.policyForm);
            // 相关历史流程数据初始化
            if (this.policyForm.businessYear && this.policyForm.customer) {
                // console.log('111111111111111');
                await this.initHistoryList(this.policyForm.businessYear, this.policyForm.customer)
            }
            // await this.initHistoryList(this.policyForm.businessYear,this.policyForm.customer)
        },
        newDetail() {
            const newDataList = [...this.policyForm.detailList]
            newDataList.push({
                id: this.policyForm.detailList.length + Math.round(Math.random() * 1000) + 1,
                series: null,
                productModel: null,
                refMochineType: null,
                refMochinePrice: null,
                refBorderRate: null,
                newPolicy: null,
                forecastSales: null,
                budgetPrice: null,
                refCost: null,
                borderRate: null,
                borderAmount: null,
                isNewRecord: 1
            });
            this.policyForm.detailList = newDataList;
        },
        showPolicyForm() {
            // console.log(this.policyForm);
            if (!this.checkRequired(this.policyForm, "customer")) {
                return;
            }
            this.policy = {};
            this.formRule = this.copy(this.initRule);
            // this.policy.calcYear = this.policyForm.calcYear;
            this.policy.customer = this.policyForm.customer;
            this.policy.customerName = this.policyForm.customerName;
            this.policy.title = "新增测算信息";
            this.showForm = true;
        },
        saveInfo() {
            if (this.policy.isView) {
                this.showForm = false;
                return;
            }
            if (!this.policy.borderRate && !this.policy.netBorderRate) {
                let that = this;
                that.$confirm({
                    content: `尚未填写收益信息，是否继续保存？`,
                    onCancel: null,
                    onOk: () => {
                        that.doSave()
                    },
                });
            } else {
                this.doSave();
            }

        },
        doSave() {
            if (this.policy.id) {
                this.policyForm.detailList.forEach(s => {
                    if (s.id == this.policy.id) {
                        s['mainId'] = this.policy.mainId;
                        s['calcYear'] = this.policy.calcYear;
                        s['productModel'] = this.policy.productModel;
                        s['refMochineType'] = this.policy.refMochineType;
                        s['baseCost'] = this.policy.baseCost;
                        s['refMochinePrice'] = this.policy.refMochinePrice;
                        s['refBorderRate'] = this.policy.refBorderRate;
                        s['refPromote'] = this.policy.refPromote;
                        s['refNetPrice'] = this.policy.refNetPrice;
                        s['refNetBorderRate'] = this.policy.refNetBorderRate;
                        s['refRemark'] = this.policy.refRemark;
                        s['newDiscount'] = this.policy.newDiscount;
                        s['newPromote'] = this.policy.newPromote;
                        s['netPrice'] = this.policy.netPrice;
                        s['netBorderRate'] = this.policy.netBorderRate;
                        s['forecastSales'] = this.policy.forecastSales;
                        s['budgetPrice'] = this.policy.budgetPrice;
                        s['refCost'] = this.policy.refCost;
                        s['borderRate'] = this.policy.borderRate;
                        s['remark'] = this.policy.remark;
                        s['materialCost'] = this.policy.materialCost;
                        s['mtRefBorderRate'] = this.policy.mtRefBorderRate;
                        s['mtRefNetBorderRate'] = this.policy.mtRefNetBorderRate;
                        s['mtBorderRate'] = this.policy.mtBorderRate;
                        s['mtNetBorderRate'] = this.policy.mtNetBorderRate;
                        s['refSales'] = this.policy.refSales;
                        s['refType'] = this.policy.refType;
                    }
                });
            } else {
                this.policy.id = this.policyForm.detailList.length + Math.round(Math.random() * 1000) + 1;
                this.policy.isNewRecord = 1;
                this.policyForm.detailList.push(this.policy);
            }
            this.showForm = false;
        },
        editPolicy(d) {
            this.policy = { ...d };
            this.policy.title = "编辑测算信息";
            this.policy.isView = false;
            this.policy.customer = this.policyForm.customer;
            this.showForm = true;
        },
        viewPolicy(d) {
            this.policy = { ...d };
            this.policy.title = "查看测算信息";
            this.policy.isView = true;
            this.showForm = true;
        },
        deleteDetail(id) {
            let dataList = [...this.policyForm.detailList];
            let newDataList = dataList.filter(item => item.id != id);
            this.policyForm.detailList = newDataList;
        },
        getCustomerList() {
            request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
                this.allCustomerList = res.data.data;
                this.customerList = this.allCustomerList;
                this.customerNameList = this.allCustomerList
            })
        },
        customerBlurs() {
            this.$nextTick(() => {
                let customer = this.policyForm.customer;
                if (!customer) {
                    this.policyForm.customerName = undefined;
                }
                let newData = this.customerList.filter(item => item.customer == customer);
                if (newData.length > 0) {
                    this.policyForm.customerName = newData[0].name;
                } else {
                    newData = this.allCustomerList.filter(item => item.customer == customer);
                    if (newData.length > 0) {
                        this.policyForm.customerName = newData[0].name;
                    }
                }
                // console.log(customer,newData)s;
                // this.checkRequired(this.policyForm, "customer");

                if (this.policyForm.businessYear && this.policyForm.customer) {
                    this.initHistoryList(this.policyForm.businessYear, this.policyForm.customer)
                }

            })

        },
        handleCustomerSearch(value) {
            console.log(value);
            let newData = this.allCustomerList.filter(item => {
                if (!value) {
                    return true;
                } else {
                    if (!item.name) {
                        return false;
                    } else {
                        return item.name.indexOf(value) != -1;
                    }
                }
            });
            this.customerList = this.cutList(newData, 0, 20);
        },
        CustomerSearch(value) {
            console.log(value);
            let newData = this.allCustomerList.filter(item => {
                if (!value) {
                    return true;
                } else {
                    if (!item.customer) {
                        return false;
                    } else {
                        return item.customer.indexOf(value) != -1;
                    }
                }
            });
            // this.customerList = this.cutList(newData, 0, 20);
            this.customerArray = this.cutList(newData, 0, 20);
        },
        handleCustomerChange(value) {
            this.policyForm.customer = value;
        },
        selectCustomer(user) {
            // this.policyForm.customer.push(user.customer);
            // 去重
            // this.policyForm.customer = ([...new Set(this.policyForm.customer)])
            // console.log(this.policyForm,this.policyForm.customer,'22222222222');
        },

        selectCustomerName() {

        },
        customerChange(value) {
            this.updateCustomer(value)
        },
        nameChange(value) {
            // console.log('Selected:', value);
            this.updateCustomerName(value)
        },
        updateCustomerName(value) {
            // console.log(value);
            // 确保 value 数组不为空
            if (value && value.length > 0) {
                // 使用 .filter() 方法来获取所有匹配的项
                const matchedItems = this.customerNameList.filter((item) => {
                    return value.includes(item.name);
                });


                // 如果有匹配项，则更新 policyForm.customer
                if (matchedItems.length > 0) {
                    this.policyForm.customer = []
                    this.policyForm.customerName = []
                    // console.log('匹配的项：', matchedItems);
                    let customer = []
                    let name = []
                    matchedItems.map(item => {
                        name.push(item.name);
                        customer.push(item.customer);
                    });
                    this.policyForm.customerList = customer
                    this.policyForm.customerNameList = name;
                    this.policyForm.customer = customer.join(',');
                    this.policyForm.customerName = name.join(',');
                    if (this.policyForm.businessYear && this.policyForm.customerList) {
                        let customer = this.policyForm.customerList.join('、');
                        this.initHistoryList(this.policyForm.businessYear, customer)
                    }
                } else {
                    console.log('没有找到匹配的客户');
                }
            } else {
                this.policyForm.customer = []
                this.policyForm.customerName = []
                this.policyForm.customerList = []
                this.policyForm.customerNameList = []
                console.log('value 数组为空');
            }
        },
        updateCustomer(value) {
            // 当客户号发生变化时，更新客户名称
            // 确保 value 数组不为空
            if (value && value.length > 0) {
                // 使用 .filter() 方法来获取所有匹配的项
                const matchedItems = this.customerNameList.filter((item) => {
                    return value.includes(item.customer);
                });


                // 如果有匹配项，则更新 
                if (matchedItems.length > 0) {
                    this.policyForm.customer = []
                    this.policyForm.customerName = []
                    let customer = []
                    let name = []
                    matchedItems.map(item => {
                        name.push(item.name);
                        customer.push(item.customer);
                    });
                    this.policyForm.customerList = customer
                    this.policyForm.customerNameList = name;
                    this.policyForm.customer = customer.join(',');
                    this.policyForm.customerName = name.join(',');
                    console.log(this.policyForm.customer, this.policyForm.customerName);
                    if (this.policyForm.businessYear && this.policyForm.customerList) {
                        let customer = this.policyForm.customerList.join('、');
                        this.initHistoryList(this.policyForm.businessYear, customer)
                    }
                } else {
                    console.log('没有找到匹配的客户');
                }
            } else {
                this.policyForm.name = []
                this.policyForm.customer = []
                this.policyForm.customerList = []
                this.policyForm.customerNameList = []
                console.log('value 数组为空');
            }
        },
        businessYearBlurs() {
            if (this.policyForm.businessYear && this.policyForm.customerList) {
                let customer = this.policyForm.customerList.join('、');
                this.initHistoryList(this.policyForm.businessYear, customer)
            }
        },
        productModelSearch(value) {
            this.productModelList = [];
            if (!this.checkRequired2(this.policy, "calcYear",)) {
                return;
            }
            if (!this.checkRequired(this.policy, "customer")) {
                return;
            }
            let that = this;
            let machineTypes = [];
            if (this.policy.machineType) {
                machineTypes.push(this.policy.machineType)
            }
            request(COMBO_PRODUCTMODEL, METHOD.POST, {
                year: this.policy.calcYear ? (this.policy.calcYear + '') : null,
                customer: this.policy.customer, machineType: machineTypes
            }).then(res => {
                if (res.data.data) {
                    let newData = res.data.data;
                    if (value) {
                        newData = newData.filter(item => item.indexOf(value) != -1);
                    }
                    newData = this.cutList(newData, 0, 20);
                    that.productModelList = newData;
                }
            })
        },
        changeProductModel() {
            this.policy.refMochineType = null;
        },
        machineTypeSearch(value) {
            this.machineTypeList = [];
            if (this.isNotVE()) {
                return;
            }
            if (!this.checkRequired2(this.policy, "calcYear")) {
                return;
            }
            if (!this.checkRequired(this.policy, "customer")) {
                return;
            }
            let that = this;
            let productModel = [];
            if (this.policy.productModel) {
                productModel.push(this.policy.productModel)
            }
            request(COMBO_MACHINETYPE, METHOD.POST, {
                year: this.policyForm.calcYear ? (this.policyForm.calcYear + '') : null,
                customer: this.policyForm.customer, productModel: productModel
            }).then(res => {
                if (res.data.data) {
                    let newData = res.data.data;
                    if (value) {
                        newData = newData.filter(item => item.indexOf(value) != -1);
                    }
                    newData = this.cutList(newData, 0, 20);
                    that.machineTypeList = newData;
                }
            })
        },
        machineTypeBlues(form, field) {
            if (this.checkRequired2(form, field)) {
                // 获取成本信息
                this.saving = true;
                this.getting = true;
                request(GET_MATERIAL_COST, METHOD.GET, { "year": this.policy.calcYear, "machineType": this.policy.refMochineType, "customer": this.policy.customer }).then(res => {
                    if (res.data.data) {
                        this.policy.baseCost = res.data.data.baseCost;
                        this.policy.materialCost = res.data.data.materialCost;
                        this.getting = false;
                        this.calcDetail(form);
                        this.policy.refType = res.data.data.type;
                        this.policy.refSales = res.data.data.sales;
                        this.getRefPriceList(this.policy);
                        // this.getPreSales();
                        this.saving = false;
                    }
                })
            }
        },
        getRefPriceList(d) {
            this.refPriceList = [];
            let queryData = {
                year: this.policy.calcYear,
                customer: this.policy.customer,
                machineType: d.refMochineType
            };
            if (!queryData.machineType) {
                this.$message.error("请选择参考状态机");
                return;
            }
            let current = 1;
            let size = 100;
            request(LIST_XSGSALLOWANCEITEM_PAGE, METHOD.GET, { ...queryData, current, size })
                .then(res => {
                    this.$nextTick(function () {
                        const { records } = res.data.data
                        let priceList = [];
                        records.forEach(s => {
                            if (s.unitNetPrice && !priceList.includes(s.unitNetPrice)) {
                                this.refPriceList.push(s);
                            }
                        });
                        if (this.refPriceList.length == 0) {
                            this.$message.error("该机型价格不存在");
                        }
                        // this.refPriceList = records;
                    })


                })
            // request(GET_REF_PRICE_LIST, METHOD.POST, queryData).then(res => {
            //     this.refPriceList = res.data.data;
            // })
        },
        selectRefPrice(d, p) {
            this.$nextTick(() => {
                d.mainId = p.masterId;
                d.refMochinePrice = p.unitNetPrice;
                this.calcDetail(d);
            });
        },
        changeRefPrice(d) {
            if (!d.refMochinePrice) {
                d.mainId = null;
                this.refPriceList = [];
                this.calcDetail(d);
            }
        },
        inputMoney(d, field) {
            this.$nextTick(() => {
                let val = event.target.value ? event.target.value.toString() : "";
                if (val == ".") {
                    val = '';
                } else {
                    val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
                    val = val.replace(/\.{2,}/g, ""); //只保留第⼀个. 清除多余的
                    val = val.replace(/^0+\./g, '0.');
                    val = val.match(/^0+[1-9]+/) ? val = val.replace(/^0+/g, '') : val
                    val = (val.match(/^\d*(\.?\d{0,2})/g)[0]) || ''
                }
                d[field] = val;
                this.calcDetail(d);
            });
        },
        coverMoney(d, field) {
            let val = event.target.value ? event.target.value.toString() : "";
            this.$nextTick(() => {
                if (val) {
                    if (val.includes(".")) {
                        if (val.endsWith(".")) {
                            val = val + "00";
                        } else {
                            let point = val.split(".")[1];
                            let intValue = val.split(".")[0];
                            point += "000";
                            val = intValue + "." + point.substr(0, 2);
                        }
                    } else {
                        val = val + ".00";
                    }
                    d[field] = val;
                }
                this.calcDetail(d);
                // if (this.checkRequired2(d, field)) {
                //     this.calcDetail(d);
                // }
            });
        },
        inputInts(d, field) {
            this.$nextTick(() => {
                let val = event.target.value ? event.target.value.toString() : "";
                if (val == ".") {
                    val = '';
                } else {
                    val = val.replace(/[^0-9]/g, ''); //整数
                }
                d[field] = val;
                if (val) {
                    this.calcDetail(d);
                }
            });
        },
        coverInts(d, field) {
            this.$nextTick(() => {
                let target = document.getElementById(d.id + "_" + field);
                let val = target.value ? target.value.toString() : "";
                if (val == ".") {
                    val = '';
                } else {
                    val = val.replace(/[^0-9]/g, ''); //整数
                }
                d[field] = val;
                if (val) {
                    this.calcDetail(d);
                }
                this.checkRequired(d, field)
            });
        },
        calcDetail(d) {
            // 计算参考基准成本边贡率 1-基准成本/参考信息区实际价格
            if (d.baseCost && d.refMochinePrice) {
                let refBorderRate = (1 - (Number(d.baseCost) / Number(d.refMochinePrice))) * 100;
                this.$set(d, 'refBorderRate', refBorderRate.toFixed(2));
            } else {
                this.$set(d, 'refBorderRate', null);
            }

            // 计算参考实际成本边贡率 1-实际成本/参考信息区实际价格
            if (d.materialCost && d.refMochinePrice) {
                let mtRefBorderRate = (1 - (Number(d.materialCost) / Number(d.refMochinePrice))) * 100;
                this.$set(d, 'mtRefBorderRate', mtRefBorderRate.toFixed(2));
            } else {
                this.$set(d, 'mtRefBorderRate', null);
            }

            // 计算参考净价 参考信息区实际价格-主机厂推广合计(单台)
            if (d.refPromote && d.refMochinePrice) {
                this.$set(d, 'refNetPrice', Number(d.refMochinePrice) - Number(d.refPromote));
            } else {
                this.$set(d, 'refNetPrice', null);
                // d['refNetPrice'] = null;
            }

            // 计算参考基准成本净价边贡率 1-基准成本/参考信息区净价
            if (d.baseCost && d.refNetPrice) {
                let refNetBorderRate = (1 - (Number(d.baseCost) / Number(d.refNetPrice))) * 100;
                this.$set(d, 'refNetBorderRate', refNetBorderRate.toFixed(2));
            } else {
                this.$set(d, 'refNetBorderRate', null);
            }

            // 计算参考实际成本净价边贡率 1-基准成本/参考信息区净价
            if (d.materialCost && d.refNetPrice) {
                let mtRefNetBorderRate = (1 - (Number(d.materialCost) / Number(d.refNetPrice))) * 100;
                this.$set(d, 'mtRefNetBorderRate', mtRefNetBorderRate.toFixed(2));
            } else {
                this.$set(d, 'mtRefNetBorderRate', null);
            }

            // 计算测算实际价格 参考信息区实际价格-新增折让
            if (d.refMochinePrice && d.newDiscount) {
                this.$set(d, 'budgetPrice', Number(d.refMochinePrice) - Number(d.newDiscount));
            } else {
                this.$set(d, 'budgetPrice', null);
            }
            // 计算测算基准成本实际价格边贡率
            if (d.baseCost && d.budgetPrice) {
                let borderRate = (1 - (Number(d.baseCost) / Number(d.budgetPrice))) * 100;
                this.$set(d, 'borderRate', borderRate.toFixed(2));
            } else {
                this.$set(d, 'borderRate', null);
            }

            // 计算测算实际成本实际价格边贡率
            if (d.materialCost && d.budgetPrice) {
                let mtBorderRate = (1 - (Number(d.materialCost) / Number(d.budgetPrice))) * 100;
                this.$set(d, 'mtBorderRate', mtBorderRate.toFixed(2));
            } else {
                this.$set(d, 'mtBorderRate', null);
            }

            // 计算净价 参考信息区净价-新增推广
            if (d.refNetPrice && d.newDiscount && d.newPromote) {
                this.$set(d, 'netPrice', Number(d.refNetPrice) - Number(d.newPromote) - Number(d.newDiscount));
            } else {
                this.$set(d, 'netPrice', null);
            }

            // 计算参考净价边贡率 1-基准成本/测算信息区实际价格
            if (d.baseCost && d.netPrice) {
                let netBorderRate = (1 - (Number(d.baseCost) / Number(d.netPrice))) * 100;
                this.$set(d, 'netBorderRate', netBorderRate.toFixed(2));
            } else {
                this.$set(d, 'netBorderRate', null);
            }

            // 测算实际成本净价边贡率
            if (d.materialCost && d.netPrice) {
                let mtNetBorderRate = (1 - (Number(d.materialCost) / Number(d.netPrice))) * 100;
                this.$set(d, 'mtNetBorderRate', mtNetBorderRate.toFixed(2));
            } else {
                this.$set(d, 'mtNetBorderRate', null);
            }
            // // 测算价格
            // if (d.newPolicy && d.refMochinePrice) {
            //     let budgetPrice = Number(d.refMochinePrice) - Number(d.newPolicy);
            //     d.budgetPrice = budgetPrice;
            // } else {
            //     d.budgetPrice = null;
            // }
            // // 边贡率
            // if (d.refCost && d.budgetPrice) {
            //     let borderRate = (1 - (Number(d.refCost) / Number(d.budgetPrice))) * 100;
            //     d.borderRate = borderRate.toFixed(2);
            //     // 边贡额
            //     if (d.forecastSales) {
            //         let borderAmount = (Number(d.budgetPrice) - Number(d.refCost)) * Number(d.forecastSales);
            //         d.borderAmount = borderAmount.toFixed(2);
            //     }
            // } else {
            //     d.borderAmount = null;
            //     d.borderRate = null;
            // }
        },
        cutList(list, start, end) {
            if (!list || list.length == 0) {
                return [];
            }
            let len = end - start;
            if (list.length <= len) {
                return list;
            } else {
                return list.slice(start, end);
            }
        },
        checkRequired(form, field, isNotRemind) {
            console.log(form);
            if (!this.formRule[field]) {
                return true;
            }
            if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
                let value = form[field];
                if (!value) {
                    this.formRule[field].border = this.requiredBorder;
                    if (!isNotRemind) {
                        this.$message.error(this.formRule[field].msg);
                    }
                    return false;
                }
            }

            this.formRule[field].border = this.normalBorder;
            return true
        },
        checkRequired2(form, field, isNotRemind) {
            if (!this.formRule[field]) {
                return true;
            }
            if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
                let value = form[field];
                if (!value) {
                    this.formRule[field].className = this.requiredClassName;
                    if (!isNotRemind) {
                        this.$message.error(this.formRule[field].msg);
                    }
                    return false;
                }
            }

            this.formRule[field].className = this.normalBorder;
            return true
        },
        checkTableRequired(form, field, isNotRemind) {
            let target = document.getElementById(form.id + "_" + field);
            if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
                let value = form[field];
                if (!value) {
                    if (target) {
                        target.style.cssText = 'border:' + this.requiredBorder;
                    }
                    if (!isNotRemind) {
                        this.$message.error(this.formRule[field].msg);
                    }
                    return false;
                }
            }
            if (target) {
                target.style.cssText = 'border:' + this.normalBorder;
            }
            return true
        },
        checkPolicyForm() {

            let msg = null;
            if (this.form.nodeInfo.nodeCode == 'value_engine_approval') {
                console.log(this.form);
                // 客户
                // if (!msg && !this.checkRequired(this.policyForm, "customer", true)) {
                //     msg = this.formRule['customer'].msg;
                // }
                // if (!msg) {
                //     let detailList = this.policyForm.detailList;
                //     if (!detailList || detailList.length == 0) {
                //         msg = "请添加商务政策详细信息";
                //     }
                // }
                // console.log(this.form,this.policyForm);
                this.form.businessYear = this.policyForm.businessYear
                this.form.customerNameList = this.policyForm.customerNameList
                this.form.customerList = this.policyForm.customerList
                if (this.form.businessYear == '' || this.form.businessYear == null || this.form.businessYear == undefined) {
                    // this.$message.error("请填写商务年度");
                    msg = "请填写商务年度";
                }
                if ((!this.form.customerNameList || !this.form.customerList)) {

                    msg = "请填写客户名称和客户编号";
                } else if (this.form.customerNameList.length < 1 && this.form.customerList.length > 1) {
                    msg = "请填写客户名称";
                } else if (this.form.customerNameList.length > 1 && this.form.customerList.length < 1) {
                    msg = "请填写客户编号";
                }

            }
            if (msg) {
                this.$message.error(msg);
            }
            return !msg;
        },
        confirmPolicyForm() {
            let msg = null;
            if (this.form.nodeInfo.nodeCode == 'value_engine_approval') {
                let detailList = this.policyForm.detailList;
                if (!detailList || detailList.length == 0) {
                    msg = "您尚未填写测算数据，是否继续提交？";
                }
            }
            return msg;
        },
        handleUpload(info) {
            this.loading = true
            if (info.file.status !== 'uploading') {
                this.getData()
            }
            if (info.file.status === 'done') {
                this.$message.success(`${info.file.name} 导入成功`);
            } else if (info.file.status === 'error') {
                this.$message.error(`${info.file.name} 导入失败`);
            }
        },
        getPolicyInfo() {
            console.log(this.policyForm);
            return this.policyForm;
        },
        isNotDrafted() {
            return this.isView || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'drafted';
        },
        isNotVE() {
            return this.isView || !this.form.nodeInfo || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'value_engine_approval' || this.form.nodeInfo.userAccount != this.userInfo.code;
        },
        isNewNotVE() {
            return this.isView || !this.form.nodeInfo || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'value_engine_approval' || this.form.nodeInfo.nodeCode != 'market_business_leader_approval' || this.form.nodeInfo.nodeCode != 'joint_power_general_manager_approval' || this.form.nodeInfo.userAccount != this.userInfo.code;
        },
        coverNumber(num) {
            return !num ? 0 : Number(num);
        },
        coverRed(num) {
            return this.coverNumber(num) < 0 ? 'color:red !important' : '';
        },
        getPreSales() {
            this.getting = true;
            request(GET_PRE_SALES, METHOD.POST, { "year": this.policy.calcYear, "machineType": this.policy.refMochineType, "customer": this.policyForm.customer }).then(res => {
                if (res.data.data) {
                    this.policy.refType = res.data.data.type;
                    this.policy.refSales = res.data.data.sales;
                    this.getting = false;
                }
            })
        },
        async initHistoryList(businessYear, customer) {
            this.historyList = [];
            // let PramsCustomer = (customer.split('、')).join(',');
            // console.log(PramsCustomer);
            await request(GET_PROCESS_PROCESSHISTORYQUERY, METHOD.GET, { "businessYear": businessYear, "customer": customer, processId: this.form.nodeInfo.processId || '' }).then(res => {
                // console.log(res);
                if (res.data.data) {
                    this.historyList = res.data.data;
                }
            })
        },
        handleView(record) {
            // console.log(record);
            let url = "#/xsgs_process_manager/xsgs_business_applove/" + 'bussPolicy' + "/" + record.processId;
            // this.$router.push({path:url, props: true})
            window.open(url, "_blank");
        },
    }
}
</script>

<style scoped>
/deep/ .fontSmall td,
.fontSmall th,
.fontSmall input,
.fontSmall textarea,
.fontSmall select,
.fontSmall a,
.fontSmall button {
    font-size: 12px !important;
}

/deep/ .fontSmall tr,
.fontSmall td,
.fontSmall th {
    height: 22px !important;
}

/deep/ .fontMiddle td,
.fontMiddle th,
.fontMiddle input,
.fontMiddle textarea,
.fontMiddle select,
.fontMiddle a,
.fontMiddle button {
    font-size: 14px !important;
}

/deep/ .fontMiddle tr,
.fontMiddle td,
.fontMiddle th {
    height: 28px !important;
}

/deep/ .fontLarge td,
.fontLarge th,
.fontLarge input,
.fontLarge textarea,
.fontLarge select,
.fontLarge a,
.fontLarge button {
    font-size: 18px !important;
}

/deep/ .fontLarge tr,
.fontLarge td,
.fontLarge th {
    height: 33px !important;
}

/deep/ .policyDetail td>input {
    font-size: 14px !important;
}

/deep/ .ant-select-selection__placeholder {
    width: 100%;
}

/deep/ .ant-table .ant-select-selection-selected-value {}

/deep/ .ant-table .ant-select-selection__rendered {
    margin: 0 !important;
}

/deep/ .ant-table input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
}

/deep/ .ant-table input[type="number"] {
    -moz-appearance: textfield !important;
}

/deep/ .annexTd>div {
    float: left !important;
}

/deep/ .bussPolicyFormModelStyle .ant-modal-body {
    padding: 5px 24px;
}

/deep/ .bussPolicyFormModelStyle .ant-card-bordered {
    border: none;
}

/deep/ .bussPolicyFormModelStyle .ant-card-head {
    padding: 0 5px;
    min-height: 0;
}

/deep/ .bussPolicyFormModelStyle .ant-card-head-title {
    padding: 3px 0;
}

/deep/ .bussPolicyFormModelStyle .ant-card-body {
    padding: 8px;
}

/deep/ .requiredClass input {
    border-color: red;
}

/deep/ .requiredClass div[role='combobox'] {
    border-color: red;
}

/deep/ .ant-collapse>.ant-collapse-item>.ant-collapse-header {
    background: #bdd7ee;
}
</style>
