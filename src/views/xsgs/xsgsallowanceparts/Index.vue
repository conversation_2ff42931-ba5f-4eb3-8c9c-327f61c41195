<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col :span="6">
            <a-form-model-item label="主表ID">
              <a-input v-model="searchForm.masterId"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="产品型号">
              <a-input v-model="searchForm.productModel"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="选配件名称">
              <a-input v-model="searchForm.partsName"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="选配件图号">
              <a-input v-model="searchForm.partsNo"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="规格">
              <a-input v-model="searchForm.specs"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="本体/后处理">
              <a-input v-model="searchForm.mainType"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="特性（标准选型、选型）">
              <a-input v-model="searchForm.subType"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="价格">
              <a-input v-model="searchForm.price"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="类型(后台应用)">
              <a-input v-model="searchForm.type"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="创建人">
              <a-input v-model="searchForm.createUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="创建时间">
              <a-input v-model="searchForm.createTime"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="更新人">
              <a-input v-model="searchForm.updateUser"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="6">
            <a-form-model-item label="更新时间">
              <a-input v-model="searchForm.updateTime"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="handleSearch">
            <a-form-model-item>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-upload
                    name="file"
                    :action="UPLOAD_XSGSALLOWANCEPARTS"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="主表ID" prop="masterId">
                <a-input v-model="form.masterId"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="产品型号" prop="productModel">
                <a-input v-model="form.productModel"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="选配件名称" prop="partsName">
                <a-input v-model="form.partsName"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="选配件图号" prop="partsNo">
                <a-input v-model="form.partsNo"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="规格" prop="specs">
                <a-input v-model="form.specs"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="本体/后处理" prop="mainType">
                <a-input v-model="form.mainType"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="特性（标准选型、选型）" prop="subType">
                <a-input v-model="form.subType"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="价格" prop="price">
                <a-input v-model="form.price"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="类型(后台应用)" prop="type">
                <a-input v-model="form.type"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="创建人" prop="createUser">
                <a-input v-model="form.createUser"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="创建时间" prop="createTime">
                <a-input v-model="form.createTime"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="更新人" prop="updateUser">
                <a-input v-model="form.updateUser"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="更新时间" prop="updateTime">
                <a-input v-model="form.updateTime"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">

            </a-col>
          </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSALLOWANCEPARTS_PAGE,DELETE_XSGSALLOWANCEPARTS,SUBMIT_XSGSALLOWANCEPARTS,DOWNLOAD_XSGSALLOWANCEPARTS,UPLOAD_XSGSALLOWANCEPARTS} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgsallowanceparts.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSALLOWANCEPARTS,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '主表ID', dataIndex: 'masterId'},
        {title: '产品型号', dataIndex: 'productModel'},
        {title: '选配件名称', dataIndex: 'partsName'},
        {title: '选配件图号', dataIndex: 'partsNo'},
        {title: '规格', dataIndex: 'specs'},
        {title: '本体/后处理', dataIndex: 'mainType'},
        {title: '特性（标准选型、选型）', dataIndex: 'subType'},
        {title: '价格', dataIndex: 'price'},
        {title: '类型(后台应用)', dataIndex: 'type'},
        {title: '创建人', dataIndex: 'createUser'},
        {title: '创建时间', dataIndex: 'createTime'},
        {title: '更新人', dataIndex: 'updateUser'},
        {title: '更新时间', dataIndex: 'updateTime'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        masterId: [{required: false, message: '主表ID不能为空', trigger: 'blur'}],
        productModel: [{required: false, message: '产品型号不能为空', trigger: 'blur'}],
        partsName: [{required: false, message: '选配件名称不能为空', trigger: 'blur'}],
        partsNo: [{required: false, message: '选配件图号不能为空', trigger: 'blur'}],
        specs: [{required: false, message: '规格不能为空', trigger: 'blur'}],
        mainType: [{required: false, message: '本体/后处理不能为空', trigger: 'blur'}],
        subType: [{required: false, message: '特性（标准选型、选型）不能为空', trigger: 'blur'}],
        price: [{required: false, message: '价格不能为空', trigger: 'blur'}],
        type: [{required: false, message: '类型(后台应用)不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
      }
    }
  },
  mounted() {
    this.getData()
    this.checkName()
  },
  methods: {
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSALLOWANCEPARTS_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.productModel} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSALLOWANCEPARTS + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSALLOWANCEPARTS, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          window.open(decodeURI(DOWNLOAD_XSGSALLOWANCEPARTS))
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>

</style>
