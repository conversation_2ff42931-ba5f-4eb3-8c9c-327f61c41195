<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
          <tr>
            <td>
              <a-form-model-item label="年度"/>
            </td>
            <td>
              <a-input v-model="searchForm.year"/>
            </td>

            <td>
              <a-form-model-item label="零部件名称"/>
            </td>
            <td>
              <a-input v-model="searchForm.partsName"/>
            </td>
          </tr>
          <tr>
            <td>
              <a-form-model-item label="结构码"/>
            </td>
            <td>
              <a-input v-model="searchForm.jgCode"/>
            </td>

            <td>
              <a-form-model-item label="规格"/>
            </td>
            <td>
              <a-input v-model="searchForm.specs"/>
            </td>

            <td>
              <a-form-model-item label="供应商名称"/>
            </td>
            <td>
              <a-input v-model="searchForm.supplier"/>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch">
                <a-icon type="search"/>
                查询
              </a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery">
                <a-icon type="reload"/>
                重置
              </a-button>
            </td>
          </tr>
          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('PARTS_PRICES_ADD')" type="primary" style="margin: 0 8px"
                        @click="dialog=true;form={version: 1,custome: null,customerName: null}">
                <a-icon type="plus"/>
                新增
              </a-button>
              <a-button v-if="checkPf('PARTS_PRICES_DELETE')" type="danger" style="margin: 0 8px"
                        @click="deleteSelectedIds">
                <a-icon type="delete"/>
                删除
              </a-button>
              <a-upload v-if="checkPf('PARTS_PRICES_IMPORT')"
                        name="file"
                        accept=".xlsx"
                        :action="UPLOAD_XSGSPARTSPRICES"
                        :headers="{'Authorization':Cookie.get('Authorization')}"
                        :showUploadList="false"
                        style="margin: 0 8px"
                        @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button v-if="checkPf('PARTS_PRICES_EXPORT')" type="primary" style="margin: 0 8px"
                        @click="handleExportExcel">
                <a-icon type="download"/>
                导出
              </a-button>

            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :scroll="{ x: 1600 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_PRICES_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PARTS_PRICES_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PARTS_PRICES_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        width="50%"
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="年度" prop="year">
              <a-input v-model="form.year"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="客户号" prop="customer">
              <a-input v-model="form.customer" @blur="customerBlurs2"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="客户名称" prop="customerName">
              <a-select
                  v-model="form.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :allowClear="true"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch2"
                  @change="handleCustomerChange2"
              >
                <a-select-option v-for="d in customerList2" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="零部件名称" prop="partsName">
              <a-input v-model="form.partsName"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="结构码" prop="jgCode">
              <a-input v-model="form.jgCode"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系列" prop="series">
              <a-input v-model="form.series"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="规格" prop="specs">
              <a-input v-model="form.specs"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="形式" prop="xs">
              <a-input v-model="form.xs"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="供应商名称" prop="supplier">
              <a-input v-model="form.supplier"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="指导售价" prop="actualPrice">
<!--              <a-input type="number" v-model="form.actualPrice"/>-->
              <a-input-number v-model="form.actualPrice"
                              :step="0.01"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="预算价格" prop="budgetedPrice">
<!--              <a-input v-model="form.budgetedPrice"/>-->
              <a-input-number v-model="form.budgetedPrice"
                              :step="0.01"
                              :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                              :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {
  LIST_XSGSPARTSPRICE_PAGE,
  DELETE_XSGSPARTSPRICE,
  SUBMIT_XSGSPARTSPRICE,
  DOWNLOAD_XSGSPARTSPRICE,
  DELETE_XSGS_PARTS_PRICE_LIST,
  LIST_XSGSCUSTOMER,
  UPLOAD_XSGSPARTSPRICES
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'

export default {
  name: "xsgspartsprices.vue",
  data() {
    return {
      UPLOAD_XSGSPARTSPRICES,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      // 功能权限关键字
      PF_FIELD: "BASE_DATA_MANAGER,PARTS_PRICES",
      PF_LIST: [],
      searchForm: {version: 1},
      form: {
        version: 1,
        custome: null,
        customerName: null
      },
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {
          title: '年度', dataIndex: 'year', width: 60,
          sorter: (a, b) => a.year - b.year,
        },
        {title: '客户号', dataIndex: 'customer', width: 70},
        {title: '客户名称', dataIndex: 'customerName', width: 300, ellipsis: true},
        {title: '零部件名称', dataIndex: 'partsName', ellipsis: true, width: 180},
        {title: '结构码', dataIndex: 'jgCode', ellipsis: true, width: 80},
        {title: '系列', dataIndex: 'series', ellipsis: true,
          customRender: (val) => {
            return val ? val.substr(0,val.length-1) : ''
          }
        },
        {title: '规格', dataIndex: 'specs', ellipsis: true, width: 100},
        {title: '形式', dataIndex: 'xs', ellipsis: true, width: 130},
        {title: '供应商名称', dataIndex: 'supplier', width: 100, ellipsis: true},
        {title: '指导售价', dataIndex: 'actualPrice', width: 90},
        {title: '预算价格', dataIndex: 'budgetedPrice', width: 90},
        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
        partsName: [{required: true, message: '零部件名称不能为空', trigger: 'blur'}],
        actualPrice: [{required: true, message: '指导售价不能为空', trigger: 'blur'}],
      },
      customerList: [],
      customerList2: [],
      allCustomerList: [],
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getCustomerList();
    this.getData()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSPARTSPRICE_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    /** 批量删除 **/
    deleteSelectedIds() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_PARTS_PRICE_LIST, METHOD.POST, {ids: this.selectedRowKeys})
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    /** 单条删除 **/
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.partsName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSPRICE + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    /** 新增编辑 提交 **/
    handleSubmit() {
      this.form.series = this.form.series ? (this.form.series + '/') : null
      this.$refs.form.validate(valid => {
        if (valid) {
          // console.log(this.form);
          // 将空值和undefined替换为null
          for (let key in this.form) {
            if (this.form[key] === undefined || this.form[key] === '') {
              this.form[key] = null;
            }
          }
          
          this.loading = true
          request(SUBMIT_XSGSPARTSPRICE, METHOD.POST, this.form)
              .then(() => {
                this.getData()
                this.$message.info('提交成功')
                this.closeForm()
              })
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {version: 1}
      this.handleSearch();
    },
    handleEdit(record) {
      if(record.series != undefined){
        record.series = record.series.substring(0,record.series.length-1)
      }
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    /** 上传 **/
    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        let msg = `${info.file.name} 上传成功`;
        if (info.file.response && info.file.response.data) {
          let data = info.file.response.data;
          let errList = data.errorList;
          let sucCount = data.sucCount;
          if (errList && errList.length > 0) {
            msg += "\n存在错误数据：" + errList.length + "条\n失败原因如下：";
            errList.forEach(item => {
              msg += "\n" + item + "；"
            })
            this.$warning({
              title: '导入失败提示',
              onOk: () => {

              },
              content: (
                  <div style="white-space: pre-wrap;">{msg}</div>
              ),
              width: 500
            });
          } else {
            msg += "，导入成功数：" + (sucCount ? sucCount : 0) + "条";
            this.$message.success(msg);
          }
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm = this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk: () => {
          exportExcel(DOWNLOAD_XSGSPARTSPRICE, {...searchForm}, "选配件价格列表.xlsx")
        }
      })
    },
    /** 获取所有客户 */
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
        const map = new Map()
        this.allCustomerList = res.data.data.filter(key => !map.has(key.name) && map.set(key.name, 1))
        this.customerList = this.allCustomerList
        this.customerList2 = this.allCustomerList
      })
    },
    /** 新增输入客户号 **/
    customerBlurs2() {
      let customer = this.form.customer;
      if (!customer) {
        this.form.customerName = null;
      }
      let newData = this.customerList2.filter(item => item.customer == customer);
      if (newData.length > 0) {
        this.form.customerName = newData[0].name;
      } else {
        newData = this.allCustomerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.form.customerName = newData[0].name;
        }
      }
    },
    /** 新增查询客户号 **/
    handleCustomerSearch2(value) {
      if (value) {
        this.customerList2 = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList2 = this.allCustomerList;
      }
    },
    /** 新增查询客户名称 **/
    handleCustomerChange2(value) {
      if (value) {
        this.customerList.forEach(item => {
          if (item.name == value) {
            this.form.customer = item.customer
            this.form.customerName = item.name
          }
        })
      } else {
        this.form.customer = null
      }
    },
  }
}
</script>

<style scoped>
.ant-input-number {
  width: 100%;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
*/
/deep/ .ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
