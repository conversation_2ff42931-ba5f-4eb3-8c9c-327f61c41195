<template>
  <div>
    <a-card>
      <a-row>
        <a-col :span="24" style="margin-bottom: 5px;margin-top: 10px">
				<span style="float: left">
					<a-button type="primary" style="margin-right: 8px" icon="plus" @click="showCreateModal">新增</a-button>
					<a-button type="danger" icon="delete" @click="delAll">删除</a-button>
          <a-upload
                    name="file"
                    accept=".xlsx"
                    :action="uploadUrl"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
				</span>
        </a-col>
      </a-row>
      <a-table rowKey="id"
               bordered
               size="small"
               :loading="spinning"
               :pagination="pagination"
               :data-source="data"
               :row-selection="rowSelection"
               :columns="columns">
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="action" slot-scope="text, record">
          <div>

            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="edit(record)">
              编辑
            </span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="del(record)">
              删除
            </span>
          </div>
        </template>
      </a-table>

      <a-modal v-drag-modal v-model="showEditFormDialog" title="编辑" @ok="update" :loading="loading">
        <a-spin :spinning="loading">
          <a-form-model ref="editForm" :rules="editFormRules" :model="editForm" :label-col="{span: 6}"
                        :wrapper-col="{span: 16}" style="text-align: center;">
            <a-form-model-item label="整车分类" prop="vehicleType">
              <a-input v-model="editForm.vehicleType"/>
            </a-form-model-item>
            <a-form-model-item label="品系" prop="type">
              <a-input v-model="editForm.type"/>
            </a-form-model-item>
            <a-form-model-item label="BU业务" prop="buBiz">
              <a-input v-model="editForm.buBiz"/>
            </a-form-model-item>
            <a-form-model-item label="新场景1级">
              <a-input v-model="editForm.newScene1"/>
            </a-form-model-item>
            <a-form-model-item label="产品子系列">
              <a-input v-model="editForm.series"/>
            </a-form-model-item>
          </a-form-model>
        </a-spin>
      </a-modal>

      <a-modal v-drag-modal v-model="showCreateFormDialog" title="新建" @ok="create" :loading="loading">
        <a-spin :spinning="loading">
          <a-form-model ref="createForm" :rules="createFormRules" :model="createForm" :label-col="{span: 6}"
                        :wrapper-col="{span: 16}" style="text-align: center;">
              <a-form-model-item label="整车分类" prop="vehicleType">
                <a-input v-model="createForm.vehicleType"/>
              </a-form-model-item>
              <a-form-model-item label="品系" prop="type">
                <a-input v-model="createForm.type"/>
              </a-form-model-item>
              <a-form-model-item label="BU业务" prop="buBiz">
                <a-input v-model="createForm.buBiz"/>
              </a-form-model-item>
              <a-form-model-item label="新场景1级">
                <a-input v-model="createForm.newScene1"/>
              </a-form-model-item>
              <a-form-model-item label="产品子系列">
                <a-input v-model="createForm.series"/>
              </a-form-model-item>
          </a-form-model>
        </a-spin>
      </a-modal>
    </a-card>

  </div>
</template>

<script>
import {
  XSGSQUICKQUOTEBUBIZRULES
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import * as _ from 'lodash';
import Cookie from 'js-cookie'

export default {
  name: 'xsgs-quick-quote-bu-biz-rule-index',

  data() {
    return {
      uploadUrl: `${XSGSQUICKQUOTEBUBIZRULES}/upload`,
      Cookie,
      createForm: {
        vehicleType: '',
        type: '',
        series: '',
        newScene1: '',
        buBiz: '',
      },

      createFormRules: {
        vehicleType: [{required: true, message: "整车分类不能为空", trigger: 'blur'}],
        type: [{required: true, message: "品系不能为空", trigger: 'blur'}],
        buBiz: [{required: true, message: "BU业务不能为空", trigger: 'blur'}]
      },

      editForm: {
        id: null,
        vehicleType: '',
        type: '',
        series: '',
        newScene1: '',
        buBiz: '',
      },
      editFormRules: {
        id: [{required: true, message: '规则ID不能为空'}],
        vehicleType: [{required: true, message: "整车分类不能为空", trigger: 'blur'}],
        type: [{required: true, message: "品系不能为空", trigger: 'blur'}],
        buBiz: [{required: true, message: "BU业务不能为空", trigger: 'blur'}]
      },
      spinning: false,
      loading: false,
      showCreateFormDialog: false,
      showEditFormDialog: false,
      selectedRowKeys: [],
      selectedRows: [],
      data: [],
      oData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ['10', '15', '20', '100'],
        showTotal: (total) => `共 ${total} 条数`,
        onChange: (current, pageSize) => this.pageChange(current, pageSize),
        onShowSizeChange: (current, pageSize) =>
            this.pageSizeChange(current, pageSize),
      },
      columns: [
        { dataIndex: 'num', title: '序号', key: 'num', width: 60, align: 'center', scopedSlots: {customRender: 'num'} },
        { dataIndex: "vehicleType", title: "整车分类", width: 180 },
        { dataIndex: "type", title: "品系" },
        { dataIndex: "series", title: '产品子系列' },
        { dataIndex: 'newScene1', title: '新场景1级'},
        { dataIndex: 'buBiz', title : 'BU业务'},
        { dataIndex: "action", title: "操作", width: 250, align: "center", scopedSlots: {customRender: 'action'} },
      ],
      flatArr: [],//权限配置树所有父节点
    };
  },

  mounted() {
    this.getData();
    this.checkName();
  },

  computed: {
    rowSelection() {
      return {
        columnWidth: '50px',
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRows = selectedRows;
          this.selectedRowKeys = selectedRowKeys;
        },
      };
    },
  },

  methods: {

    async getData() {
      this.spinning = true;
      this.data = [];
      const {current, size} = this.pagination;
      const _this = this;

      await request(XSGSQUICKQUOTEBUBIZRULES, METHOD.GET, {current, size})
          .then((res) => {
            if (res.data && res.data.data && res.data.data.records && res.data.data.records.length) {
              this.data = res.data.data.records;
            }
            _this.pagination.total = Number(res.data.data.total);
          }).finally(() => {
            _this.spinning = false;
          })
    },

    edit(record) {
      this.editForm.id = record.id;
      this.editForm.vehicleType = record.vehicleType;
      this.editForm.type = record.type;
      this.editForm.series = record.series;
      this.editForm.newScene1 = record.newScene1;
      this.editForm.buBiz = record.buBiz;
      this.showEditFormDialog = true;
    },

    del(record) {
      const _this = this;
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: async () => {
          _this.spinning = true;
          await request(`${XSGSQUICKQUOTEBUBIZRULES}/ids/${record.id}`, METHOD.DELETE)
              .then(async () => {
                await _this.getData();
              })
              .finally(() => {
                _this.spinning = false;
              })
        }
      })
    },

    showCreateModal() {
      this.showCreateFormDialog = true;
    },

    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      this.loading = true;
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
        this.loading = false;
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
        this.loading = false;
      }
    },

    async create() {
      const _this = this;
      await this.$refs.createForm.validate(async (valid) => {
        if (valid) {
          _this.loading = true;
          await request(XSGSQUICKQUOTEBUBIZRULES, METHOD.POST, _this.createForm)
              .then(async () => {
                _this.$message.info("创建成功！");
                _this.createForm = {
                  vehicleType: '',
                  type: '',
                  series: '',
                  newScene1: '',
                  buBiz: '',
                }
                await _this.getData();
                _this.showCreateFormDialog = false;
              }).finally( () => {
                _this.loading = false;
              })
        }
      })
    },

    async update() {
      const _this = this;
      await this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          _this.loading = true;
          await request(XSGSQUICKQUOTEBUBIZRULES, METHOD.PUT, this.editForm)
              .then(async () => {
                _this.$message.info("保存成功");
                await _this.getData();
                _this.showEditFormDialog = false;
              }).finally(() => {
                _this.loading = false;
              })
        }
      });
    },

    async delAll() {
      if (this.selectedRowKeys.length < 1) {
        this.$message.warning('没有选择要删除的数据！');
        return;
      }
      const ids = this.selectedRowKeys
      await this.del({id: _.join(ids, ",")});
      this.selectedRowKeys = [];
    },

    pageChange(current) {
      this.pagination.current = current;
      this.getData();
    },

    pageSizeChange(current, pageSize) {
      this.pagination.current = 1;
      this.pagination.pageSize = pageSize;
      this.getData();
    },
  }
};
</script>

<style>
</style>
