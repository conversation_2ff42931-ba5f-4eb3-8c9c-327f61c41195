<template>
    <div>
        <a-spin :spinning="loading">
            <table style="width: 100%" class="incContInfo">
                <tbody class="ant-table">
                <tr>
                    <th colspan="8">合同审批信息区</th>
                </tr>
                <tr>
                    <th style="width: 7%">
                        年度
                    </th>
                    <th style="width: 10%">
                        客户号
                    </th>
                    <th style="width: 18%">
                        客户名称
                    </th>
                    <th style="width: 10%">
                        合同编号
                    </th>
                    <th style="width: 14%">
                        合同类型
                    </th>
                    <th style="width: 20%">
                        合同文件
                    </th>
                    <th  v-if="isShowNum()" style="width: 9%">
                        合同流水号
                    </th>
                    <th style="width: 12%">操作</th>
                </tr>
                <tr v-for="d in intContForm.intContList" :key="d.id">
                    <td style="width: 7%;text-align: center">{{d.year}}</td>
                    <td style="width: 10%">{{d.customer}}</td>
                    <td style="width: 18%;min-width:130px">{{d.customerName}}</td>
                    <td style="width: 10%;min-width:130px">{{d.contractNo}}</td>
                    <td style="width: 14%;min-width:125px;text-align: center">{{coverType(d.contractType, d)}}</td>
                    <td style="width: 20%;max-width: 300px">
                        <FileUpload class="title" v-model="d.annexList" path="xsgs/" field="intCont" :multiple="true" :disabled="true"/>
                    </td>
                    <td  v-if="isShowNum()" style="width: 9%;min-width: 120px;white-space:normal;word-wrap:break-word;word-break:break-all;">{{d.refContNum}}</td>
                    <td style="width: 12%;min-width:120px;text-align: center">
                        <a @click="editIntCont(d,1)" style="margin: 0">查看</a>
                        <a-divider v-if="isRefNum()" type="vertical" style="margin: 0 3px"/>
                        <a v-if="isRefNum()" @click="showRefModal(d)" style="margin: 0">关联流水号</a>
                        <a-divider v-if="!isNotDrafted()" type="vertical" style="margin: 0 3px"/>
                        <a v-if="!isNotDrafted()" @click="editIntCont(d)" style="margin: 0">编辑</a>
                        <a-divider v-if="!isNotDrafted()" type="vertical" style="margin: 0 3px"/>
                        <a v-if="!isNotDrafted()" @click="deleteIntCont(d)" style="margin: 0">删除</a>
                    </td>
                </tr>
                <tr v-if="!isNotDrafted()">
                    <td colspan="12">
                        <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newIntCont">新增
                        </a-button>
                    </td>
                </tr>
                </tbody>
            </table>
        </a-spin>
        <div class="intContFormModelStyle" ref="intContFormModel">
        <a-modal
                width="40%"
                :title="intCont.title"
                :getContainer="()=>$refs.intContFormModel"
                @cancel="showForm = false"
                @ok="saveInfo"
                :visible="showForm">
            <a-spin :spinning="saving">
            <a-form-model ref="intCont" :model="intCont">
                <table style="width: 100%">
                    <tbody class="ant-table model-table">
                        <tr>
                            <th style="width: 20%">年度</th>
                            <td style="width: 80%;">
                                <a-input :maxLength="4" v-if="!intCont.isView" :style="`border:${formRule.year.border}`" @blur="blurYear" v-model="intCont.year"/>
                                <span v-if="intCont.isView">{{intCont.year}}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>客户</th>
                            <td>
                                <a-select v-if="!intCont.isView"
                                        v-model="intCont.customerList"
                                        show-search
                                        :style="`border:${formRule.customer.border}`"
                                        :default-active-first-option="false"
                                        :show-arrow="false"
                                        :filter-option="false"
                                        :not-found-content="null"
                                        mode="multiple"
                                        :allowClear='true'
                                        @blur="customerBlurs"
                                        @search="handleCustomerSearch"
                                        @change="handleCustomerChange"
                                >
                                    <a-select-option v-for="d in customerList" :key="d.customer">
                                        {{ d.customer + '-' + d.name }}
                                    </a-select-option>
                                </a-select>
                                <span v-if="intCont.isView">{{intCont.customerName}}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>合同编号</th>
                            <td><a-input :allowClear="true" v-if="!intCont.isView" @blur="checkRequired(intCont, 'contractNo')" :style="`border:${formRule.contractNo.border}`" v-model="intCont.contractNo"/>
                            <span v-if="intCont.isView">{{intCont.contractNo}}</span></td>
                        </tr>
                        <tr>
                            <th>合同类型</th>
                            <td v-if="intCont.contractType == 'OTHER_PROTOCOL'">
                                <a-select v-if="!intCont.isView"
                                        style="width: 30%"
                                        :style="`border:${formRule.contractType.border}`"
                                        v-model="intCont.contractType"
                                        :default-active-first-option="false"
                                        :show-arrow="false"
                                        :filter-option="false"
                                        :not-found-content="null"
                                        @blur="checkRequired(intCont,'contractType')"
                                        :allowClear='true'
                                >
                                    <a-select-option v-for="d in contractTypes" :key="d.code">
                                        {{ d.name }}
                                    </a-select-option>
                                </a-select>
                                <a-input v-if="!intCont.isView" style="width: 70%" placeholder="请输入其他合同类型" v-model="intCont.contractTypeOther"/>
                                <span v-if="intCont.isView">{{coverType(intCont.contractType, intCont)}}</span>
                            </td>
                            <td  v-if="intCont.contractType != 'OTHER_PROTOCOL'">
                                <a-select v-if="!intCont.isView"
                                        v-model="intCont.contractType"
                                        :default-active-first-option="false"
                                        :style="`border:${formRule.contractType.border}`"
                                        :show-arrow="false"
                                        :filter-option="false"
                                        :not-found-content="null"
                                        @blur="checkRequired(intCont,'contractType')"
                                        :allowClear='true'
                                >
                                    <a-select-option v-for="d in contractTypes" :key="d.code">
                                        {{ d.name }}
                                    </a-select-option>
                                </a-select>
                                <span v-if="intCont.isView">{{coverType(intCont.contractType, intCont)}}</span>
                            </td>
                        </tr>
                        <tr>
                            <th>合同文件</th>
                            <td>
                                <FileUpload class="title" v-model="intCont.annexList" path="xsgs/" field="intCont" :multiple="true" :disabled="intCont.isView"/>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </a-form-model>
            </a-spin>
        </a-modal>
        </div>
        <a-modal
                width="30%"
                title="关联合同流水号"
                :getContainer="()=>$refs.intContFormModel"
                @cancel="showRefForm = false"
                @ok="saveRefInfo"
                :visible="showRefForm">
            <a-spin :spinning="refLoading">
            <a-form-model ref="refContForm" :model="refContForm" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
                <a-row>
                    <a-col>
                        <a-form-model-item label="合同流水号">
                            <a-input :maxLength="50" v-model="refContForm.refContNum" @blur="checkRequired(refContForm, 'refContNum')"/>
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>
            </a-spin>
        </a-modal>
    </div>

</template>

<script>
    import {
        LIST_XSGSCUSTOMER,
        LIST_XSGSPROCESS_INTCONT,
        GET_INT_CONT_SEQ,UPDATE_REF_CONT_NUM,GET_CONTRACT_TYPES
    } from "@/services/api/xsgs";
    import {METHOD, request, exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie';
    import FileUpload from "../../../components/upload/FileUpload";
    import {prefixZero} from "@/utils/util";

    export default {
        name: "int_cont_list",
        props: {
            form: Object,
            userInfo: Object,
            isView: {type:Boolean,default:false},
        },
        components: {
            FileUpload
        },
        data() {
            return {
                Cookie,
                loading:false,
                saving:false,
                refLoading:false,
                requiredBorder: "1px solid red !important",
                normalBorder: "1px solid #d9d9d9 !important",
                fullPath: null,
                showForm:false,
                showRefForm:false,
                autoContractNo:null,
                intContForm: {
                    annexList: [],
                    linkList: [],
                    intContList:[],
                },
                refContForm:{
                    id:null,
                    refContNum:null
                },
                intCont:{},
                customerList: [],
                allCustomerList: [],
                seqLen:4,
                contractTypes:[],
                formRule:{},
                initRule: {
                    year: {border: "1px solid #d9d9d9 !important", nodeCode: "drafted", msg: "请输入年度"},
                    customer: {
                        border: "1px solid #ffffff !important",
                        nodeCode: "drafted",
                        msg: "请选择客户"
                    },
                    contractNo: {
                        border: "1px solid #ffffff !important",
                        nodeCode: "drafted",
                        msg: "请输入合同编号"
                    },
                    contractType: {border: "1px solid #d9d9d9 !important", nodeCode: "drafted", msg: "请选择合同类型"},
                    refContNum: {border: "1px solid #d9d9d9 !important", nodeCode: "archive_review", msg: "请选择输入合同流水号"},

                }
            }
        },
        created() {
            this.initIntCont();
        },
        mounted() {
            this.fullPath = this.$route.fullPath;

            this.getContractTypes();
        },
        methods: {
            async initData(processId) {
                   if (processId) {
                       this.loading = true;
                    request(LIST_XSGSPROCESS_INTCONT, METHOD.GET, {"processId": processId}).then(res => {
                        if (res.data.data) {
                            this.intContForm.intContList = res.data.data.intContList ? res.data.data.intContList : [];
                            this.loading = false;
                        }
                    });
                }
                this.getCustomerList();
            },
            getContractTypes() {
                request(GET_CONTRACT_TYPES, METHOD.GET).then(res => {
                    this.contractTypes = res.data.data;
                })
            },
            getCustomerList() {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
                    this.allCustomerList = res.data.data;
                    this.customerList = this.allCustomerList;
                })
            },
            customerBlurs() {
                let customers = null;
                let customerNames = null;
                let that = this;
                this.intCont.customerList.forEach(s=>{
                    if (!customers) {
                        customers = s
                    } else {
                        customers += "、" + s;
                    }
                    let cust = that.allCustomerList.filter(item=>item.customer == s);
                    if (cust.length > 0) {
                        if (!customerNames) {
                            customerNames = cust[0].name
                        } else {
                            customerNames += "、" + cust[0].name;
                        }
                    }

                });
                this.intCont.customer = customers;
                this.intCont.customerName = customerNames;
                this.checkRequired(this.intCont, "customer");
                this.computeSeq();
            },
            handleCustomerSearch(value) {
                let newData = this.allCustomerList.filter(item => {
                    if (!value) {
                        return true;
                    } else {
                        if (!item.name) {
                            return false;
                        } else {
                            return (item.customer.indexOf(value) != -1 || item.name.indexOf(value) != -1);
                        }
                    }
                });
                this.customerList = this.cutList(newData, 0, 20);
            },
            handleCustomerChange(value) {
                this.computeSeq();
                if (!value) {
                    this.customerList = this.allCustomerList;
                }
            },
            initIntCont(){
                this.intCont = {
                    title:'新增合同信息',
                    year:null,
                    customer:null,
                    customerName:undefined,
                    contractNo:null,
                    contractTypeOther:null,
                    customerList:[],
                    contractType:null,
                    annexList: []
                };
                this.formRule = this.copy(this.initRule);
            },
            blurYear(){
                this.checkRequired(this.intCont,'year');
                this.computeSeq();
            },
            selectCustomer(user) {
                this.$set( this.intCont, "customer", user.customer)
                this.$set( this.intCont, "customerName", user.name)
            },
            computeSeq(){
                // 手动输入的不用自动修改
                if (!this.intCont.contractSeq && this.intCont.contractNo) {
                    return;
                }
                if(!this.isEmpty(this.intCont.year) && !this.isEmpty(this.intCont.customerList)) {
                    let year = this.intCont.year;
                    let customer = this.intCont.customerList[0];
                    // 是否是当前数据
                    if(this.intCont.id) {
                        let id = this.intCont.id;
                        let contList = this.intContForm.intContList.filter(s=>s.id == id && s.year == year && s.customer.startsWith(customer));
                        if(!this.isEmpty(contList)) {
                            let it = contList[0];
                            if (it.contractSeq) {
                                this.intCont.contractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                                this.autoContractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                                return;
                            }
                        }
                    }
                    // 先从现有列表里获取
                    let contractSeq = null;
                    if (!this.isEmpty(this.intContForm.intContList)) {
                        let contList = this.intContForm.intContList.filter(s=>s.year == year && s.customer.startsWith(customer) && s.contractSeq);
                        if (!this.isEmpty(contList)) {
                            let maxSeq = contList[0].contractSeq?contList[0].contractSeq:1;
                            contList.forEach(s=>{
                                if (s.contractSeq && Number(s.contractSeq) > Number(maxSeq)) {
                                    maxSeq =  Number(s.contractSeq);
                                }
                            });
                            contractSeq = maxSeq + 1;
                        }
                    }
                    if (contractSeq) {
                        this.intCont.contractSeq = contractSeq;
                        this.intCont.contractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                        this.autoContractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                    }
                    if (!contractSeq) {
                        request(GET_INT_CONT_SEQ, METHOD.GET, {year,customer}).then(res => {
                            let maxSeq = res.data.data;
                            if (!maxSeq) {
                                maxSeq = 0
                            }
                            this.intCont.contractSeq = maxSeq + 1;
                            this.intCont.contractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                            this.autoContractNo = "YC" + customer + year + prefixZero(this.intCont.contractSeq, this.seqLen);
                        });
                    }

                } else {
                    this.intCont.contractNo = null;
                    this.intCont.contractSeq = null;
                }
            },
            newIntCont(){
                this.initIntCont();
                this.showForm = true;
            },
            editIntCont(d, flag){
                this.intCont = {...d};
                this.intCont.title = '编辑合同信息';
                this.formRule = this.copy(this.initRule);
                if (this.intCont.contractSeq) {
                    this.autoContractNo = this.intCont.contractNo;
                } else {
                    this.autoContractNo = null;
                }
                let customer = this.intCont.customer;
                this.intCont.customerList = customer.split(";");
                if (flag) {
                    this.intCont["isView"] = true;
                }
                this.showForm = true;
            },
            deleteIntCont(d){
                let newData = this.intContForm.intContList.filter(s=>s.id != d.id);
                this.intContForm.intContList = newData;
                // 调用父组件中的initHistoryList事件
                this.$emit("intConHistoryList",this.intContForm.intContList);
            },
            saveInfo(){
                let msg = this.checkIntCont();
                if (msg) {
                    this.$message.error(msg);
                    return
                }
                let detail = {...this.intCont};
                if (detail.contractNo && this.autoContractNo && detail.contractNo != this.autoContractNo) {
                    detail.contractSeq = null;
                }
                if (detail.id) {
                    this.intContForm.intContList.forEach(s=>{
                        if (s.id == detail.id) {
                            s['year'] = detail['year'];
                            s['processId'] = detail['processId'];
                            s['customer'] = detail['customer'];
                            s['customerName'] = detail['customerName'];
                            s['contractNo'] = detail['contractNo'];
                            s['contractSeq'] = detail['contractSeq'];
                            s['contractType'] = detail['contractType'];
                            s['contractTypeOther'] = detail['contractTypeOther'];
                            s['isAutoNo'] = detail['isAutoNo'];
                            s['sort'] = detail['sort'];
                            s['createUser'] = detail['createUser'];
                            s['createTime'] = detail['createTime'];
                        }
                    })
                } else {
                    detail["id"] = this.intContForm.intContList.length + Math.round(Math.random() * 1000) + 1;
                    this.intContForm.intContList.push(detail);
                }
                // 调用父组件中的initHistoryList事件
                // console.log(this.intContForm.intContList);
                this.$emit("intConHistoryList",this.intContForm.intContList);
                this.showForm = false;
            },
            showRefModal(record){
                this.refContForm = {};
                this.refContForm.id = record.id;
                this.refContForm.refContNum = record.refContNum;
                this.showRefForm = true;
            },
            saveRefInfo(){
                if (this.checkRequired(this.refContForm,'refContNum')) {
                    this.refLoading = true;
                    request(UPDATE_REF_CONT_NUM, METHOD.POST, {...this.refContForm}).then(res => {
                        this.refLoading = false;
                        if (res.data.code == 0) {
                            this.$message.info("关联成功");
                            this.showRefForm = false;
                            this.initData(this.form.id);
                        } else {
                            this.$message.error(res.data.msg);
                        }
                    });
                }

            },
            checkRequired(form, field, isNotRemind) {
                if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
                    let value = form[field];
                    if (!value) {
                        this.formRule[field].border = this.requiredBorder;
                        if (!isNotRemind) {
                            this.$message.error(this.formRule[field].msg);
                        }
                        return false;
                    }
                }
                this.formRule[field].border = this.normalBorder;
                return true
            },
            checkIntCont() {
                let msg = null;
                // 年度
                if (!this.checkRequired(this.intCont, "year", true)) {
                    msg = this.formRule['year'].msg;
                }
                // 客户
                if (!msg && !this.checkRequired(this.intCont, "customer", true)) {
                    msg = this.formRule['customer'].msg;
                }
                // 合同编号
                if (!msg && !this.checkRequired(this.intCont, "contractNo", true)) {
                    msg = this.formRule['contractNo'].msg;
                }
                // 合同类型
                if (!msg && !this.checkRequired(this.intCont, "contractType", true)) {
                    msg = this.formRule['contractType'].msg;
                }
                if (!msg && this.intCont.annexList.length == 0) {
                    msg = "请添加合同文件";
                }
                return msg;
            },
            checkIntContForm(){
                if (this.isEmpty(this.intContForm.intContList)) {
                    this.$message.error("请维护合同审批信息");
                    return false;
                }
                return true;
            },
            checkNum(){
                if (this.form.nodeInfo.nodeCode == 'archive_review') {
                    // 归档节点提交校验是否已经关联合同流水号
                    let intContList = this.intContForm.intContList;
                    let len = intContList.length;
                    let newList = intContList.filter(s=>!s.refContNum);
                    if (newList.length > 0) {
                        this.$message.error("请为所有的合同审批信息关联对应法务合规平台的合同流水号，再进行提交完成");
                        return false;
                    }
                }
                return true;
            },
            getIntContInfo() {
                return this.intContForm;
            },
            isNotDrafted() {
                return this.isView || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'drafted';
            },
            isShowNum() {
                return this.form.nodeInfo.nodeCode && (this.form.nodeInfo.nodeCode == 'archive_review' || this.form.nodeInfo.nodeCode == 'complete');
            },
            isRefNum() {
                return !this.isView && this.form.createUser == this.userInfo.code && this.form.nodeInfo.nodeCode && (this.form.nodeInfo.nodeCode == 'archive_review' || this.form.nodeInfo.nodeCode == 'complete');
            },
            coverType(type, d){
                if (!type) {
                    return "";
                }
                let items = this.contractTypes.filter(s=>s.code == type);
                if (items.length > 0) {
                    let item = items[0];
                    if (item.code == 'OTHER_PROTOCOL') {
                        return d.contractTypeOther?d.contractTypeOther:item.name;
                    } else {
                        return item.name;
                    }
                } else {
                    return "";
                }
            },
        }
    }
</script>

<style scoped>
    /deep/ .fontSmall td,
    .fontSmall th,
    .fontSmall input,
    .fontSmall textarea,
    .fontSmall select,
    .fontSmall a,
    .fontSmall button {
        font-size: 12px !important;
    }

    /deep/ .fontSmall tr,
    .fontSmall td,
    .fontSmall th {
        height: 22px !important;
    }
    /deep/ .fontMiddle td,
    .fontMiddle th,
    .fontMiddle input,
    .fontMiddle textarea,
    .fontMiddle select,
    .fontMiddle a,
    .fontMiddle button {
        font-size: 14px !important;
    }

    /deep/ .fontMiddle tr,
    .fontMiddle td,
    .fontMiddle th {
        height: 28px !important;
    }

    /deep/ .fontLarge td,
    .fontLarge th,
    .fontLarge input,
    .fontLarge textarea,
    .fontLarge select,
    .fontLarge a,
    .fontLarge button {
        font-size: 18px !important;
    }

    /deep/ .fontLarge tr,
    .fontLarge td,
    .fontLarge th {
        height: 33px !important;
    }

    /deep/ .ant-table > tr {
        height: 33px;
    }

    /deep/ .ant-table > tr > td input {
        /*border-radius: 0px;*/
        padding: 3px;
    }

    /deep/ .ant-table > tr > td a {
        margin-left: 5px;
    }

    /deep/ .ant-table > tr .textTd {
        padding-left: 3px !important;
    }

    /deep/ .ant-table > tr > th {
        width: 15%;
        text-align: center;
        background-color: #bdd7ee;
        border: 0.5px solid #464646;
        background-clip:padding-box;
    }

    /deep/ .ant-table > tr > td {
        width: 35%;
        text-align: left;
        border: 0.5px solid #464646;
        padding: 1px 2px !important;
        background-clip:padding-box;
    }

    /deep/ .intContFormModelStyle .ant-modal-body {
        padding: 16px 24px
    }

    /deep/ .ant-select-selection__placeholder {
        padding-left: 3px;
        width: 100%;
    }

    /deep/ .ant-table .ant-select-selection-selected-value {
        padding-left: 3px;
    }

    /deep/ .ant-table .ant-select-selection__rendered {
        margin: 0 !important;
    }

    /deep/ .ant-table input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
    }

    /deep/ .ant-table input[type="number"] {
        -moz-appearance: textfield !important;
    }

    /deep/ .annexTd > div {
        float: left !important;
    }


    /deep/ .ant-table > tr {
        height: 33px;
    }

    /deep/ .title {
      width: 280px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
    }

</style>

