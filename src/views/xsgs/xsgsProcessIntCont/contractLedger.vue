<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18 }">
                <a-row>
                    <a-col :span="6">
                        <a-form-model-item label="年度">
                            <a-input v-model="searchForm.year" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="10">
                        <a-form-model-item label="客户" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
                            <a-select style="width: 100%;" v-model="searchForm.customerList" show-search
                                :default-active-first-option="false" :show-arrow="false" mode="multiple"
                                :filter-option="false" :not-found-content="null" :allowClear='true'
                                @search="handleCustomerSearch" @change="handleCustomerChange">
                                <a-select-option v-for="d in customerList" :key="d.customer">
                                    {{ d.customer + '-' + d.name }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>

                    <a-col :span="8">
                        <a-form-model-item style="text-align: left">
                            <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon
                                    type="search" />查询</a-button>
                            <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon
                                    type="reload" />重置</a-button>
                            <a @click="advanced ? advanced = false : advanced = true" style="margin-left: 8px">
                                {{ advanced ? '收起' : '展开' }}
                                <a-icon :type="advanced ? 'up' : 'down'" />
                            </a>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row v-show="advanced">
                    <a-col :span="6">
                        <a-form-model-item label="合同编号">
                            <a-input v-model="searchForm.contractNo" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item label="合规-合同流水号" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                            <a-input v-model="searchForm.refContNum" />
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item label="合同类型" style="text-align: left">
                            <a-select :style="`width: ${searchForm.contractType == 'OTHER_PROTOCOL' ? '100' : '180'}px`"
                                v-model="searchForm.contractType" :default-active-first-option="false"
                                :show-arrow="false" :filter-option="false" :not-found-content="null" :allowClear='true'>
                                <a-select-option v-for="d in contractTypes" :key="d.code">
                                    {{ d.name }}
                                </a-select-option>
                            </a-select>
                            <a-input v-if="searchForm.contractType == 'OTHER_PROTOCOL'" style="width: 180px"
                                v-model="searchForm.refContNum" placeholder="请输入其他合同类型"></a-input>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-model-item label="流程编号" style="text-align: left">
                            <a-input v-model="searchForm.processNumber" />
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>
            <div ref="screenFullArea">
                <ColumnTree ref="ColumnTree" :dataList="initColumns" keyField="dataIndex" pageName="contractLedger"
                    @selectOk="changeColumns" @reduction="reductionColumns" />
                <a-row style="margin-bottom: 3px;margin-top: 3px">
                    <a-col :span="24">
                        <a-button v-if="checkPf('LEGAL_LEDGER_ADD')" type="primary" style="margin-right: 5px"
                            @click="handleAdd">新增</a-button>
                        <a-button v-if="checkPf('LEGAL_LEDGER_IMPORT')" type="primary" style="margin-right: 5px"
                            @click="handleExportTemplate">下载导入模板</a-button>

                        <a-upload v-if="checkPf('LEGAL_LEDGER_IMPORT')" name="file" accept=".xlsx"
                            :action="UPLOAD_LEDGER" :headers="{ 'Authorization': Cookie.get('Authorization') }"
                            :showUploadList="false" style="margin: 0 8px" @change="handleUpload">
                            <a-button type="primary">
                                <a-icon type="upload" />
                                导入历史台账
                            </a-button>
                        </a-upload>
                        <div style="float: right">
                            <a-button v-if="checkPf('LEGAL_LEDGER_EXPORT')" type="normal" shape="circle"
                                style="margin: 0 5px;" @click="exportData"><a-icon type="download" /></a-button>
                            <a-button type="normal" shape="circle" style="" @click="fullScreen"><a-icon
                                    :type="isFullFlag ? 'fullscreen-exit' : 'fullscreen'" /></a-button>
                            <a-button type="normal" shape="circle" class="showfloattree" style="margin: 0 5px;"
                                @click="showColumnSelect($event)"><a-icon type="appstore" /></a-button>

                        </div>
                    </a-col>
                </a-row>
                <a-table :columns="columns" :rowKey="record => record.id" :pagination="pagination" :data-source="data"
                    :scroll="{ x: 1000 }" size="small" bordered :customRow="onRowClick" :loading="loading">
                    <!-- 序号 -->
                    <template slot="num" slot-scope="text, record, index">
                        {{ index + 1 }}
                    </template>
                    <template slot="contractType" slot-scope="text, record">
                        {{ coverType(record.contractType, record.contractTypeOther) }}
                    </template>
                    <template slot="processContractType" slot-scope="text, record">
                        {{ coverType(record.processContractType, record.processContractTypeOther) }}
                    </template>
                    <span slot="action" slot-scope="text,record">
                        <a v-if="checkPf('LEGAL_LEDGER_VIEW')" @click="handleView(record)">查看</a>
                        <a-divider v-if="checkPf('LEGAL_LEDGER_EDIT')" type="vertical" style="margin: 0 3px" />
                        <a v-if="checkPf('LEGAL_LEDGER_EDIT')" @click="handleEdit(record)">编辑</a>
                        <a-divider v-if="checkPf('LEGAL_LEDGER_DELETE')" type="vertical" style="margin: 0 3px" />
                        <a v-if="checkPf('LEGAL_LEDGER_DELETE')" @click="handleDelete(record)">删除</a>
                    </span>
                </a-table>
            </div>
        </a-card>

    </div>

</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import { LIST_LEGAL_PAGE, CONT_LEGER_CUSTOMER_LIST, GET_CONTRACT_TYPES, DOWLOAD_LEDGER, DOWLOAD_TEMPLATE, UPLOAD_LEDGER, LEGAL_DELETE, LIST_LEDGER_ROLES } from "@/services/api/xsgs";
import { METHOD, request, exportExcel } from "@/utils/request";
import { dateFormat } from "@/utils/dateUtil";
import ColumnTree from "@/components/tree/ColumnTree";
import Cookie from 'js-cookie';
export default {
    name: "xsgsProcessQuote.vue",
    components: {
        ColumnTree
    },
    data() {
        return {
            hasAuth,
            loading: false,
            advanced: false,
            UPLOAD_LEDGER,
            Cookie,
            // 功能权限关键字
            PF_FIELD: "BUSINESS_PROCESS,INTCONT_PROCESS,INTCONT_LEGAL_LEDGER",
            PF_LIST: [],
            searchForm: {
                year: null,
                customerList: [],
                contractNo: null,
                contractType: null,
                contractTypeOther: null,
                refContNum: null,
                processNumber: null
            },
            isHasAllAuth: false,
            isConfirm: false,
            flowNodeList: [],
            customerList: [],
            allCustomerList: [],
            baseCustomerList: [],
            isFullFlag: false,
            ApprovalOption: {
                WAITING: '保存', SUBMIT: '提交', BACK: '退回', HANGUP: '挂起', TERMINATE: '终止', COMPLETE: '完成',
            },
            contractTypes: [],
            pagination: {
                total: 0,
                current: 1,
                size: '10',
                showSizeChanger: true, // 是否可以改变 size
                showQuickJumper: true, // 是否可以快速跳转至某页
                pageSizeOptions: ['10', '20', '30', '40', '50'],
                showTotal: (total) =>
                    `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                        total / this.pagination.size
                    )}页`, // 显示总数
                onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
                onShowSizeChange: (current, size) =>
                    this.onSizeChange(current, size) // 改变每页数量时更新显示
            },
            data: [],
            hasLedgerAuth: false,
            columns: [],
            seqColumn: {
                title: '序号', dataIndex: 'num', key: 'num', width: 60, align: 'center', fixed: true, scopedSlots: {
                    customRender: 'num'
                },
            },
            actionColumn: { title: '操作', fixed: 'right', width: 125, align: "center", scopedSlots: { customRender: 'action' } },
            initColumns: [

                {
                    title: '商务合同内部审批流程信息', dataIndex: 'processInfo', minWidth: 280, children: [
                        { title: '流程编号', align: 'center', width: 130, dataIndex: 'processNumber' },
                        { title: '合同编号', width: 130, dataIndex: 'processContractNo' },
                        { title: '年度', align: 'center', width: 60, dataIndex: 'processYear' },
                        { title: '客户号', width: 120, dataIndex: 'processCustomer' },
                        { title: '客户名称', width: 280, dataIndex: 'processCustomerName', ellipsis: true },
                        { title: '合同类型', width: 150, align: 'center', dataIndex: 'processContractType', scopedSlots: { customRender: 'processContractType' } },
                        { title: '起草人', align: 'center', width: 80, dataIndex: 'processAuthorName' },
                        { title: '流程起草时间', align: 'center', dataIndex: 'processStartTime', width: 150 },
                        { title: '当前环节', align: 'center', width: 80, dataIndex: 'processCurrentNode' },
                    ]
                },
                {
                    title: '股份公司法务合规信息', dataIndex: 'legalInfo', width: 280, children: [
                        { title: '合规-合同流水号', width: 130, dataIndex: 'legalNum' },
                        { title: '合同编号', align: 'left', width: 130, dataIndex: 'contractNo' },
                        { title: '客户号', width: 120, dataIndex: 'customer' },
                        { title: '对方单位', width: 280, dataIndex: 'oppositeUnit', ellipsis: true },
                        { title: '合同名称', width: 200, dataIndex: 'contractName' },
                        { title: '是否含价格', align: 'center', width: 50, dataIndex: 'isContainPrice' },
                        { title: '合同版本', align: 'center', width: 80, dataIndex: 'contractVersion' },
                        { title: '起草人', align: 'center', width: 80, dataIndex: 'authorName' },
                        { title: '流程起草时间', align: 'center', dataIndex: 'startTime', width: 150 },
                        { title: '当前环节', align: 'center', width: 80, dataIndex: 'currentNode' },
                        { title: '当前环节提交日期', align: 'center', width: 120, dataIndex: 'submitTime' },
                        { title: '签署状态', width: 100, align: 'center', dataIndex: 'isSigned' },
                        { title: '不签署原因', width: 100, dataIndex: 'noSignedReason', ellipsis: true },
                        { title: '上一年度销量\n（台）', width: 100, dataIndex: 'preSales' },
                        { title: '上一年度销售额\n（万元）', width: 100, dataIndex: 'preIncome' },
                        { title: '合同签订人', width: 100, align: 'center', dataIndex: 'signedPerson' },
                        { title: '我司签订日期', width: 100, dataIndex: 'ourSignedTime' },
                        { title: '对方签订日期', width: 100, dataIndex: 'oppositeSignedTime' },
                        { title: '对方印章种类', width: 100, align: 'center', dataIndex: 'oppositeSealType' },
                        { title: '板块领导', width: 100, align: 'center', dataIndex: 'plateLeader' },
                        { title: '办事处', width: 100, align: 'center', dataIndex: 'office' },
                        { title: '业务员', width: 100, align: 'center', dataIndex: 'salesMan' },
                        { title: '不含税标的额\n（万元）', width: 100, dataIndex: 'underAmount' },
                        { title: '合同期限', width: 150, dataIndex: 'contractTerm' },
                        { title: '结算方式', width: 200, dataIndex: 'settleMethod', ellipsis: true },
                        { title: '现金比例', width: 300, dataIndex: 'cashRatio' },
                        { title: '账龄', width: 100, align: 'center', dataIndex: 'aging' },
                        { title: '年底清款约定', width: 100, dataIndex: 'paymentAgree' },
                        { title: '单价是否含运费', width: 100, dataIndex: 'isContainFreight' },
                        { title: '运费结算', width: 100, dataIndex: 'freightPayment' },
                        { title: '运输方式', width: 100, dataIndex: 'transport', ellipsis: true },
                        { title: '下年度商务函', width: 100, dataIndex: 'nextBussLetter' },
                    ]
                },
            ],
            rules: {},

        }
    },
    async created() {
        // 获取页面功能权限
        await this.getUserAuth(this.PF_FIELD);
        this.PF_LIST = this.getPfList(this.PF_FIELD);
    },
    mounted() {
        this.getCustomerList();
        this.getContractTypes();
        this.getData();
        this.checkName();
    },
    methods: {
        checkPf(field) {
            return this.PF_LIST.findIndex(s => s == field) != -1;
        },

        getData: function () {
            this.loading = true
            let { current, size } = this.pagination
            request(LIST_LEGAL_PAGE, METHOD.POST, { ...this.searchForm, current, size })
                .then(res => {
                    const { records, total } = res.data.data
                    this.loading = false
                    this.data = records
                    this.pagination.total = parseInt(total)
                })
        },
        getContractTypes() {
            request(GET_CONTRACT_TYPES, METHOD.GET).then(res => {
                this.contractTypes = res.data.data;
            })
        },
        handleSearch() {
            this.pagination.current = 1
            this.getData()
        },
        handleAdd(redcord) {
            let query = {
                option: 0,
                parentPath: this.$route.fullPath
            }
            this.$router.push({ path: 'legalInfo/:id', props: true, query });
        },
        handleEdit(redcord) {
            let query = {
                option: 1,
                parentPath: this.$route.fullPath
            }
            this.$router.push({ path: 'legalInfo/' + redcord.id, props: true, query });
        },
        handleView(redcord) {
            let query = {
                option: 2,
                parentPath: this.$route.fullPath
            }
            this.$router.push({ path: 'legalInfo/' + redcord.id, props: true, query });
        },
        handleDelete(record) {
            this.$confirm({
                content: `是否确认删除 ${record.processContractNo} ？`,
                onOk: () => {
                    this.loading = true
                    request(LEGAL_DELETE, METHOD.POST, { id: record.id })
                        .then(res => {
                            this.$message.info('删除成功')
                            this.getData()
                        }).catch(() => {
                            this.loading = false
                        })
                }
            });

        },
        openProcess(record) {
            let url = "/xsgs_process_manager/xsgs_intcont_applove/" + record.process + "/" + record.processId;
            this.$router.push({ path: url, props: true })
        },
        getCustomerList() {
            let that = this;
            that.allCustomerList = [];
            that.customerList = [];
            request(CONT_LEGER_CUSTOMER_LIST, METHOD.POST, this.searchForm).then(res => {
                that.allCustomerList = res.data.data;
                that.customerList = that.allCustomerList;
            })
        },
        handleCustomerSearch(value) {
            if (value) {
                this.customerList = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
            } else {
                this.customerList = this.allCustomerList;
            }

        },
        handleCustomerChange(value) {
            if (!value) {
                this.customerList = this.allCustomerList;
            }
        },
        exportData() {
            exportExcel(DOWLOAD_LEDGER, { ...this.searchForm }, "合同台账.xlsx")
        },
        handleUpload(info) {
            this.loading = true
            if (info.file.status !== 'uploading') {
                this.getData()
            }
            if (info.file.status === 'done') {
                this.$message.success(`${info.file.name} 导入成功`);
            } else if (info.file.status === 'error') {
                this.$message.error(`${info.file.name} 导入失败`);
            }
        },
        handleExportTemplate() {
            exportExcel(DOWLOAD_TEMPLATE, {}, "法务合规合同信息导入模板.xlsx")
        },
        showColumnSelect(e) {
            this.$refs.ColumnTree.showTree(e);
        },
        fullScreen() {
            if (!this.isFullFlag) {
                let target = this.$refs.screenFullArea;
                target.className = "screenfull";
                this.isFullFlag = true;
            } else {
                let target = this.$refs.screenFullArea;
                target.className = "";
                this.isFullFlag = false;
            }

        },
        changeColumns(e) {
            this.columns = [];
            this.columns.push(this.seqColumn);
            // 计算动态宽度
            this.columns.push.apply(this.columns, [...e]);
            this.columns.push(this.actionColumn);
            // this.columns = e;
            console.log(this.columns);
        },
        reductionColumns() {
            this.columns = [];
            let e = this.copy(this.initColumns);
            this.columns.push(this.seqColumn);
            this.columns.push.apply(this.columns, [...e]);
            this.columns.push(this.actionColumn);
            console.log(this.columns);
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.searchForm = {
                process: 'quote'
            }
            this.handleSearch();
        },

        onPageChange(page, size) {
            this.pagination.current = page;
            this.pagination.size = size.toString();
            this.getData();
        },
        onSizeChange(current, size) {
            this.pagination.current = 1;
            this.pagination.size = size.toString();
            this.getData();
        },

        dateFormat(date) {
            if (!date) {
                return '';
            }
            return dateFormat(date);
        },
        coverProcess(process) {
            let p = this.processList.filter(item => item.value == process);
            if (p.length > 0) {
                return p[0].label;
            } else {
                return process;
            }

        },
        coverOpt(opt) {
            return this.ApprovalOption[opt] ? this.ApprovalOption[opt] : '';
        },
        coverType(type, contractTypeOther) {
            if (!type) {
                return "";
            }
            let items = this.contractTypes.filter(s => s.code == type);
            if (items.length > 0) {
                let item = items[0];
                if (item.code == 'OTHER_PROTOCOL') {
                    return contractTypeOther ? contractTypeOther : item.name;
                } else {
                    return item.name;
                }
            } else {
                return type;
            }
        },
    },

}
</script>

<style scoped>
.ant-input-number {
    width: 100%;
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
}

/*/deep/  样式穿透
    */
/*/deep/.ant-col-8{*/
/*    display: block;*/
/*    -webkit-box-sizing: border-box;*/
/*    box-sizing: border-box;*/
/*    width: 100%;*/
/*}*/
/deep/.ant-form-item {
    text-align: right;
    margin: 0;
}

/deep/.ant-form>table>tbody>tr>td {
    border: 1px solid #f0f0f0;
}

/deep/ .customerLabel {
    width: 60%;
}

/deep/ .customerLabel .ant-form-item-label {
    width: 13.8%;
}

/deep/ #optionBudget :after {
    -webkit-appearance: none !important;
}

/deep/ #year::-webkit-outer-spin-button,
#year::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: textfield;
}

/deep/input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: textfield;
}

/deep/.ant-input-number-handler-wrap {
    display: none;
}

/deep/ .searchUser .ant-form-item-control-wrapper {
    text-align: left;
}

/deep/ .buttonArea .ant-form-item-control-wrapper {
    width: 100%;
}

/deep/ .advancedRow .ant-col-8 {
    width: 22%;
}

/deep/ .conditionArea tbody>tr>th {
    width: 100px;
    font-weight: normal !important;
    text-align: right;
    color: rgba(0, 0, 0, 0.85);
    background-clip: padding-box;
}

/deep/ .conditionArea tbody>tr>td {
    width: 10%;
    font-weight: normal !important;
    text-align: right;
    border: none !important;
    background-clip: padding-box;
}

/deep/ .ant-table-thead>tr>th {
    background-color: #cccccc !important;
    text-align: center !important;
}

/deep/ .screenfull {
    position: fixed;
    overflow: auto;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    background-color: #ffffff;
}

/deep/ .screenfull::-webkit-scrollbar {
    width: 6px;
}

/deep/ .screenfull::-webkit-scrollbar-thumb {
    background: #bdd7ee;
}

/deep/ .screenfull::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);

}
</style>
