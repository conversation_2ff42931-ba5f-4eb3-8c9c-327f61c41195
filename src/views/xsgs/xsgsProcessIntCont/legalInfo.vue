<template>
    <div style="position: relative; overflow-y: auto;padding-top: 0;">
        <a-form-model class="intContForm" ref="intContForm" :model="intContForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
            <a-spin :spinning="loading">
            <div class="header-button-area">
                <a-button v-if = "!isView" type="primary" @click="handleSave" style="margin-right: 8px">
                    保存
                </a-button>
                <a-button type="primary" @click="handleClose()">
                    关闭
                </a-button>
            </div>
            <a-card title="商务合同内部审批流程信息" class="businessInfo" style="padding-right: 5px">
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="合同编号">
                            {{intContForm.processContractNo}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="年度">
                            {{intContForm.processYear}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="合同类型">
                            {{intContForm.processContractType}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="流程编号">
                            {{intContForm.processNumber}}
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="客户号">
                            {{intContForm.processCustomer}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="客户名称">
                            {{intContForm.processCustomerName}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="起草人">
                            {{intContForm.processAuthorName}}
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="流程起草时间">
                            {{intContForm.processStartTime}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="当前环节">
                            {{intContForm.processCurrentNode}}
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="当前环节提交日期">
                            {{intContForm.processSubmitTime}}
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="24" class="fileCol">
                        <a-form-model-item label="合同文件">
                            <FileUpload v-model="intContForm.annexList" path="xsgs/" field="intCont" :multiple="true" customerstyle="float: left;margin-top: 8px" :disabled="isView"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="24" class="fileCol">
                        <a-form-model-item label="相关补充文件">
                            <FileUpload v-model="intContForm.processAnnexList" path="xsgs/" field="processApproval" :multiple="true" customerstyle="float: left;margin-top: 8px" :disabled="isView"/>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="24" class="fileCol">
                        <a-form-model-item label="相关链接">
                            <LinkUpload v-model="intContForm.linkList"  field="intCont" customerstyle="float: left;margin-top: 8px" :disabled="isView"/>
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-card>
            <a-card title="股份公司法务合规信息" style="padding-right: 5px;padding-bottom: 10px">
                <a-row style="margin-top: 5px">
                    <a-col :span="8">
                        <a-form-model-item label="合同流水号">
                            <a-input v-if="!isView" v-model="intContForm.legalNum"></a-input>
                            <span v-if="isView">{{intContForm.legalNum}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="合同编号">
                            <a-input v-if="!isView" v-model="intContForm.contractNo"></a-input>
                            <span v-if="isView">{{intContForm.contractNo}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item v-if="intContForm.contractType == 'OTHER_PROTOCOL'" label="合同类型">
                            <a-select v-if="!isView"
                                      style="width: 40%"
                                      v-model="intContForm.contractType"
                                      :default-active-first-option="false"
                                      :show-arrow="false"
                                      :filter-option="false"
                                      :not-found-content="null"
                                      :allowClear='true'
                            >
                                <a-select-option v-for="d in contractTypes" :key="d.code">
                                    {{ d.name }}
                                </a-select-option>
                            </a-select>
                            <a-input v-if="!isView" style="width: 60%" placeholder="请输入其他合同类型" v-model="intContForm.contractTypeOther"/>
                            <span v-if="isView">{{coverType(intContForm.contractType, intContForm.contractTypeOther)}}</span>
                        </a-form-model-item>
                        <a-form-model-item v-if="intContForm.contractType != 'OTHER_PROTOCOL'" label="合同类型">
                            <a-select v-if="!isView"
                                      v-model="intContForm.contractType"
                                      :default-active-first-option="false"
                                      :show-arrow="false"
                                      :filter-option="false"
                                      :not-found-content="null"
                                      :allowClear='true'
                            >
                                <a-select-option v-for="d in contractTypes" :key="d.code">
                                    {{ d.name }}
                                </a-select-option>
                            </a-select>
                            <span v-if="intCont.isView">{{coverType(intContForm.contractType, intContForm.contractTypeOther)}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="客户号">
                            <a-select v-if="!isView"
                                      v-model="intContForm.customerList"
                                      show-search
                                      :default-active-first-option="false"
                                      :show-arrow="false"
                                      :filter-option="false"
                                      :not-found-content="null"
                                      mode="multiple"
                                      :allowClear='true'
                                      @blur="customerBlurs"
                                      @search="handleCustomerSearch"
                                      @change="handleCustomerChange"
                            >
                                <a-select-option v-for="d in customerList" :key="d.customer">
                                    {{ d.customer}}
                                </a-select-option>
                            </a-select>
                            <span v-if="isView">{{intContForm.customer}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="对方单位">
                            <a-select v-if="!isView"
                                      v-model="intContForm.customerList"
                                      show-search
                                      :default-active-first-option="false"
                                      :show-arrow="false"
                                      :filter-option="false"
                                      :not-found-content="null"
                                      mode="multiple"
                                      :allowClear='true'
                                      @blur="customerBlurs"
                                      @search="handleCustomerSearch"
                                      @change="handleCustomerChange"
                            >
                                <a-select-option v-for="d in customerList" :key="d.customer">
                                    {{ d.name}}
                                </a-select-option>
                            </a-select>
                            <span v-if="isView">{{intContForm.oppositeUnit}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="合同名称">
                            <a-input v-if="!isView" v-model="intContForm.contractName"></a-input>
                            <span v-if="isView">{{intContForm.contractName}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="是否含价格">
                            <a-radio-group v-if="!isView" name="radioGroup"  v-model="intContForm.isContainPrice">
                                <a-radio value="是">是</a-radio>
                                <a-radio value="否">否</a-radio>
                            </a-radio-group>
                            <span v-if="isView">{{intContForm.isContainPrice}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="合同版本">
                            <a-select v-if="!isView"
                                      v-model="intContForm.contractVersion"
                                      show-search
                                      :default-active-first-option="false"
                                      :show-arrow="false"
                                      :filter-option="false"
                                      :not-found-content="null"
                                      :allowClear='true'
                            >
                                <a-select-option value="我方范本（固定版本）">我方范本（固定版本）</a-select-option>
                                <a-select-option value="我方范本（相对固定）">我方范本（相对固定）</a-select-option>
                                <a-select-option value="我方范本（参考版本）">我方范本（参考版本）</a-select-option>
                                <a-select-option value="非范本">非范本</a-select-option>
                                <a-select-option value="对方范本">对方范本</a-select-option>
                            </a-select>
                            <span v-if="isView">{{intContForm.contractVersion}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="起草人">
                            <a-input v-if="!isView" v-model="intContForm.authorName"></a-input>
                            <span v-if="isView">{{intContForm.authorName}}</span>
                         </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="流程起草时间">
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            v-if="!isView"
                                            v-model="intContForm.startTime"/>
                            <span v-if="isView">{{intContForm.startTime}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="当前环节">
                             <a-input v-if="!isView" v-model="intContForm.currentNode"></a-input>
                            <span v-if="isView">{{intContForm.currentNode}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="当前环节提交日期">
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            v-if="!isView"
                                            v-model="intContForm.submitTime"/>
                            <span v-if="isView">{{intContForm.submitTime}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="是否签署">
                            <a-radio-group v-if="!isView" name="radioGroup"  v-model="intContForm.isSigned">
                                <a-radio value="已签署">已签署</a-radio>
                                <a-radio value="未签署">未签署</a-radio>
                            </a-radio-group>
                            <span v-if="isView">{{intContForm.isSigned}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="不签署原因">
                            <a-input v-if="!isView" v-model="intContForm.noSignedReason"></a-input>
                            <span v-if="isView">{{intContForm.noSignedReason}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="上年销量（台）">
                            <a-input-number :step="1" :precision="0" v-if="!isView" style="width: 60%" v-model="intContForm.preSales"></a-input-number>
                            <span v-if="isView">{{intContForm.preSales}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="上年销售额(万元)">
                            <a-input-number :step="0.01" :precision="2" style="width: 60%" v-if="!isView" v-model="intContForm.preIncome"></a-input-number>
                            <span v-if="isView">{{intContForm.preIncome}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="合同签订人">
                            <a-input v-if="!isView" v-model="intContForm.signedPerson"></a-input>
                            <span v-if="isView">{{intContForm.signedPerson}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="我司签订日期">
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            v-if="!isView"
                                            v-model="intContForm.ourSignedTime"/>
                            <span v-if="isView">{{intContForm.ourSignedTime}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="对方签订日期">
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            v-if="!isView"
                                            v-model="intContForm.oppositeSignedTime"/>
                            <span v-if="isView">{{intContForm.oppositeSignedTime}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="对方印章种类">
                            <a-input v-if="!isView" v-model="intContForm.oppositeSealType"></a-input>
                            <span v-if="isView">{{intContForm.oppositeSealType}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="板块领导">
                            <a-input v-if="!isView" v-model="intContForm.plateLeader"></a-input>
                            <span v-if="isView">{{intContForm.plateLeader}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="办事处">
                            <a-input v-if="!isView" v-model="intContForm.office"></a-input>
                            <span v-if="isView">{{intContForm.office}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="业务员">
                            <a-input v-if="!isView" v-model="intContForm.salesMan"></a-input>
                            <span v-if="isView">{{intContForm.salesMan}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="标的额（万元）">
                            <a-input v-if="!isView" v-model="intContForm.underAmount"></a-input>
                            <span v-if="isView">{{intContForm.underAmount}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="合同期限">
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            style="width: 45%"
                                            v-if="!isView"
                                            v-model="intContForm.contractTermFrom"/>
                            <span v-if="!isView">~</span>
                            <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                            placeholder=""
                                            style="width: 45%"
                                            v-if="!isView"
                                            v-model="intContForm.contractTermTo"/>
                            <span v-if="isView">{{intContForm.contractTerm}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="结算方式">
                            <textarea class="xsgs-textarea" :readOnly="isView" :title="intContForm.settleMethod" v-model="intContForm.settleMethod"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="现金比例">
                            <a-input v-if="!isView" v-model="intContForm.cashRatio"></a-input>
                            <span v-if="isView">{{intContForm.cashRatio}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="账龄">
                            <a-input v-if="!isView" v-model="intContForm.aging"></a-input>
                            <span v-if="isView">{{intContForm.aging}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="年底清款约定">
                            <a-input v-if="!isView" v-model="intContForm.paymentAgree"></a-input>
                            <span v-if="isView">{{intContForm.paymentAgree}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="是否包含运费">
                            <a-radio-group v-if="!isView" name="radioGroup"  v-model="intContForm.isContainFreight">
                                <a-radio value="含">含</a-radio>
                                <a-radio value="不含">不含</a-radio>
                            </a-radio-group>
                            <span v-if="isView">{{intContForm.isContainFreight}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="运输方式">
                            <a-input v-if="!isView" v-model="intContForm.transport"></a-input>
                            <span v-if="isView">{{intContForm.transport}}</span>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="下年度商务函">
                            <a-input v-if="!isView" v-model="intContForm.nextBussLetter"></a-input>
                            <span v-if="isView">{{intContForm.nextBussLetter}}</span>
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-card>
            </a-spin>
        </a-form-model>

    </div>

</template>

<script>
    import {
        LIST_XSGSCUSTOMER,GET_CONTRACT_TYPES,LEGAL_GETBYID,LEGAL_SAVE
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import FileUpload from "@/components/upload/FileUpload";
    import LinkUpload from "@/components/upload/LinkUpload";
    import {prefixZero} from "@/utils/util";

    export default {
        name: "legal_info",
        components: {
            FileUpload,LinkUpload
        },
        data() {
            return {
                loading:false,
                fullPath: null,
                parentPath:null,
                isView:false,
                headerWidth: null,
                autoContractNo:null,
                id:null,
                contractTypes:[],
                intContForm: {
                    customerList:[],
                    annexList: [],
                    processAnnexList: [],
                    linkList: [],
                },
                refContForm:{
                    id:null,
                    refContNum:null
                },
                intCont:{},
                customerList: [],
                allCustomerList: [],
                seqLen:4,
            }
        },
        created() {

        },
        mounted() {
            this.fullPath = this.$route.fullPath;
            this.id = this.$route.params.id;
            this.parentPath = this.$route.query.parentPath;
            if (!this.$route.query.option || this.$route.query.option == '2') {
                this.isView = true;
            }
            this.initData();
        },
        methods: {
            initData() {
                this.getCustomerList();
                this.getContractTypes();
                if (this.id && this.id != ':id') {
                    request(LEGAL_GETBYID, METHOD.GET, {"id": this.id}).then((res)=>{
                        this.intContForm = res.data.data;
                    });
                }
            },
            getContractTypes() {
                request(GET_CONTRACT_TYPES, METHOD.GET).then(res => {
                    this.contractTypes = res.data.data;
                })
            },
            getCustomerList() {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
                    this.allCustomerList = res.data.data;
                    this.customerList = this.allCustomerList;
                })
            },
            customerBlurs() {
                let customers = null;
                let customerNames = null;
                let that = this;
                this.intContForm.customerList.forEach(s=>{
                    if (!customers) {
                        customers = s
                    } else {
                        customers += "、" + s;
                    }
                    let cust = that.allCustomerList.filter(item=>item.customer == s);
                    if (cust.length > 0) {
                        if (!customerNames) {
                            customerNames = cust[0].name
                        } else {
                            customerNames += "、" + cust[0].name;
                        }
                    }

                });
                this.intContForm.customer = customers;
                this.intContForm.oppositeUnit = customerNames;
                // this.computeSeq();
            },
            handleCustomerSearch(value) {
                let newData = this.allCustomerList.filter(item => {
                    if (!value) {
                        return true;
                    } else {
                        if (!item.name) {
                            return false;
                        } else {
                            return (item.customer.indexOf(value) != -1 || item.name.indexOf(value) != -1);
                        }
                    }
                });
                this.customerList = this.cutList(newData, 0, 20);
            },
            handleCustomerChange(value) {
                // this.computeSeq();
                if (!value) {
                    this.customerList = this.allCustomerList;
                }
            },

            handleSave(){
                this.loading = true;
                request(LEGAL_SAVE, METHOD.POST, this.intContForm).then((res)=>{
                    this.$message.info("保存成功");
                    this.loading = false;
                });
            },
            handleClose(){
                this.$closePage(this.fullPath,this.parentPath);
            },
            coverType(type, d){
                if (!type) {
                    return "";
                }
                let items = this.contractTypes.filter(s=>s.code == type);
                if (items.length > 0) {
                    let item = items[0];
                    if (item.code == 'OTHER_PROTOCOL') {
                        return d.contractTypeOther?d.contractTypeOther:item.name;
                    } else {
                        return item.name;
                    }
                } else {
                    return "";
                }
            },
        }
    }
</script>

<style scoped>
    /deep/ .intContForm .businessInfo .ant-card-head {
        margin-top: 45px;
    }
    /deep/ .header-button-area {
        position: fixed;
        z-index: 999;
        height: 45px;
        background-color: #ffffff;
        line-height: 45px;
        width: 100%;
        border-bottom: 1px solid #f0f0f0;
        padding: 0 16px;
        min-width: 700px;
    }
    /deep/ .intContForm .ant-row .ant-col .ant-form-item {
        margin-bottom: 5px !important;
    }
    /deep/ .ant-card-body {
        padding: 0px !important;
    }
    /deep/ .fileCol .ant-form-item-label {
        width: 11%;
    }

    /deep/ .longTextView {
        width: 100%;
        height: 28px;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
</style>

