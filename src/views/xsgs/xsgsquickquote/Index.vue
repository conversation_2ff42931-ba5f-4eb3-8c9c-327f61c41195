<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度" /></td>
            <td><a-input-number v-model="searchForm.zyear"/></td>
            
            <td><a-form-model-item label="核算单号" /></td>
            <td><a-input v-model="searchForm.orderNo" style="min-width: 150px"/></td>

            <td><a-form-model-item label="客户" /></td>
            <td style="min-width: 150px">
              <a-select
              style="width: 100%;"
                v-model="customers"
                show-search
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                mode="multiple"
                :allowClear='true'
                @search="handleCustomerSearch"
                @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.customer">
                  {{ d.customer + '-' + d.name }}
                </a-select-option>
              </a-select>
            </td>

            <td class="subTable">
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>

          <tr>
            <td><a-form-model-item label="产品型号" /></td>
            <td><a-input v-model="searchForm.productModel" style="min-width: 150px"/></td>

            <td><a-form-model-item label="虚拟供货号" /></td>
            <td><a-input v-model="searchForm.virtualSupplyNumber" style="min-width: 150px"/></td>

            <td><a-form-model-item label="创建日期" /></td>
            <td><a-date-picker v-model="searchForm.bidderTime" style="min-width: 100%" /></td>
            <td>
              <a-form-model-item label="发起人" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
                <a-input v-model="searchForm.bidderName" style="min-width: 150px"/>
              </a-form-model-item>
            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <template slot="orderNo" slot-scope="index, record">
          <a @click="toProcces(record)">{{ record.orderNo }}</a>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSCUSTOMER,
  LIST_XSGSQUICKVIEW_PAGE,
  UPLOAD_XSGSPARTSOPTION,
  DOWNLOAD_PAGE_XSGSQUICKQUOTE
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSOPTION,
      Cookie,
      loading: false,
      dialog: false,
      optionDialog: false,
      confirmLoading: false,
      confirmOptionLoading: false,
      searchForm: {},
      customerList: [],
      customerAllList: [],
      customers: [],
      searchCustomer: '',

      // 功能权限关键字
      PF_FIELD:"QUICK_QUOTE_MANAGER,PRE_QUOTE_REPORT",
      PF_LIST:[],

      baseCodes:[], //基本型代号
      dh_list:[], //基本型代号
      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num',  key: 'num',  width: 50,  align: 'center',  scopedSlots: {customRender: 'num'}},
        {title: '核算单号', dataIndex: 'orderNo', width: 120, scopedSlots: {customRender: 'orderNo'}},
        {title: '客户号', dataIndex: 'custNumber', width: 120},
        {title: '客户名称', dataIndex: 'custName'},
        {title: '产品型号', dataIndex: 'productModel', width: 160},
        {title: '虚拟供货号', dataIndex: 'virtualSupplyNumber', width: 160},
        {title: '发起日期', dataIndex: 'bidderTime', width: 150},
        {title: '发起人', dataIndex: 'bidderName', width: 120}
      ],
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getCustomerList();
    this.getData();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getData: function () {
      this.loading = true;
      let {current, size} = this.pagination;
      this.searchForm.customers = this.customers.join(",");
      request(LIST_XSGSQUICKVIEW_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data;
        this.loading = false;
        this.data = records;
        this.pagination.total = parseInt(total);
      });
    },
    toProcces(data) {
      this.$router.push({path:'/quick_quote/xsgs_quick_quote_order/' + data.id, props: true})
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.searchForm.customers = this.customers;
      const searchForm =this.searchForm;
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          exportExcel(DOWNLOAD_PAGE_XSGSQUICKQUOTE, {...searchForm}, "预报价台账.xlsx")
        },
        onCancel() {},
      });
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1;
      this.getData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
        const map = new Map();
        this.customerAllList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1));
        this.customerList = this.customerAllList;
      });
    },
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.customerAllList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.customerAllList;
      }
    },
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.customerAllList;
      }
      this.$forceUpdate();
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
/deep/.subTable {
    margin-left: 10px;
}
</style>
