<template>
  <div>
    <a-card>
      <a-spin :spinning="loading">
        <a-form-model ref="dataFormRef" :model="dataForm" >
          <h2 style="text-align: center;"><strong>预报价单</strong></h2>
          <a-row v-if="dataForm.baseInfo">
            <a-col :span="24">
              <table style="width: 98%;" class="tableClass" border="0" cellspacing="0">
              </table>
              <table style="width: 98%;" class="tableClass" border="1" cellspacing="0">
                <tr>
                  <th class="titleClass" colspan="15">基本信息</th>
                  <th class="titleClass" colspan="9">全明细清单</th>
                </tr>

                <tr>
                  <td class="thClass" colspan="2">核算单号</td>
                  <td colspan="2">{{ dataForm.baseInfo.orderNo }}</td>
                  <td class="thClass" colspan="2">客户号</td>
                  <td colspan="2">{{ dataForm.baseInfo.custNumber }}</td>
                  <td class="thClass" colspan="2">客户名称</td>
                  <td colspan="5">{{ dataForm.baseInfo.custName }}</td>

                  <td style="text-align: left;" colspan="9" :rowspan="qmxRowSpan">
                    <div :style="qmxStylye">
                      <a-tree
                          :tree-data="dataForm.boms"
                      />
                    </div>
                  </td>
                </tr>

                <tr>
                  <td class="thClass" colspan="2">产品型号</td>
                  <td class="thClass" colspan="2">虚拟供货号</td>
                  <td class="thClass">燃料</td>
                  <td class="thClass" colspan="2">品系</td>
                  <td class="thClass" colspan="2">场景1</td>
                  <td class="thClass" colspan="3">场景2</td>
                  <td class="thClass" colspan="3">场景3</td>

                </tr>

                <tr>
                  <td colspan="2">{{ dataForm.baseInfo.productModel }}</td>
                  <td colspan="2">{{ dataForm.baseInfo.virtualSupplyName }}</td>
                  <td >{{ dataForm.baseInfo.fuel }}</td>
                  <td colspan="2">{{ dataForm.baseInfo.strain }}</td>
                  <td colspan="2">{{ dataForm.baseInfo.scene1 }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.scene2 }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.scene3 }}</td>
                </tr>

                <tr>
                  <th class="titleClass" colspan="15">价格信息（价格为预报价，不含专用件等相关费用，最终以正式报价为准）</th>
                </tr>

                <tr>
                  <td class="thClass" colspan="5" style="text-align: right;">整机开票价（无税）</td>
                  <td colspan="5">{{ computeTaxRate(dataForm.baseInfo.taxlessOverallUnitInvoicePrice, false) }}</td>
                  <td class="thClass" colspan="5">元/台</td>
                </tr>

                <tr>
                  <td class="thClass" colspan="5" style="text-align: right;">整机开票价（含税）</td>
                  <td colspan="5">{{ computeTaxRate(dataForm.baseInfo.taxlessOverallUnitInvoicePrice, true) }}</td>
                  <td class="thClass" colspan="5">元/台</td>
                </tr>

                <tr>
                  <td class="thClass" colspan="5" style="text-align: right;">基本型开票价（无税）</td>
                  <td colspan="5">{{ computeTaxRate(dataForm.baseInfo.taxlessBasicInvoicePrice, false) }}</td>
                  <td class="thClass" colspan="3" style="text-align: right;">选配件合计（无税）</td>
                  <td colspan="2" >{{ computeTaxRate(dataForm.baseInfo.taxlessOptionalAccessoriesAmount, false) }}</td>
                </tr>

                <tr>
                  <th class="titleClass" colspan="15">配置信息（无税，单位：元/台）基于基本型基础上变动</th>
                </tr>

                <tr>
                  <td colspan="15" style="width: 100%; padding: 0; border: 1px solid transparent !important;" >
                    <table class="tableClass" border="1">
                      <tr>
                        <td class="thClass">类型</td>
                        <td class="thClass" colspan="2">零件名称</td>
                        <td class="thClass">系列</td>
                        <td class="thClass">规格</td>
                        <td class="thClass" colspan="2">形式</td>
                        <td class="thClass">供应商</td>
                        <td class="thClass">售价</td>
                        <td class="thClass">价差</td>
                      </tr>
                      <tr v-for="item in dataForm.options" :key="item.id">
                        <td>{{ item.mainType }}</td>
                        <td colspan="2">{{ item.partsName }}</td>
                        <td>{{ item.series }}</td>
                        <td>{{ item.specs }}</td>
                        <td colspan="2">{{ item.xs }}</td>
                        <td>{{ item.supplier }}</td>
                        <td>{{ computeTaxRate(item.price, false) }}</td>
                        <td>{{ item.priceGap }}</td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <tr>
                  <td colspan="15" style="width: 100%; padding: 0; border: 1px solid transparent !important;" >
                    <table class="tableClass" border="1">
                      <tr>
                        <td class="thClass" :rowspan="reductionsTableLength">基本型配置<br>取消</td>
                        <td class="thClass" colspan="2">零件名称</td>
                        <td class="thClass">系列</td>
                        <td class="thClass">规格</td>
                        <td class="thClass" colspan="2">形式</td>
                        <td class="thClass">供应商</td>
                        <td class="thClass" colspan="2">价差</td>
                      </tr>
                      <tr v-for="item in dataForm.reductions" :key="item.id">
                        <td colspan="2">{{ item.partsName }}</td>
                        <td>{{ item.series }}</td>
                        <td>{{ item.specs }}</td>
                        <td colspan="2">{{ item.xs }}</td>
                        <td>{{ item.supplier }}</td>
                        <td colspan="2">{{  formatPriceGap(item.priceGap ) }}</td>
                      </tr>
                    </table>
                  </td>
                </tr>


                <tr>
                  <th class="titleClass" colspan="15">专用件费用（无税，单位：万元）</th>
                </tr>

                <tr>
                  <td colspan="15" style="width: 100%;padding: 0; border: 1px solid transparent !important;">
                    <table class="tableClass" border="1">
                      <tr>
                        <td class="thClass" colspan="3">零件名称</td>
                        <td class="thClass">快制费</td>
                        <td class="thClass">改模费</td>
                        <td class="thClass">开模费</td>
                        <td class="thClass">合计</td>
                        <td class="thClass" colspan="2">交货周期(天)</td>
                      </tr>
                      <tr v-for="item in dataForm.parts" :key="item.id">
                        <td colspan="3">{{ item.partName }}</td>
                        <td>{{ computeTaxRate(item.rapidProductionFee, false) }}</td>
                        <td>{{ computeTaxRate(item.moldModificationFee, false) }}</td>
                        <td>{{ computeTaxRate(item.moldOpeningFee, false) }}</td>
                        <td>{{ computeTaxRate(item.rapidProductionFee + item.moldModificationFee + item.moldOpeningFee, false) }}</td>
                        <td colspan="2">{{ item.leadTime }}</td>
                      </tr>
                    </table>
                  </td>
                </tr>

                <tr>
                  <th class="titleClass" colspan="15">收益信息（无税，单位：万元）</th>
                </tr>

                <tr>
                  <td class="thClass" rowspan="2" colspan="6">基准成本</td>
                  <td class="thClass" colspan="9">实际成本</td>
                </tr>

                <tr>
                  <td class="thClass" colspan="3">最高成本</td>
                  <td class="thClass" colspan="3">最低成本</td>
                  <td class="thClass" colspan="3">加权成本</td>
                </tr>

                <tr>
                  <td colspan="6">{{ dataForm.baseInfo.jprice }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.cmaxPrice }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.cminPrice }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.cavgPrice }}</td>
                </tr>
              </table>
              <div style="width: 98%; display: flex; margin:5px auto auto auto; justify-content: end;">
                <a-button style="border-radius: 0; font-size: .7rem;" @click="close">关闭</a-button>
                <a-button v-if="canDownloadExcel" style="border-radius: 0; font-size: .7rem;" :loading="loading" @click="handleExportExcel" >下载Excel</a-button>
              </div>
            </a-col>
          </a-row>

        </a-form-model>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  GETONE_XSGSQUICKVIEW_DETAIL,
  DOWNLOAD_XSGSQUICKQUOTE
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
export default {
  data() {
    return {
      hasAuth,
      fullPath: this.$route.path,
      qmxRowSpan: "",
      qmxStylye: {
        "overflow-y": "scroll"
      },
      orderId: "",
      dataForm: {},
      // 功能权限关键字
      PF_FIELD:"QUICK_QUOTE_MANAGER,PRE_QUOTE_REPORT,PRE_QUOTE_REPORT_DETAIL",
      PF_LIST:[],
      loading: false,
      reductionsTableLength: 1,
      tax_rate: 1,
      canDownloadExcel: false,
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
    // 获取id
    let path = this.$route.path.split("/");
    this.orderId = path[3];
    this.getData();
  },
  methods: {
    formatPriceGap(priceGap) {
      if (typeof priceGap === 'number' && !isNaN(priceGap)) {
        return priceGap !== 0 ? `-${Math.abs(priceGap).toFixed(2)}` : '0.00';
      }
      return '';
    },
    close() {
      this.$closePage(this.fullPath);
    },
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    getData: function () {
      this.loading = true;
      request(GETONE_XSGSQUICKVIEW_DETAIL, METHOD.GET, {id:this.orderId}).then(res => {
        if (res.data.data.accountingReportAccessEnabled || this.checkPf('PRE_QUOTE_REPORT_DETAIL')) {
          this.canDownloadExcel = true;
        }
        // else {
        //   this.$router.push("/xsgs-home");
        //   return;
        // }
        this.dataForm = res.data.data;
        // 获取页面功能权限
        this.arrayChunk();
        // 设置全明细高度
        this.setQmxHeight();
        // 获取税率
        this.tax_rate = this.dataForm.baseInfo.taxRate;
        this.loading = false;


      });
    },
    // 计算税率
    computeTaxRate(value, flag) {
      let res = value ? value : 0;
      if(res == '***'){
        return "***";
      }else{
        return flag ? (res * this.tax_rate).toFixed(2) : Number(res).toFixed(2);
      }
      // if (!this.canShowDetails) {
      //   return res;
      // }
      // return flag ? (res * this.tax_rate).toFixed(2) : Number(res).toFixed(2);
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      let that = this;
      this.$confirm({
        title: '提示',
        content: '是否确认导出预报价单?',
        onOk() {
          exportExcel(DOWNLOAD_XSGSQUICKQUOTE, {id: that.orderId}, "预报价单.xlsx");
        }
      });
    },
    arrayChunk() {
      if (!this.dataForm.options) {
        this.dataForm.options = [];
      }
      if (this.dataForm.options.length === 0) {
        this.dataForm.options.push({});
      }

      if (!this.dataForm.parts) {
        this.dataForm.parts = [];
      }

      if (this.dataForm.parts.length === 0) {
        this.dataForm.parts.push({});
      }

      if (!this.dataForm.reductions) {
        this.dataForm.reductions = [];
      }

      if (this.dataForm.reductions.length === 0) {
        this.dataForm.reductions.push({});
      }
    },
    setQmxHeight() {
      this.reductionsTableLength = this.dataForm.reductions.length + 1;
      this.qmxRowSpan = this.dataForm.options.length + this.dataForm.parts.length + this.dataForm.reductions.length + 10;
      this.qmxStylye.height = (this.qmxRowSpan * 25.5) + "px";
    },
  }
}
</script>

<style scoped>

/deep/ .header-button-area {
  z-index: 999;
  height: 45px;
  background-color: #ffff;
  line-height: 45px;
  margin-top: -25px;
}

/deep/ .ant-form-item {
  margin: 0;
}

.panelTitle {
  width:100%;
  background-color: #bdd7ee;
  font-weight:bold;
  text-align: left;
  font-size:15px;
}

.titleClass{
  background-color: #bdd7ee;
  text-align: left;
  font-weight:bold;
  font-size: .75rem;
}

.thClass {
  background-color: #bdd7ee;
}

.tableClass {
  text-align:center;
  margin:auto;
  width: 100%;
  font-size: 15px;
  table-layout:fixed;
}

td {
  font-size: .75rem;
}

</style>
