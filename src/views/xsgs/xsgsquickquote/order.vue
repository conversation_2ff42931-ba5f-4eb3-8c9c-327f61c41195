<template>
  <div>
    <a-card>
      <a-spin :spinning="loading">
        <a-form-model ref="dataFormRef" :model="dataForm" v-if="dataForm">
          <h2 style="text-align: center;"><strong>预报价单</strong></h2>
          <a-row v-if="dataForm.baseInfo">
            <a-col :span="24">
              <table  style="width: 98%" class="tableClass" border="1" cellspacing="0">
                <tr>
                  <th class="titleClass" colspan="24">基本信息</th>
                </tr>
                <tr>
                  <td class="thClass" colspan="3">核算单号</td>
                  <td colspan="5">{{ dataForm.baseInfo.orderNo }}</td>
                  <td class="thClass" colspan="3">客户号</td>
                  <td colspan="5">{{ dataForm.baseInfo.custNumber }}</td>
                  <td class="thClass" colspan="3">客户名称</td>
                  <td colspan="5">{{ dataForm.baseInfo.custName }}</td>
                </tr>
                <tr>
                  <td class="thClass" colspan="3">产品型号</td>
                  <td class="thClass" colspan="5">虚拟供货号</td>
                  <td class="thClass" colspan="3">燃料</td>
                  <td class="thClass" colspan="2">品系</td>
                  <td class="thClass" colspan="3">场景1</td>
                  <td class="thClass" colspan="4">场景2</td>
                  <td class="thClass" colspan="4">场景3</td>
                </tr>
                <tr>
                  <td colspan="3">{{ dataForm.baseInfo.productModel }}</td>
                  <td colspan="5">{{ dataForm.baseInfo.virtualSupplyName  }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.fuel }}</td>
                  <td colspan="2">{{ dataForm.baseInfo.strain }}</td>
                  <td colspan="3">{{ dataForm.baseInfo.scene1 }}</td>
                  <td colspan="4">{{ dataForm.baseInfo.scene2 }}</td>
                  <td colspan="4">{{ dataForm.baseInfo.scene3 }}</td>
                </tr>
                <tr>
                  <th class="titleClass" colspan="24">价格信息（价格为预报价，不含专用件等相关费用，最终以正式报价为准）</th>
                </tr>
                <tr  style="height: 45px;">
                  <td class="thClass" colspan="4">整机开票价（含税）：</td>
                  <td colspan="5">{{ computeTaxRate(dataForm.baseInfo.taxlessOverallUnitInvoicePrice) }}</td>
                  <td class="thClass" colspan="3">单位：元/台</td>
                  <td class="thClass" colspan="4">整机开票价（无税）：</td>
                  <td colspan="5">{{ dataForm.baseInfo.taxlessOverallUnitInvoicePrice }}</td>
                  <td class="thClass" colspan="3">单位：元/台</td>
                </tr>
                <tr>
                  <th class="titleClass" colspan="24">配置信息（含税，单位：元/台</th>
                </tr>
                <tr>
                  <td class="thClass" colspan="3">零件名称</td>
                  <td class="thClass" colspan="2">系列</td>
                  <td class="thClass" colspan="2">规格</td>
                  <td class="thClass" colspan="2">形式</td>
                  <td class="thClass" colspan="1">供应商</td>
                  <td class="thClass" colspan="2" >售价</td>
                  <td class="thClass" colspan="3">名称</td>
                  <td class="thClass" colspan="2">系列</td>
                  <td class="thClass" colspan="2">规格</td>
                  <td class="thClass" colspan="2">形式</td>
                  <td class="thClass" colspan="1">供应商</td>
                  <td class="thClass" colspan="2" >售价</td>
                </tr>
                <tr v-for="item in dataForm.showOptionData" :key="item.num1">
                  <td colspan="3">{{ item.partsName }}</td>
                  <td colspan="2">{{ item.series }}</td>
                  <td colspan="2">{{ item.specs }}</td>
                  <td colspan="2">{{ item.xs }}</td>
                  <td >{{ item.supplier }}</td>
                  <td colspan="2">{{  computeTaxRate(item.price) }}</td>
                  <td colspan="3">{{ item.partsName2 }}</td>
                  <td colspan="2">{{ item.series2 }}</td>
                  <td colspan="2">{{ item.specs2 }}</td>
                  <td colspan="2">{{ item.xs2 }}</td>
                  <td>{{ item.supplier2 }}</td>
                  <td colspan="2">{{  computeTaxRate(item.price2) }}</td>
                </tr>
                <tr>
                  <th class="titleClass" colspan="24">专用件费用（含税，单位：万元）</th>
                </tr>
                <tr>
                  <td class="thClass" colspan="13">专用件费用明细</td>
                  <td class="thClass" colspan="11">专用件费用分摊规则</td>
                </tr>
                <tr>
                  <td colspan="13" style="width: 100%;padding: 0; border: 1px solid transparent !important;" border="0">
                    <table class="tableClass" border="1">
                      <tr>
                        <td class="thClass" colspan="3">零件名称</td>
                        <td class="thClass" colspan="2">快制费</td>
                        <td class="thClass" colspan="2">改模费</td>
                        <td class="thClass" colspan="2">开模费</td>
                        <td class="thClass" colspan="2">合计</td>
                        <td class="thClass" colspan="2">交货周期(天)</td>
                      </tr>
                      <tr v-for="item in dataForm.parts" :key="item.id">
                        <td colspan="3">{{ item.partName }}</td>
                        <td colspan="2">{{ computeTaxRate(item.rapidProductionFee) }}</td>
                        <td colspan="2">{{ computeTaxRate(item.moldModificationFee) }}</td>
                        <td colspan="2">{{ computeTaxRate(item.moldOpeningFee) }}</td>
                        <td colspan="2">{{ computeTaxRate(item.rapidProductionFee + item.moldModificationFee + item.moldOpeningFee) }}</td>
                        <td colspan="2">{{ item.leadTime }}</td>
                      </tr>
                    </table>
                  </td>
                  <td colspan="11" style="text-align: left">
                    {{ ruleContent }}
                  </td>
                </tr>
                <tr >
                  <th class="titleClass" colspan="24">收益信息（含税，单位：万元）</th>
                </tr>
                <tr >
                  <td class="thClass" colspan="12">基准成本</td>
                  <td class="thClass" colspan="12">实际成本</td>
                </tr>
                <tr >
                  <td rowspan="2" colspan="12">{{ dataForm.baseInfo.jprice }}</td>
                  <td class="thClass" colspan="4">最高成本</td>
                  <td class="thClass" colspan="4">最低成本</td>
                  <td class="thClass" colspan="4">加权成本</td>
                </tr>
                <tr >
                  <td  colspan="4">{{ dataForm.baseInfo.cmaxPrice }}</td>
                  <td  colspan="4">{{ dataForm.baseInfo.cminPrice }}</td>
                  <td  colspan="4">{{ dataForm.baseInfo.cavgPrice }}</td>
                </tr>
              </table>
              <div style="width: 98%; display: flex; margin:5px auto auto auto; justify-content: end;">
                <a-button style="border-radius: 0; font-size: .7rem;" @click="close">关闭</a-button>
                <a-button v-if="canShowAccountingReport" style="border-radius: 0; font-size: .7rem;" :loading="loading" @click="seeDetail" >查看核算详情</a-button>
              </div>
            </a-col>

          </a-row>
        </a-form-model>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  GETONE_XSGSQUICKVIEW_ORDER,
  XSGSQUICKQUOTEPARTSALLOCATIONRULES
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
export default {
  data() {
    return {
      hasAuth,
      dataForm: {},
      fullPath: this.$route.path,
      // 功能权限关键字
      PF_FIELD:"QUICK_QUOTE_MANAGER,PRE_QUOTE_REPORT,PRE_QUOTE_REPORT_DETAIL",
      PF_LIST:[],
      loading: false,
      tax_rate: 1,
      ruleContent: '',
      canShowAccountingReport: false,
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
    // 获取id
    let path = this.$route.path.split("/");
    this.orderId = path[3];
    this.getData();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s === field) !== -1;
    },

    close() {
      this.$closePage(this.fullPath);
    },

    getData: function () {
      this.loading = true;
      const _this = this;
      request(XSGSQUICKQUOTEPARTSALLOCATIONRULES, METHOD.GET, { current: 1, size: 1})
          .then((res) => {
            if (res.data && res.data.data && res.data.data.records && res.data.data.records.length) {
              _this.ruleContent = res.data.data.records[0]['content'];
            }
            request(GETONE_XSGSQUICKVIEW_ORDER, METHOD.GET, {id:this.orderId}).then(res => {
              // if (!(res.data.data.partsPriceAccessEnabled || _this.checkPf('PRE_QUOTE_REPORT_DETAIL'))) {
              //   _this.$router.push("/xsgs-home");
              //   return;
              // }
              _this.dataForm = res.data.data;
              console.log(_this.dataForm.accountingReportAccessEnabled,_this.dataForm.partsPriceAccessEnabled);
              
              // 拆分零部件表
              _this.arrayChunk();
              // 获取税率
              _this.tax_rate = _this.dataForm.baseInfo.taxRate;

              if (res.data.data.accountingReportAccessEnabled === 1) {
                _this.canShowAccountingReport = true;
              }else{
                _this.canShowAccountingReport = false;
              }
              // if (res.data.data.accountingReportAccessEnabled === 1 || _this.checkPf('PRE_QUOTE_REPORT_DETAIL')) {
              //   _this.canShowAccountingReport = true;
              // }
            });
          }).finally(() => {
            _this.loading = false;
      })

    },
    // 计算税率
    computeTaxRate(value) {
      if(value === null || value === undefined){
        return "";
      }else if(value == '***'){
        return "***";
      }else{
        return value ? (value * this.tax_rate).toFixed(2) : "0.00";
      }
    },
    seeDetail() {
      this.$router.push({path:'/quick_quote/xsgs_quick_quote_detail/' + this.orderId, props: true})
    },
    arrayChunk() {
      this.dataForm.showOptionData = [];
      if (!this.dataForm.options) {
        this.dataForm.options = [];
      }
      if (this.dataForm.options.length > 0) {
        let median = Math.round(this.dataForm.options.length / 2);
        let index = 0;
        for (let i = median; i < this.dataForm.options.length; i++) {
          this.dataForm.options[index].partsName2 = this.dataForm.options[i].partsName;
          this.dataForm.options[index].series2 = this.dataForm.options[i].series;
          this.dataForm.options[index].specs2 = this.dataForm.options[i].specs;
          this.dataForm.options[index].xs2 = this.dataForm.options[i].xs;
          this.dataForm.options[index].supplier2 = this.dataForm.options[i].supplier;
          this.dataForm.options[index].price2 = this.dataForm.options[i].price;
          this.dataForm.showOptionData.push(this.dataForm.options[index++]);
        }
        if (this.dataForm.showOptionData.length < median) {
          this.dataForm.showOptionData.push(this.dataForm.options[index]);
        }
      } else {
        this.dataForm.showOptionData.push({});
      }

      if (!this.dataForm.parts) {
        this.dataForm.parts = [];
      }

      if (this.dataForm.parts.length === 0) {
        this.dataForm.parts.push({});
      }
    }

  }
}
</script>

<style scoped>
/deep/ .header-button-area {
  z-index: 999;
  height: 45px;
  background-color: #ffff;
  line-height: 45px;
  margin-top: -25px;
  width: 100%;
}

/deep/ .ant-form-item {
  margin: 0;
}

.panelTitle {
  width:100%;
  background-color: #bdd7ee;
  font-weight:bold;
  text-align: left;
  font-size:15px;
}

.titleClass{
  background-color: #bdd7ee;
  text-align: left;
  font-weight:bold;
  font-size: .75rem;
}

.thClass {
  background-color: #bdd7ee;
}

.tableClass {
  text-align:center;
  margin:auto;
  width: 100%;
  font-size: 15px;
  table-layout: fixed;
  border-collapse: collapse;
}

td {
  font-size: .75rem;
}

</style>
