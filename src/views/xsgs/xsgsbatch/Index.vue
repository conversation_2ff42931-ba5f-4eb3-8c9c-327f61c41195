<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
            <tr>
              <td><a-form-model-item label="批次名称" /></td>
              <td><a-input v-model="searchForm.batchName" /></td>
              <td><a-form-model-item label="批次类型" /></td>
              <td>
                <a-select v-model="searchForm.batchType" style="min-width: 150px;width: 100%">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option v-for="d in batchTypes" :key="d.value">
                    {{ d.label }}
                  </a-select-option>
                </a-select>
              </td>
              <td><a-form-model-item label="批次状态" /></td>
              <td>
                <a-select v-model="searchForm.batchState" style="min-width: 150px;width: 100%">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="WAIT">待执行</a-select-option>
                  <a-select-option value="RUNNING">执行中</a-select-option>
                  <a-select-option value="FINISH">执行完成</a-select-option>
                </a-select>
              </td>
              <td><a-button type="primary" style="float: right" @click="handleSearch"><a-icon
                    type="search" />查询</a-button></td>
              <td><a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon
                    type="reload" />重置</a-button></td>
            </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus" />新增</a-button>
            <a-button type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon
                type="delete" />删除</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table :columns="columns" rowKey="id" :pagination="pagination" :data-source="data" :scroll="{ x: 1000 }"
        size="small" :customRow="onRowClick"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="batchType" slot-scope="text, record">
          {{ coverType(record.batchType) }}
        </template>
        <template slot="batchState" slot-scope="text, record">
          {{ coverState(record.batchState) }}
        </template>
        <span slot="action" slot-scope="record">
          <a @click="handleExecute(record)">执行</a>
          <a-divider type="vertical" />
          <a @click="handleShowLogs(record)">日志</a>
          <a-divider type="vertical" />
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a @click="handleDelete(record)">删除</a>
        </span>

      </a-table>
    </a-card>
    <a-modal :title="form.id ? '编辑' : '新增'" :visible="dialog" :confirm-loading="confirmLoading" width="50%"
      @ok="handleSubmit" @cancel="closeForm">
      <a-form-model ref="form" :model="form" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }" style="height: auto;">
        <a-row class="form-row">
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="任务名称" prop="plate">
              <a-input v-model="form.batchName" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="任务类型" prop="plate">
              <a-select v-model="form.batchType" :disabled="!!form.id" @change="showParams">
                <a-select-option v-for="d in batchTypes" :key="d.value">
                  {{ d.label }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-show="showParam1" :lg="24" :md="24" :sm="24">
            <a-form-model-item :label="param1Title">
              <a-input v-model="form.param1" />
            </a-form-model-item>
          </a-col>
          <a-col v-show="showParam2" :lg="24" :md="24" :sm="24">
            <a-form-model-item :label="param2Title" prop="plate">
              <a-input v-model="form.param2" />
            </a-form-model-item>
          </a-col>
          <a-col v-show="showParam3" :lg="24" :md="24" :sm="24">
            <a-form-model-item :label="param3Title">
              <a-checkbox
                v-if="form.batchType != 'WNGL' && form.batchType != 'PC' && form.batchType != 'ZJS' && form.batchType != 'CB'"
                v-model="isCover" :checked="isCover" @change="coverChange" />
              <a-input
                v-if="form.batchType == 'WNGL' || form.batchType == 'PC' || form.batchType == 'ZJS' || form.batchType == 'CB'"
                v-model="form.param3" />
            </a-form-model-item>
          </a-col>
          <a-col v-show="showParam4" :lg="24" :md="24" :sm="24">
            <a-form-model-item :label="param4Title">
              <a-checkbox-group v-if="form.batchType == 'JC'" v-model="checkedItems">
                <a-checkbox value="BP">基本型开票价</a-checkbox>
                <a-checkbox value="BD">基本型预算</a-checkbox>
                <a-checkbox value="MC">材料成本</a-checkbox>
                <a-checkbox value="PP">零部件价格</a-checkbox>
              </a-checkbox-group>
              <a-input v-if="form.batchType == 'JG' || form.batchType == 'SJ'" v-model="form.param4" />
            </a-form-model-item>
          </a-col>
          <a-col v-show="showParam5" :lg="24" :md="24" :sm="24">
            <a-form-model-item v-show="form.batchType == 'JG' || form.batchType == 'SJ'" :label="param5Title" prop="plate">
              <a-input v-model="form.param5" />
            </a-form-model-item>
          </a-col>
          <a-col :lg="24" :md="24" :sm="24">
            <a-form-model-item label="备注">
              <textarea class="xsgs-textarea" v-model="form.remark" />
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal title="执行日志" :visible="showlog" width="80%" @ok="showlog = false" @cancel="showlog = false">
      <a-form-model ref="logsForm" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }" style="height: auto;">
        <a-row class="form-row">
        </a-row>
        <a-table :columns="columns2" rowKey="id" :pagination="pagination2" :data-source="data2" size="middle"
          :customRow="onRowClick" :loading="loading">
          <!-- 序号 -->
          <template slot="num" slot-scope="text, record, index">
            {{ index + 1 }}
          </template>
          <template slot="optTime" slot-scope="text, record">
            {{ dateFormat(record.optTime) }}
          </template>
        </a-table>
      </a-form-model>

    </a-modal>

  </div>
</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import { LIST_XSGSBATCH_PAGE, DELETE_XSGSBATCH, DELETE_XSGSBATCH_LIST, SUBMIT_XSGSBATCH, EXECUTE_XSGSBATCH, LIST_XSGSBATCHLOG_PAGE, GET_BATCH_TYPES } from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import Cookie from 'js-cookie'
import { dateFormat } from "@/utils/dateUtil";
export default {
  name: "xsgsbatch.vue",
  data() {
    return {
      hasAuth,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      showlog: false,
      searchForm: {},
      checkedItems: [],
      form: {},
      logsForm: {},
      param1Title: "",
      param2Title: "",
      param3Title: "",
      param4Title: "",
      param5Title: "",
      showParam1: false,
      showParam2: false,
      showParam3: false,
      showParam4: false,
      showParam5: false,
      isCover: false,
      batchId: null,
      selectedRowKeys: [],
      batchTypes: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
          `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
            total / this.pagination.size
          )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
          this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      pagination2: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        batchTypes: [],
        showTotal: (total) =>
          `共 ${total} 条 第${this.pagination2.current}/${Math.ceil(
            total / this.pagination2.size
          )}页`, // 显示总数
        onChange: (page, size) => this.onLogPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
          this.onLogSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        { title: '任务名称', dataIndex: 'batchName' },
        { title: '任务类型', dataIndex: 'batchType', width: 180, scopedSlots: { customRender: 'batchType' } },
        { title: '任务状态', dataIndex: 'batchState', width: 85, scopedSlots: { customRender: 'batchState' } },
        { title: '备注', dataIndex: 'remark' },
        { title: '操作', fixed: 'right', width: 185, scopedSlots: { customRender: 'action' } }
      ],
      data2: [],
      columns2: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        { title: '操作人', dataIndex: 'optUser' },
        { title: '内容', dataIndex: 'optContent' },
        { title: '操作时间', dataIndex: 'optTime', scopedSlots: { customRender: 'optTime' } },
      ],
    }
  },
  mounted() {

    this.getData()
  },
  created() {
    this.getBatchTypes();
  },
  methods: {
    getData: function () {
      this.loading = true
      let { current, size } = this.pagination
      request(LIST_XSGSBATCH_PAGE, METHOD.GET, { ...this.searchForm, current, size })
        .then(res => {
          const { records, total } = res.data.data
          this.loading = false
          this.data = records
          this.pagination.total = parseInt(total)
        })
    },
    getBatchTypes() {
      request(GET_BATCH_TYPES, METHOD.GET, {})
        .then(res => {
          this.batchTypes = res.data.data;
        })
    },
    getDataLogs: function (record) {
      this.loading = true
      let { current, size } = this.pagination2
      request(LIST_XSGSBATCHLOG_PAGE, METHOD.GET, { ...this.logsForm, current, size })
        .then(res => {
          const { records, total } = res.data.data
          this.loading = false
          this.data2 = records
          this.pagination2.total = parseInt(total)
        })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.batchName} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSBATCH + record.id, METHOD.DELETE)
            .then(() => {
              this.$message.info('删除成功')
              this.getData()
            }).catch(() => {
              this.loading = false
            })
        }
      });
    },
    handleSubmit() {
      //  校验
      let batchType = this.form.batchType;
      if (!this.form.batchName || this.form.batchName.length == 0) {
        this.$message.error('请输入任务标题');
        return;
      }
      if (!this.form.batchType || this.form.batchType.length == 0) {
        this.$message.error('请选择任务类型');
        return;
      }
      if (batchType == "JG" || batchType == "ZR" || batchType == "JC") {
        if (!this.form.param1 || this.form.param1.length == 0) {
          this.$message.error('请输入源年度');
          return;
        }
      }
      if (batchType == "JC" && this.checkedItems.length > 0) {
        this.form.param4 = this.checkedItems.toString();
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSBATCH, METHOD.POST, this.form)
            .then(() => {
              this.getData()
              this.$message.info('提交成功')
              this.closeForm()
            })
          // console.log(this.form);

        }
      })
    },
    handleShowLogs(record) {
      this.showlog = true;
      this.logsForm.batchId = record.id;
      this.getDataLogs()
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    coverChange(e) {
      this.checked = e.target.checked;
      if (e.target.checked) {
        this.form.param3 = true;
      } else {
        this.form.param3 = false;
      }
    },
    deleteSelectedIds() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSBATCH_LIST, METHOD.POST, { ids: this.selectedRowKeys })
            .then(() => {
              this.$message.info('删除成功')
              this.getData()
            }).catch(() => {
              this.loading = false
            })
        }
      });

    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = { ...record }
      if (this.form.param3 == 'true') {
        this.isCover = true;
      }
      this.coverParams(this.form.batchType);
      if (this.form.batchType == 'JC' || this.form.batchType == 'JG' || this.form.batchType == 'ZR') {
        if (this.form.param3 == 'true') {
          this.checked = true;
        } else {
          this.checked = false;
        }
      }
      if (this.form.batchType == 'JC' && this.form.param4) {
        let items = this.form.param4.split(",");
        this.checkedItems = items;
      }
    },
    handleExecute(record) {
      this.$confirm({
        content: `是否确认执行【${record.batchName}】任务？`,
        onOk: () => {
          // this.loading = true
          request(EXECUTE_XSGSBATCH, METHOD.POST, { ...record })
            .then((res) => {
              this.$message.info('执行操作提交成功，任务执行中')
              this.getData()
            }).catch(() => {
              // this.loading = false
            })
        }
      });
    },


    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
      this.coverParams();
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      // this.$confirm({
      //   title: '提示',
      //   content: '是否确认导出所有信息数据项?',
      //   onOk() {
      //     window.open(decodeURI(DOWNLOAD_XSGSPLATEORDER))
      //   },
      //   onCancel() {},
      // });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },

    onLogPageChange(page, size) {
      this.pagination2.current = page;
      this.pagination2.size = size.toString();
      this.getDataLogs();
    },
    onLogSizeChange(current, size) {
      this.pagination2.current = 1;
      this.pagination2.size = size.toString();
      this.getDataLogs();
    },
    coverType(batchType) {
      const typeMap = {
        'JG': '价格管理复制',
        'WLGX': '物料更新',
        'PC': '碰出基本型',
        'ZR': '折让条件复制',
        'JC': '基础数据复制',
        'WNGL': '往年价格关联',
        'XS': '销售数据同步',
        'SJJG': '计算实际价格',
        'RMC': '同步RM状态号',
        'ZJS': '上年选配件合计再计算',
        'CB': '同步成本信息',
        'SJ': 'A客户复制数据到B客户',
        'DDSGX': '同步更新状态机进度跟踪表订单数'
      };
      return typeMap[batchType] || batchType; // 使用对象映射简化代码
    },
    coverState(batchState) {
      if (batchState == 'WAIT') {
        return '待执行';
      } else if (batchState == 'RUNNING') {
        return '执行中';
      } else if (batchState == 'FINISH') {
        return '已完成';
      } else if (batchState == 'ERROR') {
        return '执行异常';
      }
    },
    showParams() {
      this.coverParams(this.form.batchType);
    },
    coverParams(batchType) {
      this.showParam1 = false;
      this.showParam2 = false;
      this.showParam3 = false;
      this.showParam4 = false;
      this.showParam5 = false;
      this.param1Title = "";
      this.param2Title = "";
      this.param3Title = "";
      this.param4Title = "";
      this.param5Title = "";
      this.checkedItems = [];
      if (batchType == "JG" || batchType == "ZR" || batchType == "JC" || batchType == "SJ") {
        this.showParam1 = true;
        this.showParam2 = true;
        this.showParam3 = true;
        this.param1Title = "源年度";
        this.param2Title = "目标年度";
        this.param3Title = "是否覆盖";
        if (batchType == "JC") {
          this.showParam4 = true;
          this.param4Title = "选择数据";
        }
        if (batchType == "JG") {
          this.showParam4 = true;
          this.param4Title = "商务核算编号";
          this.showParam5 = true;
          this.param5Title = "客户号";
        }
        if (batchType == "SJ") {
          this.showParam2 = false;
          this.param2Title = "";
          this.showParam4 = true;
          this.param4Title = "源客户";
          this.showParam5 = true;
          this.param5Title = "目标客户";
        }
      } else if (batchType == "WLGX") {
        this.showParam1 = true;
        this.param1Title = "年度";
      } else if (batchType == "XS") {
        this.showParam1 = true;
        this.param1Title = "年度";
        this.showParam2 = true;
        this.param2Title = "月份";
      } else if (batchType == "WNGL") {
        this.showParam1 = true;
        this.showParam2 = true;
        this.showParam3 = true;
        this.param1Title = "目标年度";
        this.param2Title = "状态机";
        this.param3Title = "商务核算编号";
      } else if (batchType == "PC") {
        this.showParam1 = true;
        this.showParam2 = true;
        this.showParam3 = true;
        this.param1Title = "年度";
        this.param2Title = "商务核算编号";
        this.param3Title = "选配件名称";
      } else if (batchType == "SJJG") {
        this.showParam1 = true;
        this.showParam2 = true;
        this.param1Title = "目标年度";
        this.param2Title = "商务核算编号";
      } else if (batchType == "RMC" || batchType == "DDSGX") {
        this.showParam1 = true;
        this.param1Title = "年度";
        this.showParam2 = true;
        this.param2Title = "月份";
      } else if (batchType == "ZJS") {
        this.showParam1 = true;
        this.showParam2 = true;
        this.showParam3 = true;
        this.param1Title = "目标年度";
        this.param2Title = "客户号";
        this.param3Title = "商务核算编号";
      } else if (batchType == "CB") {
        this.showParam1 = true;
        this.param1Title = "年度";
        this.showParam2 = true;
        this.param2Title = "客户";
        this.showParam3 = true;
        this.param3Title = "状态机";
      }
    },
    dateFormat(date) {
      return dateFormat(date, 'YYYY-MM-DD HH:mm:ss');
    },
  }
}
</script>

<style scoped>
.ant-input-number {
  width: 100%;
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
  */
/deep/.ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/.ant-form-item {
  margin: 0;
}

/deep/.ant-form>table>tbody>tr>td {
  border: 1px solid #f0f0f0;
}
</style>
