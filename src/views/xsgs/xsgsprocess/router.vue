<template>
    <div>
        <a-card>
            <div>{{message}}</div>
        </a-card>
    </div>

</template>

<script>
    import {ROUTER_XSGSPROCESS,PROCESS_CALLBACK} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";


    export default {
        name: "router.vue",
        data() {
            return {
                loading: false,
                message:"跳转中...",
                processCode:null,
                action:null,
                formId:null,
                taskId:null,
                instanceId:null,
                fullPath: this.$route.path,
            }
        },
        async mounted() {
            this.checkName();
            let res = await this.router();
            if (res) {
                if (res.data.code == 0) {
                    let process = res.data.data;
                    let  url = "";
                    if (process.process == 'bussPolicy') {
                        url = "/xsgs_process_manager/xsgs_business_applove/" + process.process + "/" + process.id;
                    } else if (process.process == 'quote') {
                        url = "/xsgs_process_manager/xsgs_quote_applove/" + process.process + "/" + process.id;
                    } else if (process.process == 'intCont') {
                        url = "/xsgs_process_manager/xsgs_intcont_applove/" + process.process + "/" + process.id;
                    } else if (process.process == 'quoteLetter') {
                        url = "/xsgs_process_manager/xsgs_quote_letter_applove/" + process.process + "/" + process.id;
                    } else {
                        this.message = "跳转失败，请联系管理员";
                        return;
                    }

                    // this.$router.push({path:url, props: true});
                    this.$closePage(this.fullPath, url);
                } else {
                    this.message = res.data.msg;
                }
            }
        },
        methods: {
             router(){
                this.instanceId = this.$route.query.instanceId;
                this.taskId = this.$route.query.taskId;
                this.processCode = this.$route.params.processCode;
                this.action = this.$route.params.processCode;
                this.formId = this.$route.params.formId;
                if (!this.instanceId) {
                    this.message = "跳转失败，请联系管理员";
                    return
                }
                return request(ROUTER_XSGSPROCESS, METHOD.GET, {instanceId:this.instanceId})
            }
        },

    }
</script>

<style scoped>

</style>
