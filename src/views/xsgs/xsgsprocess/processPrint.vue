<template>
    <div style="position: relative; overflow-y: auto;padding-top: 0">
        <a-spin :spinning="loading">

            <a-card>
                <div class="header-button-area" :style="`width:${headerWidth};`">
                    <a-button type="primary" v-print="print">
                        打印
                    </a-button>
                    <a-button type="primary" @click="handleClose()">
                        关闭
                    </a-button>
                </div>

                <div style="margin-top: 20px">
                    <a-row style="margin-bottom: 5px;color: blue">
                        <span>请选择打印的字号：</span>
                        <a-radio-group v-model="fontStyle" @change="changeFontStyle">
                            <a-radio style="color: blue" value="fontSmall">
                                小号字
                            </a-radio>
                            <a-radio style="color: blue" value="fontMiddle">
                                中号字
                            </a-radio>
                            <a-radio style="color: blue" value="fontLarge">
                                大号字
                            </a-radio>

                        </a-radio-group>
                    </a-row>
                    <a-row style="margin-bottom: 3px;color: blue">
                        <span>请选择打印的部分：</span>
                        <a-checkbox-group v-model="partList" @change="changeParts">
                            <a-checkbox style="color:blue" value="processBase">打印上部</a-checkbox>
                            <a-checkbox style="color:blue" value="processInfo_">打印正文</a-checkbox>
                            <a-checkbox style="color:blue" value="processApproval">打印流程审批</a-checkbox>
                            <a-checkbox style="color:blue" value="processAnnex">打印附件</a-checkbox>
                        </a-checkbox-group>
                    </a-row>
                    <a-form-model id="printArea" :class="fontStyle" ref="processForm">
                        <table style="width: 100%">
                            <tbody class="ant-table">
                            <tr v-show="isPrint('processBase')">
                                <th>作者</th>
                                <td class="textTd">
                                    {{processForm.author}}
                                </td>
                                <th>创建时间</th>
                                <td style="text-align: center">
                                    {{processForm.createTime?dateFormat(processForm.createTime):''}}
                                </td>
                            </tr>
                            <tr v-show="isPrint('processBase')">
                                <th>
                                    流程(<a target="_blank" :href="`${processimageUrl}`">流程图</a>)
                                </th>
                                <td  class="textTd">
                                    {{processForm.processName}}
                                </td>
                                <th>
                                    流程编号
                                </th>
                                <td  class="textTd">
                                    {{processForm.processNumber}}
                                </td>
                            </tr>
                            <tr v-show="isPrint('processBase')">
                                <th>
                                    <span v-if="isDrafted()" style="color: red">*</span>
                                    主题
                                </th>
                                <td colspan="3" class="textTd">
                                    {{processForm.title}}
                                </td>
                            </tr>
                            <tr v-show="isPrint('processBase')">
                                <th>当前位置</th>
                                <td class="textTd">
                                    {{processForm.nodeInfo.nodeDisplayName}}
                                </td>
                                <th>处理人</th>
                                <td class="textTd">
                                    {{processForm.nodeInfo.userDisplayName}}
                                </td>
                            </tr>
                            <tr v-show="processForm.process == 'quoteLetter'">
                                <th>客户号</th>
                                <td class="textTd">
                                    {{processForm.customer}}
                                </td>
                                <th>客户名称</th>
                                <td class="textTd">
                                    {{processForm.customerName}}
                                </td>
                            </tr>
                            <tr v-show="isPrint('processInfo_')">
                                <td colspan="4">
                                    <div class="w-e-text-container">
                                        <div style="width:100%; height:100%; display:block;" class="w-e-text" v-html="info_"></div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <bussPolicy v-show="isPrint('processInfo_')" v-if="process==allProcess.bussPolicy.code" :isView="true" :form="processForm"
                                    :userInfo="userInfo" ref="bussPolicy"/>
                        <quote v-show="isPrint('processInfo_')" :isView="true" v-if="process==allProcess.quote.code" :form="processForm"
                               :userInfo="userInfo" ref="quote"/>
                        <intCont v-show="isPrint('processInfo_')" :isView="true" v-if="process==allProcess.intCont.code" :form="processForm"
                                 :userInfo="userInfo" ref="intCont"/>
                        <quoteLetter v-show="isPrint('processInfo_')" :isView="true" v-if="process==allProcess.quoteLetter.code" :form="processForm"
                                     :userInfo="userInfo" ref="quoteLetter"/>
                        <appInfo v-show="isPrint('processApproval')" :isView="true" :form="processForm" :userInfo="userInfo" ref="appInfo"/>
                        <appLog v-show="isPrint('processApproval')" :processId="processForm.id" ref="appLog"/>
                    </a-form-model>
                </div>
            </a-card>
        </a-spin>
    </div>
</template>

<script>
    import {
        GET_XSGSPROCESS_ID,
        GET_NODE_INFO_CODE,
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import {dateFormat} from "@/utils/dateUtil";
    import Cookie from 'js-cookie'
    import $ from 'jquery';
    import appInfo from "./applove_info";
    import appLog from "./applove_log";
    import bussPolicy from "../xsgsprocesspolicy/buss_policy";
    import quote from "../xsgsprocessquote/quoteList";
    import intCont from "../xsgsProcessIntCont/intContList";
    import quoteLetter from "../xsgsprocessquoteletter/quoteLetterList";

    export default {
        name: "ViewProcess.vue",
        components: {
            appInfo,
            appLog,
            bussPolicy,
            quote,
            intCont,
            quoteLetter
        },
        data() {
            return {
                Cookie,
                loading: false,
                getting: false,
                confirmLoading: false,
                isBackForm: false,
                isFullFlag: false,
                fontStyle:'fontSmall',
                partList:['processBase','processInfo_','processApproval','processAnnex'],
                smallFont:'12px',
                middleFont:'14px',
                largeFont:'18px',
                editorHeight: "250px",
                userInfo: this.$store.state.account.user,
                processId: this.$route.params.id,
                fullPath: this.$route.path,
                headerWidth: null,
                process: null,
                processimageUrl: null,
                pageTitle: null,
                allProcess: {
                    bussPolicy: {
                        code: "bussPolicy", bpmCode: "YcBpmXsgsBussPolicyProcess", name: "商务政策审批流程", node: {
                            drafted: "办事处/系统部部门申请"
                        }
                    },
                    quote: {
                        code: "quote", bpmCode: "YcBpmXsgsQuoteProcess", name: "商务报价审批流程", node: {
                            drafted: "起草"
                        }
                    },
                    intCont: {
                        code: "intCont", bpmCode: "YcBpmXsgsIntContProcess", name: "商务合同内部审批流程", node: {
                            drafted: "起草"
                        }
                    },
                    quoteLetter: {
                        code: "quoteLetter", bpmCode: "YcBpmXsgsQuoteLetterProcess", name: "商务报价函审批流程", node: {
                            drafted: "起草"
                        }
                    }
                },
                annexList: [],
                processForm: {
                    id: null,
                    title: null,
                    author: null,
                    authorName: null,
                    authType: null,
                    processName: null,
                    customer: null,
                    customerName: null,
                    process: null,
                    position: null,
                    option: null, // 0-保存 1-提交 2-保存关闭
                    state: null,
                    handler: null,
                    content: null,
                    flowNodeList: [],
                    linkList: [],
                    annexList: [],
                    nodeInfo: {
                        processId: null,
                        nodeCode: null,
                        nodeName: null,
                        nodeDisplayName: null,
                        preNode: null,
                        nextNode: null,
                        isCurrent: null,
                        isGroup: null,
                        isMultiple: null,
                        isRequired: null,
                    },
                    policyInfo: {},
                    quoteInfo: {},
                    intContInfo: {},
                    quoteLetterInfo: {},
                    approvalInfo: {},
                    processNumber:null,
                },
                print: {
                    id: "printArea", //打印的区域的id名
                    popTitle: "", // 打印配置页上方标题
                    extraHead: "", //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                    extraCss: "",
                },
                editor: null,
                info_: null,
            }
        },
        created() {

        },
        async mounted() {
            this.processForm.id = this.$route.params.id;
            this.process = this.$route.query.process;
            this.pageTitle = this.getProcessName(this.process);
            this.$setPageTitle(this.fullPath, "打印" + this.pageTitle);
            this.checkName();
            let that = this;
            this.$nextTick(function () {
                setTimeout(() => {
                    that.resizeHeader();
                }, 300)
            });
            window.addEventListener("resize", this.resizeHeader);
            this.processForm.authType = 0;
            if (this.processForm.id === ":id") {
                this.processForm.id = null;
            }
            if (!this.processForm.id) {
                
                // 新建流程初始化内容
                //  作者
                this.processForm.author = this.userInfo.fullOrgInfo;
                this.processForm.authorName = this.userInfo.name;
                // 流程名称
                this.processForm.processName = this.getProcessName(this.process);
                this.processForm.process = this.process;
                // 节点信息
                let res = await this.getNodeInfo('drafted');
                if (!res.data.data) {
                    this.$message.error("起草节点配置信息不存在，请联系管理员");
                    return
                }
                this.processForm.nodeInfo = res.data.data;
                this.processForm.nodeInfo.userAccount = this.userInfo.code;
                this.processForm.nodeInfo.userName = this.userInfo.name;
                this.processForm.nodeInfo.userDisplayName = this.userInfo.fullOrgInfo;

                if (this.processForm.process == that.allProcess.bussPolicy.code) {
                    that.$refs.bussPolicy.initData(null, this.processForm.nodeInfo.nodeCode);
                }
                if (this.processForm.process == that.allProcess.quote.code) {
                    that.$refs.quote.initData();
                }
                if (this.processForm.process == that.allProcess.intCont.code) {
                    that.$refs.intCont.initData();
                }
                if (this.processForm.process == that.allProcess.quoteLetter.code) {
                    that.$refs.quoteLetter.initData();
                }
                this.$refs.appInfo.initFlowNodeList(this.processForm.process, this.processForm.id);
            } else {
                this.getData();
            }
            setTimeout(()=>{
                that.changeParts();
                setTimeout(()=>{
                    that.changeParts();
                },500)
            },500)
        },
        beforeDestroy() {
            window.removeEventListener("resize", this.resizeHeader);
        },
        methods: {
            resizeHeader() {
                this.headerWidth = this.$refs.processForm.$el.clientWidth + 1 + 'px';
            },
            async getNodeInfo(nodeCode) {
                return request(GET_NODE_INFO_CODE, METHOD.GET, {
                    "processId": this.processForm.id,
                    "templateCode": this.processForm.process,
                    "currNode": nodeCode
                });
            },
            setImageUrl() {
                if (this.allProcess[this.processForm.process]) {
                    let processCode = this.allProcess[this.processForm.process].bpmCode
                    this.processimageUrl = process.env.VUE_APP_API_BPM + '?processCode=' + processCode;
                }
            },
            getData: function () {
                let that = this;
                this.loading = true;
                request(GET_XSGSPROCESS_ID, METHOD.GET, {
                    "id": this.processForm.id
                }).then(res => {
                    that.processForm = res.data.data;
                    console.log(that.processForm);
                    that.pageTitle = this.allProcess[this.processForm.process].name;
                    that.print.popTitle = this.pageTitle;
                    that.info_ = that.processForm.content;
                    that.setImageUrl();
                    setTimeout(() => {
                        if (that.processForm.process == that.allProcess.bussPolicy.code) {
                            that.$refs.bussPolicy.initData(this.processForm.refId, this.processForm.nodeInfo.nodeCode);
                        }
                        if (that.processForm.process == that.allProcess.quote.code) {
                            that.$refs.quote.initData(this.processForm.id);
                            that.$refs.quote.customer = this.processForm.customer
                            that.$refs.quote.customerName = this.processForm.customerName
                        }
                        if (this.processForm.process == that.allProcess.intCont.code) {
                            that.$refs.intCont.initData(this.processForm.id);
                        }
                        if (this.processForm.process == that.allProcess.quoteLetter.code) {
                            that.$refs.quoteLetter.initData(this.processForm.id);
                        }

                        that.$refs.appInfo.initFlowNodeList(this.processForm.process, this.processForm.id);
                        that.$refs.appInfo.setLinkList(this.processForm.linkList);
                        that.$refs.appInfo.setAnnexList(this.processForm.annexList);
                        that.$refs.appLog.initLogList(this.processForm.id);
                        that.$refs.appInfo.initContractData(this.processForm.id, this.processForm.process);
                        that.$refs.appInfo.setPageLinkList(this.processForm.pageLinkList);
                        // //如果是报价流程则赋值
                        // if (this.processForm.process === "quote") {
                        //     that.$refs.quote.customer = this.processForm.customer
                        //     that.$refs.quote.customerName = this.processForm.customerName
                        // }
                        setTimeout(()=>{
                            that.changeFontStyle();
                            setTimeout(()=>{
                                that.changeFontStyle();
                            },500)
                        },500)
                    }, 150);
                    that.loading = false;
                });
            },

            handlePrint() {

            },
            handleClose() {
                this.$closePage(this.fullPath);
            },
            getProcessName(code) {
                return this.allProcess[code].name;
            },
            dateFormat(date) {
                return dateFormat(date);
            },
            isDrafted() {
                return this.processForm.nodeInfo && this.processForm.nodeInfo.nodeCode && this.processForm.nodeInfo.nodeCode == 'drafted';
            },
            handleFullscreen() {
                // 浏览器窗口全屏
                this.isFullFlag = !this.isFullFlag;
                if (this.isFullFlag) {
                    this.editorHeight = "100%";
                    $("#wangEditorElem").addClass('screenfull');
                    $("#doScreenfull").attr('data-title', '取消全屏');
                    $("#doScreenfull i").attr('class', 'w-e-icon-fullscreen_exit');
                } else {
                    this.editorHeight = "250px";
                    $("#wangEditorElem").removeClass('screenfull');
                    $("#doScreenfull").attr('data-title', '全屏');
                    $("#doScreenfull i").attr('class', 'w-e-icon-fullscreen');
                }
            },
            changeFontStyle(val){
                if (this.fontStyle == 'fontSmall') {
                    $(".ant-upload-list-item-name").css("font-size", this.smallFont);
                    $(".ant-upload-list-item").css("height", "18px");
                    $(".ant-upload-list-item-info span").css("height", "18px");
                    $(".ant-upload-list-item-info span").css("line-height", "18px");
                    $(".ant-upload-list-item-info i").css("width", this.smallFont);
                    $(".ant-upload-list-item-info i").css("height", this.smallFont);
                    $(".ant-upload-list-item-info i").css("top", "3px");
                } else if (this.fontStyle == 'fontMiddle') {
                    $(".ant-upload-list-item-name").css("font-size", this.middleFont);
                    $(".ant-upload-list-item-info span").css("height", "22px");
                    $(".ant-upload-list-item").css("height", "22px");
                    $(".ant-upload-list-item-info span").css("line-height", "22px");
                    $(".ant-upload-list-item-info i").css("width", this.middleFont);
                    $(".ant-upload-list-item-info i").css("height", this.middleFont);
                    $(".ant-upload-list-item-info i").css("top", "5px");
                } else if (this.fontStyle == 'fontLarge') {
                    $(".ant-upload-list-item-name").css("font-size", this.largeFont);
                    $(".ant-upload-list-item").css("height", "26px");
                    $(".ant-upload-list-item-info span").css("height", "26px");
                    $(".ant-upload-list-item-info span").css("line-height", "26px");
                    $(".ant-upload-list-item-info i").css("width", this.largeFont);
                    $(".ant-upload-list-item-info i").css("height", this.largeFont);
                    $(".ant-upload-list-item-info i").css("top", "8px");
                }
            },
            changeParts(){
                if (!this.isPrint("processAnnex")) {
                    $(".annexRow").hide();
                } else {
                    $(".annexRow").show();
                }
            },
            isPrint(partName) {
                return this.partList.includes(partName);
            }
        }
    }
</script>

<style scoped>
    /deep/ .fontSmall td,
    .fontSmall th,
    .fontSmall input,
    .fontSmall textarea,
    .fontSmall select,
    .fontSmall a,
    .fontSmall .w-e-text-container p
    .fontSmall button {
        font-size: 12px !important;
    }

    /deep/ .fontSmall tr,
    .fontSmall td,
    .fontSmall th {
        height: 22px !important;
    }
    /deep/ .fontMiddle td,
    .fontMiddle th,
    .fontMiddle input,
    .fontMiddle textarea,
    .fontMiddle select,
    .fontMiddle a,
    .fontMiddle .w-e-text-container p
    .fontMiddle button {
        font-size: 14px !important;
    }

    /deep/ .fontMiddle tr,
    .fontMiddle td,
    .fontMiddle th {
        height: 28px !important;
    }

    /deep/ .fontLarge td,
    .fontLarge th,
    .fontLarge input,
    .fontLarge textarea,
    .fontLarge select,
    .fontLarge a,
    .fontLarge .w-e-text-container p
    .fontLarge button {
        font-size: 18px !important;
    }

    /deep/ .fontLarge tr,
    .fontLarge td,
    .fontLarge th {
        height: 33px !important;
    }

    /deep/ .header-button-area {
        position: fixed;
        z-index: 999;
        height: 45px;
        background-color: #ffffff;
        line-height: 45px;
        margin-top: -25px;
        min-width: 700px;
    }

    /deep/ .header-button-area > button {
        margin-right: 8px;
    }

    /deep/ .ant-table > tr {
        height: 33px;
    }

    /deep/ .ant-table > tr > td input {
        /*border-radius: 0px;*/
    }

    /deep/ .ant-table > tr > td a {
        margin-left: 5px;
    }

    /deep/ .ant-table > tr .textTd {
    }

    /deep/ .ant-table > tr > th {
        width: 15%;
        text-align: center;
        background-color: #bdd7ee;
        border: 0.5px solid #464646;
        background-clip: padding-box;
    }

    /deep/ .ant-table > tr > td {
        width: 35%;
        text-align: left;
        border: 0.5px solid #464646;
        background-clip: padding-box;
    }

    .ant-table .editor {
        width: 100%;
        margin: 0 auto;
        border: 1px solid #ccc;
        position: relative;
    }

    .ant-table .toolbar {
        z-index: 0;
        border-top: none;
    }

    .ant-table .text {
        height: 200px;
        border-top: 1px solid #ccc;
        position: static !important;
    }

    .ant-table .w-e-menu {
        z-index: 0 !important;
    }

    .ant-table .w-e-text p, .w-e-text h1, .w-e-text h2, .w-e-text h3, .w-e-text h4, .w-e-text h5, .w-e-text table, .w-e-text pre {
        margin: 2px 0;
        line-height: 1.2;
    }

    /deep/ .ant-table .w-e-text-container {
        z-index: 1 !important;
    }

    /deep/ .ant-table .w-e-menu {
        z-index: 2 !important;
    }

    /deep/ .ant-table .w-e-toolbar {
        z-index: 3 !important;
    }

    /deep/ .ant-table .ant-select-selection {
        /*border-radius: 0px !important;*/
        /*border: none !important;*/
    }

    /deep/ .ant-table .ant-select-selection__rendered {
        margin-left: 0px !important;
    }

    /deep/ .ant-form-item {
        margin-bottom: 10px !important;
    }

    /deep/ input[disabled] {
        color: rgba(0, 0, 0, 0.65) !important;
        background-color: #ffffff !important;
    }

    /deep/ div[role='combobox'] {
        background-color: #ffffff !important;
    }

    /deep/ .ant-select-disabled {
        color: rgba(0, 0, 0, 0.65) !important;
        background-color: #ffffff !important;
    }

    /deep/ .disabelTd {
        background-color: #ffffff !important
    }

    /deep/ .screenfull {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100000;
    }

    /deep/ #doScreenfull {
        z-index: 10007 !important;
    }

    /deep/ .ant-upload-list-item {
        margin-top: 0px !important;
    }
</style>

<style media="print" lang="scss">
@page {
  size: auto;
  font-size: 14px;
}

@media print {
  html {
    zoom: 76%; //设置打印页面的缩放，大小
    margin: 0 auto;
  }
}

.w-e-text-container {
  height: auto;
}
</style>
