<template>
    <div>
        <table v-if="!getting" style="width: 100%;word-wrap: break-word;">
            <tbody class="ant-table apploveInfo">
                <tr v-if="form.process == 'quoteLetter'">
                    <th style="width: 15%">流程提醒</th>
                    <td style="width: 100%" class="textTd" colspan="3">
                        {{ remind }}
                    </td>
                </tr>
                <tr class="annexRow">
                    <th style="width: 15%">{{ form.process == 'intCont' ? '相关补充文件' : '附件'  }}</th>
                    <td style="width: 100%;" colspan="3">
                        <template v-if="form.process === 'quoteLetter'">
                            
                            <FileUpload v-model="annexList" path="xsgs/" field="processApproval" :multiple="true" />
                        </template>
                        <template v-else>
                            <div v-if=" policyForm.isCover == 1">
                                ****
                            </div>
                            <div v-else>
                                <FileUpload v-if="form.process == 'bussPolicy' ? !isShow : true" v-model="annexList"
                                    path="xsgs/" field="processApproval" :multiple="true"
                                    :disabled="form.process == 'bussPolicy' ? isShow : isNotDrafted()" />
                            </div>
                        </template>
                    </td>
                </tr>
                <tr v-if="form.process == 'quoteLetter'">
                    <th style="width: 15%">审批流程</th>
                    <td style="width: 100%;padding:5px !important;" colspan="3">    
                        <table style="width: 100%">
                            <tbody class="ant-table link-table">
                                <tr>
                                    <th style="width: 10%;text-align: center !important;">
                                        <a v-if="form.authType == 1" @click="showApprovalLinkForm"> 添加</a>
                                        <span v-if="form.authType != 1">序号</span>
                                    </th>
                                    <th style="width: 80%">流程编号</th>
                                    <th style="width: 10%">链接</th>
                                </tr>
                                <tr v-for="d in pageLinkList" :key="d.id">
                                    <td style="width: 10%;text-align: center !important;">
                                        <a @click="handleDeleteApprovalLink(d.id)"> 删除</a>
                                        <!-- <span>{{ index + 1 }}</span> -->
                                    </td>
                                    <td style="width: 80%;text-align: center">{{ d.processNumber }}</td>
                                    <td style="width: 10%;text-align: center !important;">
                                        <a target="_blank" :href="d.link">链接</a>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div
                            :style="{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%', padding: '10px' }">
                            <a-empty v-if="pageLinkList.length == 0"  :description="false"/>
                        </div>
                    </td>
                </tr>
                <tr v-if="form.process == 'intCont' || form.process == 'quoteLetter'">
                    <th style="width: 15%">相关链接</th>
                    <td style="width: 100%;padding:5px !important;" colspan="3">
                        <table style="width: 100%">
                            <tbody class="ant-table link-table">
                                <tr>
                                    <th style="width: 10%;text-align: center !important;">
                                        <a v-if="form.authType == 1" @click="showLinkForm"> 添加</a>
                                        <span v-if="form.authType != 1">序号</span>
                                    </th>
                                    <th style="width: 80%">链接描述</th>
                                    <th style="width: 10%">链接</th>
                                </tr>
                                <tr v-for="(d, index) in linkList" :key="d.id">
                                    <td style="width: 10%;text-align: center !important;">
                                        <a v-if="form.authType == 1" @click="handleDeleteLink(d.id)"> 删除</a>
                                        <span v-if="form.authType != 1">{{ index + 1 }}</span>
                                    </td>
                                    <td style="width: 80%">{{ d.linkDesc }}</td>
                                    <td style="width: 10%;text-align: center !important;"><a target="_blank"
                                            :href="d.link">链接</a></td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- <div
                            :style="{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', width: '100%', padding: '10px' }">
                            <a-empty v-if="linkList.length == 0" />
                        </div> -->
                    </td>
                </tr>
                <!-- 相关历史流程 -->
                <tr v-if="form.process == 'intCont'">
                    <th style="text-align: center">历史流程</th>
                    <td style="width: 100%;" colspan="3">
                        <a-collapse v-model="activeKey" style="float:none;">
                            <a-collapse-panel key="1" header="相关历史流程">
                                <a-spin :spinning="histiorySpinning" class="relevant_history" style="display: flex;flex-direction:column;">
                                    <table style="border-collapse: collapse;">
                                        <tbody>
                                            <tr>
                                                <th style="width: 5%" class="history-table-th">年度</th>
                                                <th style="width: 5%" class="history-table-th">客户号</th>
                                                <th style="width: 10%" class="history-table-th">客户名称</th>
                                                <th style="width: 10%" class="history-table-th">合同编号</th>
                                                <th style="width: 10%" class="history-table-th">合同类型</th>
                                                <th style="width: 10%" class="history-table-th">合同文件</th>
                                                <th style="width: 7%" class="history-table-th">操作</th>
                                            </tr>
                                            <tr v-for="d in historyList" :key="d.id">
                                                <td class="textTd" style="width: 5%;;text-align: center">
                                                    {{ d.year }}
                                                </td>
                                                <td class="textTd" style="width: 5%;;text-align: center">
                                                    {{ d.customer }}
                                                </td>
                                                <td class="textTd" style="width: 10%;text-align: center">
                                                    {{ d.customerName }}
                                                </td>
                                                <td class="textTd" style="width: 10%;text-align: center">
                                                    {{ d.contractNo }}
                                                </td>
                                                <td class="textTd" style="width: 10%;text-align: center">
                                                    {{coverType(d.contractType, d)}}
                                                </td>
                                                <td class="textTd" style="width: 10%;text-align: center">
                                                    <!-- {{ d.url }} -->
                                                    <FileUpload class="title" v-model="d.annexList" path="xsgs/"
                                                        field="intCont" :                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       ="true" :disabled="true" />
                                                </td>
                                                <td class="textTd" style="width: 7%;text-align: center">
                                                    <a type="primary" @click="handleView(d)">查看</a>
                                                </td>
                                            </tr>

                                        </tbody>
                                        
                                    </table>
                                    <div class="empty" v-if="historyList.length == 0 && form.process == 'intCont'" style="margin: 20px;align-self: center;">
                                        <a-empty description="暂无相关历史流程" > </a-empty>
                                    </div>
                                    
                                    <!-- <a-table :columns="historyListColumns" :data-source="historyList">
                                        <div slot="action" slot-scope="text,record">
                                            <a-button type="primary" @click="handleView(record)">查看</a-button>
                                        </div>
                                    </a-table> -->
                                </a-spin>

                            </a-collapse-panel>
                        </a-collapse>
                    </td>
                </tr>
                <tr v-if="form.process == 'intCont' && isNotDrafted()">
                    <th style="width: 15%"><span style="color: red;" v-if="isPreviousNode()">*</span>客户是否确认</th>
                    <td style="width: 35%">
                        <!-- 采用选择框形式 -->
                        <a-tooltip :title="isPreviousNode() ? '请选择客户是否确认' : '客户确认反馈环节才可以进行填写' ">
                            <a-select v-model="form.isConfirm" :disabled="!isPreviousNode()">
                                <a-select-option :value="'1'">是</a-select-option>
                                <a-select-option :value="'0'">否</a-select-option>
                            </a-select>
                        </a-tooltip>
                    </td>
                    <th style="width: 15%"><span style="color: red;" v-if="form.isConfirm == 0">*</span>未确认原因</th>
                    <td style="width: 35%">
                        <span v-show="form.isConfirm == 1">
                            客户是否确认选择“否”，可以填写未确认原因
                        </span>
                        <a-textarea placeholder="请填写未确认原因" v-show="form.isConfirm == 0" v-model="form.onfirmReason"
                            :readOnly="isView" />
                    </td>
                </tr>
                <tr>
                    <th style="width: 15%;cursor: pointer;" @click="control_linkDrawer">
                        <span>审批环节</span>
                        <a-icon :class="linkDrawerVisiable ? 'iconRoate':'' " type="right" style="margin-left: 5px;margin-top: 3px;transition: all .2s;" />
                    </th>
                    <th style="width: 100%" colspan="3">
                        处理人
                    </th>
                </tr>
                <tr v-for="d in flowNodeList" :key="d.nodeCode" :class="linkDrawerVisiable? '' : 'collapseState'" style="animation: all .5s;">
                    <td style="width: 15%;text-align: center">{{ d.nodeDisplayName }}</td>
                    <td style="width: 100%;" colspan="3">
                        <a-input :id="d.nodeCode"
                            v-if="!isView && form.nodeInfo.nodeCode == d.inputNode && (d.isCurrent == 1 || d.isGroup == 1)"
                            v-model="d.userDisplayName" :style="`border:${inputBorder[d.nodeCode]}`" readOnly />
                        <a-select :id="d.nodeCode" :placeholder="d.placeholder"
                            v-if="!isView && d.isCurrent != 1 && d.isGroup != 1 && d.sort > form.nodeInfo.sort && (form.nodeInfo.nodeCode == d.inputNode || d.isRequired == 0)"
                            :value="d.userAccount ? d.userAccount : undefined" :allowClear="d.isRequired == 0"
                            show-search :filter-option="filterOption" @change="changeNodeUser2($event, d)"
                            @blur="checkTableRequired(d, d.nodeCode)">
                            <!-- <a-select-option @click="changeNodeUser(d, u)" v-for="u in d.nodeUserList"
                                :key="u.userAccount">
                                {{ u.userDisplayName }}
                            </a-select-option> -->
                            <a-select-option @click="changeNodeUser(d, u)" v-for="u in filteredNodeUserList(d)" :key="u.userAccount">
                                {{ u.userDisplayName }}
                            </a-select-option>
                        </a-select>
                        <span
                            v-if="isView || (form.nodeInfo.nodeCode != d.inputNode && (d.isRequired == 1 || (d.isRequired == 0 && d.sort <= form.nodeInfo.sort)))">{{
            d.userDisplayName }}</span>
                    </td>
                </tr>

                <tr v-if="form.process != 'quoteLetter'">
                    <th style="width: 15%">流程提醒</th>
                    <td style="width: 100%" class="textTd" colspan="3">
                        {{ remind }}
                    </td>
                </tr>
                <tr v-if="form.process != 'intCont' && form.process != 'quoteLetter'">
                    <th style="width: 15%">相关链接</th>
                    <td style="width: 100%;padding:5px !important;" colspan="3">
                        <table style="width: 100%">
                            <tbody class="ant-table link-table">
                                <tr>
                                    <th style="width: 10%;text-align: center !important;">
                                        <a v-if="!isView && form.authType == 1" @click="showLinkForm"> 添加</a>
                                        <span v-if="isView || form.authType != 1">序号</span>
                                    </th>
                                    <th style="width: 80%">链接描述</th>
                                    <th style="width: 10%">链接</th>
                                </tr>
                                <tr v-for="(d, index) in linkList" :key="d.id">
                                    <td style="width: 10%;text-align: center !important;">
                                        <a v-if="!isView && form.authType == 1" @click="handleDeleteLink(d.id)"> 删除</a>
                                        <span v-if="isView || form.authType != 1">{{ index + 1 }}</span>
                                    </td>
                                    <td style="width: 80%">{{ d.linkDesc }}</td>
                                    <td style="width: 10%;text-align: center !important;"><a target="_blank"
                                            :href="d.link">链接</a></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        <a-modal width="50%" title="添加链接" :visible="isLinkForm" :confirm-loading="confirmLoading" @ok="handleAddLink"
            @cancel="closeLinkForm">
            <a-form-model ref="linkForm" :model="linkForm">
                <table style="width: 100%">
                    <tbody class="ant-table model-table">
                        <tr>
                            <th>描述：</th>
                            <td><a-input v-model="      linkForm.linkDesc" :maxLength="500" placeholder="请输入链接描述" /></td>
                        </tr>
                        <tr>
                            <th style="text-align: right;width: 10%;background-color: #bdd7ee">链接：</th>
                            <td><a-input v-model="linkForm.link" :maxLength="2500" placeholder="请输入链接" /></td>
                        </tr>
                    </tbody>
                </table>
            </a-form-model>
        </a-modal>
        <!-- 审批流程添加链接 -->
        <a-modal width="50%" title="添加链接" :visible="approvalLinkFormLoading" :confirm-loading="confirmApprovalLoading"
            @ok="handleAddApprovalLink" @cancel="closeApprovalLinkForm">
            <a-form-model ref="approvalink" :model="approvalink">
                <table style="width: 100%">
                    <tbody class="ant-table model-table">
                        <tr>
                            <th>流程编号：</th>
                            <td><a-input v-model="approvalink.processNumber" :maxLength="500" placeholder="流程编号" /></td>
                        </tr>
                        <tr>
                            <th style="text-align: right;width: 10%;background-color: #bdd7ee">链接：</th>
                            <td><a-input v-model="approvalink.link" :maxLength="2500" placeholder="请输入链接" /></td>
                        </tr>
                    </tbody>
                </table>
            </a-form-model>
        </a-modal>
    </div>

</template>

<script>
import {
    GET_FLOW_NODE_LIST,
    GET_PROCESS_PROCESSHISTORYQUERYINTCONT,
    LIST_XSGSPROCESS_INTCONT,
    GET_CONTRACT_TYPES,
    GET_XSGSPROCESSPOLICY_ID
} from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import FileUpload from "../../../components/upload/FileUpload";
export default {
    name: "applove_info",
    props: {
        form: Object,
        userInfo: Object,
        isView: { type: Boolean, default: false },
    },
    data() {
        return {
            getting: false,
            isLinkForm: false,
            approvalLinkForm: false,
            confirmLoading: false,
            confirmApprovalLoading: false,
            approvalLinkFormLoading: false,
            border: "0.5px solid #d9d9d9",
            requiredBorder: "1px solid red !important",
            normalBorder: "none",
            flowNodeList: [],
            inputBorder: {},
            remind: "",
            linkList: [],
            annexList: [],
            linkForm: {
                linkDesc: null,
                link: null
            },
            approvalink: {
                processNumber: null,
                link: null
            },
            isShow: true,
            activeKey: [],
            historyList: [],
            intContList: [],
            pageLinkList: [],
            contractTypes: [],
            histiorySpinning:false,
            linkDrawerVisiable:false,
            policyForm:{
                isCover:null,
            },
        }
    },
    created() {
        // this.initContractData()
    },
    mounted() {
        this.getContractTypes();
        console.log(this.form);
        // this.initPolicyForm(this.form.refId,this.form.nodeInfo.nodeCode)
    },
    components: {
        FileUpload
    },
    computed: {
       
    },
    methods: {
        filteredNodeUserList(d) {
            // 确保 d.nodeUserList 存在
            if (!d || !d.nodeUserList) {
                return [];
            }
            return d.nodeUserList.filter(u => u.nodeCode === d.nodeCode);
        },
        filterOption(inputValue, option) {
            return option.componentOptions.children[0].text.indexOf(inputValue) != -1
        },
        initFlowNodeList(process, processId) {
            let that = this;
            this.flowNodeList = [];
            request(GET_FLOW_NODE_LIST, METHOD.GET, { "templateCode": process, "processId": processId }).then(res => {
                let nodeList = res.data.data;
                nodeList.forEach(item => {
                    that.inputBorder[item.nodeCode] = that.border;
                    if (!item.id && item.isCurrent == 1) {
                        item['userDisplayName'] = that.userInfo.fullOrgInfo;
                        // 构建用户对象
                        let nodeUser = {};
                        nodeUser["nodeCode"] = item.nodeCode;
                        nodeUser["userAccount"] = that.userInfo.code;
                        nodeUser['userName'] = that.userInfo.name;
                        nodeUser['userDisplayName'] = that.userInfo.fullOrgInfo;
                        let userList = [];
                        userList.push(nodeUser);
                        item["userList"] = userList;
                    }
                    that.flowNodeList.push(item);
                });
                if (this.flowNodeList.length > 0) {
                    this.flowNodeList.filter(item => item.nodeCode !== "drafted" &&
                        item.nodeCode !== "sysdept_approval" && item.nodeCode !== "submit_release").forEach(item => {
                            if (item.userAccount === that.userInfo.code) {
                                this.isShow = false
                                // console.log(8822)
                            }
                        });
                }
                this.getRemind();
            })
        },
        setLinkList(linkList) {
            this.linkList = linkList;
        },
        setPageLinkList(pageLinkList) {
            this.pageLinkList = pageLinkList;
        },
        setAnnexList(annexList) {
            this.annexList = annexList;
        },
        changeNodeUser(node, user) {
            console.log(node, user);
            if (!node) {
                this.$message.error("节点异常");
                return;
            }
            if (node.isCurrent == 1 || node.isGroup == 1) {
                return;
            }
            let that = this;
            this.$nextTick(() => {
                let nodeUserList = node.nodeUserList;
                let userList = nodeUserList.filter(item => item.userAccount == user.userAccount);
                node["userList"] = userList;
                node.userAccount = user.userAccount;
                node.userName = user.userName;
                node.userDisplayName = user.userDisplayName;
                node.isJump = 0;
                node['isChanged'] = 1;
                that.flowNodeList.forEach(s => {
                    if (s.id == node.id && s.nodeCode == node.nodeCode) {
                        s['userList'] = node["userList"];
                        s['userAccount'] = node["userAccount"]; 
                        s['userName'] = node["userName"];
                        s['userDisplayName'] = node["userDisplayName"];
                        s['isJump'] = node["isJump"];
                        s['isChanged'] = node["isChanged"];
                    }
                });
                if (!this.isNotDrafted()) {
                    if (!that.isEmpty(node.linkNode)) {
                        console.log(node);
                        let newData = [];
                        
                        that.flowNodeList.forEach(s => {
                            if (s.nodeCode == node.linkNode) {
                                console.log(s);
                                const exists = s.nodeUserList.some(item => 
                                    item.id === node.id && item.userAccount === node.userAccount
                                );
                                let linkUsers = that.copy(userList);
                                if (exists) {
                                    linkUsers.forEach((it) => {
                                        it["nodeCode"] = s.nodeCode;
                                    });
                                    s['userList'] = linkUsers;
                                    s['userAccount'] = user.userAccount;
                                    s['userName'] = user.userName;
                                    s['userDisplayName'] = user.userDisplayName;
                                }
                                
                            }
                        });
                    }
                }

                this.getRemind();
            });
        },
        changeNodeUser2(value, node) {
            if (!value) {
                node.userAccount = undefined;
                node.userName = null;
                node.userDisplayName = null;
                node.userList = [];
                if (this.isNotDrafted()) {
                    node.isJump = 1;
                    node['isChanged'] = 1;
                    this.flowNodeList.forEach(s => {
                        if (s.id == node.id && s.nodeCode == node.nodeCode) {
                            s['userList'] = node["userList"];
                            s['userAccount'] = node["userAccount"];
                            s['userName'] = node["userName"];
                            s['userDisplayName'] = node["userDisplayName"];
                            s['isJump'] = node["isJump"];
                            s['isChanged'] = node["isChanged"];
                        }
                    });
                } else {
                    if (!this.isEmpty(node.linkNode)) {
                        this.flowNodeList.forEach(s => {
                            if (s.nodeCode == node.linkNode) {
                                s['userList'] = [];
                                s['userAccount'] = undefined;
                                s['userName'] = null;
                                s['userDisplayName'] = null;
                            }
                        });
                    }
                }
                this.getRemind();
            }
        },
        getRemind() {
            let remind = "";
            this.flowNodeList.forEach(item => {
                let userList = item.userList;
                if (userList) {
                    userList.forEach(user => {
                        if (user.userName) {
                            let name = item.nodeDisplayName + "(" + user.userName + ")";
                            if (remind.indexOf(name) == -1) {
                                if (remind.length == 0) {
                                    remind = name;
                                } else {
                                    remind += "-" + name;
                                }
                            }
                        }
                    })
                }
            });
            this.remind = remind;
        },
        checkTableRequired(form, node, isNotRemind) {
            let target = document.getElementById(node);
            if (form.isRequired == 1) {
                let value = form.userAccount;
                if (!value) {
                    if (target) {
                        target.style.cssText = 'border:' + this.requiredBorder;
                    }
                    if (!isNotRemind) {
                        this.$message.error("请选择" + form.nodeDisplayName + "处理人");
                    }
                    return false;
                }
            }
            if (target) {
                target.style.cssText = 'border:' + this.normalBorder;
            }
            return true
        },
        checkNodeInfo() {
            let nodeList = this.flowNodeList;
            let msg = null;
            if (!nodeList && nodeList == 0) {
                msg = "处理环节未配置，请联系管理员";
            } else {
                let that = this;
                let checkResult = true;
                nodeList.forEach(d => {
                    if (d.isCurrent != 1 && d.isGroup != 1) {
                        let ret = that.checkTableRequired(d, d.nodeCode, true);
                        if (checkResult && !ret) {
                            checkResult = false;
                        }
                    }
                });
                if (!checkResult) {
                    msg = "请完善处理人信息";
                }
            }
            if (msg) {
                this.$message.error(msg);
            }
            return !msg;
        },
        getFlowNodeList() {
            return this.flowNodeList;
        },
        getLinkList() {
            return this.linkList;
        },
        getPageLinkList() {
            return this.pageLinkList;
        },
        getAnnexList() {
            return this.annexList;
        },
        handleAddLink() {
            if (!this.linkForm.link) {
                this.$message.error("请输入链接");
                return;
            }
            let link = { ...this.linkForm };
            link['id'] = this.linkList.length + Math.round(Math.random() * 1000) + 1;

            this.linkList.push(link);
            this.isLinkForm = false;
        },
        handleAddApprovalLink() {
            if (!this.approvalink.link) {
                this.$message.error("请输入链接");
                return;
            }
            let link = { ...this.approvalink };
            link['id'] = this.pageLinkList.length + Math.round(Math.random() * 1000) + 1;

            this.pageLinkList.push(link);
            // console.log(this.pageLinkList, link);
            this.approvalLinkFormLoading = false;
        },
        handleDeleteLink(id) {
            let newData = this.linkList.filter(item => item.id != id);
            this.linkList = newData;
        },
        handleDeleteApprovalLink(id) {
            let newData = this.pageLinkList.filter(item => item.id != id);
            this.pageLinkList = newData;
        },
        showLinkForm() {
            this.linkForm = [];
            this.isLinkForm = true;
        },
        showApprovalLinkForm() {
            // this.approvalink = [];
            this.approvalLinkFormLoading = true;
        },
        closeLinkForm() {
            this.isLinkForm = false;
        },
        closeApprovalLinkForm() {
            this.approvalink = {
                processNumber: null,
                link: null
            },
                this.approvalLinkFormLoading = false;
        },
        isNotDrafted() {
            return this.isView || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'drafted';
        },
        getMode(d) {

        },
        // 判断是否是 “客户反馈环节”
        isPreviousNode() {
            // 判断是否是归档环节的前一个环节
            // this.$nextTick(() => {
            //     let currentNode = this.form.nodeInfo;
            //     if (currentNode && currentNode.nodeCode && this.flowNodeList.length > 0) {
            //         let currentNodeCode = currentNode.nodeCode;
            //         let prenode = this.flowNodeList[this.flowNodeList.length - 2]
            //         if (currentNodeCode == prenode.nodeCode) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     }
            // })

            // 判断是否是 “客户反馈环节”
            // this.$nextTick(() => {
                // 判断该流程环节是否是客户反馈环节
                if ( this.form.nodeInfo && this.form.nodeInfo.nodeCode == 'sysdept_deal') {
                    this.isPreviousNodeState = true;
                    return true;
                } else {
                    this.isPreviousNodeState = false;
                    return false;
                }
            // })

        },
        initHistoryList() {
            let params = []
            params = this.intContList.map(item => ({
                customer: item.customer,
                year: item.year,
                processId: item.processId
            }));
            // console.log(params);
            if( params && params.length > 0 ){
                this.histiorySpinning = true
                // 对象数组转JSON字符串用于接口参数
                params = JSON.stringify(params);
                request(GET_PROCESS_PROCESSHISTORYQUERYINTCONT, METHOD.POST, params, {
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8' // 正确设置 Content-Type
                    }
                }).then(res => {
                    // console.log(res);
                    if (res.data.data) {
                        this.historyList = res.data.data;
                        this.historyList.map((item) => {
                            item.annexList = []
                            item.annexList.push({
                                name: item.name,
                                path: item.path,
                                uid: item.uid,
                                url: item.url,
                                finalName: item.finalName
                            })
                        })
                        this.histiorySpinning = false
                    }else{
                        this.histiorySpinning = false
                    }
                })
            }else{
                return
            }
            
        },
        initContractData(processId, process) {
            // console.log(1232131231231231, processId,process);
            if (processId && process == 'intCont') {
                // console.log(processId,process);
                // console.log('通过');
                request(LIST_XSGSPROCESS_INTCONT, METHOD.GET, { "processId": processId }).then(res => {
                    if (res.data.data) {
                        this.intContList = res.data.data.intContList ? res.data.data.intContList : [];
                        this.initHistoryList()
                    }
                })
            }
        },
        getApprovalLinklist(list) {
            this.pageLinkList = list ?? []
        },
        getContractTypes() {
            request(GET_CONTRACT_TYPES, METHOD.GET).then(res => {
                this.contractTypes = res.data.data;
            })
        },
        coverType(type, d) {
            if (!type) {
                return "";
            }
            let items = this.contractTypes.filter(s => s.code == type);
            if (items.length > 0) {
                let item = items[0];
                if (item.code == 'OTHER_PROTOCOL') {
                    return d.contractTypeOther ? d.contractTypeOther : item.name;
                } else {
                    return item.name;
                }
            } else {
                return "";
            }
        },
        handleView(record){
            let url = "#/xsgs_process_manager/xsgs_intcont_applove/" + record.process + "/" + record.processId;
            window.open(url, "_blank");
        },
        // 合同审批区数据发生变化时由父组件调用该事件
        intConHistoryList(intContList){
            // console.log(intContList,'触发approve_info的方法');
            let useIntContList = intContList
            let params = []
            useIntContList.map((item)=>{
                let customerList = item.customer.split('、')
                if( customerList && customerList.length > 0 ){
                    customerList.map((item2) =>{
                        params.push({
                            customer: item2,
                            year: item.year,
                            processId: item.processId || ''
                        })
                    })
                }else{
                    return
                }
            })
            // 去重数组
            // params = params.reduce((acc, value) => acc.includes(value) ? acc : [...acc, value], []);
            
            if (params.length > 0) {
                // 对象数组转JSON字符串用于接口参数
                params = JSON.stringify(params);
                this.histiorySpinning = true
                request(GET_PROCESS_PROCESSHISTORYQUERYINTCONT, METHOD.POST, params, {
                    headers: {
                        'Content-Type': 'application/json; charset=utf-8' // 正确设置 Content-Type
                    }
                }).then(res => {
                    // console.log(res);
                    if (res.data.data) {
                        this.historyList = res.data.data;
                        this.historyList.map((item) => {
                            item.annexList = []
                            item.annexList.push({
                                name: item.name,
                                path: item.path,
                                uid: item.uid,
                                url: item.url,
                                finalName: item.finalName
                            })
                        })
                        this.histiorySpinning = false
                    }
                }).catch(err=>{
                    this.$message.warning("相关历史记录获取失败！");
                    this.historyList = [];
                    this.histiorySpinning = false
                    return;
                })

            }else {
                this.$message.warning("合同列表为空，相关历史记录获取失败！");
                this.historyList = [];
                this.histiorySpinning = false
                return;
            }
            
        },
        control_linkDrawer(){
            this.linkDrawerVisiable = !this.linkDrawerVisiable
            // console.log(this.linkDrawerVisiable);
        },
        checkintContForm(){
            // console.log(this.form);
            // isConfirm  onfirmReason
            let isSysdept_deal = this.form.nodeInfo.nodeCode == 'sysdept_deal'
            if (this.isEmpty(this.form.isConfirm) && this.isEmpty(this.form.onfirmReason) && isSysdept_deal) {
                this.$message.error("未填写客户确认信息区域 ！");
            }else if(this.isEmpty(this.form.isConfirm) && isSysdept_deal){
                this.$message.error("请选择'客户是否确认'选项 ！");
                return false;
            }else if( this.form.isConfirm == 0 && this.isEmpty(this.form.onfirmReason) && isSysdept_deal){
                this.$message.error("请填写'未确认原因'项 ！");
                return false;
            }else if( this.form.isConfirm == 1 && !this.isEmpty(this.form.onfirmReason) && isSysdept_deal){
                this.form.onfirmReason = ''
                return true;
            }
            else{
                return true;
            }
            // 写个判断，当this.form.isConfirm为 1 是不用填this.forxin m.onfirmReason，为0是必填this.form.onfirmReason，都为空时需要填写

            // return true;
        },

        async initPolicyForm(refId, nodeCode,processId) {
            if (!refId) {
                return;
            }
            await request(GET_XSGSPROCESSPOLICY_ID, METHOD.GET, { "id": refId, "nodeCode": nodeCode,"processId":processId }).then(res => {
                
                if (res.data.data) {
                    // console.log(res.data.data);
                    this.policyForm = res.data.data;
                    // console.log(this.policyForm);

                }
            })
        },

    }
}
</script>

<style scoped>
/deep/ .fontSmall td,
.fontSmall th,
.fontSmall input,
.fontSmall textarea,
.fontSmall select,
.fontSmall a,
.fontSmall button {
    font-size: 12px !important;
}

/deep/ .fontSmall tr,
.fontSmall td,
.fontSmall th {
    height: 22px !important;
}

/deep/ .fontMiddle td,
.fontMiddle th,
.fontMiddle input,
.fontMiddle textarea,
.fontMiddle select,
.fontMiddle a,
.fontMiddle button {
    font-size: 14px !important;
}

/deep/ .fontMiddle tr,
.fontMiddle td,
.fontMiddle th {
    height: 28px !important;
}

/deep/ .fontLarge td,
.fontLarge th,
.fontLarge input,
.fontLarge textarea,
.fontLarge select,
.fontLarge a,
.fontLarge button {
    font-size: 18px !important;
}

/deep/ .fontLarge tr,
.fontLarge td,
.fontLarge th {
    height: 33px !important;
}

/deep/ .fontLarge tr,
.fontLarge td,
.fontLarge th {
    height: 35px !important;
}

/deep/.apploveInfo>tr>td>div {
    float: left;
}

/deep/ .ant-select-selection__placeholder {
    /*padding-left: 3px;*/
}

/deep/ .ant-select-selection-selected-value {
    /*padding-left: 3px;*/
}

/deep/ .ant-select {
    border-radius: 3px;
}

/deep/ .model-table>tr>th,
td {
    border: 0.5px solid #464646
}

/deep/ .model-table>tr>th {
    background-color: #bdd7ee;
    text-align: right;
    width: 13%;
}

/deep/ .model-table>tr>td>input {
    /*border-radius: 0px;*/
    font-size: 14px;
}

/deep/ .ant-select-selection__rendered {
    padding-left: 3px !important;
}

/deep/ .ant-select-selection__placeholder {
    padding-left: 3px !important;
}

/deep/ .ant-collapse>.ant-collapse-item>.ant-collapse-header {
    background: #bdd7ee;
}

.history-table-th {
    background: #bdd7ee;
    text-align: center;
    padding: 5px;
}
.collapseState{
    display: none;
}
.iconRoate{
    transform: rotate(90deg);
}
</style>
