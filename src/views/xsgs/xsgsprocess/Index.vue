<template>
    <div style="position: relative; overflow-y: auto;padding-top: 0;">
        <a-spin :spinning="loading">

            <a-card>
                <div class="header-button-area" :style="`width:${headerWidth};`">
                    <a-button v-if="processForm.authType == 1" type="primary" @click="handleSave">
                        保存
                    </a-button>
                    <a-button v-if="processForm.authType == 1" type="primary" @click="handleSaveAndClose()">
                        保存关闭
                    </a-button>
                    <a-button v-if="processForm.authType == 1 || processForm.authType == 2" type="primary"
                        @click="handleSubmit">
                        提交
                    </a-button>
                    <a-button v-if="!isDrafted() && !isFinish() && processForm.createUser == userInfo.code"
                        type="primary" @click="handleUrge">
                        催办
                    </a-button>
                    <a-button v-if="processForm.id" type="primary" @click="handleNotice">
                        阅读通知
                    </a-button>
                    <a-button type="primary" @click="handlePrint">
                        打印
                    </a-button>
                    <a-button v-if="isCanWithdraw()" type="primary" @click="handleWithdraw">
                        撤回
                    </a-button>
                    <a-button type="primary" @click="handleClose()">
                        关闭
                    </a-button>
                </div>
                <a-form ref="processForm" style="margin-top: 30px" :model="processForm" :labelCol="{ span: 8 }"
                    :wrapperCol="{ span: 16 }" :loading="loading">
                    <table style="width: 100%">
                        <tbody class="ant-table">
                            <tr>
                                <th>作者</th>
                                <td class="textTd">
                                    {{ processForm.author }}
                                </td>
                                <th>创建时间</th>
                                <td style="text-align: center">
                                    {{ processForm.createTime ? dateFormat(processForm.createTime) : '' }}
                                </td>
                            </tr>
                            <tr>
                                <th>
                                    流程(<a target="_blank" :href="`${processimageUrl}`">流程图</a>)
                                </th>
                                <td :colspan="process != 'quoteLetter' ? 1:8 " class="textTd">
                                    {{ processForm.processName }}
                                </td>
                                <th v-if="process != 'quoteLetter'">
                                    流程编号
                                </th>
                                <td colspan="1" class="textTd" v-if="process != 'quoteLetter'">
                                    {{ processForm.processNumber }}
                                    <!-- <span v-if="!processForm.processNumber && isDrafted()">
                                        保存后自动生成
                                    </span> -->
                                    <a-button v-if="processForm.process == 'quote'" @click="copyInfo()"
                                        style="float: right;margin-right: 15px;" type="primary" shape="circle"
                                        size="small" icon="copy">
                                    </a-button>
                                </td>
                            </tr>
                            <tr>
                                <th>
                                    <span v-if="isDrafted()" style="color: red">*</span>
                                    主题
                                </th>
                                <td colspan="3" prop="title">
                                    <a-input v-model="processForm.title" :maxLength="250" v-if="isDrafted()"
                                        :style="`border:${formRule.title.border}`" @blur="checkField('title')"
                                        placeholder="请输入主题" />
                                    <span v-if="!isDrafted()">{{ processForm.title }}</span>
                                </td>
                            </tr>
                            <tr>
                                <th>当前位置</th>
                                <td class="textTd">
                                    {{ processForm.nodeInfo.nodeDisplayName }}
                                </td>
                                <th>处理人</th>
                                <td class="textTd">
                                    {{ processForm.nodeInfo.userDisplayName }}
                                </td>
                            </tr>
                            <tr v-if="process == 'quoteLetter'">
                                <th>客户号</th>
                                <td class="textTd">
                                    <a-input v-if="isDrafted()" v-model="processForm.customer" @blur="customerBlurs" />
                                    <span v-if="!isDrafted()">{{ processForm.customer }}</span>
                                </td>
                                <th>客户名称</th>
                                <td class="textTd">
                                    <a-select v-model="processForm.customerName" show-search v-if="isDrafted()"
                                        :default-active-first-option="false" :show-arrow="false" :allowClear="true"
                                        :filter-option="false" :not-found-content="null" @search="handleCustomerSearch"
                                        @change="handleCustomerChange">
                                        <a-select-option v-for="d in customerList" :key="d.name">
                                            {{ d.name }}
                                        </a-select-option>
                                    </a-select>
                                    <span v-if="!isDrafted()">{{ processForm.customerName }}</span>
                                </td>
                            </tr>
                            <!-- <tr> -->
                                <!-- <td colspan="4"> -->
                                    <!-- <div id="wangEditorElem" style="width: 100%; word-wrap: break-word; white-space: normal;">
                                        <div ref="toolbar" class="toolbar" />
                                        <div ref="editor" :style="`min-height:700px;height:auto;width: 100%; word-wrap: break-word; white-space: normal;`"
                                            class="text" />
                                    </div> -->
                                <!-- </td> -->
                            <!-- </tr> -->

                        </tbody>
                        
                    </table>
                    <div id="wangEditorElem" >
                        <div ref="toolbar" class="toolbar" />
                        <div ref="editor" :style="`min-height:700px;height:auto;width: 100%; word-wrap: break-word; white-space: normal;`"
                                            class="text" />
                    </div>
                    <bussPolicy v-if="process == allProcess.bussPolicy.code" :form="processForm" :userInfo="userInfo"
                        ref="bussPolicy" />
                    <quote v-if="process == allProcess.quote.code" :form="processForm" :userInfo="userInfo"
                        ref="quote" />
                    <intCont v-if="process == allProcess.intCont.code" :form="processForm" :userInfo="userInfo"
                        @intConHistoryList="intConHistoryList" ref="intCont" />
                    <quoteLetter v-if="process == allProcess.quoteLetter.code" :form="processForm" :userInfo="userInfo"
                        ref="quoteLetter" />
                    <appInfo :form="processForm" :userInfo="userInfo" ref="appInfo" />
                    <appLog :processId="processForm.id" ref="appLog" />
                </a-form>
            </a-card>
        </a-spin>
        <a-modal v-drag-modal width="60%" title="流程审批" :visible="isApproveForm" class="approvalForm"
            :confirm-loading="confirmLoading" @cancel="closeApproveForm">
            <template slot="footer">
                <a-button @click="closeApproveForm">取消</a-button>
            </template>
            <a-spin :spinning="confirmLoading">
                <a-form-model ref="approvalInfo" :model="approvalInfo" :rules="approvalRules" :labelCol="{ span: 2 }"
                    :wrapperCol="{ span: 22 }">
                    <h2 style="border-bottom:1px solid black; margin: 0;padding-bottom: 0.5em">审批意见</h2>
                    <a-row>
                        <a-col :span="24">
                            <a-form-model-item label="简单意见" prop="approvalContent">
                                <a-select style="max-width: 100px" :allowClear='true'
                                    v-model="approvalInfo.approvalContent">
                                    <a-select-option value="同意">同意</a-select-option>
                                    <a-select-option value="不同意">不同意</a-select-option>
                                </a-select>
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="24">
                            <a-form-model-item label="详细意见" prop="approvalDetail">
                                <!--                            <a-textarea-->
                                <!--                                    v-model="approvalInfo.approvalDetail"-->
                                <!--                                    :auto-size="{ minRows: 5, maxRows: 20 }"-->
                                <!--                            />-->
                                <textarea rows="10" class="xsgs-textarea" v-model="approvalInfo.approvalDetail" />
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                    <!--   -->
                    <!-- {{  ( ) }} -->
                    <h2 v-if="this.isPreviousNodeState"
                        style="border-bottom:1px solid black; margin: 0 0 15px 0;padding-bottom: 0.5em">选择流向</h2>
                    <a-row v-if="this.isPreviousNodeState">
                        <a-col :span="24">
                            <a-button v-for="b in approvalInfo.nextNodeList" :key="b.nodeCode" type="primary"
                                style="margin-right: 8px" @click="handleApprovalSubmit(b)">
                                {{ b.nodeDisplayName }}
                            </a-button>
                        </a-col>
                    </a-row>
                    <h2 style="border-bottom:1px solid black; margin: 15px 0 15px 0;padding-bottom: 0.5em">流程操作</h2>
                    <a-row>
                        <a-col :span="24">
                            <a-button v-if="!isDrafted() && processForm.bpmInstanceId != null" type="primary"
                                style="margin-right: 8px" @click="handleBack()">
                                退回
                            </a-button>
                            <a-button v-if="processForm.id != null" type="primary" style="margin-right: 8px"
                                @click="handleTerminate()">
                                终止流程
                            </a-button>
                        </a-col>
                    </a-row>
                </a-form-model>
            </a-spin>
        </a-modal>
        <a-modal v-drag-modal width="50%" title="退回选择" :visible="isBackForm" :confirm-loading="confirmLoading"
            @ok="handleBackSelectOk" @cancel="handleBackCancel">
            <a-spin :spinning="confirmLoading">
                <a-form-model>
                    <table style="width: 100%">
                        <tbody class="ant-table model-table">
                            <tr>
                                <th style="text-align: right">当前节点：</th>
                                <td style="padding-left: 5px !important;">
                                    {{ processForm.nodeInfo.nodeDisplayName }}
                                </td>
                            </tr>
                            <tr>
                                <th style="text-align: right;width: 20%;background-color: #bdd7ee">退回至：</th>
                                <td style="width: 80%;padding: 5px !important;">
                                    <a-radio-group>
                                        <a-radio style="width: 100%" v-for="d in approvalInfo.preNodeList"
                                            :key="d.nodeCode" :value="d.nodeCode" @click="selectBankNode(d)">{{
            d.nodeDisplayName }}</a-radio>
                                    </a-radio-group>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </a-form-model>
            </a-spin>
        </a-modal>
        <mailEdit ref="mailEdit" @send="handleSend" />
    </div>
</template>

<script>
import E from "wangeditor";
import {
    GET_XSGSPROCESS_ID,
    SUBMIT_XSGSPROCESS,
    GET_NODE_INFO_CODE,
    APPROVAL_PROCESS,
    BACK_PROCESS,
    TERMINATE_PROCESS,
    LIST_XSGSCUSTOMER,
    WITHDRAW_PROCESS,
    PROCESS_URGE,
    PROCESS_NOTICE,
} from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import { dateFormat } from "@/utils/dateUtil";
import Cookie from 'js-cookie'
import appInfo from "./applove_info";
import appLog from "./applove_log";
import bussPolicy from "../xsgsprocesspolicy/buss_policy";
import quote from "../xsgsprocessquote/quoteList";
import intCont from "../xsgsProcessIntCont/intContList";
import quoteLetter from "../xsgsprocessquoteletter/quoteLetterList";
import $ from 'jquery';
import mailEdit from "../../../components/notice/mailEdit";
export default {
    name: "XsgsProcess.vue",
    components: {
        appInfo,
        appLog,
        bussPolicy,
        quote,
        intCont,
        quoteLetter,
        mailEdit
    },
    data() {
        return {
            Cookie,
            loading: false,
            confirmLoading: false,
            process: null,
            dialog: false,
            isApprove: false,
            isApproveForm: false,
            isBackForm: false,
            isFullFlag: false,
            editorHeight: "700px",
            userInfo: this.$store.state.account.user,
            processId: this.$route.params.id,
            fullPath: this.$route.path,
            headerWidth: null,
            processimageUrl: null,
            pageTitle: null,
            customerList: [],
            allCustomerList: [],
            requiredBorder: "1px solid red !important",
            normalBorder: "1px solid #d9d9d9 !important",
            allProcess: {
                bussPolicy: {
                    code: "bussPolicy", bpmCode: "YcBpmXsgsBussPolicyProcess", name: "商务政策审批流程", node: {
                        drafted: "办事处/系统部部门申请"
                    }
                },
                quote: {
                    code: "quote", bpmCode: "YcBpmXsgsQuoteProcess", name: "商务报价审批流程", node: {
                        drafted: "起草"
                    }
                },
                intCont: {
                    code: "intCont", bpmCode: "YcBpmXsgsIntContProcess", name: "商务合同内部审批流程", node: {
                        drafted: "起草"
                    }
                },
                quoteLetter: {
                    code: "quoteLetter", bpmCode: "YcBpmXsgsQuoteLetterProcess", name: "商务报价函审批流程", node: {
                        drafted: "起草"
                    }
                }
            },
            annexList: [],
            processForm: {
                id: null,
                title: null,
                author: null,
                authorName: null,
                authType: null,
                processName: null,
                customer: null,
                customerName: null,
                process: null,
                position: null,
                option: null, // 0-保存 1-提交 2-保存关闭 3-退回 4-撤回 5-终止
                state: null,
                handler: null,
                content: null,
                flowNodeList: [],
                linkList: [],
                annexList: [],
                nodeInfo: {
                    processId: null,
                    nodeCode: null,
                    nodeName: null,
                    nodeDisplayName: null,
                    preNode: null,
                    nextNode: null,
                    isCurrent: null,
                    isGroup: null,
                    isMultiple: null,
                    isRequired: null,
                },
                policyInfo: {},
                approvalInfo: {},
                processNumber: null,
            },
            approvalInfo: {
                approvalState: null,
                approvalContent: null,
                approvalDetail: null,
                approvalOption: null,
                nodeCode: null,
                nodeName: null,
                isOpinionReq: null,
                isContentReq: null,
                nodeDisplayName: null,
                nextNodeList: [],
                preNodeList: [],

            },
            approvalRules: {},
            formRule: {
                title: { border: "1px solid #d9d9d9 !important", msg: "请输入主题" },
            },

            editor: null,
            info_: null,
            chooseUserForm: {
                nodeCode: null,
                userList: [],
                chooseList: [],
            },
            temporary: {
                approvalContent: '',
                approvalDetail: '',
            },
            isPreviousNodeState: true,
        }
    },
    created() {
        let path = this.$route.path.split("/");
        this.process = path[3];
    },
    watch: {
        'processForm.nodeInfo.nodeCode': function (newVal) {
            // this.$refs["bussPolicy"].setNodeCode(newVal);
            if (this.$refs["bussPolicy"]) {
                this.$refs["bussPolicy"].setNodeCode(newVal);
            }
        }
    },
    async mounted() {
        this.checkName();
        this.seteditor();
        this.setImageUrl();
        this.getCustomerList();
        let that = this;
        // 监听页面全屏
        // window.addEventListener("fullscreenchange", (e)=> {
        //     that.isFullFlag = !screenfull.isFullscreen;
        //    that.handleFullscreen();
        // })

        this.$nextTick(function () {
            setTimeout(() => {
                that.resizeHeader();
            }, 300)
        })
        window.addEventListener("resize", this.resizeHeader);
        // 清除从富文本框复制内容时多出来的换行符
        this.$nextTick(() => {
            const editorTextEl = document.querySelector('.w-e-text')
            editorTextEl.addEventListener('copy', (e) => {
                console.log(e);
                let clipboardData = e.clipboardData || window.clipboardData
                if (clipboardData) {
                    let html = window.getSelection().toString();
                    if (html) {
                        e.preventDefault();
                        html = html.replaceAll('\n\n', '\n');
                        clipboardData.setData('text/plain', html);
                    }
                }
            })
        });
        this.pageTitle = this.allProcess[this.process].name;
        if (this.processId === ":id" || !this.processId) {
            that.$setPageTitle(this.fullPath, "新建" + this.pageTitle);
            this.processForm.authType = 1;
            this.editor.enable();
            // 新建流程初始化内容
            //  作者
            this.processForm.author = this.userInfo.fullOrgInfo;
            this.processForm.authorName = this.userInfo.name;
            // 流程名称
            this.processForm.processName = this.getProcessName(this.process);
            this.processForm.process = this.process;
            // 操作：新增
            this.processForm.option = 0;
            // 节点信息
            let res = await this.getNodeInfo('drafted');
            
            
            if (!res.data.data) {
                this.$message.error("起草节点配置信息不存在，请联系管理员");
                return
            }

            this.processForm.nodeInfo = res.data.data;
            this.processForm.nodeInfo.userAccount = this.userInfo.code;
            this.processForm.nodeInfo.userName = this.userInfo.name;
            this.processForm.nodeInfo.userDisplayName = this.userInfo.fullOrgInfo;


            if (this.processForm.process == that.allProcess.bussPolicy.code) {
                that.$refs.bussPolicy.initData(null, this.processForm.nodeInfo.nodeCode,this.processForm.id);
            }
            if (this.processForm.process == that.allProcess.quote.code) {
                that.$refs.quote.initData();
            }
            if (this.processForm.process == that.allProcess.intCont.code) {
                that.$refs.intCont.initData();
            }
            if (this.processForm.process == that.allProcess.quoteLetter.code) {
                that.$refs.quoteLetter.initData();
            }

            this.$refs.appInfo.initFlowNodeList(this.processForm.process, this.processForm.id);
        } else {
            that.$setPageTitle(this.fullPath, "编辑" + this.pageTitle);
            this.processForm.id = this.processId;
            this.processForm.process = this.process;
            this.getData();
        }
    },
    beforeRouteEnter(to, from, next) {
        next(vm => {
            let isNewQuoteLetter = to.path.includes('/xsgs_process_manager/xsgs_quote_letter_applove/quoteLetter');
            let isQuoteApplove = from.path.includes('/xsgs_process_manager/xsgs_quote_applove/quote/');
            // console.log(isQuoteApplove,isNewQuoteLetter);
            if (isNewQuoteLetter && isQuoteApplove) {
                // 打印路由的数据
                // vm.processForm.pageLinkList = to.query.linkList;
                // vm.processForm.pageLinkList.map((item)=>{
                //     item.processNumber = to.query.processNumber;
                // })
                // vm.$refs.appInfo.getApprovalLinklist(to.query.linkList)
                // this.$router.push({path:url, props: true})
                let url = "#/xsgs_process_manager/xsgs_quote_applove/" + to.query.process + "/" + to.query.processId;
                vm.processForm.pageLinkList = []
                vm.processForm.pageLinkList.push({
                    link: url,
                    processNumber: to.query.processNumber
                })
                console.log(vm.processForm.pageLinkList);
                vm.$refs.appInfo.getApprovalLinklist(vm.processForm.pageLinkList)
            }
        })
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.resizeHeader);
    },

    methods: {
        resizeHeader() {
            this.headerWidth = this.$refs.processForm.$el.clientWidth + 1 + 'px';
        },
        async getNodeInfo(nodeCode) {
            return request(GET_NODE_INFO_CODE, METHOD.GET, {
                "processId": this.processForm.id,
                "templateCode": this.processForm.process,
                "currNode": nodeCode
            });
        },
        // async getProNumber(){
        //     return request(GET_PROCESS_PROCESSNUMBER, METHOD.GET, {
        //         templateCode: 'bussPolicy'
        //     }).then((res)=>{
        //         if(res.data.code == 0){
        //             return res.data.data
        //         }
        //     }).catch(()=>{
        //         this.$message.error('流程编号获取失败')
        //         return
        //     })
        // },
        setImageUrl() {
            if (this.allProcess[this.process]) {
                let processCode = this.allProcess[this.process].bpmCode
                this.processimageUrl = process.env.VUE_APP_API_BPM + '?processCode=' + processCode;
            }
        },
        getData: function () {
            let that = this;
            this.loading = true;
            request(GET_XSGSPROCESS_ID, METHOD.GET, {
                "id": this.processForm.id,
                "process": this.processForm.process
            }).then(res => {
                if (that.processForm.option == 0) {
                    that.$setPageTitle(this.fullPath, "编辑" + this.pageTitle);
                }
                that.processForm = res.data.data;
                console.log(res.data.data);
                
                // 操作状态重置为保存
                that.processForm.option = 0;
                that.info_ = that.processForm.content;


                if (that.isDrafted()) {
                    that.editor.txt.html(that.info_);
                    this.editor.enable();
                } else {
                    if (this.isEmpty(that.info_)) {
                        that.editor.txt.html('<span></span>');
                    } else {
                        that.editor.txt.html(that.info_);
                    }
                }
                if (this.processForm.process == that.allProcess.bussPolicy.code) {
                    that.$refs.bussPolicy.initData(this.processForm.refId, this.processForm.nodeInfo.nodeCode, this.processForm.id);
                }
                if (this.processForm.process == that.allProcess.quote.code) {
                    that.$refs.quote.initData(this.processForm.id);
                }
                if (this.processForm.process == that.allProcess.intCont.code) {
                    that.$refs.intCont.initData(this.processForm.id);
                }
                if (this.processForm.process == that.allProcess.quoteLetter.code) {
                    that.$refs.quoteLetter.initData(this.processForm.id);
                }
                that.$refs.appInfo.initFlowNodeList(this.processForm.process, this.processForm.id);
                that.$refs.appInfo.initContractData(this.processForm.id, this.processForm.process);
                that.$refs.appInfo.setLinkList(this.processForm.linkList);
                that.$refs.appInfo.setPageLinkList(this.processForm.pageLinkList);
                that.$refs.appInfo.setAnnexList(this.processForm.annexList);
                that.$refs.appInfo.initPolicyForm(this.processForm.refId,this.processForm.nodeInfo.nodeCode,this.processForm.id);
                that.$refs.appLog.initLogList(this.processForm.id);
                that.loading = false;
                //如果是报价流程则赋值
                if (this.processForm.process === "quote") {
                    that.$refs.quote.customer = this.processForm.customer
                    that.$refs.quote.customerName = this.processForm.customerName
                }
            });
        },
        getCustomerList() {
            if (this.process == 'quoteLetter') {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
                    this.allCustomerList = res.data.data
                    this.customerList = res.data.data
                })
            }
        },
        customerBlurs() {
            let customer = this.processForm.customer;
            if (!customer) {
                this.processForm.customerName = null;
            }
            let newData = this.customerList.filter(item => item.customer == customer);
            if (newData.length > 0) {
                this.processForm.customerName = newData[0].name;
            } else {
                newData = this.allCustomerList.filter(item => item.customer == customer);
                if (newData.length > 0) {
                    this.processForm.customerName = newData[0].name;
                }
            }
        },

        handleCustomerSearch(value) {
            if (value) {
                this.customerList = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
            } else {
                this.customerList = this.allCustomerList;
            }

        },
        handleCustomerChange(value) {
            if (value) {
                this.customerList.forEach(item => {
                    if (item.name == value) {
                        this.processForm.customer = item.customer
                        this.processForm.customerName = item.name
                    }
                })
                console.log('客户名称发生了改变');
            } else {
                this.processForm.customer = null
            }

        },
        handlePrint() {
            let id = this.processId;
            if (!id) {
                id = ":id";
            }
            this.$router.push({ path: "/xsgs_process_manager/processPrint/" + id, query: { process: this.process } });
        },
        handleUrge() {
            let that = this;
            that.$confirm({
                content: `是否确定催办？`,
                onCancel: null,
                onOk: () => {
                    that.loading = true;
                    request(PROCESS_URGE, METHOD.POST, { id: this.processForm.id }).then(res => {
                        that.loading = false;
                        that.$message.info("催办成功");
                    })
                },
            });
        },
        handleNotice() {
            // 获取初始范围
            let userList = [];
            let nodeList = this.$refs.appInfo.getFlowNodeList();
            nodeList.forEach(s => {
                let nodeUserList = s.nodeUserList;
                if (nodeUserList && nodeUserList.length) {
                    nodeUserList.forEach(item => {
                        if (userList.findIndex(u => u.employeeCode == item.userAccount) == -1) {
                            userList.push({
                                employeeCode: item.userAccount,
                                employeeName: item.userName,
                                orgFullName: item.userDisplayName
                            })
                        }
                    });
                }
            });
            this.$refs.mailEdit.show(this.processForm.id, this.processForm.nodeInfo.nodeCode, "PNC", userList);
        },
        handleClose() {
            this.$closePage(this.fullPath);
        },
        handleSave() {
            if (!this.checkForm()) {
                return false;
            }
            // this.processForm.policyInfo = this.$refs.bussPolicy.getPolicyInfo();
            this.processForm.option = 0;
            let that = this;
            that.$confirm({
                content: `是否保存当前数据？`,
                onCancel: null,
                onOk: () => {
                    that.handleSaveAction()
                },
            });

        },
        getProcessInfo() {
            // 校验审批信息
            this.processForm.flowNodeList = this.$refs.appInfo.getFlowNodeList();
            if (this.isDrafted()) {
                // 校验商务政策信息
                if (this.processForm.process == this.allProcess.bussPolicy.code) {
                    this.processForm.policyInfo = this.$refs.bussPolicy.getPolicyInfo();
                }
                if (this.processForm.process == this.allProcess.quote.code) {
                    this.processForm.quoteInfo = this.$refs.quote.getQuoteInfo();
                }
                if (this.processForm.process == this.allProcess.intCont.code) {
                    this.processForm.intContInfo = this.$refs.intCont.getIntContInfo();
                }
                if (this.processForm.process == this.allProcess.quoteLetter.code) {
                    this.processForm.quoteLetterInfo = this.$refs.quoteLetter.getQuoteLetterInfo();
                }
                this.processForm.content = this.info_;
                this.processForm.linkList = this.$refs.appInfo.getLinkList();
                this.processForm.pageLinkList = this.$refs.appInfo.getPageLinkList();
                this.processForm.annexList = this.$refs.appInfo.getAnnexList();
                // this.processForm.businessYear = this.$refs.bussPolicy.getBusinessYear();
            } else {
                if (this.processForm.process == this.allProcess.bussPolicy.code && this.processForm.nodeInfo.nodeCode == 'value_engine_approval') {

                    this.processForm.policyInfo = this.$refs.bussPolicy.getPolicyInfo();
                    console.log(this.processForm);
                }
            }
        },
        handleSaveAndClose() {
            // 保存关闭
            this.processForm.option = 2;
            this.handleSave();
        },
        handleSaveAction() {
            // this.loading = true;
            this.getProcessInfo()
            this.processForm.content = this.info_;
            // 判断this.processForm.policyInfo是否存在，不为null或者undefined
            if (this.processForm.policyInfo && this.processForm.policyInfo != null && this.processForm.policyInfo != undefined) {
                this.processForm.businessYear = this.processForm.policyInfo.businessYear
            }
            // console.log(this.processForm);
            request(SUBMIT_XSGSPROCESS, METHOD.POST, this.processForm).then(res => {
                this.loading = false;
                if (res.data.code == -1) {
                    this.$message.error(res.data.msg);
                    return;
                }
                // 提交
                if (this.processForm.option == 1) {
                    this.$message.info("提交成功");
                } else {
                    this.$message.info("保存成功");
                }
                // 保存关闭
                if (this.processForm.option == 1 || this.processForm.option == 2) {
                    this.handleClose();
                } else {
                    this.processForm.id = res.data.data.id;
                    this.processId = res.data.data.id;
                    this.getData();
                }
            })
        },
        handleSubmit() {
            if (!this.checkForm()) {
                return false;
            }
            // console.log(this.processForm,this.approvalInfo);
            // 获取审批信息
            // this.approvalInfo = {};
            this.approvalInfo.isOpinionReq = this.processForm.nodeInfo.isOpinionReq;
            this.approvalInfo.isContentReq = this.processForm.nodeInfo.isContentReq;
            let nextNode = this.processForm.nodeInfo.nextNode;
            let preNode = this.processForm.nodeInfo.preNode;
            let nodeCode = this.processForm.nodeInfo.nodeCode;
            let flowNodeList = this.$refs.appInfo.getFlowNodeList()
            
            if (!this.isEmpty(nextNode)) {
                let nextNodeList = [];
                if (nextNode.indexOf('complete') != -1) {
                    let node = {};
                    node['nodeCode'] = "complete";
                    node['nodeName'] = '完成';
                    node['nodeDisplayName'] = '完成';
                    nextNodeList.push(node)
                }
                let nexts = nextNode.split(";");
                let that = this;
                nexts.forEach(item => {
                    let list = flowNodeList.filter(node => node.nodeCode == item && node.isJump != 1 && !that.isEmpty(node.userList));
                    if (list.length > 0) {
                        nextNodeList.push(list[0]);
                    }
                });
                if( nextNodeList && nextNodeList.length>0 ){
                    nextNodeList = [nextNodeList[0]]; // 只保留第一个元素
                }
                // console.log(nextNode,nexts,nextNodeList);
                this.approvalInfo.nextNodeList = nextNodeList;
            }
            // 流程退回 原逻辑
            // if (!this.isEmpty(preNode)) {
            //     let preNodeList = [];
            //     if (preNode.indexOf('drafted') != -1) {
            //         let nodeName = this.allProcess[this.processForm.process].node['drafted'];
            //         let node = {};
            //         node['nodeCode'] = "drafted";
            //         node['nodeName'] = nodeName;
            //         node['nodeDisplayName'] = nodeName;
            //         preNodeList.push(node);
            //     }
            //     let pres = preNode.split(";");
            //     pres.forEach(item => {
            //         let list = flowNodeList.filter(node => node.nodeCode == item && node.isJump != 1);
            //         if (list.length > 0) {
            //             preNodeList.push(list[0]);
            //         }
            //     });
            //     this.approvalInfo.preNodeList = preNodeList;
            // }
            
            // 流程退回新逻辑：当前环节可退回前面任何环节
            if (!this.isEmpty(nodeCode)) {
                flowNodeList.find((item, index) => {
                    if (item.nodeCode == nodeCode) {
                        this.approvalInfo.preNodeList = flowNodeList.slice(0, index).filter((item2) => item2.isJump != 1)
                    }
                })
            }
            let msg = this.checkConfirm();
            if (msg) {
                this.$confirm({
                    content: `${msg}`,
                    onOk: () => {
                        this.isApproveForm = true;
                    }
                });
            } else {
                this.isApproveForm = true;
            }
            this.isPreviousNode()

        },

        handleApprovalSubmit(b) {
            // 校验合同审批信息
            if (this.processForm.process == this.allProcess.intCont.code) {
                let checkIntCont = this.$refs.intCont.checkIntContForm();
                if (!checkIntCont) {
                    return false;
                }
            }
            // 意见必填节点，检查是否填写意见
            if (this.approvalInfo.isOpinionReq == 1) {
                if (!this.approvalInfo.approvalContent) {
                    this.$message.error("请选择简单意见");
                    return false;
                }
            }
            if (this.approvalInfo.isContentReq == 1) {
                if (!this.approvalInfo.approvalDetail) {
                    this.$message.error("请填写详细意见");
                    return false;
                }
            }

            // 报价审批校验是否发布
            if (this.processForm.process == this.allProcess.quote.code) {
                let checkQuote = this.$refs.quote.checkRelease();
                if (!checkQuote) {
                    return false;
                }
            }

            // 合同内部审批校验是否关联了流水号
            if (this.processForm.process == this.allProcess.intCont.code) {
                let checkIntCont = this.$refs.intCont.checkNum();
                if (!checkIntCont) {
                    return false;
                }
            }

            // 是否是归档的上一个环节
            // if (this.processForm.process == 'intCont' && !this.isDrafted() && this.isPreviousNode()) {
            //     // 获取 this.processForm.flowNodeList数组的最后一项的前一项（归档的前一项）
            //     let preNode = this.processForm.flowNodeList[this.processForm.flowNodeList.length - 2];
            //     if (preNode.nodeCode == this.processForm.nodeInfo.nodeCode) {
            //         // 校验isConfirm onfirmReason是否填写
            //         let isConfirm = !(this.processForm.isConfirm == null || this.processForm.isConfirm == undefined)
            //         let onfirmReason = !(this.processForm.onfirmReason == null || this.processForm.onfirmReason == undefined)
            //         // console.log(isConfirm, onfirmReason, this.processForm.onfirmReason);
            //         if (!isConfirm && !onfirmReason) {
            //             this.$message.warning("请填写对应的客户确认信息");
            //             return false;
            //         }
            //         // 客户是否确认isConfirm 为 否（0）时，未确认信息onfirmReason 不能为空
            //         else if (isConfirm && this.processForm.isConfirm == 0 && this.processForm.onfirmReason == '') {
            //             this.$message.warning("请填写对应的客户确认信息");
            //             return false;
            //         }
            //     }
            // }
            // 在当前“商务审批合同” 流程环节为“客户反馈”时才进行校验
            if (this.processForm.process == 'intCont' && this.processForm.nodeInfo && this.processForm.nodeInfo.nodeCode == 'sysdept_deal' ) {
                let isConfirm = !(this.processForm.isConfirm == null || this.processForm.isConfirm == undefined)
                let onfirmReason = !(this.processForm.onfirmReason == null || this.processForm.onfirmReason == undefined)
                // console.log(isConfirm, onfirmReason, this.processForm.onfirmReason);
                if (!isConfirm && !onfirmReason) {
                    this.$message.warning("请填写对应的客户确认信息");
                    return false;
                }
                // 客户是否确认isConfirm 为 否（0）时，未确认信息onfirmReason 不能为空
                else if (isConfirm && this.processForm.isConfirm == 0 && this.processForm.onfirmReason == '') {
                    this.$message.warning("请填写对应的客户确认信息");
                    return false;
                }
            }

            let that = this;
            this.$confirm({
                content: `是否提交当前数据？`,
                onOk: () => {
                    that.confirmLoading = true;
                    that.loading = true;
                    that.approvalInfo.nodeCode = b.nodeCode;
                    that.approvalInfo.nodeName = b.nodeDisplayName;
                    that.approvalInfo.approvalOption = 'SUBMIT';
                    // 获取页面信息
                    that.getProcessInfo();
                    this.processForm.option = 1;
                    that.processForm.approvalInfo = { ...that.approvalInfo };
                    // console.log(that.processForm);
                    // that.processForm.businessYear = that.processForm.policyInfo.businessYear
                    request(APPROVAL_PROCESS, METHOD.POST, that.processForm).then(res => {
                        that.confirmLoading = false;
                        that.loading = false;
                        if (res.data.code == -1) {
                            that.$message.error(res.data.msg);
                            return;
                        }
                        let msg = "";
                        let tip = "后续处理人是：";
                        let userList = res.data.data.nodeInfo.userList;
                        if (userList.length > 0) {
                            if (userList.length == 1) {
                                msg = userList[0].userDisplayName;
                            } else {
                                userList.forEach(item => msg += item.userDisplayName + '\n')
                            }
                        }
                        if (msg.length == 0) {
                            msg = "";
                            tip = "";
                        }
                        this.$success({
                            title: `提交成功`,
                            content: (
                                <div style="white-space: pre-wrap;">
                                    <p>{tip}</p>
                                    <p>{msg}</p>

                                </div>
                            ),
                            onOk: () => {
                                that.closeApproveForm();
                                that.handleClose();
                            },
                        })
                    });
                },
            });

        },

        handleBackCancel() {
            this.isBackForm = false;
        },
        async handleBackSelectOk() {
            let that = this;
            let nodeName = this.approvalInfo.nodeName;
            this.$confirm({
                content: `确定退回到【${nodeName}】？`,
                onOk: () => {
                    that.confirmLoading = true;
                    that.approvalInfo.approvalOption = 'BACK';
                    that.processForm.approvalInfo = { ...that.approvalInfo };
                    this.processForm.option = 3;
                    that.beforBackSave()
                    request(BACK_PROCESS, METHOD.POST, that.processForm).then(res => {
                        that.confirmLoading = false;
                        if (res.data.code == -1) {
                            that.$message.error(res.data.msg);
                            return;
                        }
                        let msg = res.data.data.nodeInfo.userDisplayName;
                        this.$success({
                            title: `退回成功`,
                            content: (
                                <div style="white-space: pre-wrap;">
                                    <p>后续处理人是：</p>
                                    <p>{msg}</p>

                                </div>
                            ),
                            onOk: () => {
                                that.closeApproveForm();
                                that.handleBackCancel();
                                that.handleClose();
                            },
                        })
                    });
                },
            });
        },
        handleWithdraw() {
            let that = this;
            this.$confirm({
                content: `确定撤回流程？`,
                onOk: () => {
                    this.loading = true;
                    that.approvalInfo.nodeCode = null;
                    that.approvalInfo.nodeName = null;
                    that.approvalInfo.approvalOption = 'WITHDRAW';
                    that.processForm.approvalInfo = { ...that.approvalInfo };
                    this.processForm.option = 4;
                    request(WITHDRAW_PROCESS, METHOD.POST, that.processForm).then(res => {
                        that.loading = false;
                        if (res.data.code == -1) {
                            that.$message.error(res.data.msg);
                            return;
                        }
                        let msg = res.data.data.nodeInfo.userDisplayName;
                        this.$success({
                            title: `撤回成功`,
                            content: (
                                <div style="white-space: pre-wrap;">
                                    <p>后续处理人是：</p>
                                    <p>{msg}</p>

                                </div>
                            ),
                            onOk: () => {
                                that.closeApproveForm();
                                that.handleBackCancel();
                                that.handleClose();
                            },
                        })
                    });
                },
            });
        },
        handleTerminate() {
            let that = this;
            this.$confirm({
                content: `是否终止流程？`,
                onOk: () => {
                    that.confirmLoading = true;
                    that.approvalInfo.nodeCode = null;
                    that.approvalInfo.nodeName = null;
                    that.approvalInfo.approvalOption = 'TERMINATE';
                    that.processForm.approvalInfo = { ...that.approvalInfo };
                    this.processForm.option = 5;
                    request(TERMINATE_PROCESS, METHOD.POST, that.processForm).then(res => {
                        that.confirmLoading = false;
                        if (res.data.code == -1) {
                            that.$message.error(res.data.msg);
                            return;
                        }
                        this.$success({
                            title: `终止流程成功`,
                            content: null,
                            onOk: () => {
                                that.closeApproveForm();
                                that.handleClose();
                            },
                        })
                    });
                },
            });

        },
        selectBankNode(d) {
            this.approvalInfo.nodeCode = d.nodeCode;
            this.approvalInfo.nodeName = d.nodeDisplayName;
        },
        handleBack() {
            // 意见必填节点，检查是否填写意见
            if (this.approvalInfo.isOpinionReq == 1) {
                if (!this.approvalInfo.approvalContent) {
                    this.$message.error("请选择简单意见");
                    return false;
                }
            }
            if (this.approvalInfo.isContentReq == 1) {
                if (!this.approvalInfo.approvalDetail) {
                    this.$message.error("请填写详细意见");
                    return false;
                }
            }
            this.isBackForm = true;
        },
        seteditor() {
            this.editor = new E(this.$refs.toolbar, this.$refs.editor)
            this.editor.config.uploadImgShowBase64 = true // base 64 存储图片
            // this.editor.config.uploadImgServer = 'http://otp.cdinfotech.top/file/upload_images'// 配置服务器端地址
            this.editor.config.uploadImgHeaders = { 'Authorization': Cookie.get('Authorization') }// 自定义 header
            this.editor.config.uploadFileName = 'file' // 后端接受上传文件的参数名
            this.editor.config.uploadImgMaxSize = 20 * 1024 * 1024 // 将图片大小限制为 20M
            this.editor.config.uploadImgMaxLength = 6 // 限制一次最多上传 6 张图片
            this.editor.config.uploadImgTimeout = 3 * 60 * 1000 // 设置超时时间
            this.editor.config.lineHeights = ["1", "1.15", "1.2", "1.3", "1.6", "2", "2.5", "3"];
            this.editor.config.pasteFilterStyle = true
            this.editor.txt.clear()
            let that = this;
            // console.log(this.editor.config);
            this.editor.config.pasteTextHandle = (html) => {
                // console.log(html);
                // html = html.trim()
                
                // 获取当前行高
                const lineHeight = getCurrentLineHeight();

                // 移除 <br> 和 <br/> 标签
                let noBrHtml = html.replace(/<br\s*\/?>/gi, '');

                // 移除换行符
                let noLineBreaksHtml = noBrHtml.replace(/(\r\n|\n|\r)/gm, '');

                // 替换内容中的<p> 为 <p style="line-height: ${lineHeight};display:inline">
                noLineBreaksHtml = noLineBreaksHtml.replace(/<p>/g, `<p style="line-height: ${lineHeight};display:inline">`);
                // console.log(noLineBreaksHtml);
                // noLineBreaksHtml = removeOutermostTags(noLineBreaksHtml)
                // this.editor.cmd.do('insertHTML', noLineBreaksHtml )
                return noLineBreaksHtml;
            };
            // 获取当前选中文本所在的段落的行高
            function getCurrentLineHeight() {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    const commonAncestor = range.commonAncestorContainer;
                    let currentNode = commonAncestor;

                    // 查找最近的 <p> 父节点
                    while (currentNode && currentNode.tagName !== 'P') {
                        currentNode = currentNode.parentNode;
                    }

                    if (currentNode && currentNode.tagName === 'P') {
                        const style = window.getComputedStyle(currentNode);
                        const lineHeight = style.lineHeight;
                        return lineHeight;
                    }
                }
                return null;
            }
            // 正则表达式用于匹配最外层的开始和结束标签
            function removeOutermostTags(html, tag) {
                const startTag = new RegExp(`<${tag}\\b[^>]*>`, 'i');
                const endTag = new RegExp(`</${tag}>`, 'i');

                // 移除开始标签
                html = html.replace(startTag, '');
                // 移除结束标签
                html = html.replace(endTag, '');

                return html;
            }
            this.editor.config.uploadImgHooks = {
                befor: (xhr, editor, files) => {
                    // 图片插入之前
                },
                fail: (xhr, editor, result) => {
                    // 插入图片失败回调
                    that.$message.error("插入图片失败");
                },
                success: (xhr, editor, result) => {
                    // 图片上传成功回调
                    that.$message.error("图片上传成功");
                },
                timeout: (xhr, editor) => {
                    // 网络超时的回调
                    that.$message.error("网络超时");
                },
                error: (xhr, editor) => {
                    // 图片上传错误的回调
                },
                customInsert: (insertImg, result, editor) => {
                    // 图片上传成功，插入图片的回调
                    // result为上传图片成功的时候返回的数据，这里我打印了一下发现后台返回的是data：[{url:"路径的形式"},...]
                    // console.log(result.data[0].url)
                    // insertImg()为插入图片的函数
                    // 循环插入图片
                    // for (let i = 0; i < 1; i++) {
                    // console.log(result)
                    const url = 'http://otp.cdinfotech.top' + result.url
                    insertImg(url)
                    // }
                }
            }

            this.editor.config.onchange = (html) => {
                this.info_ = html // 绑定当前逐渐地值
            }
            // 创建富文本编辑器
            this.editor.create()
            // $('#wangEditorElem' + " .w-e-toolbar")
            //     .append('<div class="w-e-menu" data-title="全屏"><a id="doScreenfull" class="_wangEditor_btn_fullscreen" style="margin: 0" href="javascript:window.toggleFullscreen(\'' + '#wangEditorElem' + '\')"><i class="w-e-icon-fullscreen"/></a></div>')
            let t = $('<div id="doScreenfull" class="w-e-menu" data-title="全屏"><i onclick="handleFullscreen" class="w-e-icon-fullscreen"/></div>')
            t.click(this.handleFullscreen)
            this.$refs.toolbar.firstChild.appendChild(t[0])
            if (this.processForm.authType != 1) {
                this.editor.disable();
            }
        },
        stripHtml(html) {
      const doc = new DOMParser().parseFromString(html, 'text/html');
      return doc.documentElement.textContent || '';
    },
        checkField(field) {
            let that = this;
            let value = this.processForm[field];
            if (!value) {
                that.formRule[field].border = this.requiredBorder;
                that.$message.error(that.formRule[field].msg);
                return false;
            }
            this.formRule[field].border = this.normalBorder;
            return true
        },
        checkForm() {
            if (this.isDrafted()) {
                if (!this.checkField('title')) {
                    return false;
                }
                // 校验审批信息
                let checkNode = this.$refs.appInfo.checkNodeInfo();
                if (!checkNode) {
                    return false;
                }
            }
            // 校验商务政策信息
            if (this.processForm.process == this.allProcess.bussPolicy.code) {
                let checkPolicy = this.$refs.bussPolicy.checkPolicyForm();
                if (!checkPolicy) {
                    return false;
                }
            }
            // 校验报价信息
            if (this.processForm.process == this.allProcess.quote.code) {
                this.$refs.quote.checkQuoteForm();
            }

            // this.allProcess.intCont.code
            // 校验报价信息
            if (this.processForm.process == this.allProcess.intCont.code && !this.isDrafted()) {

                let checkIntCont = this.$refs.appInfo.checkintContForm();
                if (!checkIntCont) {
                    return false;
                }
            }

            return true;
        },
        checkConfirm() {
            // 校验是否有确认信息
            if (this.processForm.process == this.allProcess.bussPolicy.code) {
                let msg = this.$refs.bussPolicy.confirmPolicyForm();
                if (msg) {
                    return msg;
                }
            }
        },
        closeApproveForm() {
            this.loading = false;
            this.confirmLoading = false;
            this.isApproveForm = false;
        },

        getProcessName(code) {
            return this.allProcess[code].name;
        },
        dateFormat(date) {
            return dateFormat(date);
        },
        isDrafted() {
            return this.processForm.nodeInfo && this.processForm.nodeInfo.nodeCode && this.processForm.nodeInfo.nodeCode == 'drafted';
        },
        isFinish() {
            return this.processForm.processState == '终止' || this.processForm.processState == '完成';
        },
        handleFullscreen() {
            // 浏览器窗口全屏
            this.isFullFlag = !this.isFullFlag;
            if (this.isFullFlag) {
                this.editorHeight = "100%";
                $("#wangEditorElem").addClass('screenfull');
                $("#doScreenfull").attr('data-title', '取消全屏');
                $("#doScreenfull i").attr('class', 'w-e-icon-fullscreen_exit');
            } else {
                this.editorHeight = "700px";
                $("#wangEditorElem").removeClass('screenfull');
                $("#doScreenfull").attr('data-title', '全屏');
                $("#doScreenfull i").attr('class', 'w-e-icon-fullscreen');
            }

        },
        handleSend(noticeForm) {
            this.loading = true;
            this.$refs.mailEdit.showLoading();
            request(PROCESS_NOTICE, METHOD.POST, { ...noticeForm }).then(res => {
                this.loading = false;
                this.$message.info("发送成功");
                this.$refs.mailEdit.closeLoading();
                this.$refs.mailEdit.close();
            })
        },
        isCanWithdraw() {
            // 是否可以撤回
            let isOk = this.processForm.nodeInfo.nodeCode != 'drafted' && this.processForm.lastLog && this.processForm.lastLog.approvalOption == 'SUBMIT' && this.processForm.lastLog.approvalUser == this.userInfo.code;
            // console.log(isOk)
            return isOk;
        },
        copyInfo() {
            console.log(this.processForm);
            // console.log(this.processForm.linkList, this.processForm.processNumber);
            this.$router.push({
                path: '/xsgs_process_manager/xsgs_quote_letter_applove/quoteLetter',
                query: {
                    // linkList:this.processForm.linkList,
                    process: this.processForm.process,
                    processNumber: this.processForm.processNumber,
                    processId: this.processForm.id
                }
            })
        },
        intConHistoryList(intContList) {
            // console.log(intContList, '触发事件');
            this.$refs.appInfo.intConHistoryList(intContList)
        },
        isPreviousNode() {
            let that = this;
            // console.log(this.processForm.isConfirm);
            // 在流程为“商务合同内部审批流程”时才需要做“客户确认”字段判断，
            // 客户是否确认：是/否，选择后是可提交至下一环节；勾选否并必须填写未确认原因后可将流程退回至任何环节
            // 隐藏流程流向只可选退回
            // 其他流程无此逻辑统一返回true 显示提交流向
            if (this.process == 'intCont') {
                this.$nextTick(() => {
                    let flowNodeList = this.$refs.appInfo.flowNodeList;
                    let currentNode = this.processForm.nodeInfo;
                    if (currentNode && currentNode.nodeCode && flowNodeList.length > 0) {
                        let currentNodeCode = currentNode.nodeCode;
                        let prenode = flowNodeList[flowNodeList.length - 2]
                        console.log(currentNodeCode, prenode.nodeCode);
                        if (currentNodeCode == prenode.nodeCode) {
                            let theProcess_Confirm = (this.process == 'intCont' && this.processForm.isConfirm == 1)
                            this.isPreviousNodeState = theProcess_Confirm
                        } else {
                            that.isPreviousNodeState = true;
                        }
                    } else {
                        that.isPreviousNodeState = true;
                    }
                })
            } else {
                that.isPreviousNodeState = true;
            }
        },
        async beforBackSave() {
            // this.loading = true;
            this.getProcessInfo()
            this.processForm.content = this.info_;
            // 判断this.processForm.policyInfo是否存在，不为null或者undefined
            if (this.processForm.policyInfo && this.processForm.policyInfo != null && this.processForm.policyInfo != undefined) {
                this.processForm.businessYear = this.processForm.policyInfo.businessYear
            }
            // console.log(this.processForm);
            await request(SUBMIT_XSGSPROCESS, METHOD.POST, this.processForm).then(res => {
                this.loading = false;
                if (res.data.code == -1) {
                    this.$message.error(res.data.msg);
                    return;
                }
                this.$message.success("保存成功");
            })
        },
    }
}
</script>

<style scoped>
/deep/ .header-button-area {
    position: fixed;
    z-index: 999;
    height: 45px;
    background-color: #ffffff;
    line-height: 45px;
    margin-top: -25px;
    min-width: 500px;
}

/deep/ .header-button-area>button {
    margin-right: 8px;
}

/deep/ .ant-table>tr {
    height: 33px;
}

/deep/ .ant-table>tr>td input {
    /*border-radius: 0px;*/
    padding: 3px;
}

/deep/ .ant-table>tr>td a {
    margin-left: 5px;
}

/deep/ .ant-table>tr .textTd {
    padding-left: 3px !important;
}

/deep/ .ant-table>tr>th {
    width: 15%;
    text-align: center;
    background-color: #bdd7ee;
    border: 0.5px solid #464646;
    background-clip: padding-box;
}

/deep/ .ant-table>tr>td {
    width: 35%;
    text-align: left;
    border: 0.5px solid #464646;
    padding: 1px 2px !important;
    background-clip: padding-box;
}

.ant-table .editor {
    width: 100%;
    margin: 0 auto;
    border: 1px solid #ccc;
    position: relative;
}

.ant-table .toolbar {
    z-index: 0;
    border-top: none;
}

.ant-table .text {
    height: 200px;
    border-top: 1px solid #ccc;
    position: static !important;
}

/* .ant-table .w-e-menu {
    z-index: 0 !important;
} */

.ant-table .w-e-text p,
.w-e-text h1,
.w-e-text h2,
.w-e-text h3,
.w-e-text h4,
.w-e-text h5,
.w-e-text table,
.w-e-text pre {
    margin: 2px 0;
    line-height: 1.2;
}

/* /deep/ .ant-table .w-e-text-container {
    z-index: 1 !important;
}

/deep/ .ant-table .w-e-menu {
    z-index: 2 !important;
}

/deep/ .ant-table .w-e-toolbar {
    z-index: 3 !important;
} */

/deep/ .ant-table .ant-select-selection {
    /*border-radius: 0px !important;*/
    /*border: none !important;*/
}

/deep/ .ant-table .ant-select-selection__rendered {
    margin-left: 0px !important;
}

/deep/ .ant-form-item {
    margin-bottom: 10px !important;
}

/deep/ input[disabled] {
    color: rgba(0, 0, 0, 0.65) !important;
    background-color: #ffffff !important;
}

/deep/ div[role='combobox'] {
    background-color: #ffffff !important;
}

/deep/ .ant-select-disabled {
    color: rgba(0, 0, 0, 0.65) !important;
    background-color: #ffffff !important;
}

/deep/ .disabelTd {
    background-color: #ffffff !important
}

/deep/ .screenfull {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
}

/deep/ #doScreenfull {
    z-index: 10007 !important;
}

/deep/ .ant-upload-list-item {
    margin-top: 0px !important;
}

/deep/ .w-e-text-container>.w-e-text>p {
    line-height: 1;
    margin: 0 !important;
}

/deep/ .w-e-text-container>.w-e-text>p:first-child {
    margin-top: 10px !important;
}

#wangEditorElem {
    width: 100%;
    border-style: none solid none;
    border-width: 1px;
    word-wrap:break-word; 
    white-space: normal;
    position: relative;
    z-index: 0;
    /* 确保编辑器宽度自适应 */
}

#wangEditorElem .text {
    width: 100%;
    /* 编辑器区域宽度自适应 */
    word-wrap: break-word;
    /* 自动换行 */
    white-space: pre-wrap;
    /* 保留空格和换行，但允许内容换行 */
    overflow-y: auto;
    /* 如果内容过多，提供垂直滚动条 */
}

.w-e-text-container {
    max-width: 100% !important;
    /* 限制编辑器内容的最大宽度 */
    overflow-x: hidden;
    /* 隐藏水平滚动条 */
    
}

.w-e-text-container p {
    width: 100%;
    /* 确保段落宽度不超过容器 */
}
</style>
