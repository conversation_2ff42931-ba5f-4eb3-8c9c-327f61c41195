<template>
   <div>
       <table style="width: 100%">
           <tbody class="ant-table">
           <tr>
               <th style="width: 15%">环节名称</th>
               <th style="width: 8%">处理人</th>
               <th style="width: 15%">时间</th>
               <th style="width: 7%">操作</th>
               <th style="width: 7%">简单意见</th>
               <th style="width: 48%">详细意见</th>
           </tr>
           <tr v-for="d in logList" :key="d.id">
               <td class="textTd" style="width: 15%;">{{d.nodeName}}</td>
               <td class="textTd" style="width: 8%;text-align: center">{{d.approvalName}}</td>
               <td class="textTd" style="width: 15%;text-align: center">{{dateFormat(d.createTime)}}</td>
               <td class="textTd" style="width: 7%;text-align: center">{{coverOption(d.approvalOption)}}</td>
               <td class="textTd" style="width: 7%;text-align: center">{{d.approvalContent}}</td>
               <td class="textTd" style="width: 48%">{{d.approvalDetail}}</td>
           </tr>
           </tbody>
       </table>

   </div>

</template>

<script>
    import {
        GET_LOG_LIST} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import {dateFormat} from "@/utils/dateUtil";

    export default {
        name: "applove_log",
        props:{
            processId:String,
        },
        data() {
            return {
                logList:[],
            }
        },
        created() {

        },
        mounted() {

        },
        methods: {
            initLogList(processId) {
                let that = this;
                this.logList = [];
                request(GET_LOG_LIST,  METHOD.GET, {"processId":processId}).then(res => {
                    if (res.data.data) {
                        that.logList = res.data.data;
                    }
                })
            },
            dateFormat(date) {
                return dateFormat(date);
            },
            coverOption(code) {
                if (code=='SUBMIT') {
                    return "提交";
                } else if (code=='BACK') {
                    return "退回";
                } else if (code=="HANGUP") {
                    return "挂起";
                } else if (code=="WITHDRAW") {
                    return "撤回";
                } else if (code == "TERMINATE") {
                    return "终止";
                } else {
                    return "未知";
                }
            }
        }
    }
</script>

<style scoped>
    /deep/ .fontSmall td,
    .fontSmall th,
    .fontSmall input,
    .fontSmall textarea,
    .fontSmall select,
    .fontSmall a,
    .fontSmall button {
        font-size: 12px !important;
    }

    /deep/ .fontSmall tr,
    .fontSmall td,
    .fontSmall th {
        height: 22px !important;
    }
    /deep/ .fontMiddle td,
    .fontMiddle th,
    .fontMiddle input,
    .fontMiddle textarea,
    .fontMiddle select,
    .fontMiddle a,
    .fontMiddle button {
        font-size: 14px !important;
    }

    /deep/ .fontMiddle tr,
    .fontMiddle td,
    .fontMiddle th {
        height: 28px !important;
    }

    /deep/ .fontLarge td,
    .fontLarge th,
    .fontLarge input,
    .fontLarge textarea,
    .fontLarge select,
    .fontLarge a,
    .fontLarge button {
        font-size: 18px !important;
    }

    /deep/ .fontLarge tr,
    .fontLarge td,
    .fontLarge th {
        height: 33px !important;
    }
</style>
