<template>
    <div>
        <div style="padding: 0.2em">
            <span style="display: inline-block;text-align: center;width: 100%;color: red">{{message}}</span>
            <van-collapse v-model="activeNames">
                <van-collapse-item title="基本信息" name="1">
                    <div class="viewItem">
                        <label class="itemLabel">流程名称</label>
                        <span class="itemWrapper">{{processForm.processName}}</span>
                    </div>
                    <div class="viewItem">
                        <label class="itemLabel">作者</label>
                        <span class="itemWrapper">{{processForm.authorName}}</span>
                    </div>
                    <div class="viewItem">
                        <label class="itemLabel">主题</label>
                        <span class="itemWrapper">{{processForm.title}}</span>
                    </div>
                    <div class="viewItem">
                        <label class="itemLabel">当前位置</label>
                        <span class="itemWrapper">{{processForm.nodeInfo.nodeDisplayName}}</span>
                    </div>
                    <div class="viewItem">
                        <label class="itemLabel">当前处理人</label>
                        <span class="itemWrapper">{{processForm.nodeInfo.userName}}</span>
                    </div>
                </van-collapse-item>
            </van-collapse>
        </div>
    </div>

</template>

<script>
    import {GET_XSGSPROCESS_ID} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";


    export default {
        name: "phoneView.vue",
        data() {
            return {
                loading: false,
                message:"",
                activeNames:["1"],
                action:null,
                refId:null,
                refCode:null,
                processForm:null,
                intContList:[],
                allProcess: {
                    bussPolicy: {code: "bussPolicy", bpmCode:"YcBpmXsgsBussPolicyProcess",name: "商务政策审批流程",node:{
                            drafted:"办事处/系统部部门申请"
                        }},
                    quote: {code: "quote", bpmCode:"YcBpmXsgsQuoteProcess",name: "商务报价审批流程",node:{
                            drafted:"起草"
                        }},
                    intCont: {code: "intCont", bpmCode:"YcBpmXsgsIntContProcess",name: "商务合同内部审批流程",node:{
                            drafted:"起草"
                        }},
                    quoteLetter: {code: "quoteLetter", bpmCode:"YcBpmXsgsQuoteLetterProcess",name: "商务报价函审批流程",node:{
                            drafted:"起草"
                        }}
                },
                fullPath: this.$route.path,
            }
        },
        async mounted() {
            this.checkName();
            let res = await this.router();
            if (res) {
                if (res.data.code == 0) {
                    if (this.action=='process') {
                        this.processForm = res.data.data;
                        // if (this.processForm.process == this.allProcess.intCont.code) {
                        //     request(LIST_XSGSPROCESS_INTCONT, METHOD.GET, {"processId": this.processForm.id}).then(res => {
                        //         if (res.data.data) {
                        //             this.intContList = res.data.data.intContList ? res.data.data.intContList : [];
                        //
                        //         }
                        //     });
                        // }
                    }
                } else {
                    this.message = res.data.msg;
                }
            }
        },
        methods: {
             router(){
                this.refId = this.$route.query.refId;
                this.action = this.$route.query.action;
                this.refCode = this.$route.query.refCode;
                if (!this.refId) {
                    this.message = "参数异常，请联系管理员";
                    return
                }
                if (this.action=='process') {
                    this.activeNames = ['1'];
                    return request(GET_XSGSPROCESS_ID, METHOD.GET, {"id": this.refId})
                }
                return null;
            }
        },

    }
</script>

<style scoped>
    .viewItem {
        width: 100%;
        display: inline-block;
    }
    .itemLabel {
        float: left;
        display: inline-block;
        width: 30%;
    }
    .itemWrapper {
        float: right;
        display: inline-block;
        color: black;
        width: 70%;
    }
</style>
