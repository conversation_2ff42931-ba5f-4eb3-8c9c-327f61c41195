<template>
  <div>
    <a-card>
      <a-row>
        <a-col :span="24" style="margin-bottom: 5px;margin-top: 10px">
				<span style="float: left">
					<a-button type="primary" style="margin-right: 8px" icon="plus" @click="showCreateModal">新增</a-button>
					<a-button type="danger" icon="delete" @click="delAll">删除</a-button>
				</span>
        </a-col>
      </a-row>
      <a-table rowKey="id"
               bordered
               size="small"
               :loading="spinning"
               :pagination="pagination"
               :data-source="data"
               :row-selection="rowSelection"
               :columns="columns">
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="dataScope" slot-scope="text, record">
          <div>
            <a-checkbox v-model="record.partsPriceAccessEnabled" @change="updateAccessEnabled(record.id, 1, record.partsPriceAccessEnabled)">
              零部件价格
            </a-checkbox>
            <a-checkbox v-model="record.revenueInfoAccessEnabled"
                        @change="updateAccessEnabled(record.id, 2, record.revenueInfoAccessEnabled)">收益信息
            </a-checkbox>
            <a-checkbox v-model="record.accountingReportAccessEnabled"
                        @change="updateAccessEnabled(record.id, 3, record.accountingReportAccessEnabled)">核算报告
            </a-checkbox>
          </div>
        </template>
        <template slot="action" slot-scope="text, record">
          <div>

            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="edit(record)">
              编辑
            </span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="memberSetting(record)">
              用户配置
            </span>
            <span :style="{ color: '#1890ff', cursor: 'pointer', margin: '10px 4px' }"
                  @click="del(record)">
              删除
            </span>
          </div>
        </template>
      </a-table>

      <a-modal v-drag-modal v-model="showEditFormDialog" title="编辑" @ok="update" :loading="loading">
        <a-spin :spinning="loading">
          <a-form-model ref="editForm" :rules="editFormRules" :model="editForm" :label-col="{span: 6}"
                        :wrapper-col="{span: 16}" style="text-align: center;">
            <a-form-model-item label="细分市场" prop="marketSegment">
              <a-input v-model="editForm.marketSegment"/>
            </a-form-model-item>
            <a-checkbox v-model="editForm.partsPriceAccessEnabled">
              零部件价格
            </a-checkbox>
            <a-checkbox v-model="editForm.revenueInfoAccessEnabled">
              收益信息
            </a-checkbox>
            <a-checkbox v-model="editForm.accountingReportAccessEnabled">
              核算报告
            </a-checkbox>
          </a-form-model>
        </a-spin>
      </a-modal>

      <a-modal v-drag-modal v-model="showCreateFormDialog" title="新建" @ok="create" :loading="loading">
        <a-spin :spinning="loading">
          <a-form-model ref="createForm" :rules="createFormRules" :model="createForm" :label-col="{span: 6}"
                        :wrapper-col="{span: 16}" style="text-align: center;">
            <a-form-model-item label="细分市场" prop="marketSegment">
              <a-input v-model="createForm.marketSegment"/>
            </a-form-model-item>
            <a-checkbox v-model="createForm.partsPriceAccessEnabled">
              零部件价格
            </a-checkbox>
            <a-checkbox v-model="createForm.revenueInfoAccessEnabled">
              收益信息
            </a-checkbox>
            <a-checkbox v-model="createForm.accountingReportAccessEnabled">
              核算报告
            </a-checkbox>
          </a-form-model>
        </a-spin>
      </a-modal>

      <!-- 开始选人组件 -->
      <YcChooseMember
          ref="chooseMember"
          @member-modal-ok="memberModalOkTo($event)"
          @member-modal-cancel="canCel"
      ></YcChooseMember>
    </a-card>

  </div>
</template>

<script>
import {
  XSGSQUICKQUOTEOAPUSH
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import * as _ from 'lodash';
import YcChooseMember from "@/components/chooseMember/YcChooseMember.vue";

export default {
  name: 'xsgs-quick-quote-oa-push-index',
  components: {
    YcChooseMember
  },
  data() {
    return {
      createForm: {
        marketSegment: '',
        partsPriceAccessEnabled: null,
        accountingReportAccessEnabled: null,
        revenueInfoAccessEnabled: null,
      },

      createFormRules: {
        marketSegment: [{required: true, message: "细分市场不能为空", trigger: 'blur'}]
      },

      editForm: {
        id: null,
        marketSegment: '',
        partsPriceAccessEnabled: null,
        accountingReportAccessEnabled: null,
        revenueInfoAccessEnabled: null,
      },
      editFormRules: {
        id: [{required: true, message: '细分市场ID不能为空'}],
        marketSegment: [{required: true, message: "细分市场不能为空", trigger: 'blur'}]
      },
      spinning: false,
      loading: false,
      showCreateFormDialog: false,
      showEditFormDialog: false,
      selectedRowKeys: [],
      selectedRows: [],
      data: [],
      oData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        pageSizeOptions: ['10', '15', '20', '100'],
        showTotal: (total) => `共 ${total} 条数`,
        onChange: (current, pageSize) => this.pageChange(current, pageSize),
        onShowSizeChange: (current, pageSize) =>
            this.pageSizeChange(current, pageSize),
      },
      columns: [
          { dataIndex: 'num', title: '序号', key: 'num', width: 60, align: 'center', scopedSlots: {customRender: 'num'} },
          { dataIndex: "marketSegment", title: "细分市场", width: 180 },
          { dataIndex: "users", title: "接收人姓名", ellipsis: true },
          { dataIndex: "dataScope", title: '价格信息是否可看', scopedSlots: {customRender: 'dataScope'} },
          { dataIndex: "action", title: "操作", width: 250, align: "center", scopedSlots: {customRender: 'action'} },
      ],
      flatArr: [],//权限配置树所有父节点
    };
  },
  mounted() {
    this.getData();
  },
  computed: {
    rowSelection() {
      return {
        columnWidth: '50px',
        selectedRowKeys: this.selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRows = selectedRows;
          this.selectedRowKeys = selectedRowKeys;
        },
      };
    },
  },

  methods: {

    async getData() {
      this.spinning = true;
      this.data = [];
      const {current, pageSize} = this.pagination;
      const _this = this;
      console.log(current, pageSize)
      await request(XSGSQUICKQUOTEOAPUSH, METHOD.GET, {current, size:pageSize})
          .then((res) => {
            if (res.data && res.data.data && res.data.data.records && res.data.data.records.length) {
              _this.oData = res.data.data.records;
              _.map(res.data.data.records, (item) => {
                const d = {
                  id: item.id,
                  marketSegment: item.marketSegment,
                  partsPriceAccessEnabled: item.partsPriceAccessEnabled === 1,
                  revenueInfoAccessEnabled: item.revenueInfoAccessEnabled === 1,
                  accountingReportAccessEnabled: item.accountingReportAccessEnabled === 1
                }

                const {users} = item
                let _users = '';
                if (users.length) {
                  _.map(users, (item) => {
                    if (_users) {
                      _users += '、' + item.username
                    } else {
                      _users += item.username;
                    }
                  })
                }
                d['users'] = _users;
                _this.data.push(d);
              })
            }
            _this.pagination.total = Number(res.data.data.total);
          }).finally(() => {
        _this.spinning = false;
      })
    },

    async updateAccessEnabled(id, accessType, enabled) {
      const value = enabled ? 1: 0;
      await request(`${XSGSQUICKQUOTEOAPUSH}/${id}`, METHOD.PUT, {accessType, enabled: value})
          .then(() => {});
    },

    canCel() {
    },

    edit(record) {
      this.editForm.id = record.id;
      this.editForm.marketSegment = record.marketSegment;
      this.editForm.partsPriceAccessEnabled = record.partsPriceAccessEnabled;
      this.editForm.accountingReportAccessEnabled = record.accountingReportAccessEnabled;
      this.editForm.revenueInfoAccessEnabled = record.revenueInfoAccessEnabled;
      this.showEditFormDialog = true;
    },

    memberSetting(record) {
      const {id} = record
      const oItem = _.find(this.oData, (item) => item.id === id);
      const {users} = oItem;
      let employees = [];
      _.map(users, (user) => {
        employees.push({
          employeeName: user.username,
          employeeCode: user.userAccount,
          orgFullName: user.userDisplayName
        })
      })
      this.$refs.chooseMember.show(record.id, [...employees]);
    },

    del(record) {
      const _this = this;
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: async () => {
          _this.spinning = true;
          await request(`${XSGSQUICKQUOTEOAPUSH}/ids/${record.id}`, METHOD.DELETE)
              .then(async () => {
                await _this.getData();
              })
              .finally(() => {
                _this.spinning = false;
              })
        }
      })
    },

    showCreateModal() {
      this.showCreateFormDialog = true;
    },

    async create() {
      const _this = this;
      await this.$refs.createForm.validate(async (valid) => {
        if (valid) {
          _this.loading = true;
          const d = _.cloneDeep(_this.createForm);
          d.accountingReportAccessEnabled = d.accountingReportAccessEnabled ? 1 : 0;
          d.partsPriceAccessEnabled = d.partsPriceAccessEnabled ? 1 : 0;
          d.revenueInfoAccessEnabled = d.revenueInfoAccessEnabled ? 1 : 0;
          await request(XSGSQUICKQUOTEOAPUSH, METHOD.POST, d)
              .then(async () => {
                _this.$message.info("创建成功！");
                _this.createForm = {
                  marketSegment: '',
                  partsPriceAccessEnabled: null,
                  accountingReportAccessEnabled: null,
                  revenueInfoAccessEnabled: null,
                }
                await _this.getData();
                _this.showCreateFormDialog = false;
              }).finally(() => {

                _this.loading = false;
              })
        }
      })
    },

    async update() {
      const _this = this;
      await this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          _this.loading = true;
          const d = _.cloneDeep(_this.editForm);
          d.accountingReportAccessEnabled = d.accountingReportAccessEnabled ? 1 : 0;
          d.partsPriceAccessEnabled = d.partsPriceAccessEnabled ? 1 : 0;
          d.revenueInfoAccessEnabled = d.revenueInfoAccessEnabled ? 1 : 0;
          await request(XSGSQUICKQUOTEOAPUSH, METHOD.PUT, d)
              .then(async () => {
                _this.$message.info("保存成功");
                await _this.getData();
                _this.showEditFormDialog = false;
              }).finally(() => {
                _this.loading = false;
              })
        }
      });
    },

    async memberModalOkTo(record) {

      const id = record.rowId;
      if (id) {
        const users = [];
        if (record.memberData && record.memberData.length) {
          _.map(record.memberData, (item) => {
            users.push({
              username: item.employeeName,
              userAccount: item.employeeCode,
              userDisplayName: item.orgFullName
            })
          })
        }
        const _this = this;
        await request(`${XSGSQUICKQUOTEOAPUSH}/${id}/users`, METHOD.PUT, users)
            .then(() => {
              _this.$message.info("保存成功");
            }).finally(async () => {
              await _this.getData();
            })
      }


    },

    async delAll() {
      if (this.selectedRowKeys.length < 1) {
        this.$message.warning('没有选择要删除的数据！');
        return;
      }
      const ids = this.selectedRowKeys
      await this.del({id: _.join(ids, ",")});
      this.selectedRowKeys = [];
    },

    pageChange(current) {
      this.pagination.current = current;
      this.getData();
    },

    pageSizeChange(current, pageSize) {
      this.pagination.current = 1;
      this.pagination.pageSize = pageSize;
      console.log(this.pagination.pageSize)
      this.getData();
    },
  }
};
</script>

<style>
</style>
