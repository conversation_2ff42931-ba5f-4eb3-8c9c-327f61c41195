<template>
  <div :style="customerstyle">
    <a-upload
        :file-list="fileList"
        :before-upload="beforeUpload"
        :customRequest="customRequest"
        :remove="removeFile"
        :multiple="multiple"
        :disabled="disabled"
        @preview="handlePreview"
    >
      <a style="color: #2db7f5">{{title}}</a>
    </a-upload>
  </div>
</template>

<script>
import {getObsDownloadUrl, uploadObsFile} from "@/services/file";

export default {
  name: "FileUpload",
  props: ['value', 'path', 'field','multiple','disabled','customerstyle','title','refId'],
  data() {
    return {}
  },
  computed: {
    fileList: function () {
      let files = []
      // this.value.map(async (file, i) => {
      //   let res = await getObsDownloadUrl(file.path + file.finalName)
      //   let f = {...file, uid: file.id || file.uid, url: res.data.data}
      //   files.push(f)
      //   this.value[i] = f
      // })
      return files
    }
  },
  methods: {
    handlePreview(file) {
      getObsDownloadUrl(file.path + file.finalName)
          .then(res => {
            window.open(res.data.data)
          })
    },
    customRequest(data) {
      this.loading = true
      uploadObsFile(data.file, this.path, this.field).then(res => {
        // this.value.push(res)
        // this.$emit('input', this.value)
        this.$message.success(`${data.file.name} 上传成功！`)
        data.onSuccess();
        res['refId'] = this.refId;
        this.$emit('onSuccess', res);
      }).catch(() => {
        this.$message.error(`${data.file.name} 上传失败！`);
        data.onError()
      }).finally(() => {
        this.loading = false
      })
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!isLt2M) {
        this.$message.error('附件不能大于 20MB!');
      }
      return isLt2M;
    },
    removeFile(file) {
      this.value.map((f, i) => {
        if (f.uid == file.uid) {
          this.value.splice(i, 1)
        }
      })
      this.$emit('input', this.value)
    }
  }
}
</script>

<style scoped>
 /deep/ .ant-upload-list-item:hover .ant-upload-list-item-card-actions{
   top:0 !important
 }
 /deep/ .ant-upload-list-item {
   margin-top: 0px !important;
 }
</style>
