<template>
    <div>
        <div class="header-button-area">
            <a-button v-if = "!isView" type="primary" @click="handleDowload" style="margin-right: 8px">
                下载
            </a-button>
            <a-button type="primary" @click="handleClose()">
                关闭
            </a-button>
        </div>
        <a-spin :spinning="loading">
        <div class="quoteLetterView">
            <div class="pageHeader" :style="`width:${pageWidth}px`">
                <img width="166px" height="46px" style="float: left" src="~@/assets/img/yc_machine.png">
                <img width="90px" height="45px" style="float: right" src="~@/assets/img/yc_fax.png">
            </div>
            <p class="pageMainTitle">广西玉柴机器股份有限公司/Guangxi Yuchai Machinery Co., Ltd.</p>
            <div class="pageTitle" :style="`width:${pageWidth}px`">
                <table>
                    <tbody>
                    <tr>
                        <td rowspan="2" style="border-top: 3px solid #548dd4;vertical-align: top;">
                            TO/收件单位：{{letterForm.customerName}}
                        </td>
                        <td style="border-top: 3px solid #548dd4">
                            TO/收件人：{{letterForm.recipient}}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            FAX/收件人传真：{{letterForm.recipientFax}}
                        </td>
                    </tr>
                    <tr>
                        <td rowspan="2" style="vertical-align: top;">
                            TOPIC/主题：{{letterForm.topic}}
                        </td>
                        <td>
                            TEL/收件人电话：{{letterForm.recipientTel}}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            CC/抄送：{{letterForm.copyTo}}
                        </td>
                    </tr>
                    <tr>
                        <td style="border-bottom: 3px solid #548dd4">
                            DATE/传真日期：{{letterForm.faxDate}}
                        </td>
                        <td style="border-bottom: 3px solid #548dd4">
                            TOTAL PAGES/页数：{{letterForm.pages}}
                        </td>
                    </tr>
                    <tr>
                        <td style="color: #1770bb">
                            ISSUER/签发人：{{letterForm.issuer}}
                        </td>
                        <td style="color: #1770bb">
                            FROM/发件人：{{letterForm.sender}}
                        </td>
                    </tr>
                    <tr>
                        <td style="color: #1770bb">
                            FAX/发件人传真：{{letterForm.senderFax}}
                        </td>
                        <td style="color: #1770bb">
                            TEL/发件人电话：{{letterForm.senderTel}}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" style="border-left: none;border-bottom: 3px solid #548dd4">
                            <a-checkbox-group style="width: 100%" class="securyText" v-model="security">
                                <a-row>
                                    <a-col :span="6">
                                        <a-checkbox value="Urgency">紧急Urgency</a-checkbox>
                                    </a-col>
                                    <a-col :span="6">
                                        <a-checkbox value="General">普通General</a-checkbox>
                                    </a-col>
                                    <a-col :span="6">
                                        <a-checkbox value="Secrecy">保密Secrecy </a-checkbox>
                                    </a-col>
                                    <a-col :span="6">
                                        <span>（请在□中打√）</span>
                                    </a-col>
                                </a-row>
                            </a-checkbox-group>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="pageContent" :style="`width:${Number(pageWidth)}px`">
                <p>
                    关于发动机报价函
                </p>
                <p style="text-align: left">
                    <label>{{letterForm.customerName}}：</label>
                </p>
                <p style="text-align: left">
                    <label v-if="type=='quoteInfo'" style="padding-left: 45px">感谢贵公司长期以来对我公司的大力支持！经我公司财务部核算，新配套贵公司的发动机<span style="color: red">不含税</span>价格如下：</label>
                    <label v-if="type=='changeInfo'" style="padding-left: 45px">感谢贵公司长期以来对我公司的大力支持！根据贵公司要求，配套贵公司的以下发动机需更改配置出厂，具体配置更改及更改后的发动机<span style="color: red">不含税</span>价格如下：</label>
                </p>
                <div v-if="letterForm.letterType=='PS_YN'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 24%">产品型号</th>
                            <th style="width: 24%">供货号</th>
                            <th style="width: 24%">客户物料</th>
                            <th style="width: 20%">整机价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index + 1}}</td>
                            <td style="width: 24%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 24%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 24%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 20%">
                                {{d.unitPrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="4" style="border-right: 1px solid">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="4" style="padding: 0">
                                <table class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 24%">配置名称</th>
                                        <th style="width: 12%">规格</th>
                                        <th style="width: 12%">是否配置</th>
                                        <th style="width: 12%">配置价格</th>
                                        <th style="width: 20%">备注</th>
                                    </tr>
                                    <tr v-for="(s,i) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 24%;text-align: center">{{s.partsName}}</td>
                                        <td style="width: 12%;text-align: center">{{s.specs}}</td>
                                        <td style="width: 12%;text-align: center">{{coverConfig(s)}}</td>
                                        <td style="width: 12%;text-align: center">{{coverPrice(s)}}</td>
                                        <td  style="width: 20%" v-if="i == 0" :rowspan="d.parts.length + 1">
                                            <a-textarea readOnly class="remarkText" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" style="text-align: center;width: 68%">合计：</td>
                                        <td style="text-align: center;width: 12%">{{getTotal(d)}}</td>
                                        <td v-if="!d || !d.parts || d.parts.length == 0">
                                            <a-textarea readOnly class="remarkText"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='PST_YNY'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 19%">产品型号</th>
                            <th style="width: 19%">供货号</th>
                            <th style="width: 19%">客户物料</th>
                            <th style="width: 19%">含税整机价格</th>
                            <th style="width: 16%">整机价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index+1}}</td>
                            <td style="width: 19%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 19%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 19%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 19%">
                                {{d.taxUnitPrice}}
                            </td>
                            <td style="width: 16%">
                                {{d.unitTicketPrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="5">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="5" style="padding: 0">
                                <table class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 24%">配置名称</th>
                                        <th style="width: 12%">规格</th>
                                        <th style="width: 12%">是否配置</th>
                                        <th style="width: 12%">配置价格</th>
                                        <th style="width: 20%">备注</th>
                                    </tr>
                                    <tr v-for="(s,i) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 24%;text-align: center" >{{s.partsName}}</td>
                                        <td style="width: 12%;text-align: center">{{s.specs}}</td>
                                        <td style="width: 12%;text-align: center">{{coverConfig(s)}}</td>
                                        <td style="width: 12%;text-align: center">{{coverPrice(s)}}</td>
                                        <td v-if="i == 0" :rowspan="d.parts.length + 1">
                                            <a-textarea readOnly class="remarkText" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="4" style="text-align: center">合计：</td>
                                        <td style="text-align: center;width: 12%">{{getTotal(d)}}</td>
                                        <td v-if="!d || !d.parts || d.parts.length == 0">
                                            <a-textarea class="remarkText" readOnly
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='PS_NN'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 24%">产品型号</th>
                            <th style="width: 24%">供货号</th>
                            <th style="width: 24%">客户物料</th>
                            <th style="width: 20%">整机价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index+1}}</td>
                            <td style="width: 24%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 24%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 24%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 20%">
                                {{d.unitPrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="4">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="4" style="padding: 0">
                                <table class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 24%">配置名称</th>
                                        <th style="width: 16%">规格</th>
                                        <th style="width: 16%">是否配置</th>
                                        <th style="width: 26%">备注</th>
                                    </tr>
                                    <tr v-for="(s,i) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 24%;text-align: center">{{s.partsName}}</td>
                                        <td style="width: 16%;text-align: center">{{s.specs}}</td>
                                        <td style="width: 16%;text-align: center">{{coverConfig(s)}}</td>
                                        <td v-if="i == 0" :rowspan="d.parts.length + 1">
                                            <a-textarea readOnly class="remarkText" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='PS_NY'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 24%">产品型号</th>
                            <th style="width: 24%">供货号</th>
                            <th style="width: 24%">客户物料</th>
                            <th style="width: 20%">整机价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index}}</td>
                            <td style="width: 24%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 24%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 24%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 20%">
                                {{d.unitPrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="4">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="4" style="padding: 0">
                                <table v-if="!partsGetting" class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 24%" colspan="2">配置名称</th>
                                        <th style="width: 12%">规格</th>
                                        <th style="width: 12%">供应商</th>
                                        <th style="width: 12%">是否配置</th>
                                        <th style="width: 20%">备注</th>
                                    </tr>
                                    <tr v-for="(s,index) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 24%;text-align: center">{{s.partsName}}</td>
                                        <td style="width: 12%;text-align: center">{{s.specs}}</td>
                                        <td style="width: 12%;text-align: center">{{s.supplierBak}}</td>
                                        <td style="width: 12%;text-align: center">{{coverConfig(s)}}</td>
                                        <td v-if="index == 0" :rowspan="d.parts.length + 1">
                                            <a-textarea class="remarkText" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='NSS_YYY'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 18%">产品型号</th>
                            <th style="width: 18%">供货号</th>
                            <th style="width: 18%">客户物料</th>
                            <th style="width: 20%">整机价格</th>
                            <th style="width: 18%">基本型价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index + 1}}</td>
                            <td style="width: 18%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 18%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 18%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 20%">
                                {{d.unitPrice}}
                            </td>
                            <td style="width: 18%">
                                {{d.basePrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="5">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="5" style="padding: 0">
                                <table class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 18%">配置名称</th>
                                        <th style="width: 12%">规格</th>
                                        <th style="width: 12%">供应商</th>
                                        <th style="width: 12%">是否配置</th>
                                        <th style="width: 12%">配置价格</th>
                                        <th style="width: 14%">备注</th>
                                    </tr>
                                    <tr v-for="(s,i) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 18%;text-align: center">{{s.partsName}}</td>
                                        <td style="width: 12%;text-align: center">{{s.sspecs}}</td>
                                        <td style="width: 12%;text-align: center">{{d.supplierBak}}</td>
                                        <td style="width: 12%;text-align: center">{{coverConfig(s)}}</td>
                                        <td style="width: 12%;text-align: center">{{coverPrice(s)}}</td>
                                        <td v-if="i == 0" :rowspan="d.parts.length + 1">
                                            <textarea class="remarkText xsgs-textarea" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></textarea>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="5" style="text-align: center">合计：</td>
                                        <td style="text-align: center;width: 12%">{{getTotal(d)}}</td>
                                        <td v-if="!d || !d.parts || d.parts.length == 0">
                                            <textarea class="remarkText xsgs-textarea"
                                                        v-model="d.remark"></textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='NSS_YYN'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table  v-for="(d, index) in letterForm.infoList" :key="index">
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 18%">产品型号</th>
                            <th style="width: 18%">供货号</th>
                            <th style="width: 18%">客户物料</th>
                            <th style="width: 20%">整机价格</th>
                            <th style="width: 18%">基本型价格</th>
                        </tr>
                        <tr>
                            <td style="width: 8%;text-align: center" rowspan="3">{{index + 1}}</td>
                            <td style="width: 18%;text-align: center">
                                {{d.productModel}}
                            </td>
                            <td style="width: 18%">
                                {{d.machineType}}
                            </td>
                            <td style="width: 18%">
                                {{d.materialNo}}
                            </td>
                            <td style="width: 20%">
                                {{d.unitPrice}}
                            </td>
                            <td style="width: 18%">
                                {{d.basePrice}}
                            </td>
                        </tr>
                        <tr>
                            <th colspan="5">整机配套清单</th>
                        </tr>
                        <tr>
                            <td colspan="5" style="padding: 0">
                                <table class="detailTable">
                                    <tbody>
                                    <tr>
                                        <th style="width: 12%">配置类型</th>
                                        <th style="width: 24%">配置名称</th>
                                        <th style="width: 12%">规格</th>
                                        <th style="width: 12%">是否配置</th>
                                        <th style="width: 12%">配置价格</th>
                                        <th style="width: 20%">备注</th>
                                    </tr>
                                    <tr v-for="(s,i) in d.parts.filter(s=>s.isDelete != 1)" :key="s.id">
                                        <td style="width: 12%;text-align: center">{{s.subType}}</td>
                                        <td style="width: 24%;text-align: center">{{s.partsName}}</td>
                                        <td style="width: 12%;text-align: center">{{s.specs}}</td>
                                        <td style="width: 12%;text-align: center">{{coverConfig(s)}}</td>
                                        <td style="width: 12%;text-align: center">{{coverPrice(s)}}</td>
                                        <td v-if="i == 0" :rowspan="d.parts.length + 1">
                                            <a-textarea class="remarkText" :style="`height: ${(d.parts.length+1)*25}px;min-height: ${(d.parts.length+1)*25}px`"
                                                        v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td colspan="4" style="text-align: center">合计：</td>
                                        <td style="text-align: center;width: 12%">{{getTotal(d)}}</td>
                                        <td v-if="!d || !d.parts || d.parts.length == 0">
                                            <a-textarea v-model="d.remark"></a-textarea>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='CP_YN'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table>
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 14%">客户物料</th>
                            <th style="width: 14%">产品型号</th>
                            <th style="width: 14%">供货号</th>
                            <th style="width: 12%">原价格</th>
                            <th style="width: 14%">成本变动</th>
                            <th style="width: 12%">现价格</th>
                            <th style="width: 12%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in letterForm.infoList" :key="d.id">
                            <td style="text-align: center;width: 8%">{{index + 1}}</td>
                            <td style="text-align: center;width: 14%">
                                {{d.materialNo}}
                            </td>
                            <td style="text-align: center;width: 14%">
                                {{d.productModel}}
                            </td>
                            <td style="text-align: center;width: 14%">
                                {{d.machineType}}
                            </td>
                            <td style="text-align: center;width: 12%">
                                {{d.orgPrice}}
                            </td>
                            <td style="text-align: center;width: 14%">
                                {{d.costChange}}
                            </td>
                            <td style="text-align: center;width: 12%">
                                {{d.nowPrice}}
                            </td>
                            <td style="width: 12%">
                                <textarea readOnly class="remarkText xsgs-textarea" style="float: left"
                                            v-model="d.remark"></textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div v-if="letterForm.letterType=='CP_YY'" class="letterPriceTable" :style="`width:${pageWidth}`">
                    <table>
                        <tbody>
                        <tr>
                            <th style="width: 8%" >序号</th>
                            <th style="width: 12%">客户物料</th>
                            <th style="width: 12%">产品型号</th>
                            <th style="width: 12%">供货号</th>
                            <th style="width: 10%">原价格</th>
                            <th style="width: 12%">成本变动</th>
                            <th style="width: 10%">现价格</th>
                            <th style="width: 14%">含税整机价格</th>
                            <th style="width: 10%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in letterForm.infoList" :key="d.id">
                            <td style="text-align: center;width: 8%;">{{index + 1}}</td>
                            <td style="text-align: center;width: 12%;">
                                {{d.materialNo}}
                            </td>
                            <td style="text-align: center;width: 12%;">
                                {{d.productModel}}
                            </td>
                            <td style="text-align: center;width: 12%;">
                                {{d.machineType}}
                            </td>
                            <td style="text-align: center;width: 10%;">
                                {{d.orgPrice}}
                            </td>
                            <td style="text-align: center;width: 12%;">
                                {{d.costChange}}
                            </td>
                            <td style="text-align: center;width: 10%;">
                                {{d.nowPrice}}
                            </td>
                            <td style="text-align: center;width: 14%;">
                                {{d.taxUnitPrice}}
                            </td>
                            <td style="text-align: center;width: 10%;">
                                <textarea class="remarkText xsgs-textarea" style="float: left" readOnly v-model="d.remark"></textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <p style="text-align: left">
                    <label style="padding-left: 45px">请贵公司收函后予以确认，以便我公司安排相关事宜。</label>
                </p>
                <p style="text-align: left">
                    <label style="padding-left: 45px">谢谢合作并致</label>
                </p>
                <p style="text-align: left">
                    <label style="padding-left: 45px">商祺！</label>
                </p>
                <p style="text-align: right">
                    广西玉柴机器股份有限公司营销公司
                </p>
                <p style="text-align: right">
                    <a-input readOnly style="width: 250px;font-weight: normal;text-align: center;border: none;box-shadow: none;" v-model="letterForm.letterDate"/>
                </p>
            </div>
            <div class="pageFooter" :style="`width:${pageWidth}px`">
                <a-col :span="24">
                    <span style="float: left;">版权归广西玉柴机器股份有限公司所有</span>
                </a-col>
                <a-col :span="16">
                    <span style="float: left;">Copyrights are held by Guangxi Yuchai Machinery Co., Ltd. </span>
                </a-col>
                <a-col :span="8" style="text-align: right">
                    <span style="float: right;">网址：www.yuchaidiesel.com</span>
                </a-col>
                <a-col :span="24">
                    <span style="float: left;">地址（Add）：<span class="wrapper">中国广西玉林市天桥西路88号/ No. 88, Tianqiao West Road, Yulin City, Guangxi, China</span></span>
                </a-col>
                <a-col :span="8">
                    <span style="float: left;">电话（Tel）：<span class="wrapper">+86 775 3228168</span></span>
                </a-col>
                <a-col :span="8" style="text-align: center">
                    <span> 传真（Fax）：<span class="wrapper">+86 775 3288168</span></span>
                </a-col>
                <a-col :span="8" style="text-align: right">
                    <span>邮编（P.C.）：<span class="wrapper">537005</span></span>
                </a-col>
            </div>
        </div>
        </a-spin>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import { GET_XSGSQUOTELETTER_BY_ID,XSGSQUOTELETTER_GETFROMCACHE,DOWNLOAD_LETTER } from "@/services/api/xsgs";
    import {METHOD, request, exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie'

    export default {
        name: "quoteLetter_view.vue",
        data() {
            return {
                hasAuth,
                Cookie,
                loading:false,
                quoteLetterStoreId:null,
                option:null,
                letterType:null,
                id:null,
                isView:false,
                checked:false,
                customer:null,
                type:null,
                pageWidth:null,
                letterForm:{
                    id:null,
                    quoteLetterStoreId:null,
                    year:null,
                    letterType:null,
                    letterName:null,
                    customer:null,
                    customerName:null,
                    recipient:null,
                    recipientFax:null,
                    recipientTel:null,
                    sender:null,
                    senderTel:null,
                    senderFax:null,
                    topic:null,
                    copyTo:null,
                    faxDate:null,
                    letterDate:null,
                    pages:null,
                    issuer:null,
                    secury:null,
                    infoList:[],
                    hasChange:null,
                    option:null
                },

            }
        },
        computed:{
            security(){
                if (this.letterForm.secury) {
                    return this.letterForm.secury.split(',');
                }
                return [];
            },
        },
        created() {
            this.pageWidth='720.5';
        },
        mounted() {
            this.loading = false;
            this.letterType = this.$route.query.letterType;
            this.option = this.$route.query.option;
            this.quoteLetterStoreId = this.$route.query.quoteLetterStoreId;
            this.parentPath = this.$route.query.parentPath;
            this.fullPath = this.$route.fullPath;
            this.customer = this.$route.query.customer;
            this.id = this.$route.params.id
            this.type = this.getType();
            this.getData();
            this.checkName();
        },
        methods: {
            getData(){
                this.loading = true;
                // 先从缓存拿，拿不到从数据库拿
                request(XSGSQUOTELETTER_GETFROMCACHE, METHOD.GET, {id: this.id, quoteLetterStoreId:this.quoteLetterStoreId})
                    .then(res => {
                        if (res.data.data) {
                            this.letterForm = res.data.data;
                            this.loading = false;
                        } else {
                            request(GET_XSGSQUOTELETTER_BY_ID, METHOD.GET, {id:this.id}).then(res=>{
                                if (res.data.data) {
                                    this.letterForm = res.data.data;
                                }
                                this.loading = false;
                            });
                        }
                    });
            },

            handleDowload(){
                exportExcel(DOWNLOAD_LETTER,{id:this.id,quoteLetterStoreId:this.quoteLetterStoreId},this.letterForm.letterName + '.doc');
                // request(DOWNLOAD_LETTER, METHOD.GET,{id:this.id,quoteLetterStoreId:this.quoteLetterStoreId}).then(res=>{
                //     console.log(res);
                // });
            },
            handleClose(){
                this.$closePage(this.fullPath, this.parentPath);
            },
            getType(){
                if (this.letterType.indexOf('CP_') != -1) {
                    return 'changeInfo';
                } else {
                    return 'quoteInfo';
                }
            },
            coverQuoteLetterType(quoteLetterType) {
                if (quoteLetterType == 'PS_YN') {
                    return "显示零件价格，无供应商";
                } else if (quoteLetterType == 'PST_YNY') {
                    return "显示零件价格，无供应商，含税价格";
                } else if (quoteLetterType == 'PS_NN') {
                    return "不显示零件价格，无供应商";
                } else if (quoteLetterType == 'PS_NY') {
                    return "不显示零件价格，显示供应商";
                } else if (quoteLetterType == 'NSS_YYY') {
                    return "基本型+选配件合计，显示供应商";
                } else if (quoteLetterType == 'NSS_YYN') {
                    return "基本型+选配件合计，无供应商";
                } else if (quoteLetterType == 'CP_YN') {
                    return "更改配置报价模板(不含税价格)";
                }  else if (quoteLetterType == 'CP_YY') {
                    return "更改配置报价模板(含税价格)";
                }else {
                    return "其他";
                }

            },
            getTotal(d) {
                let total = 0;
                if (d && !this.isEmpty(d.parts)) {
                    d.parts.forEach(s=>{
                        let price = 0;
                        if (s.subType == '选型件' && s.isSubtraction != '1') {
                            price = this.coverNumber(s.valuePrice);
                        } else {
                            price = this.coverNumber(s.actualPrice);
                        }
                        total = total + price;
                    });
                }
                return total;
            },
            coverConfig(d){
                let conf = '是';
                if (d.subType == '选型件') {
                    if (d.isSubtraction == '1') {
                        conf = '否';
                    } else if (this.nullToTrim(d.specs) != this.nullToTrim(d.standardSpecs)) {
                        conf = '配置变更';
                    }
                }
                return conf;
            },
            nullToTrim(val) {
                return !val?"":val;
            },
            coverPrice(d) {
                if (d.subType == '选型件' && d.isSubtraction != '1') {
                    if (d.valuePrice && d.valuePrice != 0) {
                        return d.valuePrice + '(价差)';
                    } else {
                        return d.valuePrice;
                    }

                } else {
                    return d.actualPrice;
                }
            },
            coverNumber(num) {
                return !num ? 0 : Number(num);
            },
        },
    }
</script>

<style scoped>
    /deep/ .header-button-area {
        position: fixed;
        z-index: 999;
        height: 45px;
        background-color: #ffffff;
        line-height: 45px;
        width: 100%;
        border-bottom: 1px solid #f0f0f0;
        padding: 0 30px;
        min-width: 700px;
    }
    /deep/ .quoteLetterView {
        background-color: #ffffff;
        text-align: center;
        width: 100%;
        padding-top: 45px;
    }
    /deep/ .pageHeader {
        height: 55px;
        border-bottom: 1px solid;
        display: inline-block;
    }
    /deep/ .pageMainTitle {
        font-family: 华文新魏;
        font-size: 21.3px;
        text-align: center;
        color: #17365d;
        margin-bottom: 8px;
        margin-top: 8px;
    }
    /deep/ .pageTitle {
        display: inline-block;
    }
    /deep/ .pageTitle td {
        width: 50%;
        text-align: left;
        border-top: 1px solid #548dd4;
        border-bottom: 1px solid #548dd4;
        font-family: 宋体;
        font-size: 14px;
        font-weight: bold;
        padding-left: 3px;
    }
    /deep/ .pageTitle tr td:last-child {
        border-left: 1px solid #548dd4;
    }
    /deep/ .securyText span {
        color: #548dd4;
    }
    /deep/ .pageContent {
        display: inline-block;
    }
    /deep/ .pageContent p {
        font-family: 仿宋_GB2312;
        font-size: 16px;
        margin: 10px 0;
    }
    /deep/ .pageContent p:first-child {
        font-size: 30px !important;
        margin: 16px 0;
    }
    /deep/ .pageContent .letterPriceTable table{
        width: 100%;
    }
    /deep/ .pageContent .letterPriceTable td,th{
        width: 100%;
        font-family: 等线;
        font-size: 15px;
        font-weight: normal !important;
        border: 0.5px solid;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/.pageContent .letterPriceTable .detailTable>tbody>tr>th:first-child {
        border-left: none !important;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr>td:first-child {
        border-left: none !important;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr>td:last-child {
        border-right: none !important;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr>th:last-child {
        border-right: none !important;
    }
    /deep/ .pageContent .letterPriceTable .detailTable>tbody>tr:last-child td {
        border-bottom: none !important;
    }
    /deep/ .pageFooter {
        padding-top: 3px;
        display: inline-block;
        border-top: 1px solid;
    }
    /deep/ .pageFooter span {
        font-family: 黑体;
        font-size: 14px;
        font-weight: bold
    }
    /deep/ .pageFooter .wrapper {
        font-family: 宋体;
        font-size: 12px;
    }
    /deep/ .remarkText {
        margin: 0;
        padding: 0 0 0 3px;
        height: 25px;
        min-height: 25px;
        resize: none;
        border: none;
        box-shadow: none;
    }

</style>
