<template>
    <div>
        <a-spin :spinning="loading">
        <table style="width: 100%" class="policyInfo">
            <tbody class="ant-table">
            <tr>
                <th colspan="5">报价函信息区</th>
            </tr>
            <tr>
                <th style="width: 5%">
                    序号
                </th>
                <th style="width: 55%">
                    报价函名称
                </th>
<!--                <th style="width: 25%">-->
<!--                    报价函文件-->
<!--                </th>-->
                <th style="width: 20%">
                    备注
                </th>
                <th style="width: 20%">操作</th>
            </tr>
            <tr v-for="(d,index) in quoteLetterForm.quoteLetterList.filter(s=>s.option != 2)" :key="d.id" :id="d.id">
                <td style="width: 5%;text-align: center">{{index+1}}</td>
                <td style="width: 55%;text-align: center;max-width: 300px">
<!--                    <a-textarea row="1" v-if="d.letterType != 'OTHERS'"-->
<!--                                v-model="d.letterName"-->
<!--                                style="margin: 0;padding: 0 0 0 2px;height: 28px;min-height:28px;max-height:150px;font-size: 14px"-->
<!--                                :placeholder="isNotDrafted()?'':'请输入报价函名称'"-->
<!--                                :readonly="isNotDrafted()"-->
<!--                                @change="changeLetterName(d)"-->
<!--                                :title="d.letterName"></a-textarea>-->
                    <textarea v-if="d.letterType != 'OTHERS'"
                                v-model="d.letterName"
                              class="xsgs-textarea"
                                style="margin: 0;padding: 0 0 0 2px;height: 30px;min-height:30px;max-height:150px;font-size: 16px;line-height: 18px"
                                :placeholder="isNotDrafted()?'':'请输入报价函名称'"
                                :readonly="isNotDrafted()"
                                @change="changeLetterName(d)"
                                :title="d.letterName"></textarea>
                    <FileUpload v-if="d.letterType == 'OTHERS'" v-model="d.annexList" path="xsgs/" field="quoteLetter" :disabled="true" :customerstyle="'text-align: left;'"/>
                </td>
<!--                <td style="width: 25%;">-->
<!--                    <FileUpload v-model="d.annexList" path="xsgs/" field="quoteLetter" :disabled="true"/>-->
<!--                </td>-->
                <td style="width: 20%">
                    <textarea
                                v-model="d.remark"
                                style="margin: 0;padding: 0 0 0 2px;height: 30px;min-height:30px;max-height:150px;font-size: 16px;line-height: 18px"
                                class="xsgs-textarea"
                                :placeholder="isNotDrafted()?'':'请输入备注'"
                                :readonly="isNotDrafted()"
                                @change="changeRemark(d)"
                                :title="d.remark"></textarea>
                </td>

                <td style="width: 20%;text-align: center">
                    <a v-if="!isNotDrafted() && d.letterType != 'OTHERS'" @click="editQuoteLetter(d)">编辑</a>
                    <LetterUpload v-if="!isNotDrafted() && d.letterType == 'OTHERS'" path="xsgs/" @onSuccess="uploadSuccess" :refId="d.id" title="编辑" customerstyle="width:35px;display:inline-block" field="quoteLetter"/>
                    <a-divider v-if="d.letterType != 'OTHERS'" type="vertical"/>
                    <a v-if="d.letterType != 'OTHERS'" @click="viewQuoteLetter(d)">预览</a>
                    <a-divider v-if="d.letterType != 'OTHERS'" type="vertical"/>
                    <a v-if="d.letterType != 'OTHERS'" @click="downloadLetter(d)">下载</a>
                    <a-divider v-if="!isNotDrafted()" type="vertical"/>
                    <a v-if="!isNotDrafted()" @click="deleteQuote(d)">删除</a>
                </td>
            </tr>
            <tr v-if="!isNotDrafted()">
                <td colspan="5">
                    <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newQuote">新增
                    </a-button>
                </td>
            </tr>
            </tbody>
        </table>
        </a-spin>
        <div class="quoteLetterModelStyle" ref="quoteLetterModelStyle">
        <a-modal
                width="25%"
                title="新增报价函"
                :footer="false"
                :getContainer="()=>$refs.quoteLetterModelStyle"
                @cancel="isChooseType = false"
                :visible="isChooseType">
            <div style="text-align: center;" class="quoteLetterTypeList">

                <p v-for="(d,index) in letterTypeList" :key="d.letterType">
                    <a v-if="d.letterType != 'OTHERS'" @click="newQuoteLetter(d.letterType)">{{(index+1) + '.' + d.desc}}</a>
                    <LetterUpload v-else path="xsgs/" @onSuccess="uploadSuccess" :title="(index+1) + '.' + d.desc" field="quoteLetter"/>
<!--                    <a v-else @click="newLetterTemplate()" style="color: #2db7f5">{{(index+1) + '.' + d.desc}}</a>-->

                </p>

<!--                <p><a @click="newQuoteLetter('PST_YNY')">2.显示零件价格，无供应商，含税价格</a></p>-->
<!--                <p><a @click="newQuoteLetter('PS_NN')">3.不显示零件价格，无供应商</a></p>-->
<!--                <p><a @click="newQuoteLetter('PS_NY')">4.不显示零件价格，显示供应商</a></p>-->
<!--                <p><a @click="newQuoteLetter('NSS_YYY')">5.基本型+选配件合计，显示供应商</a></p>-->
<!--                <p><a @click="newQuoteLetter('NSS_YYN')">6.基本型+选配件合计，无供应商</a></p>-->
<!--                <p><a @click="newQuoteLetter('CP_YN')">7.更改配置报价模板(不含税价格)</a></p>-->
<!--                <p><a @click="newQuoteLetter('CP_YY')">8.更改配置报价模板(含税价格)</a></p>-->
<!--                <p><a @click="newLetterTemplate()" style="color: #2db7f5">9.已有报价函，即可上传</a></p>-->
            </div>
        </a-modal>
        </div>
    </div>

</template>

<script>
    import {
        UPLOAD_CONF,
        DOWNLOAD_TEMPLATE,
        GET_LETTER_TYPES,
        XSGSQUOTELETTER_GETBY_PROCESSID,
        XSGSQUOTELETTER_CLEAR_CACHE,DOWNLOAD_LETTER
    } from "@/services/api/xsgs";
    import {METHOD, request, exportExcel} from "@/utils/request";
    import Cookie from 'js-cookie';
    import Fingerprint2 from 'fingerprintjs2';
    import LetterUpload from "./LetterUpload";
    import FileUpload from "../../../components/upload/FileUpload";

    export default {
        name: "quote_list",
        props: {
            form: Object,
            userInfo: Object,
            isView: {type:Boolean,default:false},
        },
        components: {
            LetterUpload,FileUpload
        },
        data() {
            return {
                Cookie,
                UPLOAD_CONF,
                loading:false,
                isChooseType:false,
                requiredBorder: "1px solid red !important",
                normalBorder: "1px solid #d9d9d9 !important",
                quoteLetterStoreId: null,
                fullPath: null,
                quoteLetterForm: {
                    customer:null,
                    customerName:undefined,
                    annexList: [],
                    quoteLetterList: []
                },
                annexList:[],
                letterTypeList:[],
                customerList: [],
                allCustomerList: [],
                quoteLetter: {
                    id: null,
                    quoteLetterType: null,
                    annexList: [],
                    remark: null,
                    isView: false
                },
                formRule: {},
            }
        },
        created() {

        },
        mounted() {
            this.fullPath = this.$route.fullPath;
        },
        beforeDestroy() {

        },
        watch: {
            "$store.state.account.quoteLetters": {
                deep: true,
                handler(val) {
                    let newList = val.filter(item => item.quoteLetterStoreId = this.quoteLetterStoreId);
                    if (!this.isEmpty(newList)) {
                        let that = this;
                        let newData = [...this.quoteLetterForm.quoteLetterList];
                        newList.forEach(item => {
                            if (!item.id) {
                                item.id = newData.length + Math.round(Math.random() * 1000) + 1;
                                item.option = 0;
                                newData.push(item);
                            } else {
                                let len = newData.filter(s => s.id == item.id);
                                if (!that.isEmpty(len)) {
                                    newData.forEach((s, index) => {
                                        if (s.id == item.id) {
                                            newData[index] = {...item};
                                        }
                                    })
                                } else {
                                    newData.push(item);
                                }

                            }
                        });
                        this.$set(this.quoteLetterForm, "quoteLetterList", newData);
                        let list = val.filter(item => item.quoteLetterStoreId != this.quoteLetterStoreId);
                        this.$store.commit("account/setQuoteLetters", list);
                    }
                }
            }
        },
        methods: {
            async initData(processId) {
                this.getLetterTypes();
                if (!processId) {
                    console.log('没有id');
                    let that = this;
                    const fingerprint = await Fingerprint2.get((components) => {
                        // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
                        const values = components.map((component) => component.value) // 配置的值的数组
                        const murmur = Fingerprint2.x64hash128(values.join(''), 31) // 生成浏览器指纹
                        //murmur就是浏览器指纹啦
                        that.quoteLetterStoreId = that.$store.state.account.user.code + murmur
                    })
                } else {
                    this.quoteLetterStoreId = this.$store.state.account.user.code + processId;

                    request(XSGSQUOTELETTER_GETBY_PROCESSID, METHOD.GET, {"processId": processId}).then(res => {
                        if (res.data.data) {
                            this.quoteLetterForm.quoteLetterList = res.data.data.quoteLetterList ? res.data.data.quoteLetterList : [];
                            // 清楚缓存数据
                            request(XSGSQUOTELETTER_CLEAR_CACHE, METHOD.POST, {"quoteLetterStoreId": this.quoteLetterStoreId,quoteList:this.quoteLetterForm.quoteLetterList}).then(res => {

                            });

                        }
                    });
                }
            },
            getLetterTypes(){
                request(GET_LETTER_TYPES, METHOD.GET).then(res=>{
                    this.letterTypeList = res.data.data;
                });
            },
            newQuote() {
                // 校验客户号
                if (!this.form.customer) {
                    this.$message.error("请输入客户号");
                    return;
                }
                this.quoteLetterForm.customer = this.form.customer;
                this.quoteLetterForm.customerName = this.form.customerName;
                this.isChooseType = true;
            },
            newQuoteLetter(quoteLetterType) {
                let query = {
                    quoteLetterStoreId: this.quoteLetterStoreId,
                    letterType: quoteLetterType,
                    option: 0,
                    customer:this.quoteLetterForm.customer,
                    parentPath: this.fullPath
                }
                this.$router.push({
                    path: '/xsgs_process_manager/xsgs_quote_letter_applove/quoteLetterInfo/:id',
                    props: true,
                    query: {...query}
                })
                this.isChooseType = false;
            },
            newLetterTemplate() {


            },

            editQuoteLetter(d) {
                let query = {
                    quoteLetterStoreId: this.quoteLetterStoreId,
                    letterType: d.letterType,
                    option: 1,
                    cutomer:this.quoteLetterForm.customer,
                    parentPath: this.fullPath
                }
                this.$router.push({
                    path: '/xsgs_process_manager/xsgs_quote_letter_applove/quoteLetterInfo/' + d.id,
                    props: true,
                    query: {...query}
                })

            },
            viewQuoteLetter(d) {
                let query = {
                    quoteLetterStoreId: this.quoteLetterStoreId,
                    letterType: d.letterType,
                    option: 2,
                    cutomer:this.quoteLetterForm.customer,
                    parentPath: this.fullPath
                }
                this.$router.push({
                    path: '/xsgs_process_manager/xsgs_quote_letter_applove/quoteLetterView/' + d.id,
                    props: true,
                    query: {...query}
                })

            },
            downloadLetter(d){
                exportExcel(DOWNLOAD_LETTER,{id:d.id,quoteLetterStoreId:this.quoteLetterStoreId},d.letterName + '.doc');
            },
            deleteQuote(d) {
                let that = this;
                this.$confirm({
                    content: `是否确认删除？`,
                    onOk: () => {
                        let newData = [...that.quoteLetterForm.quoteLetterList];
                        if (d.option == 0) {
                            newData = newData.filter(s => s.id != d.id);
                            that.quoteLetterForm.quoteLetterList = newData;
                        } else {
                            newData.forEach(s => {
                                if (s.id == d.id) {
                                    s["option"] = 2;
                                    s["hasChange"] = 1;
                                }
                            });
                            that.quoteLetterForm.quoteLetterList = newData;
                        }
                    },
                });

            },
            changeRemark(d) {
                let newData = [...this.quoteLetterForm.quoteLetterList];
                newData.forEach(s => {
                    if (s.id == d.id) {
                        s['hasChange'] = 1;
                        s['option'] = 1;
                    }
                });
                this.quoteLetterForm.quoteLetterList = newData;
            },
            changeLetterName(d) {
                let newData = [...this.quoteLetterForm.quoteLetterList];
                newData.forEach(s => {
                    if (s.id == d.id) {
                        s['hasChange'] = 1;
                        s['option'] = 1;
                    }
                });
                this.quoteLetterForm.quoteLetterList = newData;
            },

            /** 上传 */
            handleUploadExcel(info) {
                this.loading = true
                if (info.file.status !== 'uploading') {
                    // this.getData()
                }
                if (info.file.status === 'done') {
                    this.$message.success(`${info.file.name} 导入成功`);
                    this.loading = false
                } else if (info.file.status === 'error') {
                    this.loading = false
                    this.$message.error(`${info.file.name} 导入失败`);
                }
            },
            /** 导出按钮操作 */
            handleDownloadTmp() {
                exportExcel(DOWNLOAD_TEMPLATE, {}, "更改配置批量导入模板.xlsx")
            },
            checkCustomer(isNotRemind){
                return true;
            },
            uploadSuccess(res){
                console.log(res);
                let annexList = [];
                annexList.push(res);
                if (res.refId) {
                    this.quoteLetterForm.quoteLetterList.forEach(s=>{
                        if(s.id == res.refId) {
                            if (!s['letterName']) {
                                s['letterName'] = res.name;
                            }
                            s['annexList'] = annexList;
                            s['option'] = 1;
                            s['hasChange'] = 1;
                        }
                    });
                } else {
                    let item = {
                        id:this.quoteLetterForm.quoteLetterList.length + Math.round(Math.random() * 1000) + 1,
                        letterType:"OTHERS",
                        letterName:res.name,
                        option:0,
                        hasChange:1,
                        annexList:annexList
                    };
                    this.quoteLetterForm.quoteLetterList.push(item);
                }
                this.isChooseType = false;
            },
            getQuoteLetterInfo() {
                return this.quoteLetterForm;
            },
            isNotDrafted() {
                return this.isView || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'drafted';
            },
            isArchive() {
                return !this.isView && this.form.nodeInfo.nodeCode && this.form.nodeInfo.nodeCode == 'archive';
            },

            coverQuoteLetterType(quoteLetterType) {
                if (quoteLetterType == 'PS_YN') {
                    return "显示零件价格，无供应商";
                } else if (quoteLetterType == 'PST_YNY') {
                    return "显示零件价格，无供应商，含税价格";
                } else if (quoteLetterType == 'PS_NN') {
                    return "不显示零件价格，无供应商";
                } else if (quoteLetterType == 'PS_NY') {
                    return "不显示零件价格，显示供应商";
                } else if (quoteLetterType == 'NSS_YYY') {
                    return "基本型+选配件合计，显示供应商";
                } else if (quoteLetterType == 'NSS_YYN') {
                    return "基本型+选配件合计，无供应商";
                } else if (quoteLetterType == 'CP_YN') {
                    return "更改配置报价模板(不含税价格)";
                }  else if (quoteLetterType == 'CP_YY') {
                    return "更改配置报价模板(含税价格)";
                }else {
                    return "其他";
                }

            },
            checkRequired(form, field, isNotRemind) {
                if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
                    let value = form[field];
                    if (!value) {
                        this.formRule[field].border = this.requiredBorder;
                        if (!isNotRemind) {
                            this.$message.error(this.formRule[field].msg);
                        }
                        return false;
                    }
                }
                this.formRule[field].border = this.normalBorder;
                return true
            },
            isNotVE() {
                return this.isView || !this.form.nodeInfo || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'value_engine_approval';
            },
        }
    }
</script>

<style scoped>
    /deep/ .fontSmall td,
    .fontSmall th,
    .fontSmall input,
    .fontSmall textarea,
    .fontSmall select,
    .fontSmall a,
    .fontSmall button {
        font-size: 12px !important;
    }

    /deep/ .fontSmall tr,
    .fontSmall td,
    .fontSmall th {
        height: 22px !important;
    }
    /deep/ .fontMiddle td,
    .fontMiddle th,
    .fontMiddle input,
    .fontMiddle textarea,
    .fontMiddle select,
    .fontMiddle a,
    .fontMiddle button {
        font-size: 14px !important;
    }

    /deep/ .fontMiddle tr,
    .fontMiddle td,
    .fontMiddle th {
        height: 28px !important;
    }

    /deep/ .fontLarge td,
    .fontLarge th,
    .fontLarge input,
    .fontLarge textarea,
    .fontLarge select,
    .fontLarge a,
    .fontLarge button {
        font-size: 18px !important;
    }

    /deep/ .fontLarge tr,
    .fontLarge td,
    .fontLarge th {
        height: 33px !important;
    }
    /deep/ .ant-select-selection__placeholder {
        padding-left: 3px;
        width: 100%;
    }

    /deep/ .ant-table .ant-select-selection-selected-value {
        padding-left: 3px;
    }

    /deep/ .ant-table .ant-select-selection__rendered {
        margin: 0 !important;
    }

    /deep/ .ant-table input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
    }

    /deep/ .ant-table input[type="number"] {
        -moz-appearance: textfield !important;
    }

    /deep/ .annexTd > div {
        float: left !important;
    }

    /deep/ .ant-table > tr {
        height: 33px;
    }

    /deep/ .quoteLetterModelStyle .ant-modal-body {
        padding: 16px 24px !important;
    }

    /deep/ .quoteLetterTypeList>p {
        padding: 3px 0 3px 16px;
        margin-bottom: 8px;
        text-align: left;
        font-size: 16px;
        border: 1px solid;
    }
    /deep/ .quoteLetterTypeList>p>a {
        color: rgba(0, 0, 0, 0.65);
    }
</style>
