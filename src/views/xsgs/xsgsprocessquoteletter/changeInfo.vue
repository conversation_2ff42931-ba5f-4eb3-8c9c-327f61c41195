<template>
    <div>
        <a-spin :spinning="spinning">
        <a-row style="margin-bottom: 5px">
            <a-col :span="24">
                <a-button type="primary" @click="handleDownloadTmp">
                    <a-icon type="download"/>
                    下载导入模板
                </a-button>
                <a-upload
                        name="file"
                        :action="UPLOAD_LETTER_UPLOADCONF"
                        :headers="{'Authorization':Cookie.get('Authorization'),'letterType':letterForm.letterType}"
                        :showUploadList="false"
                        style="margin-left: 8px"
                        @change="handleUploadExcel">
                    <a-button type="primary">
                        <a-icon type="upload"/>
                        批量导入
                    </a-button>
                </a-upload>
            </a-col>
        </a-row>

        <!-- 7.更改配置报价模板(不含税价格) -->
        <table v-if="letterForm.letterType=='CP_YN' && !isGetting" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 12%">客户物料</th>
                <th style="width: 12%">产品型号</th>
                <th style="width: 14%">供货号</th>
                <th style="width: 12%">原价格</th>
                <th style="width: 12%">成本变动</th>
                <th style="width: 12%">现价格</th>
                <th style="width: 12%">备注</th>
                <th style="width: 6%" >操作</th>
            </tr>
            <tr v-for="(d,index) in infoList" :key="d.id">
                <td style="text-align: center">{{index + 1}}</td>
                <td>
                    <a-input v-model="d.materialNo" />
                </td>
                <td>
                    <a-input v-model="d.productModel" />
                </td>
                <td>
                    <a-input v-model="d.machineType" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.orgPrice" step="0.01" :precision="2" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.costChange" step="0.01" :precision="2" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.nowPrice" step="0.01" :precision="2" />
                </td>
                <td>
                    <textarea :style="`margin: 0;padding: 0 0 0 3px;height: 33px;min-height: 33px;float:left`"
                              class="xsgs-textarea"
                                v-model="d.remark"></textarea>
                </td>
                <td style="text-align: center"><a @click="deleteDetail(d)">删除</a></td>
            </tr>
            <tr>
                <td colspan="9">
                    <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newDetail">新增
                    </a-button>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 8.更改配置报价模板(含税价格) -->
        <table v-if="letterForm.letterType=='CP_YY'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 6%" >序号</th>
                <th style="width: 12%">客户物料</th>
                <th style="width: 12%">产品型号</th>
                <th style="width: 10%">供货号</th>
                <th style="width: 10%">原价格</th>
                <th style="width: 12%">成本变动</th>
                <th style="width: 10%">现价格</th>
                <th style="width: 12%">含税整机价格</th>
                <th style="width: 10%">备注</th>
                <th style="width: 6%" >操作</th>
            </tr>
            <tr v-for="(d,index) in infoList" :key="d.id">
                <td style="text-align: center">{{index + 1}}</td>
                <td>
                    <a-input v-model="d.materialNo" />
                </td>
                <td>
                    <a-input v-model="d.productModel" />
                </td>
                <td>
                    <a-input v-model="d.machineType" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.orgPrice" step="0.01" :precision="2" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.costChange" step="0.01" :precision="2" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.nowPrice" step="0.01" :precision="2" />
                </td>
                <td>
                    <a-input-number style="width: 100%" v-model="d.taxUnitPrice" step="0.01" :precision="2" />
                </td>
                <td>
                    <textarea :style="`margin: 0;padding: 0 0 0 3px;height: 33px;min-height: 33px;float:left`"
                                class="xsgs-textarea" v-model="d.remark"></textarea>
                </td>
                <td style="text-align: center"><a @click="deleteDetail(d)">删除</a></td>
            </tr>
            <tr>
                <td colspan="10">
                    <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newDetail">新增
                    </a-button>
                </td>
            </tr>
            </tbody>
        </table>
        </a-spin>
    </div>

</template>

<script>
    import {
        DOWNLOAD_LETTER_CONFI_TEMPLATE,
        UPLOAD_LETTER_UPLOADCONF
    } from "@/services/api/xsgs";
    import {METHOD, request,exportExcel} from "@/utils/request";
    import Cookie from "js-cookie";

    export default {
        name: "changeInfo",
        props: {
            letterForm: Object,
            form:Object,
            isView:{
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                UPLOAD_LETTER_UPLOADCONF,
                Cookie,
                spinning:false,
                isGetting:false,
                infoList:[],
            }
        },
        created() {

        },
        mounted() {
        },
        methods: {
            initData(list){
                this.infoList = this.copy(list);
            },
            newDetail(){
                let newData={id:this.letterForm.infoList.length + Math.round(Math.random() * 1000) + 1}
                this.infoList.push(newData);
            },
            deleteDetail(d){
                let newData = this.infoList.filter(s=>s.id != d.id);
                this.infoList = newData;
            },
            getInfoList(){
                return this.infoList;
            },
            /** 上传 */
            handleUploadExcel(info) {
                this.spinning = true
                if (info.file.status !== 'uploading') {
                    // this.getData()
                }
                if (info.file.status === 'done') {
                    this.spinning = false
                    this.$message.success(`${info.file.name} 导入成功`);
                    let data = info.file.response.data.data;
                    this.infoList = data;
                    this.spinning = false
                    let errors = info.file.response.data.errors;
                } else if (info.file.status === 'error') {
                    this.spinning = false
                    this.$message.error(`${info.file.name} 导入失败`);
                }
            },
            /** 导出按钮操作 */
            handleDownloadTmp() {
                let fileName = "更改配置报价模板（含税价格）";
                if (this.letterForm.letterType == "CP_YN") {
                    fileName = "更改配置报价模板（不含税价格）";
                }
                exportExcel(DOWNLOAD_LETTER_CONFI_TEMPLATE, {letterType: this.letterForm.letterType}, fileName + ".xlsx")
            },
        },
    }
</script>

<style scoped>

    /deep/ .letterPriceTable tr td,th {
        font-family: '等线' !important;
    }

    td {
        border: 0.5px solid;
    }
    /deep/ .detailTable {
        width: 100%;
        margin: 0;
    }
    /deep/ .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/.detailTable>tbody>tr>th:first-child {
        border-left: none !important;
    }
    /deep/ .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/ .detailTable>tbody>tr>td:first-child {
        border-left: none !important;
    }
    /deep/ .detailTable>tbody>tr>td:last-child {
        border-right: none !important;
    }
    /deep/ .detailTable>tbody>tr>th:last-child {
        border-right: none !important;
    }
    /deep/ .detailTable>tbody>tr:last-child td {
        border-bottom: none !important;
    }

</style>
