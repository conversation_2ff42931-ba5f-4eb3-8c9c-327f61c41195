<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
                <a-row :gutter="10" type="flex" justify="space-around" align="middle">
                    <a-col :span="6">
                        <a-form-item label="年度" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-input v-model="searchForm.processYear" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="客户" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-select v-model="searchForm.customerNameList" show-search
                                :default-active-first-option="false" :show-arrow="false" mode="multiple"
                                :filter-option="false" :not-found-content="null" :allowClear='true'
                                @search="handleCustomerSearch" @change="handleCustomerChange">
                                <a-select-option v-for="d in customerList" :key="d?.customer + '-' + d?.customerName">
                                    {{ d?.customer + '-' + d?.customerName }}
                                </a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="类型" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-select v-model="searchForm.searchType" :allowClear="true">
                                <a-select-option value="0">全部</a-select-option>
                                <a-select-option value="1">待处理</a-select-option>
                                <a-select-option value="2">已处理</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <div style="display: flex;align-items: center;">
                            <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon
                                    type="search" />查询</a-button>
                            <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon
                                    type="reload" />重置</a-button>
                            <a @click="advanced ? advanced = false : advanced = true" style="margin-left: 8px;flex:1;">
                                {{ advanced ? '收起' : '展开' }}
                                <a-icon :type="advanced ? 'up' : 'down'" />
                            </a>
                        </div>
                    </a-col>
                </a-row>
                <a-row style="transition: all .2s;" :class="advanced ? 'searchOpen' : 'searchColl'" type="flex"
                    :gutter="10">
                    <a-col :span="6">
                        <a-form-item label="创建日期" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-date-picker style="width: 100%;" value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                placeholder="" v-model="searchForm.createDate" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="主题" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-input v-model="searchForm.title" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="作者" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-input v-model="searchForm.authorName" />
                        </a-form-item>
                    </a-col>
                    <a-col :span="6">
                        <a-form-item label="环节" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-select v-model="searchForm.nodeCode" :allowClear="true">
                                <a-select-option v-for="d in flowNodeList" :key="d.nodeCode">{{ d.nodeDisplayName
                                    }}</a-select-option>
                            </a-select>
                        </a-form-item>
                    </a-col>
                    <!-- <a-col :span="6">
                        <a-form-item label="流程编号" :label-col="formItemLayout.labelCol"
                            :wrapper-col="formItemLayout.wrapperCol">
                            <a-input v-model="searchForm.processNumber" />
                        </a-form-item>
                    </a-col> -->

                </a-row>
                <!-- <table style="width: 100%" class="conditionArea">
                    <tbody>
                        <tr>
                            <th>年度：</th>
                            <td style="text-align: left">
                                <a-input v-model="searchForm.processYear"/>
                            </td>
                            <th>客户：</th>
                            <td colspan="3" style="max-width: 300px;min-width: 300px">
                                <a-select
                                        style="width: 100%;"
                                        v-model="searchForm.customerNameList"
                                        show-search
                                        :default-active-first-option="false"
                                        :show-arrow="false"
                                        mode="multiple"
                                        :filter-option="false"
                                        :not-found-content="null"
                                        :allowClear='true'
                                        @search="handleCustomerSearch"
                                        @change="handleCustomerChange"
                                >
                                    <a-select-option v-for="d in customerList" :key="d.customerName">
                                        {{ d.customer + '-' +d.customerName }}
                                    </a-select-option>
                                </a-select>
                            </td>
                            <th>类型：</th>
                            <td>
                                <a-select v-model="searchForm.searchType" style="min-width: 60px"
                                          :allowClear="true">
                                    <a-select-option value="0">全部</a-select-option>
                                    <a-select-option value="1">待处理</a-select-option>
                                    <a-select-option value="2">已处理</a-select-option>
                                </a-select>
                            </td>
                            <td colspan="3" style="width:25%;text-align: left">
                                <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
                                <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
                                <a @click="advanced?advanced=false:advanced=true" style="margin-left: 8px">
                                    {{ advanced ? '收起' : '展开' }}
                                    <a-icon :type="advanced ? 'up' : 'down'"/>
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th></th>
                            <td style="min-width: 120px">


                            </td>
                            <th></th>
                            <td>

                            </td>
                            <th></th>
                            <td colspan="3">

                            </td>
                            <th></th>
                            <td>

                            </td>
                            <td></td>
                        </tr>

                        <tr v-show="advanced">
                            <th>创建日期：</th>
                            <td style="min-width: 120px">
                                <a-date-picker  value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                                               placeholder=""
                                               v-model="searchForm.createDate"/>

                            </td>
                            <th>作者：</th>
                            <td>
                                <a-input v-model="searchForm.authorName"/>
                            </td>
                            <th>主题：</th>
                            <td colspan="3">
                                <a-input v-model="searchForm.title"/>
                            </td>
                            <th>环节：</th>
                            <td>
                                <a-select v-model="searchForm.nodeCode" style="min-width: 60px"
                                          :allowClear="true">
                                    <a-select-option v-for="d in flowNodeList" :key="d.nodeCode">{{d.nodeDisplayName}}</a-select-option>
                                </a-select>
                            </td>
                            <td></td>
                        </tr>
                    </tbody>
                </table> -->
            </a-form-model>
            <br>
            <a-table :columns="columns" rowKey="id" :pagination="pagination" :data-source="data" :scroll="{ x: 1000 }"
                size="small" bordered :customRow="onRowClick" :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{ index + 1 }}
                </template>
                <template slot="createTime" slot-scope="text, record">
                    {{ dateFormat(record.createTime) }}
                </template>
                <template slot="preSubmitTime" slot-scope="text, record">
                    {{ dateFormat(record.preSubmitTime) }}
                </template>
                <template slot="process" slot-scope="text, record">
                    {{ coverProcess(record.process) }}
                </template>
                <template slot="processTitle" slot-scope="text, record">
                    <a style="cursor: pointer" @click="openProcess(record)">{{ record.title }}</a>
                </template>
                <template slot="isRelease" slot-scope="text, record">
                    {{ (record.isRelease && record.isRelease == '1') ? "是" : "否" }}
                </template>
                <template slot="archiveTime" slot-scope="text, record">
                    {{ dateFormat(record.archiveTime) }}
                </template>

            </a-table>
        </a-card>

    </div>

</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import { LIST_QUOTE_LETTER_LEDGER_PAGE, LIST_FLOW_NODE, LEDGER_CUSTOMER_LIST } from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import { dateFormat } from "@/utils/dateUtil";

export default {
    name: "quoteLetterLedger.vue",
    data() {
        return {
            hasAuth,
            loading: false,
            advanced: false,
            searchForm: {
                processYear: null,
                process: "quoteLetter",
                title: null,
                taskState: null,
                searchType: null,
                isAllSearch: null,
                customer: null,
                customerName: null,
                isRelease: null,
                isArchive: null,
                nodeCode: null,
                createDate: null,
                authorName: null
            },
            isHasAllAuth: false,
            flowNodeList: [],
            customerList: [],
            allCustomerList: [],
            ApprovalOption: {
                WAITING: '保存', SUBMIT: '提交', BACK: '退回', HANGUP: '挂起', TERMINATE: '终止', COMPLETE: '完成',
            },
            pagination: {
                total: 0,
                current: 1,
                size: '10',
                showSizeChanger: true, // 是否可以改变 size
                showQuickJumper: true, // 是否可以快速跳转至某页
                pageSizeOptions: ['10', '20', '30', '40', '50'],
                showTotal: (total) =>
                    `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                        total / this.pagination.size
                    )}页`, // 显示总数
                onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
                onShowSizeChange: (current, size) =>
                    this.onSizeChange(current, size) // 改变每页数量时更新显示
            },
            data: [],
            columns: [
                {
                    title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                        customRender: 'num'
                    }
                },
                // {title: '客户号', width:80, dataIndex: 'customer'},
                // {title: '客户名称', width:280, dataIndex: 'customerName'},
                { title: '主题', dataIndex: 'title', scopedSlots: { customRender: 'processTitle' } },
                { title: '起草日期', align: 'center', dataIndex: 'createTime', width: 150, scopedSlots: { customRender: 'createTime' } },
                { title: '作者', align: 'center', width: 280, dataIndex: 'author' },
                { title: '当前环节', align: 'center', width: 150, dataIndex: 'nodeName' },
                { title: '归档时间', align: 'center', width: 150, dataIndex: 'archiveTime', scopedSlots: { customRender: 'archiveTime' } },
            ],
            rules: {},
            formItemLayout: {
                labelCol: { span: 8 },
                wrapperCol: { span: 16 }
            },
        }
    },
    async mounted() {
        let res = await this.getFlowNodeList();
        this.flowNodeList = res.data.data;

        this.getCustomerList();
        this.getData();
        this.checkName();
    },
    methods: {

        onSelectChange(selectedRowKeys) {
            this.selectedRowKeys = selectedRowKeys;
        },

        getData: function () {
            this.loading = true
            let { current, size } = this.pagination
            request(LIST_QUOTE_LETTER_LEDGER_PAGE, METHOD.POST, { ...this.searchForm, current, size })
                .then(res => {
                    const { records, total } = res.data.data
                    this.loading = false
                    this.data = records
                    this.pagination.total = parseInt(total)
                })
        },
        async getFlowNodeList() {
            return request(LIST_FLOW_NODE, METHOD.GET, { templateCode: this.searchForm.process });
        },
        handleSearch() {
            this.pagination.current = 1
            this.getData()
        },
        openProcess(record) {
            let url = "/xsgs_process_manager/xsgs_quote_letter_applove/" + record.process + "/" + record.processId;
            this.$router.push({ path: url, props: true })
        },
        getCustomerList() {
            request(LEDGER_CUSTOMER_LIST, METHOD.POST, this.searchForm).then(res => {
                const map = new Map()
                this.allCustomerList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1) && key.customerName !== undefined)
                this.customerList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1) && key.customerName !== undefined)
            })
        },
        handleCustomerSearch(value) {
            this.allCustomerList = this.allCustomerList.filter(x => {
                if(x.customer){
                    return {
                    customerName: x?.customerName || '',
                    customer: x.customer
                }
                }
                
            })
            if (value) {
                this.customerList = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
            } else {
                this.customerList = this.allCustomerList;
            }

        },
        handleCustomerChange(value) {
            console.log('点击展开', value)
            if (!value) {
                this.customerList = this.allCustomerList;
            }
        },

        /** 重置按钮操作 */
        resetQuery() {
            this.searchForm = {
                process: 'quoteLetter'
            }
            this.handleSearch();
        },

        onPageChange(page, size) {
            this.pagination.current = page;
            this.pagination.size = size.toString();
            this.getData();
        },
        onSizeChange(current, size) {
            this.pagination.current = 1;
            this.pagination.size = size.toString();
            this.getData();
        },

        dateFormat(date) {
            if (!date) {
                return '';
            }
            return dateFormat(date);
        },
        coverProcess(process) {
            let p = this.processList.filter(item => item.value == process);
            if (p.length > 0) {
                return p[0].label;
            } else {
                return process;
            }

        },
        coverOpt(opt) {
            return this.ApprovalOption[opt] ? this.ApprovalOption[opt] : '';
        }
    },

}
</script>

<style scoped>
.ant-input-number {
    width: 100%;
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
}

/*/deep/  样式穿透
    */
/*/deep/.ant-col-8{*/
/*    display: block;*/
/*    -webkit-box-sizing: border-box;*/
/*    box-sizing: border-box;*/
/*    width: 100%;*/
/*}*/
/deep/.ant-form-item {
    text-align: right;
    margin: 0;
}

/deep/.ant-form>table>tbody>tr>td {
    border: 1px solid #f0f0f0;
}

/deep/ .customerLabel {
    width: 60%;
}

/deep/ .customerLabel .ant-form-item-label {
    width: 13.8%;
}

/deep/ #optionBudget :after {
    -webkit-appearance: none !important;
}

/deep/ #year::-webkit-outer-spin-button,
#year::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: textfield;
}

/deep/input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    -moz-appearance: textfield;
}

/deep/.ant-input-number-handler-wrap {
    display: none;
}

/deep/ .searchUser .ant-form-item-control-wrapper {
    text-align: left;
}

/deep/ .buttonArea .ant-form-item-control-wrapper {
    width: 100%;
}

/deep/ .advancedRow .ant-col-8 {
    width: 22%;
}

/deep/ .conditionArea tbody>tr>th {
    width: 100px;
    font-weight: normal !important;
    text-align: right;
    color: rgba(0, 0, 0, 0.85);
    background-clip: padding-box;
}

/deep/ .conditionArea tbody>tr>td {
    width: 10%;
    font-weight: normal !important;
    text-align: right;
    border: none !important;
    background-clip: padding-box;
}

.searchOpen {
    height: 50px;
}

.searchColl {
    height: 0;
    overflow: hidden;
}
</style>
