<template>
    <div>
        <!-- 1.显示零件价格，无供应商 -->
        <table v-if="letterForm.letterType=='PS_YN'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 24%">产品型号</th>
                <th style="width: 24%">供货号</th>
                <th style="width: 24%">客户物料</th>
                <th style="width: 20%">整机价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 24%;text-align: center">
                    <a-select style="text-align: center"
                               v-model="priceForm.productModel"
                               show-search
                               :allowClear="true"
                               :default-active-first-option="false"
                               :show-arrow="false"
                               :filter-option="false"
                               :not-found-content="null"
                               @dropdownVisibleChange="productModelSearch()"
                               @search="productModelSearch"
                               @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-select
                               v-model="priceForm.machineType"
                               show-search
                               :default-active-first-option="false"
                               :show-arrow="false"
                               :filter-option="false"
                               :not-found-content="null"
                               :allowClear="true"
                               @dropdownVisibleChange="machineTypeSearch()"
                               @search="machineTypeSearch"
                               @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 20%">
                    <a-select v-if="!priceGetting"
                            v-model="priceForm.mainId"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
            </tr>
            <tr>
                <th colspan="4">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="4" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 20%" colspan="2">配置名称</th>
                            <th style="width: 10%">规格</th>
                            <th style="width: 12%">是否配置</th>
                            <th style="width: 12%">配置价格</th>
                            <th style="width: 20%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox  v-if="!checking" :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 20%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 10%;text-align: center">{{d.specs}}</td>
                            <td style="width: 12%;text-align: center">{{coverConfig(d)}}</td>
                            <td style="width: 12%;text-align: center">{{coverPrice(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="6" style="text-align: center">合计：</td>
                            <td style="text-align: center">{{priceForm.partsTotal}}</td>
                            <td v-if="!priceForm || !priceForm.parts || priceForm.parts.length == 0">
                                <a-textarea class="remarkText"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 2.显示零件价格，无供应商，含税价格 -->
        <table v-if="letterForm.letterType == 'PST_YNY'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 19%">产品型号</th>
                <th style="width: 19%">供货号</th>
                <th style="width: 19%">客户物料</th>
                <th style="width: 19%">含税整机价格</th>
                <th style="width: 16%">整机价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 19%;text-align: center">
                    <a-select style="text-align: center"
                              v-model="priceForm.productModel"
                              show-search
                              :allowClear="true"
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              @dropdownVisibleChange="productModelSearch()"
                              @search="productModelSearch"
                              @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 19%">
                    <a-select
                            v-model="priceForm.machineType"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @dropdownVisibleChange="machineTypeSearch()"
                            @search="machineTypeSearch"
                            @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 19%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 19%">
                    <a-input v-model="priceForm.taxUnitPrice"/>
                </td>
                <td style="width: 16%">
                    <a-select v-if="!priceGetting"
                              v-model="priceForm.mainId"
                              show-search
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              :allowClear="true"
                              @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
            </tr>
            <tr>
                <th colspan="5">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="5" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 20%" colspan="2">配置名称</th>
                            <th style="width: 10%">规格</th>
                            <th style="width: 12%">是否配置</th>
                            <th style="width: 12%">配置价格</th>
                            <th style="width: 20%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox v-if="!checking" :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 20%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 10%;text-align: center">{{d.specs}}</td>
                            <td style="width: 12%;text-align: center">{{coverConfig(d)}}</td>
                            <td style="width: 12%;text-align: center">{{coverPrice(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="6" style="text-align: center">合计：</td>
                            <td style="text-align: center">{{priceForm.partsTotal}}</td>
                            <td v-if="!priceForm || !priceForm.parts || priceForm.parts.length == 0">
                                <a-textarea class="remarkText" v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 3.不显示零件价格，无供应商 -->
        <table v-if="letterForm.letterType=='PS_NN'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 24%">产品型号</th>
                <th style="width: 24%">供货号</th>
                <th style="width: 24%">客户物料</th>
                <th style="width: 20%">整机价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 24%;text-align: center">
                    <a-select style="text-align: center"
                              v-model="priceForm.productModel"
                              show-search
                              :allowClear="true"
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              @dropdownVisibleChange="productModelSearch()"
                              @search="productModelSearch"
                              @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-select
                            v-model="priceForm.machineType"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @dropdownVisibleChange="machineTypeSearch()"
                            @search="machineTypeSearch"
                            @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 20%">
                    <a-select v-if="!priceGetting"
                              v-model="priceForm.mainId"
                              show-search
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              :allowClear="true"
                              @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
            </tr>
            <tr>
                <th colspan="4">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="4" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 20%" colspan="2">配置名称</th>
                            <th style="width: 14%">规格</th>
                            <th style="width: 16%">是否配置</th>
                            <th style="width: 26%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 20%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 14%;text-align: center">{{d.specs}}</td>
                            <td style="width: 16%;text-align: center">{{coverConfig(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 4.不显示零件价格，显示供应商 -->
        <table v-if="letterForm.letterType=='PS_NY'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 24%">产品型号</th>
                <th style="width: 24%">供货号</th>
                <th style="width: 24%">客户物料</th>
                <th style="width: 20%">整机价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 24%;text-align: center">
                    <a-select style="text-align: center"
                              v-model="priceForm.productModel"
                              show-search
                              :allowClear="true"
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              @dropdownVisibleChange="productModelSearch()"
                              @search="productModelSearch"
                              @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-select
                            v-model="priceForm.machineType"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @dropdownVisibleChange="machineTypeSearch()"
                            @search="machineTypeSearch"
                            @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 24%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 20%">
                    <a-select v-if="!priceGetting"
                              v-model="priceForm.mainId"
                              show-search
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              :allowClear="true"
                              @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
            </tr>
            <tr>
                <th colspan="4">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="4" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 20%" colspan="2">配置名称</th>
                            <th style="width: 10%">规格</th>
                            <th style="width: 12%">供应商</th>
                            <th style="width: 12%">是否配置</th>
                            <th style="width: 20%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 20%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 10%;text-align: center">{{d.specs}}</td>
                            <td style="width: 12%;text-align: center">{{d.supplierBak}}</td>
                            <td style="width: 12%;text-align: center">{{coverConfig(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 5.基本型+选配件合计，显示供应商 -->
        <table v-if="letterForm.letterType=='NSS_YYY'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 18%">产品型号</th>
                <th style="width: 18%">供货号</th>
                <th style="width: 18%">客户物料</th>
                <th style="width: 20%">整机价格</th>
                <th style="width: 18%">基本型价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 18%;text-align: center">
                    <a-select style="text-align: center"
                              v-model="priceForm.productModel"
                              show-search
                              :allowClear="true"
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              @dropdownVisibleChange="productModelSearch()"
                              @search="productModelSearch"
                              @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-select
                            v-model="priceForm.machineType"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @dropdownVisibleChange="machineTypeSearch()"
                            @search="machineTypeSearch"
                            @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 20%">
                    <a-select v-if="!priceGetting"
                              v-model="priceForm.mainId"
                              show-search
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              :allowClear="true"
                              @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-input readOnly v-if="!priceGetting" v-model="priceForm.basePrice"/>
                </td>
            </tr>
            <tr>
                <th colspan="5">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="5" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 16%" colspan="2">配置名称</th>
                            <th style="width: 10%">规格</th>
                            <th style="width: 10%">供应商</th>
                            <th style="width: 12%">是否配置</th>
                            <th style="width: 12%">配置价格</th>
                            <th style="width: 14%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 16%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 10%;text-align: center">{{d.specs}}</td>
                            <td style="width: 10%;text-align: center">{{d.supplierBak}}</td>
                            <td style="width: 12%;text-align: center">{{coverConfig(d)}}</td>
                            <td style="width: 12%;text-align: center">{{coverPrice(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="7" style="text-align: center">合计：</td>
                            <td style="text-align: center">{{priceForm.partsTotal}}</td>
                            <td v-if="!priceForm || !priceForm.parts || priceForm.parts.length == 0">
                                <a-textarea class="remarkText" v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <!-- 6.基本型+选配件合计，无供应商 -->
        <table v-if="letterForm.letterType=='NSS_YYN'" class="letterPriceTable">
            <tbody>
            <tr>
                <th style="width: 8%" >序号</th>
                <th style="width: 18%">产品型号</th>
                <th style="width: 18%">供货号</th>
                <th style="width: 18%">客户物料</th>
                <th style="width: 20%">整机价格</th>
                <th style="width: 18%">基本型价格</th>
            </tr>
            <tr>
                <td style="width: 8%;text-align: center" rowspan="3">{{num+1}}</td>
                <td style="width: 18%;text-align: center">
                    <a-select style="text-align: center"
                              v-model="priceForm.productModel"
                              show-search
                              :allowClear="true"
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              @dropdownVisibleChange="productModelSearch()"
                              @search="productModelSearch"
                              @change="changeProductModel"
                    >
                        <a-select-option v-for="str in productModelList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-select
                            v-model="priceForm.machineType"
                            show-search
                            :default-active-first-option="false"
                            :show-arrow="false"
                            :filter-option="false"
                            :not-found-content="null"
                            :allowClear="true"
                            @dropdownVisibleChange="machineTypeSearch()"
                            @search="machineTypeSearch"
                            @change="changedMachineType"
                    >
                        <a-select-option v-for="str in machineTypeList" :key="str">
                            {{ str }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-input v-if="!materialGetting" v-model="priceForm.materialNo"/>
                </td>
                <td style="width: 20%">
                    <a-select v-if="!priceGetting"
                              v-model="priceForm.mainId"
                              show-search
                              :default-active-first-option="false"
                              :show-arrow="false"
                              :filter-option="false"
                              :not-found-content="null"
                              :allowClear="true"
                              @change="changePrice"
                    >
                        <a-select-option v-for="d in priceList" :key="d.masterId">
                            {{ d.unitTicketPrice }}
                        </a-select-option>
                    </a-select>
                </td>
                <td style="width: 18%">
                    <a-input readOnly v-if="!priceGetting" v-model="priceForm.basePrice"/>
                </td>
            </tr>
            <tr>
                <th colspan="5">整机配套清单</th>
            </tr>
            <tr>
                <td colspan="5" style="padding: 0">
                    <table v-if="!partsGetting" class="detailTable">
                        <tbody>
                        <tr>
                            <th style="width: 6%">选择</th>
                            <th style="width: 12%">配置类型</th>
                            <th style="width: 20%" colspan="2">配置名称</th>
                            <th style="width: 10%">规格</th>
                            <th style="width: 12%">是否配置</th>
                            <th style="width: 12%">配置价格</th>
                            <th style="width: 20%">备注</th>
                        </tr>
                        <tr v-for="(d,index) in priceForm.parts" :key="d.id">
                            <td style="width: 6%;text-align: center">
                                <a-checkbox :checked="!d.isDelete || d.isDelete == 0" @change="checkChange($event,d)"/>
                            </td>
                            <td style="width: 12%;text-align: center">{{d.subType}}</td>
                            <td style="width: 20%;text-align: center" colspan="2">{{d.partsName}}</td>
                            <td style="width: 10%;text-align: center">{{d.specs}}</td>
                            <td style="width: 12%;text-align: center">{{coverConfig(d)}}</td>
                            <td style="width: 12%;text-align: center">{{coverPrice(d)}}</td>
                            <td v-if="index == 0" :rowspan="priceForm.parts.length + 1">
                                <a-textarea class="remarkText" :style="`height: ${(priceForm.parts.length+1)*26.2}px;min-height: ${(priceForm.parts.length+1)*26.2}px`"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="5" style="text-align: center">合计：</td>
                            <td style="text-align: center">{{priceForm.partsTotal}}</td>
                            <td v-if="!priceForm || !priceForm.parts || priceForm.parts.length == 0">
                                <a-textarea class="remarkText"
                                            v-model="priceForm.remark"></a-textarea>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

</template>

<script>
    import {
        COMBO_MACHINETYPE,
        COMBO_PRODUCTMODEL,
        LIST_XSGSCUSTOMERMATERIAL,
        GET_PRICE_LIST,
        GET_PARTS_LIST
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";

    export default {
        name: "quoteInfo",
        props: {
            letterForm: Object,
            form:Object,
            num:Number,
            isView:{
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
                loading:false,
                materialGetting:false,
                priceGetting:false,
                partsGetting:false,
                checking:false,
                priceForm:{
                    year:null,
                    productModel:null,
                    machineType:null,
                    materialNo:null,
                    unitPrice:null,
                    basePrice:null,
                    partsTotal:null,
                    parts:[],
                },
                allProductModelList:[],
                productModelList:[],
                allMachineTypeList:[],
                machineTypeList:[],
                priceList:[]

            }
        },
        created() {

        },
        mounted() {

        },
        methods: {
            initData(d){
                this.priceForm = this.copy(d);
                this.getPartsTotal();
                this.getPriceList();
            },
            productModelSearch(value, mct) {
                this.productModelList = [];
                let that = this;
                let machineTypes=[];
                if (mct) {
                    machineTypes.push(mct)
                }
                request(COMBO_PRODUCTMODEL, METHOD.POST, {
                    year: this.letterForm.year + '',
                    customer: this.letterForm.customer,machineType: machineTypes
                }).then(res => {
                    if (res.data.data) {
                        let newData = res.data.data;
                        if (value) {
                            newData = newData.filter(item => item.indexOf(value) != -1);
                        }
                        newData = this.cutList(newData, 0, 20);
                        that.productModelList = newData;
                        if (that.productModelList.length == 1) {
                            that.priceForm.productModel = that.productModelList[0];
                            this.changeProductModel(that.priceForm.productModel);
                        }
                    }
                })
            },
            changeProductModel(value){
                if (!value) {
                    this.priceForm.machineType = null;
                    this.priceForm.materialNo = null;
                    this.priceForm.mainId = null;
                    this.getPartsList();
                    this.getMaterialNo();
                }
                let that = this;
                this.$nextTick(()=>{
                    that.machineTypeSearch();
                });

            },
            machineTypeSearch(value) {
                this.machineTypeList = [];
                let that = this;
                let productModel = [];
                if (this.priceForm.productModel) {
                    productModel.push(this.priceForm.productModel)
                }
                request(COMBO_MACHINETYPE, METHOD.POST, {
                    year: this.letterForm.year + '',
                    customer: this.letterForm.customer,productModel:productModel
                }).then(res => {
                    if (res.data.data) {
                        let newData = res.data.data;
                        if (value) {
                            newData = newData.filter(item => item.indexOf(value) != -1);
                        }
                        newData = this.cutList(newData, 0, 20);
                        that.machineTypeList = newData;
                        if (that.machineTypeList.length == 1) {
                            that.priceForm.machineType = that.machineTypeList[0];
                            this.changedMachineType(that.priceForm.machineType);
                        }
                    }
                })
            },

            changedMachineType(value){
                let that = this;
                if (value) {
                    if (!this.priceForm.productModel) {
                        this.productModelSearch(null, value);
                    } else {
                        this.$nextTick(()=>{
                            that.getMaterialNo();
                        });
                        this.$nextTick(()=>{
                            that.getPriceList();
                        });
                    }
                } else {
                    this.materialGetting = true;
                    this.priceGetting = true;
                    this.partsGetting = true;
                    this.$nextTick(()=>{
                        that.priceForm.machineType = null;
                        that.priceForm.materialNo = null;
                        that.priceForm.productModel = null;
                        that.priceForm.mainId = null;
                        that.priceList.unitPrice = null;
                        that.priceList.basePrice = null;
                        that.priceList = [];
                        that.priceForm.parts = [];
                        this.materialGetting = false;
                        this.priceGetting = false;
                        this.partsGetting = false;
                        this.getPartsTotal();
                    });

                }
            },
            getMaterialNo(){
                if (this.priceForm.machineType) {
                    let that = this;
                    this.materialGetting = true;
                    request(LIST_XSGSCUSTOMERMATERIAL, METHOD.POST, {customer:this.letterForm.customer,machineType:this.priceForm.machineType}).then(res=>{
                        let list = res.data.data;
                        if (list.length > 0) {
                            let materialNo = list[0].materialNo;
                            that.priceForm.materialNo = materialNo;
                            // that.$set(this.priceForm,'materialNo', materialNo);
                            // this.form['materialNo'] = materialNo;
                        }
                        this.materialGetting = false;
                    });
                } else {
                    this.priceForm.materialNo = null;
                }

            },
            getPriceList(){
                if (this.priceForm.machineType) {
                    let that = this;
                    this.priceGetting = true;
                    request(GET_PRICE_LIST, METHOD.GET, {year:this.letterForm.year,customer:this.letterForm.customer,machineType:this.priceForm.machineType}).then(res=>{
                        let list = res.data.data;
                        that.priceList = list;
                        if (list.length > 0 && !this.priceForm.mainId) {
                            let unitTicketPrice = list[0].unitTicketPrice;
                            let baseTicketPrice = list[0].basicTicketPrice;
                            let mainId = list[0].masterId;
                            that.priceForm.unitPrice = unitTicketPrice;
                            that.priceForm.basePrice = baseTicketPrice;
                            that.priceForm.mainId = mainId;
                            that.getPartsList();
                        }
                        this.priceGetting = false;
                    });
                } else {
                    this.$nextTick(()=>{
                        this.priceForm.unitPrice = null;
                        this.priceForm.basePrice = null;
                        this.priceForm.mainId = null;
                        this.priceForm.parts = [];
                        this.getPartsTotal();
                    });

                }
            },
            changePrice(d) {
                if (!d) {
                    this.priceForm.mainId = d;
                    this.priceForm.basePrice = null;
                    this.priceForm.unitPrice = null;
                    this.getPartsList();
                } else {
                    let priceList = this.priceList.filter(s=>(s.masterId+'') === d);
                    if (priceList.length > 0) {
                        let price = priceList[0];
                        let that = this;
                        this.priceGetting = true;
                        this.$nextTick(()=>{
                            that.priceForm.mainId = price.masterId;
                            that.priceForm.basePrice = price.basicTicketPrice;
                            that.priceForm.unitPrice = price.unitTicketPrice;
                            this.priceGetting = false;
                            that.getPartsList();
                        });

                    }
                }
            },
            getPartsList(){
                this.partsGetting = true;
                if (this.priceForm.mainId) {
                    request(GET_PARTS_LIST, METHOD.GET,{mainId:this.priceForm.mainId}).then(res=>{
                        this.priceForm.parts = res.data.data;
                        this.getPartsTotal();
                        this.partsGetting = false;
                    });
                } else {
                    this.priceForm.parts = [];
                    this.getPartsTotal();
                    this.partsGetting = false;
                }

            },
            getPartsTotal(){
                if (this.priceForm && this.priceForm.parts && !this.isEmpty(this.priceForm.parts)) {
                    let total = 0;
                    this.priceForm.parts.forEach(s=>{
                        let price = 0;
                        if (s.subType == '选型件' && s.isSubtraction != '1') {
                            price = this.coverNumber(s.valuePrice);
                        } else {
                            price = this.coverNumber(s.actualPrice);
                        }

                        total = total + price;
                    });
                    this.priceForm.partsTotal = total;
                } else {
                    this.priceForm.partsTotal = null;
                }
            },
            checkChange(e, d){
                this.checking = true;
                this.$nextTick(()=>{
                    if (e.target.checked == true) {
                        d['isDelete'] = 0;
                    } else {
                        d['isDelete'] = 1;
                    }
                    this.checking = false;
                });

            },
            getPriceForm(){
                return this.priceForm;
            },
            coverConfig(d){
                let conf = '是';
                if (d.subType == '选型件') {
                    if (d.isSubtraction == '1') {
                        conf = '否';
                    } else if (this.nullToTrim(d.specs) != this.nullToTrim(d.standardSpecs)) {
                        conf = '配置变更';
                    }
                }
                return conf;
            },
            nullToTrim(val) {
                return !val?"":val;
            },
            coverPrice(d) {
                if (d.subType == '选型件' && d.isSubtraction != '1') {
                    if (d.valuePrice && d.valuePrice != 0) {
                        return d.valuePrice + '(价差)';
                    } else {
                        return d.valuePrice;
                    }

                } else {
                    return d.actualPrice;
                }
            },
            coverNumber(num) {
                return !num ? 0 : Number(num);
            },
        }
    }
</script>

<style scoped>

    /deep/ .letterPriceTable tr{
        height: 25px !important;
    }
    /deep/ .letterPriceTable tr td,th {
        font-family: '等线' !important;
    }

    td {
        border: 0.5px solid;
    }
    /deep/ .detailTable {
        width: 100%;
        margin: 0;
    }
    /deep/ .detailTable>tbody>tr {
        height: 25px !important;
    }
    /deep/ .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/.detailTable>tbody>tr>th:first-child {
        border-left: none !important;
    }
    /deep/ .detailTable>tbody>tr>th {
        border-top: none !important;
    }
    /deep/ .detailTable>tbody>tr>td:first-child {
        border-left: none !important;
    }
    /deep/ .detailTable>tbody>tr>td:last-child {
        border-right: none !important;
    }
    /deep/ .detailTable>tbody>tr>th:last-child {
        border-right: none !important;
    }
    /deep/ .detailTable>tbody>tr:last-child td {
        border-bottom: none !important;
    }

    /deep/ .remarkText {
        margin: 0;
        padding: 0 0 0 3px;
        height: 26.2px;
        min-height: 26.2px;
    }

</style>
