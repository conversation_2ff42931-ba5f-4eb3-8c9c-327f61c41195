<template>
    <div style="position: relative; overflow-y: auto;padding-top: 0;">
        <div class="header-button-area">
            <a-button v-if = "!isView" type="primary" @click="handleSave" style="margin-right: 8px">
                保存
            </a-button>
            <a-button type="primary" @click="handleClose()">
                关闭
            </a-button>
        </div>
        <a-spin :spinning="loading">
        <a-card class="quoteLetterInfo">
            <div class="container">
                <a-form-model class="letterForm" ref="letterForm" :model="letterForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
                <a-row class="header">
                    <img width="166px" height="46px" src="~@/assets/img/yc_machine.png">
                    <img width="90px" height="45px" style="float: right" src="~@/assets/img/yc_fax.png">
                </a-row>
                <a-row class="body">
                    <a-col :span="24">
                        <h2 style="color:#1a385f;font-family: 华文新魏;margin-bottom: 10px">广西玉柴机器股份有限公司/Guangxi Yuchai Machinery Co.,Ltd.</h2>
                    </a-col>
                    <a-col class="letterTitle" :span="24">
                        <table>
                            <tbody>
                                <tr>
                                    <td style="border-bottom: none;vertical-align: baseline" rowspan="2">
                                        <label>TO/收件单位：</label>
                                        <span>{{letterForm.customerName}}</span>
<!--                                        <a-form-model-item label="TO/收件单位">-->
<!--                                            <a-textarea readOnly style="margin-top:4px;min-height: 72px" v-model="letterForm.customerName"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                    <td>
                                        <label>TO/收件人：</label>
                                        <span>{{letterForm.recipient}}</span>
<!--                                        <a-form-model-item label="TO/收件人">-->
<!--                                            <span>{{letterForm.recipient}}</span>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>FAX/收件人传真：</label>
                                        <span>{{letterForm.recipientFax}}</span>
<!--                                        <a-form-model-item label="FAX/收件人传真">-->
<!--                                            <span>{{letterForm.recipientFax}}</span>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td rowspan="2" style="vertical-align: baseline">
                                        <label>TOPIC/主题：</label>
                                        <span>{{letterForm.topic}}</span>
<!--                                        <a-form-model-item label="TOPIC/主题">-->
<!--                                            <a-textarea readOnly style="margin-top:4px;min-height: 72px" v-model="letterForm.topic"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                    <td>
                                        <label>TEL/收件人电话：</label>
                                        <span>{{letterForm.recipientTel}}</span>
<!--                                        <a-form-model-item label="TEL/收件人电话">-->
<!--                                            <span>{{letterForm.recipientTel}}</span>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>CC/抄送：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.copyTo"/>
<!--                                        <a-form-model-item label="CC/抄送">-->
<!--                                            <a-input v-model="letterForm.copyTo"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>DATE/传真日期：</label>
                                        <span>{{letterForm.faxDate}}</span>
<!--                                        <a-form-model-item label="DATE/传真日期">-->
<!--                                            <span>{{letterForm.faxDate}}</span>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                    <td>
                                        <label>TOTAL,PAGES/页数：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.pages"/>
<!--                                        <a-form-model-item label="TOTAL,PAGES/页数">-->
<!--                                            <a-input v-model="letterForm.pages"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>ISSUER/签发人：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.issuer"/>
<!--                                        <a-form-model-item label="ISSUER/签发人">-->
<!--                                            <a-input v-model="letterForm.issuer"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                    <td>
                                        <label>FROM/发件人：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.sender"/>
<!--                                        <a-form-model-item label="FROM/发件人">-->
<!--                                            <a-input readOnly v-model="letterForm.sender"/>-->
<!--                                            <span>{{letterForm.sender}}</span>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>FAX/发件人传真：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.senderFax"/>
<!--                                        <a-form-model-item label="FAX/发件人传真">-->
<!--                                            <a-input v-model="letterForm.senderFax"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                    <td>
                                        <label>TEL/发件人电话：</label>
                                        <a-input style="width: 70%; height:28px" v-model="letterForm.senderTel"/>
<!--                                        <a-form-model-item label="TEL/发件人电话">-->
<!--                                            <a-input readOnly v-model="letterForm.senderTel"/>-->
<!--                                        </a-form-model-item>-->
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="padding-top: 5px;">
<!--                                        <a-form-model-item :wrapperCol="{span: 24}">-->
                                            <a-checkbox-group style="width: 100%;height: 28px;float:left;color: black" v-model="security">
                                                <a-row>
                                                    <a-col :span="6">
                                                        <a-checkbox value="Urgency">紧急Urgency</a-checkbox>
                                                    </a-col>
                                                    <a-col :span="6">
                                                        <a-checkbox value="General">普通General</a-checkbox>
                                                    </a-col>
                                                    <a-col :span="6">
                                                        <a-checkbox value="Secrecy">保密Secrecy </a-checkbox>
                                                    </a-col>
                                                    <a-col :span="6">
                                                        <span>（请在□中打√）</span>
                                                    </a-col>
                                                </a-row>
                                            </a-checkbox-group>
<!--                                        </a-form-model-item>-->

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </a-col>
                    <a-col  class="letterContent" :span="24">
                        <a-col :span="24">
                            <h2 style="margin: 28px 0 18px 0;font-weight: bold">关于发动机报价函</h2>
                            <p>
                                <label style="font-size: 16px">{{letterForm.customerName+'：'}}</label>
                            </p>
                            <p style="padding-left: 45px" v-if="type=='quoteInfo'">
                                感谢贵公司长期以来对我公司的大力支持！经我公司财务部核算，新配套贵公司的发动机<span style="color: red;font-family: '仿宋_GB2312'">不含税</span>价格如下：
                            </p>
                            <p style="padding-left: 45px" v-if="type=='changeInfo'">
                                感谢贵公司长期以来对我公司的大力支持！根据贵公司要求，配套贵公司的以下发动机需更改配置出厂，具体配置更改及更改后的发动机<span style="color: red;font-family: '仿宋_GB2312'">不含税</span>价格如下：
                            </p>
                            <p style="text-align: right">
                                单位：元/台
                            </p>
                        </a-col>

                        <a-col v-if="type=='quoteInfo'"  :span="24">
                            <quoteInfo
                                    v-for="(d,index) in letterForm.infoList" :key="index"
                                    :ref="'quoteInfo' + index" :letterForm="letterForm" :form="d" :num="index"/>
                            <span style="display: inline-block;text-align: center;width: 100%">
                                <a-button class="addQuoteBtn" @click="addNewQuote"><a-icon style="margin: 0 15px" type="plus"/></a-button>
                            </span>

                        </a-col>
                        <a-col v-if="type=='changeInfo'" :span="24">
                            <changeInfo ref="changeInfo" :letterForm="letterForm"/>
                        </a-col>
                        <a-col :span="24">
                            <p style="padding-left: 45px;margin-top: 20px">
                                请贵公司收函后予以确认，以便我公司安排相关事宜。
                            </p>
                            <p style="padding-left: 45px">
                                谢谢合作并致
                            </p>
                            <p style="padding-left: 45px">
                                商祺！
                            </p>
                            <p>&nbsp;</p>
                            <p style="text-align: right">
                                广西玉柴机器股份有限公司营销公司
                            </p>
                            <p style="text-align: right">
                                <a-input readOnly style="width: 250px;font-weight: normal;text-align: center" v-model="letterForm.letterDate"/>
                            </p>
                        </a-col>
                    </a-col>
                </a-row>
                <a-row class="footer">
                    <a-col :span="24">
                        <span>版权归广西玉柴机器股份有限公司所有</span>
                    </a-col>
                    <a-col :span="12">
                        <span>Copyrights are held by Guangxi Yuchai Machinery Co., Ltd. </span>
                    </a-col>
                    <a-col :span="12" style="text-align: right">
                        <span>网址：www.yuchaidiesel.com</span>
                    </a-col>
                    <a-col :span="24">
                        <span>地址（Add）：中国广西玉林市天桥西路88号/ No. 88, Tianqiao West Road, Yulin City, Guangxi, China </span>
                    </a-col>
                    <a-col :span="8">
                        <span>电话（Tel）：+86 775 3228168</span>
                    </a-col>
                    <a-col :span="8" style="text-align: center">
                        <span> 传真（Fax）：+86 775 3288168</span>
                    </a-col>
                    <a-col :span="8" style="text-align: right">
                        <span>邮编（P.C.）：537005</span>
                    </a-col>
                </a-row>
                </a-form-model>
            </div>

        </a-card>
        </a-spin>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import { GET_TEMPLATE_BY_CUSTOMER,GET_XSGSQUOTELETTER_BY_ID,getUuid,XSGSQUOTELETTER_SAVETOCACHE,XSGSQUOTELETTER_GETFROMCACHE } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import Cookie from 'js-cookie'
    import quoteInfo from "./quoteInfo";
    import changeInfo from "./changeInfo";

    export default {
        name: "quoteLetter.vue",
        components: {
            quoteInfo,changeInfo
        },
        data() {
            return {
                hasAuth,
                Cookie,
                loading:false,
                quoteLetterStoreId:null,
                option:null,
                letterType:null,
                id:null,
                isView:false,
                checked:false,
                customer:null,
                type:null,
                letterForm:{
                    id:null,
                    quoteLetterStoreId:null,
                    year:null,
                    letterType:null,
                    letterName:null,
                    customer:null,
                    customerName:null,
                    recipient:null,
                    recipientFax:null,
                    recipientTel:null,
                    sender:null,
                    senderTel:null,
                    senderFax:null,
                    topic:null,
                    copyTo:null,
                    faxDate:null,
                    letterDate:null,
                    pages:null,
                    issuer:null,
                    secury:null,
                    infoList:[],
                    hasChange:null,
                    option:null
                },

            }
        },
        computed:{
            security(){
                if (this.letterForm.secury) {
                    return this.letterForm.secury.split(',');
                }
                return [];
            },
        },
        mounted() {
            this.loading = false;
            this.letterType = this.$route.query.letterType;
            this.option = this.$route.query.option;
            this.quoteLetterStoreId = this.$route.query.quoteLetterStoreId;
            this.parentPath = this.$route.query.parentPath;
            this.fullPath = this.$route.fullPath;
            this.customer = this.$route.query.customer;
            this.type = this.getType();
            if (":id" === this.$route.params.id) {
                this.$setPageTitle(this.fullPath, "新建报价函");
            } else {
                this.id = this.$route.params.id
                this.letterForm.id = this.$route.params.id;
                this.$setPageTitle(this.fullPath, "编辑报价函");
            }
            if (this.isView) {
                this.$setPageTitle(this.fullPath, "查看报价");
            }
            this.getData();
            this.checkName();
        },
        methods: {
            getData(){
                if (this.id) {
                    let that = this;
                    this.loading = true;
                    // 先从缓存拿，拿不到从数据库拿
                    request(XSGSQUOTELETTER_GETFROMCACHE, METHOD.GET, {id: this.id, quoteLetterStoreId:this.quoteLetterStoreId})
                        .then(res => {
                            if (res.data.data != null) {
                                this.letterForm = res.data.data;
                                this.$nextTick(()=>{
                                    // 初始化价格信息
                                    if (this.type == 'quoteInfo') {
                                        this.letterForm.infoList.forEach((s,index)=>{
                                            let key = this.type + index;
                                            that.$refs[key][0].initData(s);
                                        });
                                    } else {
                                        that.$refs.changeInfo.initData(this.letterForm.infoList);
                                    }

                                });

                                this.loading = false;
                            } else {
                                request(GET_XSGSQUOTELETTER_BY_ID, METHOD.GET, {id:this.id}).then(res=>{
                                    let data = res.data.data;
                                    if (data) {
                                        this.letterForm = data;
                                        this.$nextTick(()=>{
                                            // 初始化价格信息
                                            if (this.type == 'quoteInfo') {
                                                this.letterForm.infoList.forEach((s,index)=>{
                                                    let key = this.type + index;
                                                    that.$refs[key][0].initData(s);
                                                });
                                            } else {
                                                that.$refs.changeInfo.initData(this.letterForm.infoList);
                                            }

                                        });
                                    }
                                    this.loading = false;
                                });
                            }
                        });
                } else {
                    request(getUuid, METHOD.GET).then(res => {
                        this.id = res.data.data;
                    });
                    this.letterForm.letterType = this.letterType;
                    this.letterForm.customer = this.customer;
                    if (this.type == 'quoteInfo') {
                        let priceInfo = {id:this.letterForm.infoList.length + Math.round(Math.random() * 1000) + 1};
                        this.letterForm.infoList.push(priceInfo);
                    }
                    this.initTemplate();
                }
            },
            initTemplate(){
                // 根据客户号获取报价函模板
                request(GET_TEMPLATE_BY_CUSTOMER, METHOD.GET, {customer:this.customer,letterType:this.letterType}).then(res=>{
                    let data = res.data.data;
                    if (!data) {
                        this.$message.error("请维护客户：" + this.customer + "的报价函模板信息");
                    } else {
                        let template = data;
                        this.letterForm.year = (new Date()).getFullYear();
                        this.letterForm.recipient = template.recipient;
                        this.letterForm.recipientFax = template.recipientFax;
                        this.letterForm.recipientTel = template.recipientTel;
                        this.letterForm.sender = template.sender;
                        this.letterForm.senderTel = template.senderTel;
                        this.letterForm.customerName = template.customerName;
                        this.letterForm.topic = template.topic;
                        this.letterForm.faxDate = template.faxDate;
                        this.letterForm.secury = template.secury;
                        this.letterForm.letterDate = template.letterDate
                    }
                });
            },
            handleSave(){
                // 获取价格信息
                console.log(this.letterForm);
                let infoList = this.letterForm.infoList;
                let newData = [];
                let that = this;
                if (this.type == 'quoteInfo') {
                    infoList.forEach((s,index)=>{
                        let key = this.type + index;
                        let priceForm = that.$refs[key][0].getPriceForm();
                        newData.push(priceForm);
                    });
                } else {
                    newData = this.$refs.changeInfo.getInfoList();
                }
                this.letterForm.infoList = newData;
                this.letterForm.id = this.id;
                this.letterForm.hasChange = 1;
                this.letterForm.hasLetterChange = 1;
                this.letterForm.option = this.option;
                this.letterForm.quoteLetterStoreId = this.quoteLetterStoreId;
                if (!this.letterForm.letterName) {
                    this.letterForm.letterName = this.coverQuoteLetterType(this.letterForm.letterType);
                }
                // 计算页数
                if (this.type == 'changeInfo') {
                    if (!this.letterForm.pages && this.letterForm.infoList && this.letterForm.infoList.length >= 7) {
                        this.letterForm.pages = '2';
                    } else {
                        this.letterForm.pages = '1';
                    }
                } else {

                    if (!this.letterForm.pages && this.letterForm.infoList && this.letterForm.infoList.length > 0) {
                        this.letterForm.pages = '1';
                        if (this.letterForm.infoList.length > 1) {
                            this.letterForm.pages = '2';
                        } else {
                            this.letterForm.infoList.forEach(s=>{
                                if (s.parts.length >= 7) {
                                    this.letterForm.pages = '2';
                                    return false;
                                }
                            })
                        }
                    }
                }
                this.loading = true;
                request(XSGSQUOTELETTER_SAVETOCACHE, METHOD.POST, this.letterForm).then(res => {
                    this.loading = false;
                    if (res.data.code == 0) {
                        let storeQuoteLetters = this.$store.state.account.quotesLetter;
                        if (!storeQuoteLetters) {
                            storeQuoteLetters = [];
                        }
                        // 检查已否已经存在，存在则更新
                        // 删除原有的
                        if (that.letterForm.id) {
                            let newData = storeQuoteLetters.filter(s=>s.id != that.letterForm.id && s.quoteLetterStoreId != that.letterForm.quoteLetterStoreId);
                            storeQuoteLetters = newData;
                        }

                        storeQuoteLetters.push(this.letterForm);
                        that.$store.commit("account/setQuoteLetters",storeQuoteLetters);
                        that.$closePage(this.fullPath,this.parentPath);
                    }
                });
            },
            handleClose(){
                this.$closePage(this.fullPath, this.parentPath);
            },
            getType(){
                if (this.letterType.indexOf('CP_') != -1) {
                    return 'changeInfo';
                } else {
                    return 'quoteInfo';
                }
            },
            addNewQuote(){
                let priceInfo = {id:this.letterForm.infoList.length + Math.round(Math.random() * 1000) + 1};
                this.letterForm.infoList.push(priceInfo);
            },
            coverQuoteLetterType(quoteLetterType) {
                const typeMap = {
                    'PS_YN': "显示零件价格，无供应商",
                    'PST_YNY': "显示零件价格，无供应商，含税价格",
                    'PS_NN': "不显示零件价格，无供应商",
                    'PS_NY': "不显示零件价格，显示供应商",
                    'NSS_YYY': "基本型+选配件合计，显示供应商",
                    'NSS_YYN': "基本型+选配件合计，无供应商",
                    'CP_YN': "更改配置报价模板(不含税价格)",
                    'CP_YY': "更改配置报价模板(含税价格)"
                };
                return typeMap[quoteLetterType] || "其他";
            },
        },
    }
</script>

<style scoped>
    /deep/ .header-button-area {
        position: fixed;
        z-index: 999;
        height: 45px;
        background-color: #ffffff;
        line-height: 45px;
        width: 100%;
        border-bottom: 1px solid #f0f0f0;
        padding: 0 30px;
        min-width: 700px;
    }
    /deep/ .quoteLetterInfo .ant-card-body {
        padding: 45px 30px 20px 30px;
    }

    /deep/ .quoteLetterInfo .container {
        border: 1px solid;
    }

    /deep/ .quoteLetterInfo .header {
        border-bottom: 1px solid;
        padding: 5px 10px 5px 10px;
    }

    /deep/ .quoteLetterInfo .body {
        border-bottom: 1px solid;
        padding: 16px 20px 16px 20px;
    }
    /deep/ .quoteLetterInfo .footer {
        padding: 25px 20px 5px 20px;
    }
    /deep/ .quoteLetterInfo .body h2{
        text-align: center;
        font-size: 26px;
    }
    /deep/ .quoteLetterInfo .body table{
        width: 100%;
    }
    /deep/ .quoteLetterInfo .body .letterTitle {

    }

    /deep/ .quoteLetterInfo .body .letterTitle tr {

    }
    /deep/ .quoteLetterInfo .body .letterTitle td label {
        width: 150px;
        display: inline-block;
        text-align: right;
    }
    /deep/ .quoteLetterInfo .body .letterTitle td label,input,textarea,span {
        font-size: 16px !important;
        font-family: 宋体 !important;
        font-weight: bold;
        color: black;
    }
    /deep/ .quoteLetterInfo input[readonly],textarea[readonly] {
        resize: none;
        border: none;
        box-shadow: none;
        -web-kit-appearance:none;
        -moz-appearance: none;
    }

    /deep/ .letterTitle input,textarea {
        font-family: 宋体 !important;
        font-weight: bold;
    }
    /deep/ .addQuoteBtn {
        border-top: none;
        border-radius: 0;
        border-bottom-right-radius: 5px;
        border-bottom-left-radius: 5px;
        background-color: #f8f8f8;
    }

    /*/deep/ .quoteLetterInfo .body .letterTitle input {*/
    /*    font-family: 宋体 !important;*/
    /*    font-size: 18px;*/
    /*    font-weight: bold;*/
    /*}*/
    /deep/ .quoteLetterInfo .body .letterTitle td{
        width: 50%;
        padding: 0 8px;
    }
    /deep/ .quoteLetterInfo .body .letterContent{
        font-family: 仿宋_GB2312;
        font-size: 16px;
    }
    /deep/ .quoteLetterInfo .body .letterContent input{
        font-family: 仿宋_GB2312 !important;
        font-size: 16px !important;
    }
    /deep/ .quoteLetterInfo .body .letterContent tr{
        height: 25px;
    }

    /deep/ .quoteLetterInfo .body .letterContent tr th{
        border: 0.5px solid;
        font-family: 仿宋_GB2312;
        text-align: center;
        font-weight: normal;
    }
    td {
        border: 0.5px solid;
    }

    .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }
    /deep/ .ant-form-item {
        margin: 0;
    }

    /deep/ .ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }



</style>
