<template>
    <div>
        <a-card>
            <a-form-model ref="searchForm" :model="searchForm" :wrapperCol="{span: 16}">
                <table style="width: 100%; border: 1px solid #f0f0f0;" >
                    <tbody class="ant-table">
                    <tr>
                        <td><a-form-model-item label="年度" /></td>
                        <td width="80px"><a-input @change="changeYear" oninput="value=value.replace(/^(0+)|[^\d]+/g,'')" v-model="searchForm.year"/>
                        </td>
                        <td><a-form-model-item label="客户" /></td>
                        <td width="350px">
                            <a-select
                                    v-model="searchForm.customerNameList"
                                    show-search
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :allowClear='true'
                                    :not-found-content="null"
                                    mode="multiple"
                                    @dropdownVisibleChange="onclickCustomer(searchForm)"
                                    @search="handleCustomerSearch"
                                    @change="changedCustomer(searchForm)"
                            >
                                <a-select-option v-for="d in customerList1" :key="d.customer + '-' + d.name">
                                    {{ d.customer + "-" +d.name }}
                                </a-select-option>
                            </a-select>
                            <!--              <a-input v-model="searchForm.name"/>-->
                        </td>
                        <td width="80px"><a-form-model-item label="预算类型" /></td>
                        <td width="80px">
                            <a-select width="80px" v-model="searchForm.budgetType" style="min-width: 120px">
                                <a-select-option value="BASIC" >预算基本型</a-select-option>
                                <a-select-option value="POINT" >预算降点</a-select-option>
                                <a-select-option value="DECL" >预算降幅</a-select-option>
                            </a-select>
                        </td>
                        <td width="80px"><a-form-model-item label="排放" /></td>
                        <td width="100px">
                            <a-select width="width:100px" v-model="searchForm.blowoffList" style="min-width: 60px"
                                      :allowClear="true"
                                      mode="multiple"
                                      @dropdownVisibleChange="getBlowoffList(searchForm)"
                                      @change="changedBlowoff(searchForm)">
                                <a-select-option v-for="d in blowoffList1" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </td>
                    </tr>
                    <tr>
                        <td width="80px"><a-form-model-item label="系列" /></td>
                        <td>
                            <a-select style="width:120px" v-model="searchForm.seriesList"
                                      @dropdownVisibleChange="onclickSeries()"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      @change="changedSeries()">
                                <a-select-option v-for="sty in seriesList1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td width="80px"><a-form-model-item label="产品型号" /></td>
                        <td>
                            <a-select v-model="searchForm.productModels"
                                      @dropdownVisibleChange="onclickProductModel()"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      @change="changedProductModel()">
                                <a-select-option v-for="sty in productModels1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td width="80px"><a-form-model-item label="品系1" /></td>
                        <td>
                            <a-select style="width: 100%" v-model="searchForm.strainList"
                                      @dropdownVisibleChange="onclickStrain()"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strainList1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td width="80px"><a-form-model-item label="品系2" /></td>
                        <td>
                            <a-select style="width: 100%" v-model="searchForm.strain1List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain1List1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>

                    </tr>
                    <tr>

                        <td width="80px"><a-form-model-item label="品系3" /></td>
                        <td>
                            <a-select style="width: 100%" v-model="searchForm.strain2List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain2List1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td width="80px"><a-form-model-item label="品系4" /></td>
                        <td>
                            <a-select style="width: 100%" v-model="searchForm.strain3List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain3List1" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </td>
                        <td colspan="4" width="250px">
                            <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
                            <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
                        </td>

                    </tr>
                    </tbody>
                </table>
                <a-row>
                    <a-col :span="24">
                        <div style="margin: 5px 0">
                            <a-button v-if="checkPf('BUDGET_ADD')" type="primary" style="margin: 0 8px" @click="handleEdit"><a-icon type="plus"/>新增</a-button>
                            <a-button v-if="checkPf('BUDGET_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
                            <a-upload v-if="checkPf('BUDGET_IMPORT')"
                                    name="file"
                                    accept=".xlsx"
                                    :action="UPLOAD_XSGSBUDGETMANAGER"
                                    :headers="{'Authorization':Cookie.get('Authorization')}"
                                    :showUploadList="false"
                                    style="margin: 0 8px"
                                    @change="handleUpload">
                                <a-button type="primary">
                                    <a-icon type="upload"/>
                                    导入预算
                                </a-button>
                            </a-upload>
                            <a-button v-if="checkPf('BUDGET_IMPORT')" type="primary" style="margin: 0 8px" @click="handleExportTemplate"><a-icon type="download"/>下载导入模板</a-button>
                            <a-button type="primary" style="margin: 0 8px" @click="showImportLog"><a-icon type="search"/>导入日志</a-button>
                        </div>
                    </a-col>
                </a-row>
            </a-form-model>
            <a-table
                    :columns="columns"
                    rowKey="id"
                    :pagination="pagination"
                    :data-source="data"
                    :scroll="{ x: 1000 }"
                    :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                    size="small"
                    bordered
                    :customRow="onRowClick"
                    :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{index+1}}
                </template>
                <template slot="customerType" slot-scope="text, record">
                    {{record.customerType?(record.customerType+'类'):''}}
                 </template>
                <template slot="budgetType" slot-scope="text, record">
                    {{coverBudgetType(record.budgetType)}}
                </template>
                <template slot="dropPoint" slot-scope="text, record">
                    {{record.dropPoint?(record.dropPoint+'%'):''}}
                </template>
                <template slot="optionBudget" slot-scope="text, record">
                    {{(record.optionBudget != null)?(record.optionBudget+'%'):''}}
                </template>
                <template slot="sysBasicBudget" slot-scope="text, record">
                    {{record.sysBasicBudget?(record.sysBasicBudget+'%'):''}}
                </template>
                <template slot="sysOptionBudget" slot-scope="text, record">
                    {{record.sysOptionBudget?(record.sysOptionBudget+'%'):''}}
                </template>
                <template slot="createTime" slot-scope="text, record">
                    {{dateFormat(record.createTime)}}
                </template>

                <template slot="budState" slot-scope="text, record">
                    {{coverState(record.budState)}}
                </template>

                <span slot="action" slot-scope="record">
                  <a v-if="checkPf('BUDGET_EDIT')" @click="handleEdit(record)">编辑</a>
<!--                  <a-divider type="vertical"/>-->
<!--                  <a @click="handleCalc(record)">计算</a>-->
                  <a-divider v-if="checkPf('BUDGET_EDIT')" type="vertical"/>
                  <a @click="handleShowLog(record)">日志</a>
                  <a-divider v-if="checkPf('BUDGET_DELETE')" type="vertical"/>
                  <a v-if="checkPf('BUDGET_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
            </a-table>
        </a-card>
        <a-modal
                width="80%"
                :title="form.id?'编辑':'新增'"
                :visible="dialog"
                :confirm-loading="confirmLoading"
                @ok="handleSubmit"
                @cancel="closeForm">
            <a-form-model ref="form"  :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="年度" prop="year">
                            <a-input-number @change="changeYear(1)" :min="0" v-model="form.year"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="预算类型" prop="budgetType">
                            <a-select v-model="form.budgetType" :value="form.budgetType" @change="changeBudgetType" style="min-width: 120px">
                                <a-select-option value="BASIC" >预算基本型</a-select-option>
                                <a-select-option value="POINT" >预算降点</a-select-option>
                                <a-select-option value="DECL" >预算降幅</a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="选配件预算" prop="optionBudget">
                            <a-input oninput="value=value.replace(/[^0-9]/g,'')" addon-after="%" v-model="form.optionBudget"/>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="客户类别" prop="customerType">
                            <a-select v-model="form.customerType" style="min-width: 120px">
                                <a-select-option value="A" >A类</a-select-option>
                                <a-select-option value="B" >B类</a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8" class="customerLabel">
                        <a-form-model-item label="客户" prop="customer">
                            <a-select
                                    style="min-width: 250px"
                                    v-model="form.customerNameList"
                                    show-search
                                    :default-active-first-option="false"
                                    :show-arrow="false"
                                    :filter-option="false"
                                    :allowClear='true'
                                    :not-found-content="null"
                                    mode="multiple"
                                    @dropdownVisibleChange="onclickCustomer(form,1)"
                                    @search="handleCustomerSearch2"
                                    @change="changedCustomer(form,1)"
                            >
                                <a-select-option v-for="d in customerList2" :key="d.customer + '-' + d.name">
                                    {{ d.customer + "-" +d.name }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="排放" prop="blowoff">
                            <a-select style="width: 100%" v-model="form.blowoffList"
                                      :allowClear="true"
                                      mode="multiple"
                                      @dropdownVisibleChange="getBlowoffList(form, 1)"
                                      @change="changedBlowoff(form,1)">
                                <a-select-option v-for="d in blowoffList2" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="系列" prop="series">
                            <a-select style="width: 100%" v-model="form.seriesList"
                                      @dropdownVisibleChange="onclickSeriesSap()"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      @change="changedSeriesSap()">
                                <a-select-option v-for="sty in seriesList2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="产品型号" prop="productModel">
                            <a-select style="width: 100%" v-model="form.productModels"
                                      @dropdownVisibleChange="onclickProductModelSap()"
                                      :allowClear="true"
                                      mode="multiple"
                                      @change="changedProductModelSap()">
                                <a-select-option v-for="sty in productModels2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="品系1" prop="strain">
                            <a-select style="width: 100%" v-model="form.strainList"
                                      @dropdownVisibleChange="onclickStrainSap()"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strainList2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="品系2" prop="strain">
                            <a-select style="width: 100%" v-model="form.strain1List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain1List2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item label="品系3" prop="strain">
                            <a-select style="width: 100%" v-model="form.strain2List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain2List2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="8">
                        <a-form-model-item label="品系4" prop="strain">
                            <a-select style="width: 100%" v-model="form.strain3List"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple">
                                <a-select-option v-for="sty in strain3List2" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8">
                        <a-form-model-item v-if="!form.budgetType || form.budgetType=='BASIC'" label="预算基本价" prop="basicPrice">
                            <a-input v-model="form.basicPrice" @input="inputMoney" @blur="coverMoney"/>
                        </a-form-model-item>
                        <a-form-model-item v-if="form.budgetType=='POINT'" label="预算降点" prop="dropPoint">
                            <a-input v-if="form.budgetType=='POINT'" oninput="value=value.replace(/[^0-9]/g,'')" :max="100" addon-after="%" v-model="form.dropPoint"/>
                        </a-form-model-item>
                        <a-form-model-item v-if="form.budgetType=='DECL'" label="预算降幅" prop="dropDecline">
                            <a-input v-model="form.dropDecline" @input="inputMoney" @blur="coverMoney"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="8"></a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="系统部基本型预算" prop="sysBasicBudget">
                            <a-input oninput="value=value.replace(/[^0-9]/g,'')" addon-after="%" v-model="form.sysBasicBudget"/>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="系统部选配件预算" prop="sysOptionBudget">
                            <a-input oninput="value=value.replace(/[^0-9]/g,'')" addon-after="%" v-model="form.sysOptionBudget"/>
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>
        </a-modal>
        <a-modal
            title="执行日志"
            :visible="logDialog1"
            width="80%"
            @ok="logDialog1 = false"
            @cancel="logDialog1 = false">
        <a-form-model ref="logsForm1"  :labelCol="{span: 4}" :wrapperCol="{span: 20}" style="height: auto;">
            <a-row class="form-row">
            </a-row>
            <a-table
                    :columns="this.logForm.logType==2?columns2:columns1"
                    rowKey="id"
                    :data-source="data1"
                    :pagination="pagination1"
                    size="middle"
                    :customRow="onRowClick"
                    :loading="loading">
                <!-- 序号 -->
                <template slot="num" slot-scope="text, record, index">
                    {{index+1}}
                </template>
                <template slot="optTime" slot-scope="text, record">
                    {{dateFormat(record.optTime)}}
                </template>

            </a-table>
        </a-form-model>
        </a-modal>
        <a-modal
                title="执行日志"
                :visible="logDialog2"
                width="80%"
                @ok="handleLogOk"
                @cancel="logDialog2 = false">
            <a-form-model ref="logsForm2"  :labelCol="{span: 4}" :wrapperCol="{span: 20}" style="height: auto;">
                <a-row class="form-row">
                </a-row>
                <a-table
                        :columns="columns1"
                        rowKey="id"
                        :data-source="data2"
                        :pagination="false"
                        size="middle"
                        :customRow="onRowClick"
                        :loading="loading">
                    <!-- 序号 -->
                    <template slot="num" slot-scope="text, record, index">
                        {{index+1}}
                    </template>
                    <template slot="optTime" slot-scope="text, record">
                      {{dateFormat(record.optTime)}}
                    </template>
                </a-table>
            </a-form-model>
        </a-modal>
    </div>

</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {LIST_XSGSBUDGETMANAGER_PAGE,
        DELETE_XSGSBUDGETMANAGER,
        DOWNLOAD_XSGSBUDGETMANAGER,
        TEMPLATE_XSGSBUDGETMANAGER,
        SUBMIT_XSGSBUDGETMANAGER,
        CALC_XSGSBUDGETMANAGER,
        COMBO_BUDGET_GETBLOWOFFLIST,
        COMBO_BUDGET_GETSERIESLIST,
        COMBO_BUDGET_GETPRODUCTMODELS,
        COMBO_BUDGET_GETSTRAINLIST,
        COMBO_BUDGET_GETSERIESLIST_SAP,
        COMBO_BUDGET_GETPRODUCTMODELS_SAP,
        COMBO_BUDGET_GETSTRAINLIST_SAP,
        LIST_XSGSCUSTOMER_GETBYNOORNAME,
        LIST_XSGSBUDGETLOG_PAGE,
        UPLOAD_XSGSBUDGETMANAGER,
        CALC_IMPORT_XSGSBUDGETMANAGER,
        DELETE_IDS_XSGSBUDGETMANAGER,
        GETSEACHLIST_XSGSALLOWANCEITEM} from "@/services/api/xsgs";
    import {METHOD, request,exportExcel} from "@/utils/request";
    import {dateFormat} from "@/utils/dateUtil";
    import {downloadFile} from "@/utils/util";
    import Cookie from 'js-cookie'
    export default {
        name: "xsgsPartPolicy.vue",
        data() {
            return {
                hasAuth,
                Cookie,
                UPLOAD_XSGSBUDGETMANAGER,
                loading: false,
                dialog:false,
                logDialog1: false,
                logDialog2: false,
                confirmLoading: false,
                // 功能权限关键字
                PF_FIELD:"BUSINESS_BUDGET",
                PF_LIST:[],
                searchForm: {
                },
                customerList1:[],
                blowoffList1: [],
                seriesList1: [],
                productModels1: [],
                strainList1: [],
                strain1List1: [],
                strain2List1: [],
                strain3List1: [],
                customerList2:[],
                blowoffList2: [],
                seriesList2: [],
                productModels2: [],
                strainList2: [],
                strain1List2: [],
                strain2List2: [],
                strain3List2: [],
                form: {},
                logForm:{
                    logType: 1
                },
                calcForm:{},
                selectedRowKeys: [],
                pagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                            total / this.pagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onSizeChange(current, size) // 改变每页数量时更新显示
                },
                pagination1: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.pagination1.current}/${Math.ceil(
                            total / this.pagination1.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onLogPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onLogSizeChange(current, size) // 改变每页数量时更新显示
                },
                data: [],
                columns: [
                    {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '年度', dataIndex: 'year', width:50},
                    {title: '预算类型', dataIndex: 'budgetType', width:80, scopedSlots: {customRender: 'budgetType'}},
                    {title: '选配件预算', dataIndex: 'optionBudget', width:60, scopedSlots: {customRender: 'optionBudget'}},
                    {title: '客户类别', dataIndex: 'customerType', width:40, scopedSlots: {customRender: 'customerType'}},
                    {title: '客户', dataIndex: 'customerName', width:350},
                    {title: '状态', dataIndex: 'budState', width:60, scopedSlots: {customRender: 'budState'}},
                    {title: '排放', dataIndex: 'blowoff', width:60},
                    {title: '系列', dataIndex: 'series', width:70},
                    {title: '产品型号', dataIndex: 'productModel', width:120},
                    {title: '品系1', dataIndex: 'strain', width:60},
                    {title: '品系2', dataIndex: 'strain1', width:60},
                    {title: '品系3', dataIndex: 'strain2', width:70},
                    {title: '品系4', dataIndex: 'strain3', width:80},
                    {title: '预算基本价', dataIndex: 'basicPrice', width:80},
                    {title: '预算降点', dataIndex: 'dropPoint', width:80, scopedSlots: {customRender: 'dropPoint'}},
                    {title: '预算降幅', dataIndex: 'dropDecline', width:80},
                    {title: '系统部基本型预算', dataIndex: 'sysBasicBudget', width:70, scopedSlots: {customRender: 'sysBasicBudget'}},
                    {title: '系统部选配件预算', dataIndex: 'sysOptionBudget', width:70, scopedSlots: {customRender: 'sysOptionBudget'}},
                    {title: '创建时间', dataIndex: 'createTime', width:140, scopedSlots: {customRender: 'createTime'}},
                    {title: '操作', fixed: 'right', width: 140, align:"center", scopedSlots: {customRender: 'action'}}
                ],
                rules: {
                    year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
                    budgetType: [{required: true, message: '预算类型不能为空', trigger: 'blur'}],
                    // customerType: [{required: true, message: '客户类别不能为空', trigger: 'blur'}],
                    // customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
                    optionBudget: [{required: true, message: '选配件预算不能为空', trigger: 'blur'}],
                },
                data1: [],
                data2: [],
                columns1: [
                    {
                        title: '序号',
                        dataIndex: 'num',
                        key: 'num',
                        width: 50,
                        align: 'center',
                        scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '操作人', width:80, dataIndex: 'optUser'},
                    {title: '内容', dataIndex: 'optContent'},
                    {title: '操作时间', width:180, dataIndex: 'optTime', scopedSlots: {customRender: 'optTime'}},
                ],
                columns2: [
                    {
                        title: '序号',
                        dataIndex: 'num',
                        key: 'num',
                        width: 50,
                        align: 'center',
                        scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '导入文件名', width:180, dataIndex: 'fileName'},
                    {title: '操作人', width:80, dataIndex: 'optUser'},
                    {title: '内容', dataIndex: 'optContent'},
                    {title: '操作时间', width:150, dataIndex: 'optTime', scopedSlots: {customRender: 'optTime'}},
                ],
            }
        },
        async created() {
            // 获取页面功能权限
            await this.getUserAuth(this.PF_FIELD);
            this.PF_LIST = this.getPfList(this.PF_FIELD);
        },
        mounted() {
            var aData = new Date();
            this.searchForm.year= Number(this.$moment(aData).format('YYYY'));
            this.getSearchList();
            this.getData()
            this.checkName()
        },
        methods: {
            checkPf(field) {
                return this.PF_LIST.findIndex(s=>s == field) != -1;
            },
            getSearchList(flag) {
                let data = {};
                if (flag) {
                    if (this.searchForm.year) {
                        data.year = this.searchForm.year;
                    }
                } else {

                    if (this.form.year) {
                        data.year = this.form.year;
                    }
                }
                if (flag) {
                    this.strain1List2 = []
                    this.strain2List2 = []
                    this.strain3List2 = []
                } else {
                    this.strain1List1 = []
                    this.strain2List1 = []
                    this.strain3List1 = []
                }
                request(GETSEACHLIST_XSGSALLOWANCEITEM, METHOD.GET, data)
                    .then(res => {
                        if (flag) {
                            this.strain1List2 = res.data.data.strain1;
                            this.strain2List2 = res.data.data.strain2;
                            this.strain3List2 = res.data.data.strain3;
                        } else {
                            this.strain1List1 = res.data.data.strain1;
                            this.strain2List1 = res.data.data.strain2;
                            this.strain3List1 = res.data.data.strain3;
                        }


                    })
            },
            onSelectChange(selectedRowKeys) {
                this.selectedRowKeys = selectedRowKeys;
            },
            getBlowoffList(target,flag) {
                if (!flag) {
                    this.blowoffList1 = [];
                } else {
                    this.blowoffList2 = [];
                }
                request(COMBO_BUDGET_GETBLOWOFFLIST, METHOD.POST, {
                }).then(res => {
                    if (!flag) {
                        this.blowoffList1 = res.data.data;
                    } else {
                        this.blowoffList2 = res.data.data;
                    }
                })
            },
            changedBlowoff(target, flag){
                if (!flag) {
                    this.seriesList1 = [];
                    this.productModels1 = [];
                    this.strainList1 = [];
                } else {
                    this.seriesList2 = [];
                    this.productModels2 = [];
                    this.strainList2 = [];
                }
                this.$set(target,"seriesList",[])
                this.$set(target,"productModels",[])
                this.$set(target,"strainList",[])
            },
            //点击获取系列
            onclickSeries() {
                this.seriesList1 = [];
                request(COMBO_BUDGET_GETSERIESLIST, METHOD.POST, {
                    year: this.searchForm.year?(this.searchForm.year+''):null,
                    budgetType: this.searchForm.budgetType?(this.searchForm.budgetType):null,
                    customerList:this.searchForm.customerList,
                    blowoffList: this.searchForm.blowoffList
                }).then(res => {
                    this.seriesList1 = res.data.data;
                })

            },
            changedSeries(){
                this.productModels1 = [];
                this.strainList1 = [];
                this.$set(this.searchForm,"productModels",[])
                this.$set(this.searchForm,"strainList",[])
            },

            onclickProductModel() {
                this.productModels1 = [];
                request(COMBO_BUDGET_GETPRODUCTMODELS, METHOD.POST, {
                    year: this.searchForm.year?this.searchForm.year:null,
                    budgetType: this.searchForm.budgetType?(this.searchForm.budgetType):null,
                    customerList:this.searchForm.customerList,
                    blowoffList: this.searchForm.blowoffList,
                    seriesList: this.searchForm.seriesList
                }).then(res => {
                    this.productModels1 = res.data.data;
                })
            },
            changedProductModel(){
                this.strainList1 = [];
                this.$set(this.searchForm,"strainList",[])
            },
            onclickStrain() {
                this.strainList1 = [];
                request(COMBO_BUDGET_GETSTRAINLIST, METHOD.POST, {
                    year: this.searchForm.year?this.searchForm.year:null,
                    budgetType: this.searchForm.budgetType?(this.searchForm.budgetType):null,
                    customerList:this.searchForm.customerList,
                    blowoffList: this.searchForm.blowoffList,
                    seriesList: this.searchForm.seriesList,
                    productModels: this.searchForm.productModels
                }).then(res => {
                    this.strainList1 = res.data.data;
                })
            },
            onclickStrain1() {

            },
            onclickStrain2() {

            },
            onclickStrain3() {

            },

            //点击获取sap系列
            onclickSeriesSap() {
                this.seriesList2 = [];
                request(COMBO_BUDGET_GETSERIESLIST_SAP, METHOD.POST, {
                    blowoffList: this.form.blowoffList
                }).then(res => {
                    this.seriesList2 = res.data.data;
                })

            },
            changedSeriesSap(target){
                this.productModels2 = [];
                this.strainList2 = [];
                this.$set(this.form,"productModels",[])
                this.$set(this.form,"strainList",[])
            },
            onclickProductModelSap() {
                this.productModels2 = [];
                request(COMBO_BUDGET_GETPRODUCTMODELS_SAP, METHOD.POST, {
                    blowoffList: this.form.blowoffList,
                    seriesList: this.form.seriesList
                }).then(res => {
                    this.productModels2 = res.data.data;
                })
            },
            changedProductModelSap(){
                this.strainList2 = [];
                this.$set(this.form,"strainList",[])
            },
            onclickStrainSap() {
                this.strainList2 = [];
                request(COMBO_BUDGET_GETSTRAINLIST_SAP, METHOD.POST, {
                    blowoffList: this.form.blowoffList,
                    seriesList: this.form.seriesList,
                    productModels: this.form.productModels
                }).then(res => {
                    this.strainList2 = res.data.data;
                })
            },

            onclickCustomer(target, flag) {
                if (!target.customerList || target.customerList.length ==0) {
                    request(LIST_XSGSCUSTOMER_GETBYNOORNAME, METHOD.POST, {
                        customer:null
                    }).then(res => {
                        if (!flag) {
                            this.customerList1 = res.data.data
                        } else {
                            this.customerList2 = res.data.data
                        }

                    })
                }
            },
            handleCustomerSearch(value) {
                request(LIST_XSGSCUSTOMER_GETBYNOORNAME, METHOD.POST, {
                    customer:value
                }).then(res => {
                    this.customerList1 = res.data.data
                })
            },
            handleCustomerSearch2(value) {
                request(LIST_XSGSCUSTOMER_GETBYNOORNAME, METHOD.POST, {
                    customer:value
                }).then(res => {
                    this.customerList2 = res.data.data
                })
            },
            changeBudgetType(){
                this.form.basicPrice=null;
                this.form.dropPoint=null;
                this.form.dropDecline=null;
            },
            changedCustomer(target) {
                let customerList = target.customerNameList;
                if (customerList) {
                    let valueList= [];
                    customerList.forEach(item => {
                        if (item) {
                            valueList.push(item.split('-')[0])
                        }
                    })
                    target.customerList = valueList;
                    target.customer = valueList.toString();
                }
            },
            getData: function () {
                this.loading = true
                let {current, size} = this.pagination
                request(LIST_XSGSBUDGETMANAGER_PAGE, METHOD.POST, {...this.searchForm, current, size})
                    .then(res => {
                        const {records, total} = res.data.data
                        this.loading = false
                        this.data = records
                        this.pagination.total = parseInt(total)
                    })
            },
            getLogData:function(){
                let {current, size} = this.pagination1;
                this.data1 = [];
                this.pagination1.total = 0;
                request(LIST_XSGSBUDGETLOG_PAGE, METHOD.GET, {...this.logForm, current, size})
                    .then(res => {
                        if (res.data.data) {
                            this.loading = false;
                            const {records, total} = res.data.data
                            this.data1 = records
                            this.pagination1.total = parseInt(total)
                        }
                    })
            },
            getImportLogData:function(){
                let {current, size} = this.pagination1;
                let searchForm = {
                    logType: 2
                }
                this.data1 = [];
                this.pagination1.total = 0;
                request(LIST_XSGSBUDGETLOG_PAGE, METHOD.GET, {...searchForm, current, size})
                    .then(res => {
                        if (res.data.data) {
                            this.loading = false;
                            const {records, total} = res.data.data
                            this.data1 = records
                            this.pagination1.total = parseInt(total)
                        }
                    })
            },
            handleDelete(record) {
                this.$confirm({
                    content: `是否确认删除预算？`,
                    onOk: () => {
                        this.loading = true
                        request(DELETE_XSGSBUDGETMANAGER + record.id, METHOD.DELETE)
                            .then((res) => {
                                if (res.data.code==-1) {
                                    this.$message.error(res.data.msg);
                                } else {
                                    this.$message.info('删除成功')
                                }
                                this.getData()
                            }).catch(() => {
                            this.loading = false
                        })
                    }
                });
            },
            deleteSelectedIds(){
                if(this.selectedRowKeys.length<=0){
                    this.$message.info('请至少选择一条记录！')
                    return
                }
                this.$confirm({
                    content: `是否确认删除选择的数据？`,
                    onOk: () => {
                        this.loading = true
                        request(DELETE_IDS_XSGSBUDGETMANAGER, METHOD.POST,{ids:this.selectedRowKeys})
                            .then(() => {
                                this.$message.info('删除成功')
                                this.getData()
                            }).catch(() => {
                            this.loading = false
                        })
                    }
                });

            },
            handleUpload(info) {
                this.loading = true
                if (info.file.status !== 'uploading') {
                    this.getData()
                }
                if (info.file.status === 'done') {
                    let msg = `${info.file.name} 上传成功`;
                    if (info.file.response && info.file.response.data) {
                        let data = info.file.response.data;
                        let errList = data.errorList;
                        let sucCount = data.sucCount;
                        let importId = data.importId;
                        if (errList && errList.length > 0) {
                            msg +="\n存在错误数据：" + errList.length + "条\n失败原因如下：";
                            errList.forEach(item => {
                                msg += "\n" + item+"；"
                            })
                            this.$warning({
                                title: '导入失败提示',
                                onOk: () => {

                                },
                                content: (
                                    <div style="white-space: pre-wrap;">{msg}</div>
                            ),
                                width:500
                        });
                        } else {
                            msg += "，导入成功数：" + (sucCount?sucCount:0) + "条， 开始计算";
                            this.$message.success(msg);
                            this.handleCalcImport(importId);
                        }
                    }
                } else if (info.file.status === 'error') {
                    this.$message.error(`${info.file.name} 导入失败`);
                }
            },
            /** 导出按钮操作 */
            handleExportExcel() {
                const searchForm =this.searchForm
                this.$confirm({
                    title: '提示',
                    content: '是否确认导出所有信息数据项?',
                    onOk:() => {
                        exportExcel(TEMPLATE_XSGSBUDGETMANAGER,{...searchForm},'预算导入模板.xlsx')
                    }
                })
            },
            /** 导出按钮操作 */
            handleExportTemplate() {
                exportExcel(TEMPLATE_XSGSBUDGETMANAGER,{},'预算导入模板.xlsx')
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.searchForm = {
                    customerName:null,
                    customerNo:null,
                }
                this.handleSearch();
            },
            handleEdit(record) {
                this.changeYear(1);
                if (record.id) {
                    this.form = {...record};
                    if (!this.form.customerList) {
                        this.form.customerList = [];
                    }
                    if (!this.form.customerNameList) {
                        this.form.customerNameList = [];
                    }
                    if (!this.form.blowoffList) {
                        this.form.blowoffList = [];
                    }
                    if (!this.form.seriesList) {
                        this.form.seriesList = [];
                    }
                    if (!this.form.productModels) {
                        this.form.productModels = [];
                    }
                    if (!this.form.strainList) {
                        this.form.strainList = [];
                    }
                    if (!this.form.strain1List) {
                        this.form.strain1List = [];
                    }
                    if (!this.form.strain2List) {
                        this.form.strain2List = [];
                    }
                    if (!this.form.strain3List) {
                        this.form.strain3List = [];
                    }
                } else {
                    this.form = {budgetType:'BASIC'};
                    var aData = new Date();
                    this.form.year= Number(this.$moment(aData).format('YYYY'));
                }
                this.dialog = true;
            },
            handleLogOk(){
                if (this.calcForm.id) {
                    request(CALC_XSGSBUDGETMANAGER, METHOD.POST, this.calcForm).then((res) => {
                        this.$message.info('提交成功，计算中')
                        this.confirmLoading = false;
                        this.closeForm();
                        this.getData()
                    })
                } else if (this.calcForm.importId) {
                    request(CALC_IMPORT_XSGSBUDGETMANAGER, METHOD.POST, this.calcForm).then((res) => {
                        this.$message.info('提交成功，计算中')
                        this.getData()
                    })
                }
                this.logDialog2 = false;
            },
            handleCalc(record){
                this.loading = true;
                let data = {
                    id:record.id
                };
                let that = this;
                request(CALC_XSGSBUDGETMANAGER, METHOD.POST, data)
                    .then((res) => {
                        this.loading=false;
                        if (res.data.code && res.data.code == 999999) {
                            this.$confirm({
                                content: res.data.msg,
                                cancelText:"查看日志",
                                onCancel:()=>{
                                    that.data2 = res.data.data;
                                    that.logDialog2 = true;
                                    that.calcForm = {
                                        id:record.id,
                                        checkOk:1
                                    }
                                },
                                onOk: () => {
                                    let data2 = {
                                        id:record.id,
                                        checkOk:1
                                    };
                                    request(CALC_XSGSBUDGETMANAGER, METHOD.POST, data2).then((res) => {
                                        that.$message.info('提交成功，计算中')
                                        that.confirmLoading = false;
                                        that.closeForm();
                                        that.getData()
                                    })
                                }
                            });
                        } else {
                            this.$message.info('提交成功，计算中')
                            this.confirmLoading = false;
                            this.closeForm();
                            this.getData()
                        }
                    })
            },
            handleCalcImport(importId){
                this.loading = true;
                let data = {
                    importId:importId
                };
                let that = this;
                request(CALC_IMPORT_XSGSBUDGETMANAGER, METHOD.POST, data)
                    .then((res) => {
                        this.loading=false;
                        if (res.data.code && res.data.code == 999999) {
                            this.$confirm({
                                content: res.data.msg,
                                cancelText:"查看日志",
                                onCancel:()=>{
                                    that.data2 = res.data.data.logs;
                                    that.logDialog2 = true;
                                    that.calcForm = {
                                        importId:res.data.data.importId,
                                        checkOk:1
                                    }
                                },
                                onOk: () => {
                                    let data2 = {
                                        importId:res.data.data.importId,
                                        checkOk:1
                                    };
                                    request(CALC_IMPORT_XSGSBUDGETMANAGER, METHOD.POST, data2).then((res) => {
                                        this.getData()
                                        this.$message.info('提交成功，计算中')
                                    })
                                }
                            });
                        } else {
                            this.getData()
                            this.$message.info('提交成功，计算中')
                        }
                    })
            },
            handleShowLog(record){

                this.logForm = {
                    budgetId:record.id,
                    logType:1
                }
                this.getLogData();

                if (record.importId) {
                    this.logForm = {
                        budgetId:record.id,
                        logType:2
                    }
                }
                this.logDialog1 = true;
            },
            showImportLog(){
                this.logForm = {
                    logType:2
                }
                this.getLogData();
                this.logDialog1 = true;
            },
            handleSearch() {
                this.pagination.current = 1
                this.getData()
            },
            handleSubmit(){
                this.$refs.form.validate(valid => {
                    if (valid) {
                        if (this.form.budgetType =='BASIC' && (!this.form.basicPrice || this.form.basicPrice.length == 0)) {
                            this.$message.error('请输入预算基本价');
                            return;
                        }

                        if (this.form.budgetType =='POINT' && (!this.form.dropPoint || this.form.dropPoint.length == 0)) {
                            this.$message.error('请输入预算降点');
                            return;
                        }

                        if (this.form.budgetType =='DECL' && (!this.form.dropDecline || this.form.dropDecline.length == 0)) {
                            this.$message.error('请输入预算降幅');
                            return;
                        }

                        this.confirmLoading = true;
                        this.form.customer = null;
                        request(SUBMIT_XSGSBUDGETMANAGER, METHOD.POST, this.form)
                            .then((res) => {
                                this.confirmLoading = false;
                               this.handleCalc(res.data.data)
                            })
                    }
                })

            },
            changeYear(flag){
                this.getSearchList(flag);
            },
            closeForm() {
                this.dialog = false
                this.confirmLoading = false;
                this.$refs.form.resetFields()
                this.form = {}
            },
            onPageChange(page, size) {
                this.pagination.current = page;
                this.pagination.size = size.toString();
                this.getData();
            },
            onSizeChange(current, size) {
                this.pagination.current = 1;
                this.pagination.size = size.toString();
                this.getData();
            },
            onLogPageChange(page, size) {
                this.pagination1.current = page;
                this.pagination1.size = size;
                this.getLogData();
            },
            onLogSizeChange(current, size) {
                this.pagination1.current = 1;
                this.pagination1.size = size;
                this.getLogData();
            },
            dateFormat(date){
              return dateFormat(date);
            },
            coverState(budState){
                if (budState=='WAIT') {
                    return '待计算';
                } else if (budState=='RUNNING') {
                    return '计算中';
                } else if (budState=='FINISH') {
                    return '完成';
                } else if (budState=='ERROR') {
                    return '出错';
                } else {
                    return '未知';
                }
            },
            coverBudgetType(budgetType){
                let type = '';
                if (budgetType == 'BASIC') {
                    type = '预算基本型';
                } else if (budgetType == 'POINT') {
                    type = '预算降点';
                } else if (budgetType == 'DECL') {
                    type = '预算降幅';
                } else {
                    type = '未知';
                }
                return type;
            },
            inputMoney(e){
                this.$nextTick(() => {
                    let val = e.target.value.toString();
                    if (val == ".") {
                        val = '';
                    } else {
                        val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
                        val = val.replace(/\.{2,}/g, ""); //只保留第⼀个. 清除多余的
                        val = val.replace(/^0+\./g, '0.');
                        val = val.match(/^0+[1-9]+/) ? val = val.replace(/^0+/g, '') : val
                        val = (val.match(/^\d*(\.?\d{0,2})/g)[0]) || ''
                    }
                    if (this.form.budgetType == 'DECL') {
                        this.form.dropDecline = val;
                    } else {
                        this.form.basicPrice = val;
                    }
                });
            },
            coverMoney(e) {
                let val = e.target.value.toString();
                if (val) {
                    if (val.includes(".")) {
                        if (val.endsWith(".")) {
                            val = val + "00";
                        } else {
                            let point = val.split(".")[1];
                            let intValue = val.split(".")[0];
                            point += "000";
                            val = intValue + "." + point.substr(0,2);
                        }
                    } else {
                        val = val + ".00";
                    }
                    if (this.form.budgetType == 'DECL') {
                        this.form.dropDecline = val;
                    } else {
                        this.form.basicPrice = val;
                    }

                }
            },

        },

    }
</script>

<style scoped>
    .ant-input-number{
        width: 100%;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }
    /*/deep/  样式穿透
    */
    /*/deep/.ant-col-8{*/
    /*    display: block;*/
    /*    -webkit-box-sizing: border-box;*/
    /*    box-sizing: border-box;*/
    /*    width: 100%;*/
    /*}*/
    /deep/.ant-form-item {
        text-align: right;
        margin: 0;
    }
    /deep/.ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }
    /deep/ .customerLabel {
        width: 60%;
    }
    /deep/ .customerLabel .ant-form-item-label {
        width: 13.8%;
    }
    /deep/ #optionBudget :after{
        -webkit-appearance: none !important;
    }
    /deep/ #year::-webkit-outer-spin-button,#year::-webkit-inner-spin-button{
        -webkit-appearance: none !important;
        -moz-appearance:textfield;
    }
    /deep/input::-webkit-outer-spin-button,input::-webkit-inner-spin-button {
        -webkit-appearance: none !important;
        -moz-appearance:textfield;
    }
    /deep/.ant-input-number-handler-wrap {
        display: none;
    }
</style>
