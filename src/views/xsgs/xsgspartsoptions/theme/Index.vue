<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度" /></td>
            <td><a-input-number v-model="searchForm.year"/></td>
            
            <td><a-form-model-item label="客户" /></td>
            <td style="min-width: 150px">
              <a-select
              style="width: 100%;"
                v-model="searchForm.customer"
                show-search
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :allowClear='true'
                @search="handleCustomerSearch"
                @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.customer">
                  {{ d.customer + '-' + d.name }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="板块" /></td>
            <td>
              <a-select v-model="searchForm.platformName" mode="multiple" style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="排放" /></td>
            <td>
              <a-select v-model="searchForm.blowoff" mode="multiple" style="min-width: 150px">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </td>
          </tr>
          <tr>
            <td><a-form-model-item label="品系" /></td>
            <td><a-input v-model="searchForm.type" style="min-width: 150px"/></td>

            <td><a-form-model-item label="系列" /></td>
            <td><a-input v-model="searchForm.series" style="min-width: 150px"/></td>

          <td><a-form-model-item label="产品型号[简化]" /></td>
          <td>
            <a-select v-model="baseCodes" mode="multiple" style="min-width: 150px">
              <a-select-option v-for="item in dh_list" :key="item"  >
                {{ item }}
              </a-select-option>
            </a-select>
          </td>
          <td><a-form-model-item label="主题" /></td>
          <td><a-input v-model="searchForm.name" style="min-width: 150px"/></td>

          </tr>
          <tr>
            <td></td><td></td><td></td><td></td><td></td><td></td>
            <td colspan="2" class="subTable">
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('PARTS_OPTIONS_NEW_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <span slot="action" slot-scope="record">
          <a @click="toProcces(record,'N')">查看</a>
          <a-divider type="vertical"/>
          <a @click="toProcces(record,'Y')">零件配置</a>
          <a-divider type="vertical"/>
          <a @click="toCodeProcces(record)">代号配置</a>
          <a-divider v-if="checkPf('PARTS_OPTIONS_NEW_EDIT')" type="vertical"/>
          <a v-if="checkPf('PARTS_OPTIONS_NEW_EDIT')" @click="handleEdit(record)">编辑</a>
          <a-divider v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" type="vertical"/>
          <a v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" @click="handleDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="50%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="年度" prop="year">
                <a-input-number v-model.trim="form.year"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="清单名称" prop="name">
                <a-input v-model.trim="form.name"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="板块" prop="platformName">
                <a-select v-model="form.platformName"  style="width: 100%">
                  <a-select-option value="卡车" >卡车</a-select-option>
                  <a-select-option value="客车" >客车</a-select-option>
                  <a-select-option value="新能源" >新能源</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="排放" prop="blowoff">
                <a-select v-model="form.blowoff"  style="width: 100%">
                  <a-select-option value="国6" >国6</a-select-option>
                  <a-select-option value="国5" >国5</a-select-option>
                  <a-select-option value="国4" >国4</a-select-option>
                  <a-select-option value="国3" >国3</a-select-option>
                  <a-select-option value="国2" >国2</a-select-option>
                  <a-select-option value="国1" >国1</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="品系" prop="type">
                <a-input v-model.trim="form.type"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="系列" prop="series">
                <a-input v-model.trim="form.series"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          
      </a-form-model>
    </a-modal>

    <!-- <a-modal
      :title="form.id?'编辑':'新增'"
      :visible="optionDialog"
      :confirm-loading="confirmOptionLoading"
      width="50%"
      @ok="handleOptionSubmit"
      @cancel="closeOptionForm">

      <editOptional 
        :masterId="masterId" 
        :basicSubtotal="searchForm.basicSubtotal"  
        :searchForm="searchForm"
        :year="searchForm.year"
        :optionList="optionList"
        :productModelJh="searchForm.productModelJh"  
        ref="editOptional">
      </editOptional>

    </a-modal> -->

  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSCUSTOMER,
  LIST_XSGSPARTSOPTIONTHEME_PAGE,
  DELETE_XSGSPARTSOPTIONTHEME,
  SUBMIT_XSGSPARTSOPTIONTHEME,
  UPLOAD_XSGSPARTSOPTION,
  DELETE_XSGS_PARTS_OPTION_THEME_LIST,
  GETSEACHLIST_XSGSPARTSOPTIONTHEME
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgspartsoption.vue",
  components: {
    // editOptional
  },
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSOPTION,
      Cookie,
      loading: false,
      dialog: false,
      optionDialog: false,
      confirmLoading: false,
      confirmOptionLoading: false,
      searchForm: {},
      customerList: [],
      customerAllList: [],
      searchCustomer: '',
      pageCustomer: '',

      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,PARTS_OPTIONS_NEW",
      PF_LIST:[],

      baseCodes:[], //基本型代号
      dh_list:[], //基本型代号
      form: {},
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
        this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num',  key: 'num',  width: 50,  align: 'center',  scopedSlots: {customRender: 'num'}},
        {title: '年度', dataIndex: 'year', width: 50},
        {title: '清单名称', dataIndex: 'name'},
        {title: '板块', dataIndex: 'platformName', width: 80},
        {title: '排放', dataIndex: 'blowoff', width: 50},
        {title: '品系', dataIndex: 'type', width: 50},
        {title: '系列', dataIndex: 'series', width: 80},
        {title: '产品型号[简化]', dataIndex: 'jbx'},
        {title: '操作', fixed: 'right',  width: 300, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        id: [{required: true, message: 'id不能为空', trigger: 'blur'}],
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        name: [{required: true, message: '清单名称不能为空', trigger: 'blur'}],
        platformName: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
        series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.checkName();
    this.getCustomerList();
    this.getSearchList();
    let path = this.$route.path.split("/");
    if(path[3]){
      this.searchCustomer = path[3];
      this.pageCustomer = path[3];
      this.searchForm.customer = path[3];
    }
    this.getData();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getData: function () {
      let that = this;
      that.loading = true;
      let {current, size} = that.pagination;
      that.searchForm.baseCodes = that.baseCodes;
      request(LIST_XSGSPARTSOPTIONTHEME_PAGE, METHOD.GET, {...that.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data;
        that.loading = false;
        that.data = records;
        that.pagination.total = parseInt(total);
        // 真实查询客户号
        if (that.searchForm.customer) {
          that.searchCustomer = that.searchForm.customer;
        } else {
          that.searchCustomer = that.pageCustomer;
        }
      });
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
     /** 获取系列 **/
     getSearchList() {
      request(GETSEACHLIST_XSGSPARTSOPTIONTHEME, METHOD.GET).then(res => {
        //基本型代号
        if (res.data.data.dh != null) {
          this.dh_list = res.data.data.dh;
        }
      });
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_PARTS_OPTION_THEME_LIST, METHOD.POST,{ids:this.selectedRowKeys})
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSOPTIONTHEME + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      let that = this;
      this.$refs.form.validate(valid => {
        if (valid) {
          that.loading = true;
          that.form.customer = that.searchCustomer;
          request(SUBMIT_XSGSPARTSOPTIONTHEME, METHOD.POST, that.form)
                  .then(res => {
            that.getData();
            if (res.data.code == -1) {
              that.$message.error(res.data.msg);
            } else {
              that.$message.info('提交成功');
            }
            that.closeForm();
          })
        }
      })
    },
    toProcces(data, type) {
      if(type === 'Y'){
        type = this.checkPf('PARTS_OPTIONS_NEW_EDIT') ? 'Y' : 'N';
      }
      this.$router.push({path:'/setting/xsgs_parts_options_part?isEdit=' + type + '&customer=' + data.customer + '&cid=' + data.id , props: true})
    },
    toCodeProcces(data) {
      this.$router.push({path:'/setting/xsgs_parts_options_code?cid=' + data.id , props: true})
    },
    /** 新增 产品型号[简化] 输入回调 */
    changes(){
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`请先填写系列`);
        this.form.productModel = undefined
      }
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true;
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true;
      this.form = {...record};
    },
    handleOptionEdit(record) {
      this.optionDialog = true;
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1;
      this.getData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModel=[],
      this.blowoff=[],
      this.plate=[],
      this.series=[],
      this.type=[],

      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false;
      this.$refs.form.resetFields();
      this.form = {};
    },
    closeOptionForm() {
      this.optionDialog = false;
      this.$refs.editOptional.clreanData();
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
        const map = new Map();
        this.customerAllList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1));
        this.customerList = this.customerAllList;
      });
    },
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.customerAllList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.customerAllList;
      }
    },
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.customerAllList;
      }
      this.$forceUpdate();
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
/deep/.subTable {
    margin-left: 10px;
}
</style>
