<template>
  <div>
    <a-card>
      <div class="header-button-area">
        <a-button :disabled="isSaving" :loading="loading" type="primary" @click="handleSave" v-if="edit">
          <a-icon type="save"/>
          保存
        </a-button>
      </div>
      <a-spin :spinning="isSaving">
        <a-form-model style="margin-top: 30px" ref="dataFormRef" :model="dataForm" :labelCol="{span: 8}"
                      :wrapperCol="{span: 16}">
          <a-card title="选型选装型配置清单">
            <a-row>
              <div>
              <!-- 查看 -->
                <a-table
                    :columns="seeOptionalColumns"
                    rowKey="num"
                    :pagination="false"
                    :data-source="dataForm.optionalData"
                    :scroll="{ x: 520 }"
                    size="small"
                    bordered
                    v-if="!edit"
                    :loading="loading">
                  <template slot="num" slot-scope="text, record, index">
                    {{ index + 1 }}
                  </template>
                </a-table>
                <!-- 编辑 -->
                <a-table
                    :columns="optionalColumns"
                    rowKey="num"
                    :pagination="false"
                    :data-source="dataForm.optionalData"
                    :scroll="{ x: 520 }"
                    size="small"
                    bordered
                    v-if="edit"
                    :loading="loading">
                  <span slot="componentName">
                    <template>部件名称</template>
                  </span>
                  <span slot="componentCode">
                    <template>部件结构码</template>
                  </span>
                  <span slot="partsName">
                  <span style="color: red">*</span>
                    <template>零部件名称</template>
                  </span>
                  <span slot="jgCode">
                    <template>零部件结构码</template>
                  </span>
                  <span slot="mainType">
                    <span style="color: red">*</span>
                    <template>类型</template>
                  </span>
                  <template slot="num" slot-scope="text, record, index">
                    {{ index + 1 }}
                  </template>
                  <template slot="componentName" slot-scope="text, record, index">
                    <a-form-model-item :prop="'optionalData.' + index + '.componentName'" :wrapper-col="{ span:24 }">
                      <a-input v-model="record.componentName"/>
                    </a-form-model-item>
                  </template>
                  <template slot="componentCode" slot-scope="text, record, index">
                    <a-form-model-item :prop="'optionalData.' + index + '.componentCode'" :wrapper-col="{ span:24 }">
                      <a-input v-model="record.componentCode"/>
                    </a-form-model-item>
                  </template>
                  <template slot="partsName" slot-scope="text, record, index">
                    <a-form-model-item :prop="'optionalData.' + index + '.partsName'" :rules="rules.partsName" :wrapper-col="{ span:24 }">
                      <a-input v-model="record.partsName"/>
                    </a-form-model-item>
                  </template>
                  <template slot="jgCode" slot-scope="text, record, index">
                    <a-form-model-item :prop="'optionalData.' + index + '.jgCode'" :rules="rules.jgCode" :wrapper-col="{ span:24 }">
                      <a-input v-model="record.jgCode"/>
                    </a-form-model-item>
                  </template>
                  <template slot="mainType" slot-scope="text, record, index">
                    <a-form-model-item :prop="'optionalData.' + index + '.mainType'" :rules="rules.mainType" :wrapper-col="{ span:24 }">
                      <a-select :style="`width: 100%;`" v-model="record.mainType">
                          <a-select-option value="选型件">选型件</a-select-option>
                          <a-select-option value="选装件">选装件</a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </template>
                  <template slot="action" slot-scope="text, record, index">
                    <a v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" @click="handleDelete(record, index)">删除</a>
                  </template>
                </a-table>

                <div>
                  <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus" @click="newMember" v-if="edit">
                    新增
                  </a-button>
                </div>
              </div>
            </a-row>
          </a-card>
        </a-form-model>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
  import {
      LIST_XSGSPARTSOPTION_PAGE,
      GETONE_XSGSPARTSOPTIONTHEME,
      DELETE_XSGSPARTSOPTION,
      getUuid,
      SAVEORUPDATE_XSGSPARTSOPTION
  } from "@/services/api/xsgs";
  import {METHOD, request} from "@/utils/request";
  export default {
    name: "edit_optional",
    data() {
        return {
            isSaving: false,
            //选型选装配置
            searchForm: {
            },
            dataForm: {
              optionalData: [],
            },
            // 功能权限关键字
            PF_FIELD:"BASE_DATA_MANAGER,PARTS_OPTIONS_NEW",
            PF_LIST:[],
            cid: '',
            customer: '',
            optionTheme: {},
            loading: false,
            edit: true,
            optionalColumns: [
                {title: '序号', dataIndex: 'num',width: 50, align: 'center', fixed: true, scopedSlots: { customRender: 'num' }},
                {slots: {title: 'componentName'}, dataIndex: 'componentName', scopedSlots: {customRender: 'componentName'}},
                {slots: {title: 'componentCode'}, dataIndex: 'componentCode', scopedSlots: {customRender: 'componentCode'}},
                {slots: {title: 'partsName'}, dataIndex: 'partsName', scopedSlots: {customRender: 'partsName'}},
                {slots: {title: 'jgCode'}, dataIndex: 'jgCode', scopedSlots: {customRender: 'jgCode'}},
                {slots: {title: 'mainType'}, dataIndex: 'mainType', scopedSlots: {customRender: 'mainType'}},
                {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
            ],
            rules: {
              partsName: {required: true, message: '零部件名称不能为空', trigger: 'blur'},
              mainType: {required: true, message: '类型不能为空', trigger: 'blur'},
            },
            seeOptionalColumns: [
                {title: '序号', dataIndex: 'num',width: 50, scopedSlots: { customRender: 'num' }},
                {title: '部件名称', dataIndex: 'componentName', scopedSlots: { customRender: 'componentName'}},
                {title: '部件结构码', dataIndex: 'componentCode', scopedSlots: { customRender: 'componentCode'}},
                {title: '零部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }},
                {title: '零部件结构码', dataIndex: 'jgCode', scopedSlots: { customRender: 'jgCode' }},
                {title: '类型', dataIndex: 'mainType', scopedSlots: { customRender: 'mainType' }},
            ]
        }
    },
    created() {
        this.searchForm = { ...this.$route.params, ...this.$route.query };
        // 获取页面功能权限
        this.getUserAuth(this.PF_FIELD);
        this.PF_LIST = this.getPfList(this.PF_FIELD);
        if(!this.searchForm.cid){
            this.edit = false;
            this.$notification.error({
                message: '操作失败',
                description: '无关联ID',
            });
        } else {
            this.cid = this.searchForm.cid;
            this.customer = this.searchForm.customer == 'A00003' ? '' : this.searchForm.customer;
            this.searchForm.customer = this.customer;
            this.getThemeData();
            this.edit = this.searchForm.isEdit == 'Y' ? true : false;
        }
    },
    methods: {
      checkPf(field) {
        return this.PF_LIST.findIndex(s => s == field) != -1;
      },
      getThemeData: function () {
        this.loading = true;
        let {id} = {
            id: this.cid
        };
        request(GETONE_XSGSPARTSOPTIONTHEME, METHOD.GET, {id})
                .then(res => {
            if(res.data.data) {
                this.optionTheme = res.data.data;
                this.getData();
                this.checkName();
            } else {
                this.edit = false;
                this.loading = false;
            }
        });
      },
      getData: function () {
        this.loading = true;
        let {current, size} = {
            current: 1,
            size: '999999',
        };
        request(LIST_XSGSPARTSOPTION_PAGE, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
            const {records} = res.data.data;
            this.loading = false;
            this.dataForm.optionalData = records;
            
        });
      },
      //新增
      newMember() {
        request(getUuid, METHOD.GET).then(res => {
          this.dataForm.optionalData.push({
              id: res.data.data,
              platformName: this.optionTheme.platformName,
              blowoff: this.optionTheme.blowoff,
              series: this.optionTheme.series,
              productModel: '',
              type: this.optionTheme.type,
              partsName: '',
              jgCode: '',
              mainType: '',
              customer: this.customer,
              year: this.optionTheme.year,
              cid: this.cid,
          });
          this.$forceUpdate();
        });
      },
      //删除
      handleDelete(record, index) {
        this.$confirm({
            content: `是否确认删除 ？`,
            onOk: () => {
                this.loading = true;
                request(DELETE_XSGSPARTSOPTION + record.id, METHOD.DELETE).then(() => {
                    this.$message.info('删除成功');
                    this.dataForm.optionalData.splice(index, 1);
                    this.loading = false;
                }).catch(() => {
                    this.loading = false;
                })
            }
        });
      },
      /** 保存数据1 **/
      handleSave() {
        this.$refs['dataFormRef'].validate(valid => {
          if (valid) {
            this.$confirm({
              content: `是否保存当前数据？`,
              onOk: () => {
                this.handleSaveAction()
              },
            });
          }
        });
      },
      handleSaveAction() {
        this.loading = true;
        let config = {
            headers:{
                "Content-Type": "application/json"
            }
        };
        request(SAVEORUPDATE_XSGSPARTSOPTION, METHOD.POST, JSON.stringify(this.dataForm.optionalData), config).then(res => {
              this.dataForm.optionalData = res.data.data;
              this.$message.success('保存成功');
              this.getData();
        }).catch((e) => {
            this.$message.error('保存失败');
            this.loading = false;
        }).catch((e) => {
            this.$message.error("保存失败" + e);
            this.loading = false;
        });
      }
    }
  }
</script>

<style scoped>
/deep/ .header-button-area {
position: fixed;
z-index: 999;
height: 45px;
background-color: #ffffff;
line-height: 45px;
margin-top: -25px;
min-width: 700px;
}

/deep/ .ant-form-item {
margin: 0;
}
</style>
