<template>
    <div>
      <a-card>
        <div class="header-button-area">
          <a-button :disabled="isSaving" :loading="loading" type="primary" @click="handleSave" v-if="checkPf('PARTS_OPTIONS_NEW_EDIT')" >
            <a-icon type="save"/>
            保存
          </a-button>
        </div>
        <a-spin :spinning="isSaving">
          <a-form-model style="margin-top: 30px" ref="dataFormRef" :model="dataForm" :labelCol="{span: 8}"
                        :wrapperCol="{span: 16}" >
            <a-card title="代号配置清单">
              <a-row>
                <div>
                  <a-table
                      :columns="optionalColumns"
                      rowKey="num"
                      :pagination="false"
                      :data-source="dataForm.optionalData"
                      :scroll="{ x: 520 }"
                      size="small"
                      bordered
                      :loading="loading">
                    <span slot="productModel">
                    <span style="color: red">*</span>
                      <template>产品型号[简化]</template>
                    </span>
                    <template slot="num" slot-scope="text, record, index">
                      {{ index + 1 }}
                    </template>
                    <template slot="productModel" slot-scope="text, record, index">
                      <a-form-model-item :prop="'optionalData.' + index + '.productModel'" :rules="rules.productModel" :wrapper-col="{ span:24 }">
                        <a-input v-model="record.productModel" />
                      </a-form-model-item>
                    </template>
                    
                    <template slot="action" slot-scope="text, record, index">
                      <a v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" @click="handleDelete(record, index)">删除</a>
                    </template>
                  </a-table>

                  <div>
                    <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" 
                      v-if="checkPf('PARTS_OPTIONS_NEW_EDIT')" icon="plus" @click="newMember" >
                      新增
                    </a-button>
                  </div>
                </div>
              </a-row>
            </a-card>
          </a-form-model>
        </a-spin>
      </a-card>
    </div>
  </template>

<script>
    import {
      LIST_XSGSPARTSOPTIONCODE_PAGE,
        DELETE_XSGSPARTSOPTIONCODE,
        getUuid,
        SAVEORUPDATE_XSGSPARTSOPTIONCODE
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "edit_optional",
        data() {
          return {
            isSaving: false,
            //选型选装配置
            searchForm: {},
            dataForm: {
              optionalData: [],
            },

            // 功能权限关键字
            PF_FIELD:"BASE_DATA_MANAGER,PARTS_OPTIONS_NEW",
            PF_LIST:[],

            cid: '',
            optionTheme: {},
            loading: false,
            optionalColumns: [
                {title: '序号', dataIndex: 'num',width: 50, align: 'center', fixed: true, scopedSlots: { customRender: 'num' }},
                {slots: {title: 'productModel'}, dataIndex: 'productModel', scopedSlots: { customRender: 'productModel' }},
                {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
            ],
            rules: {
              productModel: {required: true, message: '产品型号', trigger: 'blur'},
            }
          }
        },
        created() {
          this.searchForm = { ...this.$route.params, ...this.$route.query };
          // 获取页面功能权限
          this.getUserAuth(this.PF_FIELD);
          this.PF_LIST = this.getPfList(this.PF_FIELD);
          if(!this.searchForm.cid){
              this.$notification.error({
                  message: '操作失败',
                  description: '无关联ID',
              });
          } else {
            this.cid = this.searchForm.cid;
            this.getData();
            this.checkName();
          }
        },
        methods: {
          checkPf(field) {
            return this.PF_LIST.findIndex(s => s == field) != -1;
          },
          getData: function () {
            this.loading = true;
            let {current, size} = {
                current: 1,
                size: '999999',
            };
            request(LIST_XSGSPARTSOPTIONCODE_PAGE, METHOD.GET, {...this.searchForm, current, size})
                    .then(res => {
                const {records} = res.data.data;
                this.loading = false;
                this.dataForm.optionalData = records;
            });
          },
          //新增
          newMember() {
            request(getUuid, METHOD.GET).then(res => {
                this.dataForm.optionalData.push({
                    id: res.data.data,
                    productModel: this.optionTheme.productModel,
                    cid: this.cid
                })
            });
          },
          //删除
          handleDelete(record, index) {
            if(record.id && record.productModel && record.productModel != ''){
              this.$confirm({
                  content: `是否确认删除 ？`,
                  onOk: () => {
                      this.loading = true;
                      request(DELETE_XSGSPARTSOPTIONCODE + record.id, METHOD.DELETE).then(() => {
                          this.$message.info('删除成功');
                          this.dataForm.optionalData.splice(index, 1);
                          this.loading = false;
                      }).catch(() => {
                          this.loading = false;
                      })
                  }
              });
            }else{
              // this.$message.error('请先填写产品型号');
              this.dataForm.optionalData.splice(index, 1);
            }
          },
          /** 保存数据1 **/
          handleSave() {
            this.$refs['dataFormRef'].validate(valid => {
              if (valid) {
                this.$confirm({
                  content: `是否保存当前数据？`,
                  onOk: () => {
                    this.handleSaveAction()
                  },
                });
              }
            });
          },
          handleSaveAction() {
            this.loading = true;
            let config = {
                headers:{
                    "Content-Type": "application/json"
                }
            };
            request(SAVEORUPDATE_XSGSPARTSOPTIONCODE, METHOD.POST, JSON.stringify(this.dataForm.optionalData), config).then(res => {
              if(res.data.code == '0') {
                this.dataForm.optionalData = res.data.data;
                this.$message.success('保存成功');
                this.getData();
              } else {
                this.$message.error("保存失败:" + res.data.msg);
                this.loading = false;
              } 
            }).catch((e) => {
                this.$message.error("保存失败" + e);
                this.loading = false;
            });
          }
        }
    }
</script>

<style scoped>
/deep/ .header-button-area {
  position: fixed;
  z-index: 999;
  height: 45px;
  background-color: #ffffff;
  line-height: 45px;
  margin-top: -25px;
  min-width: 700px;
}

/deep/ .ant-form-item {
  margin: 0;
}
</style>
