<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" >
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>

            <td><a-form-model-item label="客户" /></td>
            <td>
              <a-select
                  style="width: 450px;"
                  v-model="searchForm.customer"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :allowClear='true'
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch"
                  @change="handleCustomerChange"
                  :row-selection="{selectedRowKeys:selectedRowKeys,onChange:onSelectChange}"
              >
                <a-select-option v-for="d in customerList" :key="d.customer">
                  {{ d.customer + '-' +d.name }}
                </a-select-option>
              </a-select>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>

          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('PARTS_OPTIONS_NEW_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('PARTS_OPTIONS_NEW_IMPORT')"
                    name="file"
                    :action="UPLOAD_XSGSPARTSOPTIONCUSTOMER"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('PARTS_OPTIONS_NEW_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
        <template slot="theme" slot-scope="index, record">
          <a @click="toProcces(record)">{{ record.theme }}</a>
        </template>
        <span slot="action" slot-scope="record">
          <a v-if="checkPf('PARTS_OPTIONS_NEW_EDIT')" @click="handleEdit(record)">编辑</a>
          <a-divider v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" type="vertical"/>
          <a v-if="checkPf('PARTS_OPTIONS_NEW_DELETE')" @click="handleDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="50%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="主题" prop="theme">
                <a-input v-model.trim="form.theme"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="客户号" prop="customer">
                <a-input v-model.trim="form.customer" @change="formCustomerChange" />
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="20" :md="4" :sm="24">
              <a-form-model-item label="客户名称" prop="customerName">
                <a-input v-model.trim="form.customerName"/>
              </a-form-model-item>
            </a-col>
          </a-row>
          
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSCUSTOMER,
  LIST_XSGSPARTSOPTIONCUSTOMER_PAGE,
  DELETE_XSGSPARTSOPTIONCUSTOMER,
  SUBMIT_XSGSPARTSOPTIONCUSTOMER,
  DOWNLOAD_XSGSPARTSOPTIONCUSTOMER,
  UPLOAD_XSGSPARTSOPTIONCUSTOMER,
  DELETE_XSGS_PARTS_OPTION_CUSTOMER_LIST
} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgspartsoption.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSOPTIONCUSTOMER,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      searchForm: {},
      customerAllList: [],
      customerList: [],
      formCustomerList: [],

      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,PARTS_OPTIONS_NEW",
      PF_LIST:[],

      form: {},
      selectedRowKeys: [],
      selectedRows: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
        this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [{
        title: '序号', 
        dataIndex: 'num', 
        key: 'num', 
        width: 50, 
        align: 'center',  
        scopedSlots: {
          customRender: 'num'
        }
      },{
        title: '主题', 
        dataIndex: 'theme',
        width:250,
        scopedSlots: {
          customRender: 'theme'
        }
      },{
        title: '客户号', 
        dataIndex: 'customer'
      },{
        title: '客户名称', 
        dataIndex: 'customerName'
      },{
        title: '操作', 
        fixed: 'right', 
        width: 165, 
        scopedSlots: {
          customRender: 'action'
        }
      }],
      rules: {
        id: [{required: true, message: 'id不能为空', trigger: 'blur'}],
        theme: [{required: true, message: '主题不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData();
    this.checkName();
    this.getCustomerList();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getData: function () {
      this.loading = true;
      let {current, size} = this.pagination;
      request(LIST_XSGSPARTSOPTIONCUSTOMER_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data;
        this.loading = false;
        this.data = records;
        this.pagination.total = parseInt(total);
      });
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      if(selectedRows.length > 0){
        selectedRows.forEach(item => {
          if(this.selectedRows.length > 0 && this.selectedRows.indexOf(item.id) == -1 ||
              this.selectedRows.length == 0){
            this.selectedRows.push(item.id);
          }
        })
      }
    },
    deleteSelectedIds(){
      if (this.selectedRowKeys.length <= 0) {
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGS_PARTS_OPTION_CUSTOMER_LIST, METHOD.POST, this.selectedRows)
              .then(() => {
                this.$message.info('删除成功')
                this.getData();
              }).catch(() => {
                this.loading = false;
          });
        }
      });
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSOPTIONCUSTOMER + record.id, METHOD.DELETE)
                  .then((res) => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSPARTSOPTIONCUSTOMER, METHOD.POST, this.form)
                  .then((res) => {
            this.getData()
            if (res.data.code == -1) {
              this.$message.error(res.data.msg);
            } else {
              this.$message.success(res.data.msg);
            }
            this.closeForm();
          })
        }
      })
    },
    toProcces(data) {
      this.$router.push({path:'/setting/xsgs_parts_options_theme/' + data.customer, props: true})
    },
    /** 新增 产品型号[简化] 输入回调 */
    changes(){
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`请先填写系列`);
        this.form.productModel = undefined
      }
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModel=[],
      this.blowoff=[],
      this.plate=[],
      this.series=[],
      this.type=[],

      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        let msg = `${info.file.name} 上传成功`;
        if (info.file.response && info.file.response.data) {
          let data = info.file.response.data;
          let errList = data.errorList;
          let sucCount = data.sucCount;
          if (errList && errList.length > 0) {
            msg +="\n存在错误数据：" + errList.length + "条\n失败原因如下：";
            errList.forEach(item => {
              msg += "\n" + item+"；"
            })
            this.$warning({
              title: '导入失败提示',
              onOk: () => {

              },
              content: (
                  <div style="white-space: pre-wrap;">{msg}</div>
              ),
              width:500
            });
          } else {
            msg += "，导入成功数：" + (sucCount?sucCount:0) + "条";
            this.$message.success(msg);
          }
        }
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm;
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {

          exportExcel(DOWNLOAD_XSGSPARTSOPTIONCUSTOMER, {...searchForm}, "选型选装清单-新.xlsx")

        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
        const map = new Map();
        this.customerAllList = res.data.data.filter(key => !map.has(key.customer) && map.set(key.customer, 1));
        this.customerList = this.customerAllList;
      })
    },
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.customerAllList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.customerAllList;
      }
    },
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.customerAllList;
      }
    },
    formCustomerChange() {
      let value = this.form.customer;
      if (value) {
        this.formCustomerList = this.customerAllList.filter(s=>s.customer == value);
        if (this.formCustomerList.length == 1) {
          this.form.customerName = this.formCustomerList[0].name;
        }
      }
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
