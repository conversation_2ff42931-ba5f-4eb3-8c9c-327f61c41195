<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 21}">
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度" /></td>
            <td><a-input v-model="searchForm.zyear"/></td>

            <td><a-form-model-item label="客户" /></td>
            <td  style="min-width: 150px">
              <a-select
                  style="width: 100%;"
                  v-model="searchForm.customerNameList"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  mode="multiple"
                  :filter-option="false"
                  :not-found-content="null"
                  :allowClear='true'
                  @search="handleCustomerSearch"
                  @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.customer + '-' + d.name }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="板块" /></td>
            <td>
              <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="排放" /></td>
            <td>
              <a-select v-model="blowoff" mode="multiple"  style="min-width: 150px">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </td>
          </tr>
          <tr>
            <td><a-form-model-item label="品系" /></td>
            <td>
              <a-select v-model="type" mode="multiple" style="min-width: 150px">
                <a-select-option v-for="item in px_list" :key="item"  >
                  {{ item }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="系列" /></td>
            <td>
              <a-select v-model="series" mode="multiple" style="min-width: 150px">
                <a-select-option v-for="item in zxl_list" :key="item"  >
                  {{ item }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="产品型号[简化]" /></td>
            <td>
              <a-select v-model="productModels" mode="multiple" style="min-width: 150px">
                <a-select-option v-for="item in dh_list" :key="item"  >
                  {{ item }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="主题"/></td>
            <td><a-input v-model="searchForm.theme"/></td>

            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('PARTS_SETTING_ADD_NEW')" type="primary" style="margin: 0 8px"
                        @click="dialog=true;form={customer: null,customerName: null,basicName: null,cid:cid}">
                <a-icon type="plus"/>
                新增
              </a-button>
              <a-button v-if="checkPf('PARTS_SETTING_DELETE_NEW')" type="danger" style="margin: 0 8px"
                        @click="deleteSelectedIds">
                <a-icon type="delete"/>
                删除
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          rowKey="id"
          :columns="columns"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{selectedRowKeys:selectedRowKeys,onChange:onSelectChange}"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <span slot="action" slot-scope="record">
          <a @click="handleLook(record)">查看</a>
          <a-divider type="vertical"/>
          <a @click="partEdit(record)">零件配置</a>
          <a-divider type="vertical"/>
          <a @click="codeEdit(record)">代号配置</a>
          <a-divider v-if="checkPf('PARTS_SETTING_EDIT_NEW')" type="vertical"/>
          <a v-if="checkPf('PARTS_SETTING_EDIT_NEW')" @click="handleEdit(record)">编辑</a>
          <a-divider v-if="checkPf('PARTS_SETTING_DELETE_NEW')" type="vertical"/>
          <a v-if="checkPf('PARTS_SETTING_DELETE_NEW')" @click="handleDelete(record)">删除</a>
        </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 5}" :wrapperCol="{span: 16}">
        <a-form-model-item label="年度" prop="zyear">
          <a-input v-model="form.zyear"/>
        </a-form-model-item>
        <a-form-model-item label="主题" prop="theme">
          <a-input v-model="form.theme"/>
        </a-form-model-item>
        <a-form-model-item label="板块" prop="platformName">
          <a-select v-model="form.platformName"  style="width: 100%">
            <a-select-option value="卡车" >卡车</a-select-option>
            <a-select-option value="客车" >客车</a-select-option>
            <a-select-option value="新能源" >新能源</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="排放" prop="blowoff">
          <a-select v-model="form.blowoff"  style="width: 100%">
            <a-select-option value="国6" >国6</a-select-option>
            <a-select-option value="国5" >国5</a-select-option>
            <a-select-option value="国4" >国4</a-select-option>
            <a-select-option value="国3" >国3</a-select-option>
            <a-select-option value="国2" >国2</a-select-option>
            <a-select-option value="国1" >国1</a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="品系" prop="type">
          <a-input v-model="form.type"/>
        </a-form-model-item>
        <a-form-model-item label="系列" prop="series">
          <a-input v-model="form.series"/>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {
  LIST_XSGSPARTSSETTINGTHEME_PAGE,
  DELETE_XSGSPARTSSETTINGTHEME,
  SUBMIT_XSGSPARTSSETTINGTHEME,
  LIST_XSGSCUSTOMER,
  DELETE_XSGSPARTSSETTINGTHEME_LIST,
  GETSEACHLIST_XSGSPARTSSETTINGTHEME
} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";

export default {
  name: "theme.vue",
  data() {
    return {
      loading: false,
      dialog: false,
      // 功能权限关键字
      PF_FIELD: "BASE_DATA_MANAGER,PARTS_SETTING_NEW",
      PF_LIST: [],
      searchForm: {customerNameList:[]},
      form: {
        customer: null,
        customerName: null,
        basicName: null,
        cid: null,
      },
      selectedRowKeys: [],
      selectedRows: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', dataIndex: 'zyear'},
        {title: '主题', dataIndex: 'theme', width: 250},
        {title: '板块', dataIndex: 'platformName'},
        {title: '排放', dataIndex: 'blowoff'},
        {title: '品系', dataIndex: 'type'},
        {title: '系列', dataIndex: 'series'},
        {title: '产品型号[简化]', dataIndex: 'productModel', width: 200, ellipsis: true},
        {title: '操作', fixed: 'right', width: 300, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        zyear: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        theme: [{required: true, message: '主题不能为空', trigger: 'blur'}],
        platformName: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
        type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
        series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
      },
      customerList: [],
      customerList2: [],
      allCustomerList: [],
      plate:[], //板块
      blowoff:[], //排放
      type:[], //品系
      px_list:[],//品系
      series:[], //系列
      zxl_list:[], //系列
      productModels:[], //产品型号[简化]
      dh_list:[], //基本型代号
      cid: null
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.checkName()
    let path = this.$route.path.split("/");
    this.searchForm.cid = path[3];
    this.cid = path[3];
    if(path[4] != "客户名称"){
      this.searchForm.customerNameList.push(path[4])
    }
    this.getCustomerList();
    this.getData()
    this.getSearchList()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    onSelectChange(selectedRowKeys,selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      if(selectedRows.length > 0){
        selectedRows.forEach(item => {
          if(this.selectedRows.length > 0 && this.selectedRows.indexOf(item.id) == -1 ||
              this.selectedRows.length == 0){
            this.selectedRows.push(item.id);
          }
        })
      }
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      this.searchForm.plates = this.plate
      this.searchForm.blowoffs = this.blowoff
      this.searchForm.types = this.type
      this.searchForm.seriess = this.series
      this.searchForm.productModels = this.productModels
      request(LIST_XSGSPARTSSETTINGTHEME_PAGE, METHOD.POST, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    /** 获取系列 **/
    getSearchList() {
      request(GETSEACHLIST_XSGSPARTSSETTINGTHEME, METHOD.GET).then(res => {
        //系列
        if (res.data.data.xl != null) {
          this.zxl_list = res.data.data.xl
        }
        //品系
        if (res.data.data.px != null) {
          this.px_list = res.data.data.px
        }
        //产品型号【简化】
        if (res.data.data.dh != null) {
          this.dh_list = res.data.data.dh
        }
      })
    },
    /** 批量删除 **/
    deleteSelectedIds() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTINGTHEME_LIST, METHOD.POST, this.selectedRows)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    /** 提交 **/
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          request(SUBMIT_XSGSPARTSSETTINGTHEME, METHOD.POST, this.form)
              .then(() => {
                this.getData()
                this.$message.info('提交成功')
                this.closeForm()
              })
        }
      })
    },
    /** 查询 **/
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.plate = [];
      this.blowoff = [];
      this.type = [];
      this.series = [];
      this.productModels = [];
      let cid = this.searchForm.cid
      this.searchForm = {customerNameList:this.searchForm.customerNameList};
      this.searchForm.cid = cid;
      this.handleSearch();
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    /** 获取所有客户 */
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
        const map = new Map()
        this.allCustomerList = res.data.data.filter(key => !map.has(key.name) && map.set(key.name, 1))
        this.customerList = this.allCustomerList
        this.customerList2 = this.allCustomerList
      })
    },
    /** 新增输入客户号 **/
    customerBlurs2() {
      let customer = this.form.customer;
      if (!customer) {
        this.form.customerName = null;
      }
      let newData = this.customerList2.filter(item => item.customer == customer);
      if (newData.length > 0) {
        this.form.customerName = newData[0].name;
      } else {
        newData = this.allCustomerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.form.customerName = newData[0].name;
        }
      }
    },
    /** 新增查询客户号 **/
    handleCustomerSearch2(value) {
      if (value) {
        this.customerList2 = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList2 = this.allCustomerList;
      }
    },
    /** 新增查询客户名称 **/
    handleCustomerChange2(value) {
      if (value) {
        this.customerList.forEach(item => {
          if (item.name == value) {
            this.form.customer = item.customer
            this.form.customerName = item.name
          }
        })
      } else {
        this.form.customer = null
      }
    },
    /** 搜索框输入搜索客户 **/
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.allCustomerList.filter(s => (s.customerName != null &&
            s.customerName.indexOf(value) != -1) || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.allCustomerList;
      }
    },
    /** 搜索框输入改变客户 **/
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.allCustomerList;
      }
    },
    /** 跳转查看 **/
    handleLook(record) {
      this.$router.push({path: '/setting/xsgs_parts_settings_partSee/' + record.id, props: true})
    },
    /** 跳转到零件配置 **/
    partEdit(record) {
      let query ={
        customer: this.$route.query.customer,
        platformName: record.platformName,
        blowoff: record.blowoff,
        type: record.type,
      }
      this.$router.push({path: "/setting/xsgs_parts_settings_part/" + record.id, props: true, query})
    },
    /** 跳转到代号配置 **/
    codeEdit(record) {
      this.$router.push({path: "/setting/xsgs_parts_settings_code/" + record.id, props: true})
    },
    /** 编辑 **/
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 删除 **/
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTINGTHEME + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
  }
}
</script>

<style scoped>
/deep/ .ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
