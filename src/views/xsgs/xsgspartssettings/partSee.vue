<template>
  <div>
    <a-card>
      <a-spin :spinning="false">
        <a-form-model style="margin-top: 30px" :model="searchForm" :labelCol="{span: 8}"
                      :wrapperCol="{span: 16}">
          <a-card title="基本型配置清单">
            <a-row>
              <div>
                <a-table
                    :columns="columns"
                    rowKey="id"
                    :pagination="false"
                    :data-source="searchForm.partList"
                    :scroll="{ x: 520 }"
                    size="small"
                    bordered
                    :loading="loading">
                  <template slot="num" slot-scope="text, record, index">
                    {{ index + 1 }}
                  </template>
                </a-table>
              </div>
            </a-row>
          </a-card>
        </a-form-model>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import {METHOD, request} from "@/utils/request";
import {LIST_XSGSPARTSSETTING_PAGE} from "@/services/api/xsgs";
export default {
  name: "part",
  data() {
    return {
      loading: false,
      pagination: {
        total: 0,
        current: 1,
        size: '50',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '零部件名称', dataIndex: 'partsName', scopedSlots: {customRender: 'partsName'}},
        {title: '结构码', dataIndex: 'jgCode', scopedSlots: {customRender: 'jgCode'}},
        {title: '系列', dataIndex: 'series', scopedSlots: {customRender: 'series'}},
        {title: '规格', dataIndex: 'specs', scopedSlots: {customRender: 'specs'}},
        {title: '形式', dataIndex: 'xs', scopedSlots: {customRender: 'xs'}},
        {title: '供应商', dataIndex: 'supplierBak', scopedSlots: {customRender: 'supplierBak'}},
        {title: '配置归属', dataIndex: 'mainType', scopedSlots: {customRender: 'mainType'}},
       ],
      searchForm: {partList: []},
    }
  },
  mounted() {
    this.checkName();
    let path = this.$route.path.split("/");
    this.searchForm.tid = path[3];
    this.getData();
  },
  methods: {
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSPARTSSETTING_PAGE, METHOD.GET, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.searchForm.partList = records
            this.pagination.total = parseInt(total)
          })
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
  }
}
</script>

<style scoped>
/deep/ .ant-form-item {
  margin: 0;
}
</style>
