<template>
  <div>
    <a-card>
      <div class="header-button-area" :style="`width:${headerWidth};`">
        <a-button :disabled="!checkPf('PARTS_SETTING_EDIT_NEW')" :loading="loading" type="primary"
          @click="handleSave('searchForm')">
          <a-icon type="save" />
          保存
        </a-button>
      </div>
      <a-spin :spinning="false">
        <a-form-model style="margin-top: 30px" ref="searchForm" :model="searchForm" :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }">
          <a-card title="基本型配置清单">
            <a-row>
              <div>
                <a-table :columns="columns" rowKey="id" :pagination="false" :data-source="searchForm.partList"
                  :scroll="{ x: 520 }" size="small" bordered :loading="loading">
                  <span slot="partsName">
                    <span style="color: red">*</span>
                    <template>零部件名称</template>
                  </span>
                  <span slot="mainType">
                    <span style="color: red">*</span>
                    <template>配置归属</template>
                  </span>
                  <template slot="num" slot-scope="text, record, index">
                    {{ index + 1 }}
                  </template>
                  <template slot="partsName" slot-scope="text, record, index">
                    <a-form-model-item :prop="'partList.' + index + '.partsName'" :rules="rules.partsName"
                      :wrapper-col="{ span: 24 }">
                      <a-input v-model="record.partsName" />
                    </a-form-model-item>
                  </template>
                  <template slot="supplierBak" slot-scope="text, record">
                    <a-input v-model="record.supplierBak" />
                  </template>
                  <template slot="specs" slot-scope="text, record">
                    <a-input v-model="record.specs" />
                  </template>
                  <template slot="mainType" slot-scope="text, record, index">
                    <a-form-model-item :prop="'partList.' + index + '.mainType'" :rules="rules.mainType"
                      :wrapper-col="{ span: 24 }">
                      <a-select v-model="record.mainType">
                        <a-select-option v-for="mty in mainType" :key="mty">
                          {{ mty }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </template>
                  <template slot="series" slot-scope="text, record">
                    <a-input v-model="record.series" />
                  </template>
                  <template slot="xs" slot-scope="text, record">
                    <a-input v-model="record.xs" />
                  </template>
                  <template slot="jgCode" slot-scope="text, record">
                    <a-input v-model="record.jgCode" />
                  </template>
                  <template slot="action" slot-scope="text, record">
                    <a v-if="checkPf('PARTS_SETTING_DELETE_NEW')" @click="handleDelete(record)">删除</a>
                  </template>
                </a-table>
                <div v-if="checkPf('PARTS_SETTING_EDIT_NEW')">
                  <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus"
                    @click="newMember">
                    新增
                  </a-button>
                </div>
              </div>
            </a-row>
          </a-card>
        </a-form-model>
      </a-spin>
    </a-card>
  </div>
</template>

<script>
import { METHOD, request } from "@/utils/request";
import {
  getUuid, LIST_XSGSPARTSSETTING_PAGE, DELETE_XSGSPARTSSETTING, SUBMIT_XSGSPARTSSETTING
} from "@/services/api/xsgs";

export default {
  name: "part",
  data() {
    return {
      PF_FIELD: "BASE_DATA_MANAGER,PARTS_SETTING_NEW",
      PF_LIST: [],
      loading: false,
      headerWidth: null,
      pagination: {
        total: 0,
        current: 1,
        size: '50',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['50'],
        showTotal: (total) =>
          `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
            total / this.pagination.size
          )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
          this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        { slots: { title: 'partsName' }, dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' } },
        { title: '结构码', dataIndex: 'jgCode', scopedSlots: { customRender: 'jgCode' } },
        { title: '系列', dataIndex: 'series', scopedSlots: { customRender: 'series' } },
        { title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' } },
        { title: '形式', dataIndex: 'xs', scopedSlots: { customRender: 'xs' } },
        { title: '供应商', dataIndex: 'supplierBak', scopedSlots: { customRender: 'supplierBak' } },
        { slots: { title: 'mainType' }, dataIndex: 'mainType', scopedSlots: { customRender: 'mainType' } },
        { title: '操作', fixed: 'right', width: 130, dataIndex: 'operation', scopedSlots: { customRender: 'action' } }
      ],
      rules: {
        partsName: [{ required: true, message: '零部件名称不能为空', trigger: 'blur' }],
        mainType: [{ required: true, message: '配置归属不能为空', trigger: 'blur' }],
      },
      mainType: ['整机配置', '本体配置', '后处理配置'],
      searchForm: { partList: [] },
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.checkName();
    let path = this.$route.path.split("/");
    this.searchForm.tid = path[3];
    this.headerWidth = this.$refs.searchForm.$el.clientWidth + 1 + 'px';
    this.getData();
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    getData: function () {
      this.loading = true
      let { current, size } = this.pagination
      request(LIST_XSGSPARTSSETTING_PAGE, METHOD.GET, { ...this.searchForm, current, size })
        .then(res => {
          const { records, total } = res.data.data
          this.loading = false
          this.searchForm.partList = records
          this.pagination.total = parseInt(total)
        })
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    //新增
    newMember() {
      request(getUuid, METHOD.GET).then(res => {
        const length = this.pagination.total;
        const newData = [...this.searchForm.partList]
        newData.push({
          id: length === 0 ? res.data.data : res.data.data,
          partsName: null,
          supplierBak: null,
          specs: null,
          mainType: null,
          jgCode: null,
          tid: this.searchForm.tid,
          customer: this.$route.query.customer == 'A00001' ? null : this.$route.query.customer,
          platformName: this.$route.query.platformName,
          blowoff: this.$route.query.blowoff,
          type: this.$route.query.type,
        })
        const target = newData.filter(item => length === 0 ? res.data.data : res.data.data === item.id)[0];
        if (target) {
          this.searchForm.partList = newData;
        }
        this.pagination.total = parseInt(newData.length);
      })
    },
    handleDelete(record) {
      this.searchForm.partList = this.searchForm.partList.filter(x => x.id != record.id)
    },
    /** 保存数据1 **/
    handleSave(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$refs.searchForm.validate(valid => {
            if (valid) {
              this.$confirm({
                content: `是否保存当前数据？`,
                onOk: () => {
                  this.handleSaveAction()
                },
              });
            }
          })
        }
      });
    },
    /** 保存数据2 **/
    handleSaveAction() {
      // this.loading = true;
      this.searchForm.partList = [...this.searchForm.partList]
      // 将this.searchForm.partList下每项中的空字符串置为null
      this.searchForm.partList = this.searchForm.partList.map(item => {
        return Object.keys(item).reduce((acc, key) => {
          acc[key] = item[key] === '' ? null : item[key];
          return acc;
        }, {});
      });
      
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          request(SUBMIT_XSGSPARTSSETTING, METHOD.POST, {...this.searchForm}).then(res => {
            if (res.data.code == 0) {
              this.$message.success('保存成功')
              this.loading = false;
            }
          })
        }
      })
    },
  }
}
</script>

<style scoped>
/deep/ .header-button-area {
  position: fixed;
  z-index: 999;
  height: 45px;
  background-color: #ffffff;
  line-height: 45px;
  margin-top: -25px;
  min-width: 700px;
}

/deep/ .ant-form-item {
  margin: 0;
}
</style>
