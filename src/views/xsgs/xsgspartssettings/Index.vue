<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" >
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
          <td>
            <a-form-model-item label="客户"/>
          </td>
          <td>
            <a-select
                style="width: 450px;"
                v-model="searchForm.customerNameList"
                show-search
                :default-active-first-option="false"
                :show-arrow="false"
                mode="multiple"
                :filter-option="false"
                :not-found-content="null"
                :allowClear='true'
                @search="handleCustomerSearch"
                @change="handleCustomerChange"
            >
              <a-select-option v-for="d in customerList" :key="d.name">
                {{ d.customer + '-' + d.name }}
              </a-select-option>
            </a-select>
            <a-button type="primary" style="margin: 0 8px" @click="handleSearch">
              <a-icon type="search"/>
              查询
            </a-button>
            <a-button type="primary" style="margin: 0 8px" @click="resetQuery">
              <a-icon type="reload"/>
              重置
            </a-button>
          </td>
          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('PARTS_SETTING_ADD_NEW')" type="primary" style="margin: 0 8px" @click="dialog=true;form={customer: null,customerName: null,basicName: null}">
                <a-icon type="plus"/>
                新增
              </a-button>
              <a-button v-if="checkPf('PARTS_SETTING_DELETE_NEW')" type="danger" style="margin: 0 8px"
                        @click="deleteSelectedIds">
                <a-icon type="delete"/>
                删除
              </a-button>
              <a-upload v-if="checkPf('PARTS_SETTING_IMPORT_NEW')"
                        name="file"
                        accept=".xlsx"
                        :action="UPLOAD_XSGSPARTSSETTINGCUSTOMER"
                        :headers="{'Authorization':Cookie.get('Authorization')}"
                        :showUploadList="false"
                        style="margin: 0 8px"
                        @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button v-if="checkPf('PARTS_SETTING_EXPORT_NEW')" type="primary" style="margin: 0 8px"
                        @click="handleExportExcel">
                <a-icon type="download"/>
                导出
              </a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table
          :columns="columns"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{selectedRowKeys:selectedRowKeys,onChange:onSelectChange}"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="basicName" slot-scope="text, record">
          <a style="cursor: pointer" @click="openTheme(record)">{{record.basicName }}</a>
        </template>
        <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_SETTING_EDIT_NEW')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('PARTS_SETTING_DELETE_NEW')" type="vertical"/>
                  <a v-if="checkPf('PARTS_SETTING_DELETE_NEW')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 16}">
        <a-form-model-item label="基本型名称" prop="basicName">
          <a-input v-model="form.basicName"/>
        </a-form-model-item>
        <a-form-model-item label="客户号" prop="customer">
          <a-input v-model="form.customer" @blur="customerBlurs2"/>
        </a-form-model-item>
        <a-form-model-item label="客户名称" prop="customerName">
          <a-select
              v-model="form.customerName"
              show-search
              :default-active-first-option="false"
              :show-arrow="false"
              :allowClear="true"
              :filter-option="false"
              :not-found-content="null"
              @search="handleCustomerSearch2"
              @change="handleCustomerChange2"
          >
            <a-select-option v-for="d in customerList2" :key="d.name">
              {{ d.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {
  LIST_XSGSPARTSSETTINGCUSTOMER_PAGE,
  DELETE_XSGSPARTSSETTINGCUSTOMER,
  SUBMIT_XSGSPARTSSETTINGCUSTOMER,
  UPLOAD_XSGSPARTSSETTINGCUSTOMER,
  DOWNLOAD_XSGSPARTSSETTINGCUSTOMER,
  DELETE_XSGSPARTSSETTINGCUSTOMER_LIST,
  LIST_XSGSCUSTOMER
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'

export default {
  name: "xsgspartssettings.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSSETTINGCUSTOMER,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      adding: false,
      // 功能权限关键字
      PF_FIELD: "BASE_DATA_MANAGER,PARTS_SETTING",
      PF_LIST: [],
      searchForm: {},
      form: {
        customer: null,
        customerName: null,
        basicName: null,
      },
      selectedRowKeys: [],
      selectedRows: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '基本型名称', dataIndex: 'basicName',width: 250,scopedSlots: {customRender: 'basicName'}},
        {title: '客户号', dataIndex: 'customer'},
        {title: '客户名称', dataIndex: 'customerName'},
        {title: '操作', fixed: 'right', width: 140, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        basicName: [{required: true, message: '基本型名称不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
        customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
      },
      customerList: [],
      customerList2: [],
      allCustomerList: [],
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getCustomerList();
    this.getData()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s => s == field) != -1;
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys;
      if(selectedRows.length > 0){
        selectedRows.forEach(item => {
          if(this.selectedRows.length > 0 && this.selectedRows.indexOf(item.id) == -1 ||
              this.selectedRows.length == 0){
            this.selectedRows.push(item.id);
          }
        })
      }
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSPARTSSETTINGCUSTOMER_PAGE, METHOD.POST, {...this.searchForm, current, size})
          .then(res => {
            const {records, total} = res.data.data
            this.loading = false
            this.data = records
            this.pagination.total = parseInt(total)
          })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTINGCUSTOMER + record.id, METHOD.DELETE)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    deleteSelectedIds() {
      if (this.selectedRowKeys.length <= 0) {
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTINGCUSTOMER_LIST, METHOD.POST, this.selectedRows)
              .then(() => {
                this.$message.info('删除成功')
                this.getData()
              }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    /** 提交 **/
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSPARTSSETTINGCUSTOMER, METHOD.POST, this.form)
              .then((res) => {
                if (res.data.code == 0) {
                  
                  this.getData()
                  this.$message.info('提交成功')
                  this.closeForm()
                }else{
                  this.$message.error('提交失败：' + res.data.msg)
                  this.closeForm()
                }
              })
        }
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        let msg = `${info.file.name} 上传成功`;
        if (info.file.response && info.file.response.data) {
          let data = info.file.response.data;
          let errList = data.errorList;
          let sucCount = data.sucCount;
          if (errList && errList.length > 0) {
            msg +="\n存在错误数据：" + errList.length + "条\n失败原因如下：";
            errList.forEach(item => {
              msg += "\n" + item+"；"
            })
            this.$warning({
              title: '导入失败提示',
              onOk: () => {

              },
              content: (
                  <div style="white-space: pre-wrap;">{msg}</div>
              ),
              width:500
            });
          } else {
            msg += "，导入成功数：" + (sucCount?sucCount:0) + "条";
            this.$message.success(msg);
          }
        }
      } else if (info.file.status === 'error') {
        let exMsg = "";
        if (info.file.response && info.file.response.msg) {
          exMsg = info.file.response.msg
        }
        this.$message.error(`${info.file.name} 导入失败：${exMsg}`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk: () => {
          exportExcel(DOWNLOAD_XSGSPARTSSETTINGCUSTOMER, {...this.searchForm}, "基本型配置.xlsx");
        }
      })
    },
    /** 获取所有客户 */
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
        const map = new Map()
        this.allCustomerList = res.data.data.filter(key => !map.has(key.name) && map.set(key.name, 1))
        this.customerList = this.allCustomerList
        this.customerList2 = this.allCustomerList
      })
    },
    /** 新增输入客户号 **/
    customerBlurs2() {
      let customer = this.form.customer;
      if (!customer) {
        this.form.customerName = null;
      }
      let newData = this.customerList2.filter(item => item.customer == customer);
      if (newData.length > 0) {
        this.form.customerName = newData[0].name;
      } else {
        newData = this.allCustomerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.form.customerName = newData[0].name;
        }
      }
    },
    /** 新增查询客户号 **/
    handleCustomerSearch2(value) {
      if (value) {
        this.customerList2 = this.allCustomerList.filter(s => s.customerName.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList2 = this.allCustomerList;
      }
    },
    /** 新增查询客户名称 **/
    handleCustomerChange2(value) {
      if (value) {
        this.customerList.forEach(item => {
          if (item.name == value) {
            this.form.customer = item.customer
            this.form.customerName = item.name
          }
        })
      } else {
        this.form.customer = null
      }
    },
    /** 搜索框输入搜索客户 **/
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.allCustomerList.filter(s => (s.customerName != null &&
                s.customerName.indexOf(value) != -1) || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.allCustomerList;
      }
    },
    /** 搜索框输入改变客户 **/
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.allCustomerList;
      }
    },
    /** 跳到主题页面 **/
    openTheme(record){
      if (record.customer == 'A00002') {
        record.customerName = "客户名称"
      }
      let query = {
        customer: record.customer == 'A00002' ? null : record.customer
      }
      let url = "/setting/xsgs_parts_settings_theme/" + record.id + "/" + record.customerName;
      this.$router.push({path:url, props: true, query})
    },
  }
}
</script>

<style scoped>
.ant-input-number {
  width: 100%;
}

.ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

</style>
