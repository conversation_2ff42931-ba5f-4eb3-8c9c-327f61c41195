<template>
  <div>
    <a-card>
      <a-form-model ref="mainForm" :model="mainForm" :rules="mainRules" :labelCol="{span: 8}"  :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">

          <tr>
            <td><a-form-model-item label="年度" prop="year"/></td>
            <td  colspan="2"><a-input v-model="mainForm.year" :disabled="mainDataReaonly"/></td>
            <td><a-form-model-item label="客户名称" prop="customerName" :disabled="mainDataReaonly"/></td>
            <td colspan="3">
              <a-select
                      v-model="mainForm.customerName"
                      show-search
                      placeholder=""
                      :disabled="mainDataReaonly"
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </td>
            <td><a-form-model-item label="客户" prop="customer"/></td>
            <td><a-input v-model="mainForm.customer"  :disabled="mainDataReaonly" @blur="customerBlurs" /></td>
          </tr>


          <tr>

            <td><a-form-model-item label="计算类型" prop="allowanceType"/></td>
            <td  colspan="2">
              <a-select v-model="mainForm.allowanceType" :disabled="mainDataReaonly">
                <a-select-option value="结算价">结算价</a-select-option>
                <a-select-option value="金额">金额</a-select-option>
                <a-select-option value="阶梯金额">阶梯金额</a-select-option>
                <a-select-option value="百分比">百分比</a-select-option>
              </a-select>
            </td>


            <td><a-form-model-item label="折让序号" prop="seqNum"/></td>
            <td ><a-input v-model="mainForm.seqNum" disabled/></td>

            <td><a-form-model-item label="折让名称" prop="seqName"/></td>
            <td><a-input v-model="mainForm.seqName" disabled/></td>

          </tr>
          <tr>
            <td><a-form-model-item label="折让组备注" prop="remarks"/></td>
            <td colspan="8">
              <a-input type="textarea" :autoSize="{minRows: 3 }" aria-placeholder="请输入备注信息" v-model="mainForm.remarks"/>

            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>

      <a-row>
        <a-col :span="24">
          <a-form-model-item>
              <div style="float: left">
              <a-button type="primary" style="margin: 0 8px" @click="handleSubmit" ><a-icon type="save"/>保存</a-button>
              <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
              </div>
              <div @click="handleupload" style="float: left">
                  <a-upload
                          :disabled="disabled"
                          name="file"
                          accept=".xlsx"
                          :action="UPLOADPRICE_XSGSALLOWANCESET"
                          :data="setPriceData"
                          :headers="{'Authorization':Cookie.get('Authorization')}"
                          :showUploadList="false"
                          style="margin: 0 8px"
                          @change="handleUploadExcel">
                    <a-button type="primary">
                      <a-icon type="upload"/>
                      结算价导入
                    </a-button>
                  </a-upload>
              </div>
              <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出结算价模板</a-button>
            </a-form-model-item>
        </a-col>
      </a-row>
      <vxe-grid   highlight-hover-row
                  row-id="id"
                  ref="xTable"
                  :seq-config="{startIndex: (gridOptions.pagerConfig.currentPage - 1) * gridOptions.pagerConfig.pageSize}"
                  stripe
                  v-bind="gridOptions"
                  @page-change="handlePageChange">
        <template #name="{row}">
          <span v-if="row.allowanceType == '百分比'">{{row.allowanceAmount}}%</span>
          <span v-if="row.allowanceType != '百分比'">{{row.allowanceAmount}}</span>
        </template>
        <template  #createTime="{row}">
          <span>{{ formatDate(row.createTime) }}</span>
        </template>
        <template  #updateTime="{row}">
          <span>{{ formatDate(row.updateTime) }}</span>
        </template>
        <template slot="isChecked" slot-scope="{ row  }" >
          {{row.isChecked==1?'是':''}}
        </template>
        <template   #action="{ row  }">
          <a @click="handleEdit(row )">编辑</a>
          <a-divider type="vertical"   />
          <a @click="handleDelete(row )" >删除</a>
        </template >


      </vxe-grid>



    </a-card>


    <a-modal
            width="60%"
            :title="form.id?'编辑':'新增'"
            :visible="dialog"
            :confirm-loading="confirmLoading"
            @ok="handleRowSubmit"
            @cancel="closeForm">
      <a-form-model ref="form"  :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="业务板块" prop="plates">
              <a-select v-model="form.plates"  mode="multiple">
                <a-select-option value="卡车">卡车</a-select-option>
                <a-select-option value="客车">客车</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="排放" prop="blowoffs">
              <a-select v-model="form.blowoffs"  mode="multiple">
                <a-select-option value="国1">国1</a-select-option>
                <a-select-option value="国2">国2</a-select-option>
                <a-select-option value="国3">国3</a-select-option>
                <a-select-option value="国4">国4</a-select-option>
                <a-select-option value="国5">国5</a-select-option>
                <a-select-option value="国6">国6</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>


          <a-col :span="12">
            <a-form-model-item label="产品子系列" prop="seriesArray">
              <a-select v-model="form.seriesArray" mode="multiple" @focus="onClickSeries('series')" >
                <a-select-option v-for="sty in seriesListData" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>


            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品型号" prop="productModels">
              <a-select v-model="form.productModels"  mode="multiple" @focus="onClickProductModel('productModel')">
                <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="form.productModel"/>-->
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>


          <a-col :span="12">
            <a-form-model-item label="产品型号[简化]" prop="productModelJhs" >


              <a-select v-model="form.productModelJhs"  mode="multiple" @focus="onClickProductModelJH('productModelJh')">
                <a-select-option v-for="sty in zcpxhjh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="功率(PS)" prop="pses">
              <a-select v-model="form.pses"  mode="multiple"  @focus="onClickPs('ps')" >
                <a-select-option v-for="sty in ps_list" :key="sty"  >
                  {{sty?sty.split('.')[0]:""}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>




        </a-row>
        <a-row>
          <a-col :span="12">

            <a-form-model-item label="状态机" prop="platformName">
<!--              <a-input v-model="form.platformName"></a-input>-->
              <a-select v-model="form.platformNames"  mode="multiple"
                        @dropdownVisibleChange="onClickPlatform"
                        @search="platformSearch">
                <a-select-option v-for="sty in platformname_list" :key="sty"  >
                  {{sty}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="选配件">
              <a-select v-model="form.partsNames"  mode="multiple"
                        @dropdownVisibleChange="onClickParts"
                        :allowClear="true"
                        @search="partsSearch">
                <a-select-option v-for="sty in partsNames_list" :key="sty"  >
                  {{sty}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="规格">
              <a-select v-model="form.specses"  mode="multiple"
                        @dropdownVisibleChange="onClickSpecs"
                        :allowClear="true"
                        @search="specsSearch">
                <a-select-option v-for="sty in specses_list" :key="sty"  >
                  {{sty}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系1">
              <a-select v-model="form.types" style="min-width: 60px"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getTypeList(form)">
                <a-select-option v-for="d in typeList" :key="d">
                  {{ d}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系2">
              <a-select v-model="form.strain1List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain1List(form)">
                <a-select-option v-for="d in strain1List" :key="d">
                  {{ d}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系3">
              <a-select width="width:100px" v-model="form.strain2List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain2List(form)">
                <a-select-option v-for="d in strain2List" :key="d">
                  {{ d}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系4">
              <a-select v-model="form.strain3List"
                        :allowClear="true"
                        mode="multiple"
                        @dropdownVisibleChange="getStrain3List(form)">
                <a-select-option v-for="d in strain3List" :key="d">
                  {{ d}}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col v-if="mainForm.allowanceType !='百分比' && mainForm.allowanceType !='阶梯金额'"  :span="12">
            <a-form-model-item label="金额" prop="allowanceAmount">
              <a-input-number style="width: 100%; " v-model="form.allowanceAmount"/>
            </a-form-model-item>
          </a-col>
          <a-col v-if="mainForm.allowanceType=='结算价'"  :span="12">
            <a-form-model-item label="金额类型" prop="allowanceName">
              <a-select v-model="form.versionNo">
                <a-select-option value="1.1">价格差</a-select-option>
                <a-select-option value="1.0">固定金额</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col  :span="12">
            <a-form-model-item label="备注" prop="remarks">
<!--              <a-textarea :row="1" style="height: 33px" type="textarea"  aria-placeholder="请输入备注信息" v-model="form.remarks"/>-->
              <textarea class="xsgs-textarea" style="height: 33px;" v-model="form.remarks"/>
              <!--              <a-input v-model="form.remarks"  type="textarea"/>-->
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="生效时间">
              <a-form-item  :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                <a-date-picker style="width: 100%" value-format="YYYY-MM-DD"  format="YYYY-MM-DD" placeholder="" v-model="form.beginTime" />
              </a-form-item>
              <span :style="{ display: 'inline-block', width: '12px', textAlign: 'center' }">~</span>
              <a-form-item :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                <a-date-picker style="width: 100%"  value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder=""  v-model="form.endTime" />
              </a-form-item>
            </a-form-model-item>
          </a-col>

          <a-col v-if="mainForm.allowanceType=='百分比'" :span="12">
            <a-form-model-item label="基值" prop="allowanceName" >
              <a-select v-model="form.allowanceName">
                <a-select-option value="结算价">结算价</a-select-option>
                <a-select-option value="开票价">开票价</a-select-option>
                <a-select-option value="开票价-折让">开票价-折让</a-select-option>
                <a-select-option value="结算价-折让">结算价-折让</a-select-option>
                <a-select-option value="折让">折让</a-select-option>
                <a-select-option value="上年度实际价格">上年度实际价格</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col :lg="12" :md="12" :sm="24" v-if="mainForm.allowanceType=='百分比'">
            <a-form-model-item label="数值" prop="percent" >
              <a-input-number v-model="form.percent"
                              :formatter="value => `${value}%`"
                              :parser="value => value.replace('%', '')"
              />
            </a-form-model-item>
          </a-col>
          <a-col v-if="mainForm.allowanceType=='百分比'" :lg="12" :md="12" :sm="24">
            <a-form-model-item label="引用" prop="refId" >
              <a-select v-model="form.refId" allowClear>
                <a-select-option v-for="item in mainDataList" :key="item.id"   :value="item.id" >
                  {{ item.seqName }}({{ item.allowanceType }})
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

          <a-col v-if="mainForm.allowanceType=='百分比'" :span="12">
            <a-form-model-item label="保留方式" prop="saveType" >
              <a-select v-model="form.saveType">
                <a-select-option value="保留两位(多位四舍五入)">保留两位(多位四舍五入)</a-select-option>
                <a-select-option value="保留两位(两位四舍五入)">保留两位(两位四舍五入)</a-select-option>
                <a-select-option value="取整(多位四舍五入)">取整(多位四舍五入)</a-select-option>
                <a-select-option value="取整(两位四舍五入)">取整(两位四舍五入)</a-select-option>
                <a-select-option value="取整(只舍不入)">取整(只舍不入)</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

        </a-row>
        <vxe-table v-if="mainForm.allowanceType=='阶梯金额'"
                   border
                   resizable
                   :data="optionalData"
                   :mouse-config="{selected: true}"
                   :checkbox-config="{range: true}"
                   :keyboard-config="{isArrow: true, isDel: true, isEnter: true, isTab: true, isEdit: true}"
                   :edit-config="{trigger: 'dblclick', mode: 'cell'}">
          <vxe-table-column field="seqName" title="序号" width="100"></vxe-table-column>
          <vxe-table-column field="number" title="销量(台)"  :edit-render="{name: 'input', attrs: {type: 'float'}}"></vxe-table-column>
          <vxe-table-column field="price" title=" 标准(元)"   :edit-render="{name: 'input', attrs: {type: 'float'}}"></vxe-table-column>
          <vxe-column title="操作" width="100">
            <template #default="{ row, rowIndex }">
              <vxe-button status="primary" @click="deleteRowEvent(rowIndex)" :loading="row.loading">删除</vxe-button>
            </template>
          </vxe-column>

        </vxe-table>

        <div  v-if="mainForm.allowanceType=='阶梯金额'">
          <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus" @click="newJt">新增</a-button>
        </div>
      </a-form-model>
    </a-modal>

  </div>
</template>

<script>
  import {hasAuth} from "@/utils/authority-utils";
  import {LIST_XSGSALLOWANCESETDETAIL_PAGE,SUBMIT_XSGSALLOWANCESET,DELETE_XSGSALLOWANCESETDETAIL,UPLOADPRICE_XSGSALLOWANCESET
    ,DOWNLOADPRICE_XSGSALLOWANCESET
    ,COMBO_TYPE
    ,COMBO_STRAIN1
    ,COMBO_STRAIN2
    ,COMBO_STRAIN3
  } from "@/services/api/xsgs";
  import {GET_XSGSALLOWANCESET_ID,SUBMIT_XSGSALLOWANCESETDETAIL,LIST_XSGSCUSTOMER,    wlxx_action,
    getCustomerZtName,getUuid,LIST_XSGSALLOWANCEJT,LIST_XSGSALLOWANCESET,COMBO_MACHINETYPE,COMBO_PARTS,COMBO_SPECS} from "@/services/api/xsgs";
  import {METHOD, request,exportExcel} from "@/utils/request";
  import Cookie from 'js-cookie'
  import {dateFormat} from "@/utils/dateUtil";
  export default {
    name: "SallowancesetDetailNew.vue",
    data() {
      return {
        UPLOADPRICE_XSGSALLOWANCESET,
        hasAuth,
        Cookie,
        loading: false,
        saveFlag: false,
        dialog: false,
        flag: false,
        confirmLoading: false,
        searchForm: {},
        form: {
          blowoffs:[],
          plates:[],
          specs:[],
          isChecked:0,
          checked:false,
        },
        setPriceData:{
          id:null,
        },
        gridOptions: {
          border: true,
          resizable: true,
          keepSource: true,
          showOverflow: true,
          loading: false,
          pagerConfig: {
            total: 0,
            currentPage: 1,
            pageSize: 10,
            pageSizes: [10, 20, 50],
            layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
            perfect: true
          },
          // 分组列头，通过 children 定义子列
          columns: [],
          data: []
        },
        columns:[
          {title: '序号', align: 'center',type: 'seq', width: 40,fixed:'left' },
          {title: '金额', width: 55,field: 'allowanceAmount', slots: { default: 'name' },},
          {title: '基值', width: 55,field: 'allowanceName'},
          {title: '引用',width: 55, field: 'refName'},
          {title: '业务板块',width: 65, field: 'plate'},
          {title: '排放', width: 55,field: 'blowoff'},
          {title: '产品子系列',width: 95, field: 'series'},
          {title: '产品型号[简化]',width: 110, field: 'productModelJh'},
          {title: '功率(PS)', width: 40,field: 'ps'},
          {title: '产品型号',width: 75, field: 'productModel'},
          {title: '状态机', width: 110,field: 'platformName'},
          {title: '选配件', width: 95,field: 'parts'},
          {title: '规格', width: 95,field: 'specs'},
          {title: '品系1', width: 95,field: 'type'},
          {title: '品系2', width: 95,field: 'strain1'},
          {title: '品系3', width: 95,field: 'strain2'},
          {title: '品系4', width: 95,field: 'strain3'},
          {title: '备注', width: 120,field: 'remarks'},
          {title: '生效开始时间', width: 120, field: 'beginTime',  align: 'center'},
          {title: '生效结束时间', width: 120, field: 'endTime',  align: 'center'},
          {title: '创建人',width: 95, field: 'createUser',  align: 'center'},
          {title: '创建时间',width: 95, field: 'createTime',  align: 'center', slots: { default: 'createTime' }},
          {title: '更新人', width: 95,field: 'updateUser',  align: 'center'},
          {title: '更新时间',width: 95, field: 'updateTime',  align: 'center',slots: { default: 'updateTime' }},
          {title: '台阶', width: 95,field: 'tjIndex',  align: 'center',   children: []},

          { title: '操作', width: 100,  align: 'center', slots: { default: 'action' },fixed:'right' },
        ],
        disabled:true,
        seriesDisabled:true,
        productModelDisabled:true,
        productModelJHDisabled:true,
        PsDisabled:true,
        mainForm: {
          customerName:null,
          customer:null,
        },
        searchCustomerForm: {},

        pagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                          total / this.pagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },

        customerPagination: {
          total: 0,
          current: 1,
          size: '10',
          showSizeChanger: true, // 是否可以改变 size
          showQuickJumper: true, // 是否可以快速跳转至某页
          pageSizeOptions: ['10', '20', '30', '40', '50'],
          showTotal: (total) =>
                  `共 ${total} 条 第${this.customerPagination.current}/${Math.ceil(
                          total / this.customerPagination.size
                  )}页`, // 显示总数
          onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
          onShowSizeChange: (current, size) =>
                  this.onSizeChange(current, size) // 改变每页数量时更新显示
        },
        data: [],
        customerList:[],
        mainDataReaonly:false,
        customerColumns: [
          {title: '客户号', dataIndex: 'customer',width:"100px"},
          {title: '客户简称', dataIndex: 'customerName',width:"200px"},
          {title: '操作', fixed: 'right', width: 90, scopedSlots: {customRender: 'action'}}
        ],
        mainRules: {
          year: [{required: true, message: '年度必填', trigger: 'blur'}],
          customerName: [{required: true, message: '客户必填', trigger: 'blur'}],
          allowanceType: [{required: true, message: '计算类型必填', trigger: 'blur'}],
          seqNum: [{required: false, message: '折让序号必填', trigger: 'blur'}],
        },
        rules: {
          allowanceType: [{required: false, message: '计算类型必填', trigger: 'blur'}],
          allowanceAmount: [{required: false, message: '数值必填', trigger: 'blur'}],
          allowanceJt: [{required: false, message: '折让必填', trigger: 'blur'}],
          allowanceName: [{required: false, message: '描述必填', trigger: 'blur'}],
          allowanceNumber: [{required: false, message: '折扣数值必填', trigger: 'blur'}],
          fromAmount: [{required: false, message: '从必填', trigger: 'blur'}],
          sequenceNumber: [{required: false, message: '序列必填', trigger: 'blur'}],
        },
        optionalData: [],
        mainDataList: [],
        zcpxh_list:[], //产品型号
        zzpx_list:[], //品系
        seriesListData:[],
        zcpxhjh_list:[],
        platformname_list:[],
        ps_list:[],
        partsNames:[],
        partsNames_list:[],
        specses:[],
        specses_list:[],
        typeList:[],
        strain1List:[],
        strain2List:[],
        strain3List:[],
        }
    },
    mounted() {

      this.checkName()
      this.loading = false;
      if(":id" !==this.$route.params.id){
        this.mainForm.id =this.$route.params.id;
        this.searchForm.masterId =this.$route.params.id;
        this.getMainData(this.$route.params.id);
        this.mainDataReaonly = true
        this.getData()
      }else{
        this.initData()
      }
    },
    methods: {
      deleteRowEvent(index){
        this.optionalData.splice(index, 1);

      },
      formatDate(date){
        if (!date) {
          return '';
        }
        return dateFormat(date);
      },
      handlePageChange ({ currentPage, pageSize }) {
        this.gridOptions.pagerConfig.currentPage = currentPage
        this.gridOptions.pagerConfig.pageSize = pageSize;
        this.getData()
      },
      customerBlurs(){
        request(LIST_XSGSCUSTOMER, METHOD.POST, {
          customer:this.mainForm.customer
        }).then(res => {
          if(res.data.data.length>0){
            if(this.mainForm.customer!=null){
              if(!this.mainForm.customer.split(" ").join("").length == 0){
                this.mainForm.customerName = res.data.data[0].name
              }
            }
          }else{
            this.$message.info('此客户号没找到信息')
          }
        })
      },

      handleCustomerSearch(value) {
        request(LIST_XSGSCUSTOMER, METHOD.POST, {
          name:value
        }).then(res => {
          this.customerList = res.data.data
        })
      },
      handleCustomerChange(value) {
        this.customerList.forEach(item =>{
          if(item.name == value){
            this.mainForm.customer = item.customer
          }
        })
      },
      getMainDataList: function () {
        this.loading = true
        request(LIST_XSGSALLOWANCESET, METHOD.POST, {
          year:this.mainForm.year,
          customer:this.mainForm.customer})
                .then(res => {
                  let resultData = []
                  res.data.data.forEach(item =>{
                    if(item.allowanceType !=="阶梯金额"){
                      if(item.id != this.mainForm.id){
                        resultData.push(item)
                      }

                    }
                  })
                  this.mainDataList = resultData
                  this.loading = false
                })
      },

      initData: function() {
        this.$refs.xTable.loadColumn(this.columns)
        var aData = new Date();
        this.mainForm.year= aData.getFullYear();
        this.$forceUpdate()
      },
      getZtName(){
        if(this.mainForm.year && this.mainForm.customer){
          request(getCustomerZtName, METHOD.POST, {
            year:  this.mainForm.year,
            customer:  this.mainForm.customer
          }).then(res =>{
            let data = res.data.data
            this.mainForm.seqNum =  data.seqNum +1
            this.mainForm.seqName =  "折让"+(data.seqNum +1)
          })
        }
      },
      getMainData:function (id){
        request(GET_XSGSALLOWANCESET_ID, METHOD.GET, {id:id})
                .then(res => {
                  if(res.data.data){
                    this.mainForm = res.data.data
                    if(this.mainForm.allowanceType =="结算价"){
                      this.setPriceData.id = res.data.data.id
                      this.disabled =false
                    }
                    this.pagination.total = this.data.length
                    this.loading = false
                  }
                })

      },




      doHandleYear() {
        let myDate = new Date();
        let tYear = myDate.getFullYear();

        this.mainForm.year = tYear;
      },
      newJt(){
        const length = this.optionalData.length ;
        this.optionalData.push({
          seqNumber:(length+1),
          price: '',
          number: '',
          seqName:'台阶'+(length+1)
        })
      },
      setHeadAndValue(records){
        let jtColums =[]
        records.forEach(d =>{
          d.jtList.forEach(item =>{
            if(!this.isExtis(jtColums,"tj_"+item.seqNumber)){
              jtColums.push({title: item.seqName+"（台）", field: "tj_"+item.seqNumber,width:"100px"})
              jtColums.push({title: "标准（元）", field: "tjje_"+item.seqNumber,width:"100px"})
            }
            d["tj_"+item.seqNumber] = item.number
            d["tjje_"+item.seqNumber] = item.price
          })
        })

        this.columns.forEach(item =>{
          if(item.field =='tjIndex'){
            item.children = jtColums
          }
        })
        this.$refs.xTable.loadColumn(this.columns) //操作a组件
      },
      getData: function () {
        this.gridOptions.loading = true
        let current= this.gridOptions.pagerConfig.currentPage
        let size = this.gridOptions.pagerConfig.pageSize
        request(LIST_XSGSALLOWANCESETDETAIL_PAGE, METHOD.GET, {...this.searchForm, current, size})
                .then(res => {
                  const {records, total} = res.data.data
                  this.gridOptions.loading = false
                  if(this.mainForm.allowanceType ==='阶梯金额'){
                    this.setHeadAndValue(records)
                  }else{
                    this.columns.forEach(item =>{
                      if(item.field =='tjIndex'){
                        delete item.children
                        item.width= "1px"
                      }
                    })
                    this.$refs.xTable.loadColumn(this.columns)
                  }
                  this.gridOptions.data = records
                  this.gridOptions.pagerConfig.total = parseInt(total)
                  this.mainDataReaonly = parseInt(total)>0
                })
      },
      isExtis(list,key){
        let flag = false;
        list.forEach(item =>{
          if(item.field == key){
            flag = true;
          }
        })
        return flag

      },
      handleDelete(record) {
        this.$confirm({
          content: `确认删除？`,
          onOk: () => {
            this.gridOptions.loading = true

            request(DELETE_XSGSALLOWANCESETDETAIL + record.id, METHOD.DELETE)
                    .then(() => {
                      this.gridOptions.loading = false
                      this.$message.info('删除成功')
                      let tempdata =[]
                      this.gridOptions.data.forEach(item =>{
                        if(item.id != record.id){
                          tempdata.push(item)
                        }
                      })
                      this.gridOptions.data = tempdata
                      this.mainDataReaonly =this.data.length > 0
                    }).catch(() => {

            })





          }
        });
      },
        handleupload(){
            if(this.mainForm.allowanceType !="结算价"){
              this.$message.info('请选择计算方式为结算价！')
              return
            }
            if(this.setPriceData.id ==null){
                this.$message.info('请先保存！')
                return
            }


        },

      /** 上传 */
      handleUploadExcel(info) {

          this.loading = true
        if (info.file.status !== 'uploading') {
          this.getData()
        }
        if (info.file.status === 'done') {
          this.$message.success(`${info.file.name} 导入成功`);
          let tab=   document.getElementsByClassName("tab");
          for (let i = 0; i <tab.length ; i++) {
            if(tab[i].offsetParent.ariaSelected==='true'){
              tab[i].lastChild.click()
              this.$router.push({path:'/policy/xsgs_allowance_set_detail/'+info.file.response.data, props: true, query: { random:Date.parse(new Date())}})
            }
          }

        } else if (info.file.status === 'error') {
          this.$message.error(`${info.file.name} 导入失败`);
        }
      },
      /** 导出按钮操作 */
      handleExportExcel() {
        this.$confirm({
          title: '提示',
          content: '是否导出折让结算价模板?',
          onOk() {
            // window.open(decodeURI(DOWNLOADPRICE_XSGSALLOWANCESET))
            exportExcel(DOWNLOADPRICE_XSGSALLOWANCESET, {}, "折让结算价导入模板.xlsx")
          },
          onCancel() {},
        });
      },

      handleSubmit() {
        if(!this.loading){
          this.$refs.mainForm.validate(valid => {
            if (valid) {
              this.loading = true
              this.mainForm.xsgsAllowanceSetDetailList = this.gridOptions.data
              request(SUBMIT_XSGSALLOWANCESET, METHOD.POST, this.mainForm)
                      .then((res) => {
                        this.loading = false
                        this.mainForm = res.data.data

                        if(this.mainForm.allowanceType =="结算价"){
                          this.setPriceData.id = res.data.data.id
                          this.disabled =false
                        }
                        this.$message.info('提交成功')
                        this.closeForm()
                        this.searchForm.masterId =res.data.data.id;
                        this.getData()
                      })
            }
          })
        }

      },
      handleRowSubmit(){
        let temp = {...this.form}
        const target = this.mainDataList.find(item => temp.refId === item.id);
        if(target){
          temp.refName=target.seqName+"("+target.allowanceType+")";
        }

        temp.jtList = this.optionalData
        // this.handleDetailSubmit(temp)
        temp.allowanceType = this.mainForm.allowanceType
        if(this.mainForm.allowanceType =='百分比'){
          temp.allowanceAmount = temp.percent
        }
        temp.plateList = []
        temp.blowoffList = []

        temp.seriesList = []
        temp.productModelList = []
        temp.productModelJhList = []
        temp.platformNameList=[]

        if(temp.plates){

          temp.plates.forEach(item =>{
            if(item){
              temp.plateList.push(item)
            }

          })
          temp.plate = temp.plateList.join(",")
        }
        if(temp.blowoffs){

          temp.blowoffs.forEach(item =>{
            if(item){
              temp.blowoffList.push(item)
            }

          })

          temp.blowoff = temp.blowoffList.join(",")
        }
        if(temp.seriesArray){

          temp.seriesArray.forEach(item =>{
            if(item){
              temp.seriesList.push(item)
            }

          })

          temp.series = temp.seriesList.join(",")
        }

        if(temp.productModels){

          temp.productModels.forEach(item =>{
            if(item){
              temp.productModelList.push(item)
            }
          })
          temp.productModel = temp.productModelList.join(",")
        }
        if(temp.platformNames){

          temp.platformNames.forEach(item =>{
            if(item){
              temp.platformNameList.push(item)
            }
          })
          temp.platformName = temp.platformNameList.join(",")
        }

        if(temp.productModelJhs){

          temp.productModelJhs.forEach(item =>{
            if(item){
              temp.productModelJhList.push(item)
            }

          })
          temp.productModelJh = temp.productModelJhList.join(",")
        }


        if(!this.isEmpty(temp.pses)){
          temp.ps = temp.pses.join(",")
        } else {
          temp.ps = null
        }


        if(!this.isEmpty(temp.partsNames)){
          temp.parts = temp.partsNames.join(",")
        } else {
          temp.parts = null
        }
        if(!this.isEmpty(temp.specses)){
          temp.specs = temp.specses.join(",")
        } else {
          temp.specs = null
        }
        if(!this.isEmpty(temp.types)){
          temp.type = temp.types.join(",")
        } else {
          temp.type = null
        }
        if(!this.isEmpty(temp.strain1List)){
          temp.strain1 = temp.strain1List.join(",")
        } else {
          temp.strain1 = null
        }
        if(!this.isEmpty(temp.strain2List)){
          temp.strain2 = temp.strain2List.join(",")
        } else {
          temp.strain2 = null
        }
        if(!this.isEmpty(temp.strain3List)){
          temp.strain3 = temp.strain3List.join(",")
        } else {
          temp.strain3 = null
        }
        this.insertOrUpdate(temp)
        // this.$refs.form.resetFields()
        this.dialog = false
      },
      getTypeList() {
        this.typeList = [];
        request(COMBO_TYPE, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames,
        }).then(res => {
          this.typeList = res.data.data;
        })
      },
      getStrain1List() {
        this.strain1List = [];
        request(COMBO_STRAIN1, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames,
          type:this.form.typeList,
        }).then(res => {
          this.strain1List = res.data.data;
        })
      },
      getStrain2List() {
        this.strain2List = [];
        request(COMBO_STRAIN2, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames,
          type:this.form.typeList,
          starin1:this.form.strain1List,
        }).then(res => {
          this.strain2List = res.data.data;
        })
      },
      getStrain3List() {
        this.strain3List = [];
        request(COMBO_STRAIN3, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames,
          type:this.form.typeList,
          starin1:this.form.strain1List,
          starin2:this.form.strain2List
        }).then(res => {
          this.strain3List = res.data.data;
        })
      },
      insertOrUpdate(temp){

        let newList = []
        let flag = false
        this.gridOptions.data.forEach(item =>{
          if(item.id == temp.id){
            newList.push(temp)
            flag = true
          }else{
            newList.push(item)
          }
        })
        if(!flag){
          newList.push(temp)
        }
        if(this.mainForm.allowanceType ==='阶梯金额'){
          this.setHeadAndValue(newList)
        }

        this.gridOptions.data = newList

        this.mainDataReaonly = this.data.length>0
      },
      handleEdit(record) {

        this.dialog = true
        this.form = JSON.parse(JSON.stringify(record));

        //得到详细
        if(this.mainForm.allowanceType =='阶梯金额'){
          if(record.jtList && record.jtList.length > 0){
            this.optionalData = record.jtList
          }else{
            request(LIST_XSGSALLOWANCEJT, METHOD.POST, {masterId:record.id})
                    .then((res) => {
                      this.optionalData = res.data.data
                      if (!this.optionalData.versionNo) {
                        this.optionalData.versionNo = '1.0';
                      }
                    })
          }

        }
        if(this.mainForm.allowanceType =='百分比'){
          this.getMainDataList()
        }
      },
      handleSearch() {
        this.pagination.current = 1
        this.getData()
      },
      /** 新增，打开对话框 */
      handleNew() {
        if(this.mainForm.allowanceType == null || this.mainForm.year == null || this.mainForm.customer == null){
          this.$message.error('请输入年度，客户，类型等信息')
          return
        }
        this.dialog = true
        this.optionalData = []
        this.newJt()
        this.newJt()
        this.newJt()
        this.$nextTick(()=>{
          request(getUuid, METHOD.GET, {})
                  .then((res) => {
                    this.form.id = res.data.data
                    this.form.sequenceNumber = this.data.length+1
                  })
        })
        this.$nextTick(()=>{
          if(this.mainForm.allowanceType =='百分比'){
            this.getMainDataList()
          }

        })
        this.form = {
          blowoffs:[],
          plates:[],
        }

      },
      handleDetailSubmit(form){
        this.$refs.mainForm.validate(valid => {
          if (valid) {

            request(SUBMIT_XSGSALLOWANCESET, METHOD.POST, this.mainForm)
                    .then((res) => {
                      this.saveDetail(res.data.data.id,form)
                    })
          }
        })
      },

      saveDetail(id,form){

        this.form = form
        this.form.masterId = id;
        this.form.jtList = this.optionalData
        request(SUBMIT_XSGSALLOWANCESETDETAIL, METHOD.POST, this.form)
                .then(() => {
                  this.getMainData(id);
                  this.$message.info('提交成功')
                  this.closeForm()
                })
      },
      closeForm() {
        this.dialog = false
        // this.$refs.form.resetFields()
        this.form = {}
      },
      onPageChange(page, size) {
        this.pagination.current = page;
        this.pagination.size = size.toString();
        this.getData();
      },
      onSizeChange(current, size) {
        this.pagination.current = 1;
        this.pagination.size = size.toString();
        this.getData();
      },
      //产品子系列
      onClickSeries(type){
        //如果为true，则调用
        if(this.seriesDisabled){
          this.onclickPartsName(type);
          //同时禁用
          this.seriesDisabled=false;
          setTimeout(() => {
            this.seriesDisabled = true;//响应后延迟几秒回复正常；
          }, 1000)
        }
      },
      //产品型号
      onClickProductModel(type){
        //如果为true，则调用
        if(this.productModelDisabled){
          this.onclickPartsName(type);
          //同时禁用
          this.productModelDisabled=false;
          setTimeout(() => {
            this.productModelDisabled = true;//响应后延迟几秒回复正常；
          }, 1000)
        }

      },
      //产品型号【简化】
      onClickProductModelJH(type){
        //如果为true，则调用
        if(this.productModelJHDisabled){
          this.onclickPartsName(type);
          //同时禁用
          this.productModelJHDisabled=false;
          setTimeout(() => {
            this.productModelJHDisabled = true;//响应后延迟几秒回复正常；
          }, 1000)
        }

      },
      //功率PS
      onClickPs(type){


        //如果为true，则调用
        if(this.PsDisabled){
          this.onclickPartsName(type);
          //同时禁用
          this.PsDisabled=false;
          setTimeout(() => {
            this.PsDisabled = true;//响应后延迟几秒回复正常；
          }, 1000)
        }

      },
      // 状态机
      onClickPlatform(){
        this.machineTypeList = [];
        this.platformname_list=[];
        request(COMBO_MACHINETYPE, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses
        }).then(res => {
          this.machineTypeList = res.data.data;
          this.platformname_list = this.cutList(this.machineTypeList,0,20);
        })
        // this.onclickPartsName('platformName');
      },
      platformSearch(value){
        if (!value) {
          this.platformname_list = this.cutList(this.machineTypeList,0,20);
        } else {
          this.platformname_list = this.cutList(this.machineTypeList.filter(s=>s.indexOf(value) != -1),0,20);
        }
      },
      // 选配件
      onClickParts(){
        this.partsNames = [];
        this.partsNames_list = [];
        request(COMBO_PARTS, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames
        }).then(res => {
          this.partsNames = res.data.data;
          this.partsNames_list = this.cutList(this.partsNames, 0, 20);
        })
      },
      partsSearch(value){
        if (!value) {
          this.partsNames_list = this.cutList(this.partsNames,0,20);
        } else {
          this.partsNames_list = this.cutList(this.partsNames.filter(s=>s.indexOf(value) != -1),0,20);
        }
      },
      // 选配件
      onClickSpecs(){
        this.specses = [];
        this.specses_list = [];
        request(COMBO_SPECS, METHOD.POST, {
          year:this.mainForm.year + '',
          customer:this.mainForm.customer,
          blowoffs: this.form.blowoffs,
          plates:this.form.plates,
          series:this.form.seriesArray,
          productModelJh:this.form.productModelJhs,
          ps:this.form.pses,
          machineType:this.form.platformNames,
          partsNames:this.form.partsNames
        }).then(res => {
          this.specses = res.data.data;
          this.specses_list = this.cutList(this.specses, 0, 20);
        })
      },
      specsSearch(value){
        if (!value) {
          this.specses_list = this.cutList(this.specses,0,20);
        } else {
          this.specses_list = this.cutList(this.specses.filter(s=>s.indexOf(value) != -1),0,20);
        }
      },
      changeChecked(){
        if (this.form.checked) {
          this.form.isChecked=1;
        } else {
          this.form.isChecked=0;
        }

      },
      //查询所有数据
      onclickPartsName(type){
        let queryParams = {

        }
        if(this.form.plates.length > 0){
          queryParams.IN_YWBKMS =[]
          this.form.plates.forEach(item =>{
            queryParams.IN_YWBKMS.push({ "VTEXT": item})
          })
        }else{
          queryParams.IN_YWBKMS =[]
          queryParams.IN_YWBKMS.push({ "VTEXT": '卡车'})
          queryParams.IN_YWBKMS.push({ "VTEXT": '客车'})
          queryParams.IN_YWBKMS.push({ "VTEXT": '新能源'})
        }

        if(this.form.blowoffs.length > 0){
          queryParams.IN_PFMS =[]
          this.form.blowoffs.forEach(item =>{
            queryParams.IN_PFMS.push({ "VTEXT": item})
          })
        }


        if(this.form.seriesArray && this.form.seriesArray.length > 0){
          queryParams.IN_CPZXL =[]
          this.form.seriesArray.forEach(item =>{
            queryParams.IN_CPZXL.push({ "ZCPZXL": item})
          })
        }

        if(type =='series'){
          delete queryParams.IN_CPZXL
        }
        if(this.form.productModels && this.form.productModels.length > 0){
          queryParams.IN_CPXH =[]
          this.form.productModels.forEach(item =>{
            queryParams.IN_CPXH.push({ "ZCPXH": item})
          })
        }
        if(type =='productModel'){
          delete queryParams.IN_CPXH
        }
        if(this.form.productModelJhs && this.form.productModelJhs.length > 0){
          queryParams.IN_CPXHJ =[]
          this.form.productModelJhs.forEach(item =>{
            queryParams.IN_CPXHJ.push({ "ZCPXHJ": item})
          })
        }

        if(type =='productModelJh'){
          delete queryParams.IN_CPXHJ
        }
        if(this.form.pses && this.form.pses.length > 0){
          queryParams.IN_GL =[]
          this.form.pses.forEach(item =>{
            queryParams.IN_GL.push({ "ZGL": item})
          })
        }

        if(type =='ps'){
          delete queryParams.IN_GL
        }
        if(this.form.platformNames && this.form.platformNames.length > 0){
          queryParams.IN_ZTJ =[]
          this.form.pses.forEach(item =>{
            queryParams.IN_ZTJ.push({ "ZZTJ": item})
          })
        }
        if(type =='platformName'){
          delete queryParams.IN_ZTJ
        }

        request(wlxx_action, METHOD.POST, queryParams).then(res => {
          let returnData = res.data.data
          let RETURN = returnData.RETURN
          if (RETURN && RETURN.ZTYPE == 'S') {

            let ZCPZXL_LIST = [] //产品子系列
            let ZCPXH_LIST = [] //产品型号
            let ZCPXHJH_LIST = [] //产品型号[简化]
            let PS_LIST = [] //功率
            let PLATFORMNAME_LIST = [] //状态机


            let T_OUT = returnData.T_OUT
            T_OUT.forEach(item =>{
              if(!this.isdou(ZCPZXL_LIST,item.ZCPZXL)){
                ZCPZXL_LIST.push(item.ZCPZXL)
              }
              if(!this.isdou(ZCPXH_LIST,item.ZCPXH)){
                ZCPXH_LIST.push(item.ZCPXH)
              }
              if(!this.isdou(ZCPXHJH_LIST,item.ZCPXHJ)){
                ZCPXHJH_LIST.push(item.ZCPXHJ)
              }
              if(!this.isdou(PS_LIST,item.ZGL)){
                PS_LIST.push(item.ZGL)
              }
              if(!this.isdou(PLATFORMNAME_LIST,item.ZZTJ)){
                PLATFORMNAME_LIST.push(item.ZZTJ)
              }

            })

            this.seriesListData = ZCPZXL_LIST
            this.zcpxh_list = ZCPXH_LIST
            this.zcpxhjh_list = ZCPXHJH_LIST
            this.ps_list = PS_LIST
            this.platformname_list = PLATFORMNAME_LIST



          }
        })
      },


      isdou(list,value){
        let flag = false
        list.forEach(item =>{
          if(item == value){
            flag = true
          }
        })
        return flag
      },
    }
  }
</script>


<style scoped>
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }

  /deep/.col-blue {
    background-color: #2db7f5;
    color: #fff;
  }

  /deep/.vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
    height: 0px;
  }
  /deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 0px 0;
  }
  /deep/.vxe-table--render-default.border--full .vxe-body--column,/deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/.vxe-table--render-default.border--full .vxe-header--column {
    background-image: -webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646)),-webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646));
    background-image: linear-gradient(#464646,#464646),linear-gradient(#464646,#464646);
    background-repeat: no-repeat;
    background-size: 1px 100%,100% 1px;
    background-position: 100% 0,100% 100%;
  }

  /deep/.vxe-table--render-default.border--default
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--full
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: #f8f8f9;
  }

  /deep/ .vxe-pager.is--perfect {
    border: 0px solid #464646;
    border-top-width: 0;
    background-color: #fff;
  }
  /deep/ .vxe-header--column{
    background-color: rgba(0, 0, 0, 0.2);
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--stripe{
    background-color: #e6fdff;
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--checked{
    background-color: #fff3e0;
  }
  /deep/.ant-col-16 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--hover{
    background-color: #fcce10;
  }
  /deep/.vxe-table--header>thead>tr>th {
    padding: 0 !important;
    text-align: center;
  }
  /deep/.vxe-table--render-default .vxe-cell{
    padding: 0 !important;
  }
</style>

