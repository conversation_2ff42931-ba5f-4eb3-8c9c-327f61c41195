<template>
  <div>
    <a-card>
      <a-form-model ref="mainForm" :model="mainForm" :rules="mainRules" :labelCol="{span: 8}"  :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table-tbody">


          <tr>


            <td><a-form-model-item label="年度" prop="year"/></td>
            <td  colspan="2"><a-input v-model="mainForm.year"/></td>

            <td><a-form-model-item label="客户" prop="customer"/></td>
            <td><a-input v-model="mainForm.customer"  @click="chooseCustomer"/></td>
            <td><a-form-model-item label="客户名称" prop="customerName"/></td>
            <td colspan="3"><a-input v-model="mainForm.customerName"   /></td>

          </tr>

          <tr>

            <td><a-form-model-item label="计算类型" prop="allowanceType"/></td>
            <td  colspan="2">
              <a-select v-model="mainForm.allowanceType">
                <a-select-option value="结算价">结算价</a-select-option>
                <a-select-option value="金额">金额</a-select-option>
                <a-select-option value="阶梯金额">阶梯金额</a-select-option>
                <a-select-option value="百分比">百分比</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="折让编号" prop="seqName"/></td>
            <td  colspan="2"><a-input v-model="mainForm.seqName"/></td>

            <td></td>
            <td  colspan="2" style="display: none"></td>



          </tr>





          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-button type="primary" style="margin: 0 8px" @click="handleSubmit"><a-icon type="plus"/>保存</a-button>
          <a-button type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增折让类型</a-button>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
                 <span slot="action" slot-scope="record">
                  <a @click="handleEdit(record)">编辑</a>
                  <a-divider type="vertical"/>
                  <a @click="handleDelete(record)">删除</a>
                </span>
        <template slot="name" slot-scope="text, record">
         <p v-if="record.allowanceType == '百分比'">{{text}}%</p>
         <p v-if="record.allowanceType != '百分比'">{{text}}</p>
        </template>
      </a-table>
    </a-card>


    <a-modal
        width="50%"
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form"  :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="状态机" prop="platformName">
              <a-input v-model="form.platformName"  @blur="blursMachineType"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="排放" prop="blowoffList">
              <a-select  v-model="form.platformName"
                         mode="tags"
                         show-search
                         option-filter-prop="children"
                         :not-found-content="null">
                <a-select-option value="国一">国一</a-select-option>
                <a-select-option value="国二">国二</a-select-option>
                <a-select-option value="国三">国三</a-select-option>
                <a-select-option value="国四">国四</a-select-option>
                <a-select-option value="国五">国五</a-select-option>
                <a-select-option value="国六">国六</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>

          <a-col :span="12">
            <a-form-model-item label="产品子系列" prop="series">
              <a-input v-model="form.series"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品型号[简化]" prop="productModel">
              <a-input v-model="form.productModel"/>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>

          <a-col :span="12">
            <a-form-model-item label="功率(PS)" prop="ps">
                <a-input v-model="form.ps"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="产品型号" prop="machineType">
              <a-input v-model="form.machineType"/>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row>

          <a-col :span="12">
            <a-form-model-item label="业务板块" prop="plate">
              <a-select v-model="form.plate">
                <a-select-option value="卡车">卡车</a-select-option>
                <a-select-option value="客车">客车</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

        </a-row>


      </a-form-model>
    </a-modal>

    <a-modal
        :title="'新增'"
        :visible="customerDialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleCustomerSubmit"
        @cancel="closeCustomerForm">
      <selectCustomer   @rowKeys='getCustomerRowKeys'  ref="selectCustomer"></selectCustomer>
    </a-modal>

  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSALLOWANCESETDETAIL_PAGE,DELETE_XSGSALLOWANCESETDETAIL,SUBMIT_XSGSALLOWANCESET} from "@/services/api/xsgs";
import {GET_XSGSALLOWANCESET_ID,SUBMIT_XSGSALLOWANCESETDETAIL,LIST_XSGSCUSTOMER,ztjxx_action} from "@/services/api/xsgs";
import {METHOD, request} from "@/utils/request";
import selectCustomer from "@/views/xsgs/xsgsallowancemain/select_customer";

export default {
  name: "SallowancesetDetailNew.vue",
  components: {
    selectCustomer,
  },
  data() {
    return {
      hasAuth,
      loading: false,
      dialog: false,
      flag: false,
      customerDialog: false,
      confirmLoading: false,
      searchForm: {},
      form: {},
      mainForm: {},
      searchCustomerForm: {},

      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },

      customerPagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
                `共 ${total} 条 第${this.customerPagination.current}/${Math.ceil(
                        total / this.customerPagination.size
                )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
                this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      customerData: [],
      columns: [
        {title: '序列', dataIndex: 'sequenceNumber'},
        {title: '计算类型', dataIndex: 'allowanceType'},
        {title: '数值', dataIndex: 'allowanceAmount'  ,scopedSlots: { customRender: 'name' },},
        {title: '阶梯/基值', dataIndex: 'allowanceName'},
        {title: '从(阶梯)', dataIndex: 'fromAmount'},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      customerColumns: [
        {title: '客户号', dataIndex: 'customer',width:"100px"},
        {title: '客户简称', dataIndex: 'customerName',width:"200px"},
        {title: '操作', fixed: 'right', width: 165, scopedSlots: {customRender: 'action'}}
      ],
      mainRules: {
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户不能为空', trigger: 'blur'}]
      },
      rules: {
        masterId: [{required: false, message: '年度不能为空', trigger: 'blur'}],
        allowanceType: [{required: false, message: '计算类型不能为空', trigger: 'blur'}],
        allowanceAmount: [{required: false, message: '数值不能为空', trigger: 'blur'}],
        allowanceJt: [{required: false, message: '折让不能为空', trigger: 'blur'}],
        allowanceName: [{required: false, message: '描述不能为空', trigger: 'blur'}],
        allowanceNumber: [{required: false, message: '折扣数值不能为空', trigger: 'blur'}],
        fromAmount: [{required: false, message: '从不能为空', trigger: 'blur'}],
        sequenceNumber: [{required: false, message: '序列不能为空', trigger: 'blur'}],
        createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
        createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
        updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
        updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
      },
      optionalData: [],
    }
  },
  mounted() {
    this.checkName()
    // this.getData()
    // this.getMainData(this.$route.params.id);
    this.onSearchCustomer();
    this.newJt2();
  },
  methods: {
    getMainData:function (id){
      console.log(id);
      request(GET_XSGSALLOWANCESET_ID, METHOD.GET, {id:id})
              .then(res => {
            if(res.data.data){
              this.mainForm = res.data.data
              let customers = []
              this.mainForm.customerList.forEach(item =>{
                customers.push(item.customer)
              })
              this.mainForm.customers = customers
              this.mainForm.blowoffList = this.mainForm.blowoff.split(",")
              this.data = this.mainForm.xsgsAllowanceSetDetailList
              this.data.forEach(item =>{
                item.sequenceNumber = "折让"+item.sequenceNumber;
              })
              this.pagination.total = this.data.length
            }
      })

    },
      saveRow (record) {
        console.log(record)
      },
      cancel (id) {
          console.log(id)
      },
      toggle (id) {
          console.log(id)
      },

    blursMachineType(){

      // if(this.searchForm.customer!=null){
      let data = {
        "P_ZTJ" :this.form.platformName
      }
      request(ztjxx_action, METHOD.POST, data).then(res => {
        let returnData = res.data.data
        let RETURN = returnData.RETURN
        if (RETURN && RETURN.ZTYPE == 'S') {
          let T_OUT = returnData.T_OUT[0];
          // this.mainForm.type = T_OUT.ZZPX;
          this.form.machineType = T_OUT.ZCPXH;
          this.form.productModel = T_OUT.ZCPXHJ;
          this.form.ps = T_OUT.ZGL;
          this.form.series = T_OUT.ZCPZXL;
          this.form.blowoffList = T_OUT.ZPF_CODE;
          // this.mainForm.plate = T_OUT.ZYWBK_CODE;


        }
      })
      // }else{
      //     this.$message.info('请先选择客户')
      // }

    },

    //选择客户111111111
    chooseCustomer(){
      this.customerDialog =true;
      //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
      this.$nextTick(()=>{
        this.$refs.selectCustomer.getData();
      })
    },
    //新增客户调用子类方法
    handleCustomerSubmit() {
      this.$refs.selectCustomer.choose();
    },
    /** 关闭客户对话框，表单重置 */
    closeCustomerForm() {
      this.customerDialog = false
    },
    //新增客户以及客户名称
    getCustomerRowKeys(data){
      if(data.length>0){
        this.mainForm.customer=data[0].customer
        this.mainForm.customerName=data[0].name
      }
      this.customerDialog=false;
    },


      newJt2(){
        this.doHandleYear();
        this.flag=true;
        this.newJt();
        this.newJt();
        this.newJt();
      },
      doHandleYear() {
        let myDate = new Date();
        let tYear = myDate.getFullYear();

        this.mainForm.year = tYear;
      },
      newJt(){
            const length = this.optionalData.length ;
            this.optionalData.push({
                seqNumber:(length+1),
                price: '',
                number: '',
                seqName:'台阶'+(length+1)
            })
        },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSALLOWANCESETDETAIL_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.name} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSALLOWANCESETDETAIL + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.mainForm.validate(valid => {
        if (valid) {
          request(SUBMIT_XSGSALLOWANCESET, METHOD.POST, this.mainForm)
                  .then((res) => {
                    this.mainForm = res.data.data
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    handleDetailSubmit(){
      this.$refs.mainForm.validate(valid => {
        if (valid) {
          if(this.mainForm.blowoffList){
            this.mainForm.blowoff = this.mainForm.blowoffList.join(",")
          }


          request(SUBMIT_XSGSALLOWANCESET, METHOD.POST, this.mainForm)
                  .then((res) => {
                    this.saveDetail(res.data.data.id)
                  })
        }
      })
    },

    saveDetail(id){
      this.form.masterId = id;
        this.form.jtList = this.optionalData
      request(SUBMIT_XSGSALLOWANCESETDETAIL, METHOD.POST, this.form)
              .then(() => {
                this.getMainData(id);
                this.$message.info('提交成功')
                this.closeForm()
              })
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSearchCustomer() {
   //   this.customerDialog = true

      request(LIST_XSGSCUSTOMER, METHOD.POST, {})
              .then(res => {
                // const {records, total} = res.data.data
                // this.loading = false
                this.customerData =  res.data.data
                // this.customerPagination.total = parseInt(total)
              })
    },
    handleSelectCustomerSubmit(value) {
      this.customerDialog = false
      console.log(value)
    },
    closeSelectCustomerForm(value) {
      this.customerDialog = false
      console.log(value)
    },
    handleCustomerEdit(data){


        let flag = false
        let list = []

        this.mainForm.customerList.forEach(item =>{
            if(item.customer == data.customer){
                flag = true
            }

        })
        if(!flag){
            data.id = null
            this.mainForm.customerList.push(data)
        }



        this.mainForm.customerList.forEach(item =>{
            list.push(item.customer)
        })
      this.mainForm.customer = list.join(",")
    },
    queryCustomer(){
      // request(LIST_XSGSCUSTOMER_PAGE, METHOD.GET, {...this.searchCustomerForm})
      //         .then(res => {
      //           const {records, total} = res.data.data
      //           this.loading = false
      //           this.customerData = records
      //           this.customerPagination.total = parseInt(total)
      //         })
    }

  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
td{
  border: 1px solid #f0f0f0;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/*/deep/  样式穿透
*/
/deep/.ant-col-8{
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}
/deep/.ant-form-item {
  margin: 0;
}
</style>
