<template>
    <div>
        <a-table
                :columns="funcColumns"
                rowKey="id"
                :pagination="false"
                :data-source="funcData"
                :scroll="{ x: 520 }"
                size="small"
                bordered
                :customRow="onRowClick"
                :loading="loading">
            <template slot="num" slot-scope="text, record, index">
                {{index+1}}
            </template>
            <template slot="action" slot-scope="record">
                <a @click="handleEdit(record )">编辑</a>
                <a-divider type="vertical"  />
                <a @click="handleDelete(record )" >删除</a>
            </template>
        </a-table>

        <div>
            <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus" :disabled="editAbled"
                      @click="newMember">新增
            </a-button>
        </div>
        <div style="text-align: right;margin-top: 10px;" :loading="loading">
            <label style="float: left;font-weight: bold;font-size: 16px">{{caverState(funcState)}}</label>
            <label style="float: left;font-weight: bold;font-size: 16px">{{lastLog}}</label>
            <a-button :disabled="funcState !='FINISH' && funcState !='REVERROR'" style="margin-left: 10px" type="primary"
                      @click="handleCancel">
                撤销
            </a-button>
            <a-button :disabled="editAbled" style="margin-left: 10px" type="primary"
                      @click="handleSaveAndCalc">保存计算
            </a-button>
            <a-button style="margin-left: 10px" type="primary" @click="showLog">查看日志</a-button>
        </div>
        <a-modal
                width="60%"
                :title="form.id?'编辑':'新增'"
                :visible="dialog"
                :confirm-loading="confirmLoading"
                @ok="handleRowSubmit"
                @cancel="closeForm">
            <a-form-model ref="form"  :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="排放" prop="blowoff">
                            <a-select style="width: 100%" v-model="form.blowoff"
                                      :allowClear="true"
                                      @dropdownVisibleChange="getBlowoffList"
                                      @change="changedBlowoff">
                                <a-select-option v-for="d in blowoffList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="系列" prop="series">
                            <a-select style="width: 100%" v-model="form.seriesArray"
                                      @dropdownVisibleChange="onclickSeries(form)"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @change="changedSeries">
                                <a-select-option v-for="sty in seriesList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="产品型号" prop="productModel">
                            <a-select style="width: 100%" v-model="form.productModels"
                                      @dropdownVisibleChange="onclickProductModel(form)"
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @change="changedProductModel">
                                <a-select-option v-for="sty in productModelList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="状态机" prop="machineType">
                            <a-select style="width: 100%" v-model="form.machineTypes"
                                      @dropdownVisibleChange="onclickMachineType(form)"
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @change="changedMachineType">
                                <a-select-option v-for="sty in machineTypeList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="选配件名称" prop="partName">
                            <a-select style="width: 100%" v-model="form.partName"
                                      show-search
                                      :show-arrow="false"
                                      :allowClear="true"
                                      @dropdownVisibleChange="onclickPartsName(form)"
                                      @search="onSearchPartsName"
                                      @change="changedPartName">
                                <a-select-option v-for="d in partsList" :key="d">
                                    {{ d}}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="规格" prop="specs">
                            <a-select style="width: 100%" v-model="form.specsList"
                                      @dropdownVisibleChange="onclickSpecs(form)"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @change="changedSpecsName">
                                <a-select-option v-for="sty in specsList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="供应商" prop="supplier">
                            <a-select style="width: 100%" v-model="form.suppliers"
                                      @dropdownVisibleChange="onclickSupplier(form)"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption"
                                      @change="changedSupplier">
                                <a-select-option v-for="sty in supplierList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                    <a-col :span="12">
                        <a-form-model-item label="原实际售价" prop="specs">
                            <a-select style="width: 100%" v-model="form.orgPrices"
                                      @dropdownVisibleChange="onclickOrgPrice(form)"
                                      showSearch
                                      :allowClear="true"
                                      mode="multiple"
                                      :filterOption="filterOption">
                                <a-select-option v-for="sty in orgPriceList" :key="sty">
                                    {{ sty }}
                                </a-select-option>
                            </a-select>
                        </a-form-model-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="12">
                        <a-form-model-item label="新实际售价" prop="price">
                            <a-input-number
                                    v-model="form.price"
                                    :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                    :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                    :precision="2"
                                    :step="0.01"
                                    :min="0"
                                    style="width: 100%"

                            />
                        </a-form-model-item>
                    </a-col>
                </a-row>
            </a-form-model>
        </a-modal>
    </div>

</template>

<script>
    import {
        LIST_XSGSPARTPOLICYDETAIL,
        getUuid,
        COMBO_BLOWOFF,
        COMBO_SERIES,
        COMBO_PRODUCTMODEL,
        COMBO_MACHINETYPE,
        COMBO_PARTS,
        COMBO_SPECS,
        COMBO_SUPPLIER,
        COMBO_PRICE,
        DELETE_XSGSPRICEPOLICYDETAIL
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";

    export default {
        name: "func1",
        props: {
            policyForm: Object,
            funcType: String
        },
        data() {
            return {
                funcData: [],
                loading: false,
                dialog:false,
                confirmLoading: false,
                form: {},
                blowoffList: [],
                seriesList: [],
                editAbled:false,
                funcState: 'WAIT',
                lastLog: '',
                productModelList: [],
                machineTypeList: [],
                partsList: [],
                allPartsList: [],
                specsList:[],
                supplierList:[],
                orgPriceList:[],
                funcPagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.funcPagination.current}/${Math.ceil(
                            total / this.funcPagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onOptionalcPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onOptionalcSizeChange(current, size) // 改变每页数量时更新显示
                },
                funcColumns: [
                    {
                        title: '序号', key: 'num', width: 40, align: 'center', fixed: true, scopedSlots: {
                            customRender: 'num'
                        }
                    },
                    {title: '排放', dataIndex: 'blowoff', width: 70},
                    {title: '系列', dataIndex: 'seriesArray', width: 90},
                    {title: '产品型号', dataIndex: 'productModels'},
                    {title: '状态机', dataIndex: 'machineTypes', width: 120},
                    {title: '选配件名称', dataIndex: 'partName'},
                    {title: '规格', dataIndex: 'specsList', width: 120},
                    {title: '供应商', dataIndex: 'suppliers', width: 80},
                    {title: '原实际售价', dataIndex: 'orgPrices', width: 80},
                    {title: '新实际售价', dataIndex: 'price', width: 80},
                    {title: '功能', width: 80, align: 'center', scopedSlots: {customRender: 'action'}},
                ],
                rules: {
                    price: [{required: true, message: '新实际售价必填', trigger: 'blur'}],
                    partName: [{required: true, message: '选配件名称必填', trigger: 'blur'}]
                },
            }
        },
        mounted() {
            this.checkName();
            this.setDisabled();
            this.getData();
        },
        created() {
        },
        methods: {
            getData() {
                if (this.policyForm.id != ":id") {
                    this.loading = true;
                    request(LIST_XSGSPARTPOLICYDETAIL, METHOD.POST, {
                        policyId: this.policyForm.id,
                        funcType: this.funcType
                    }).then(res => {
                        this.funcData = res.data.data;
                        this.loading = false;
                    })
                }
            },
            getBlowoffList() {
                this.$nextTick(() => {
                    if (this.blowoffList.length == 0 && this.policyForm.calcYear) {
                        request(COMBO_BLOWOFF, METHOD.POST, {
                            year: this.policyForm.calcYear + '',
                            customer:this.policyForm.customerNo,
                            plate:this.policyForm.plate,
                        }).then(res => {
                            this.blowoffList = res.data.data;
                        })
                    }
                })
            },
            changedBlowoff(){
                this.$set(this.form,"seriesArray",[])
                this.$set(this.form,"productModels",[])
                this.$set(this.form,"machineTypes",[])
                this.$set(this.form,"partName",null)
                this.$set(this.form,"specsList",[])
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedSeries(){
                this.$set(this.form,"productModels",[])
                this.$set(this.form,"machineTypes",[])
                this.$set(this.form,"partName",null)
                this.$set(this.form,"specsList",[])
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedProductModel(){
                this.$set(this.form,"machineTypes",[])
                this.$set(this.form,"partName",null)
                this.$set(this.form,"specsList",[])
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedMachineType(){
                this.$set(this.form,"partName",null)
                this.$set(this.form,"specsList",[])
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedPartName(){
                this.$set(this.form,"specsList",[])
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedSpecsName(record){
                this.$set(this.form,"suppliers",[])
                this.$set(this.form,"orgPrices",[])
            },
            changedSupplier(record){
                this.$set(this.form,"orgPrices",[])
            },
            // 将输入的内容与显示的内容进行匹配
            filterOption(value, option) {
                return option.componentOptions.children[0].text.indexOf(value) >= 0
            },
            //点击获取系列
            onclickSeries(record) {
                this.seriesList = [];
                request(COMBO_SERIES, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff
                }).then(res => {
                    this.seriesList = res.data.data;
                })

            },
            onclickProductModel(record) {
                this.productModelList = [];
                request(COMBO_PRODUCTMODEL, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray
                }).then(res => {
                    this.productModelList = res.data.data;
                })
            },
            onclickMachineType(record) {
                this.machineTypeList = [];
                request(COMBO_MACHINETYPE, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray,
                    productModel: record.productModels
                }).then(res => {
                    this.machineTypeList = res.data.data;
                })
            },
            onclickPartsName(record) {
                this.partsList = [];
                request(COMBO_PARTS, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray,
                    productModel: record.productModels,
                    machineType: record.machineTypes,
                    partsName: record.partName,
                }).then(res => {
                    this.partsList = res.data.data;
                    this.allPartsList = res.data.data;
                })
            },
            onSearchPartsName(value) {
                if (value) {
                    this.partsList = this.allPartsList.filter(item=>item && item.indexOf(value) != -1);
                } else {
                    this.partsList = this.allPartsList;
                }

            },


            onclickSpecs(record) {
                this.specsList = [];
                request(COMBO_SPECS, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray,
                    productModel: record.productModels,
                    machineType: record.machineTypes,
                    partsName: record.partName
                }).then(res => {
                    this.specsList = res.data.data;
                })
            },
            onclickSupplier(record) {
                this.supplierList = [];
                request(COMBO_SUPPLIER, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray,
                    productModel: record.productModels,
                    machineType: record.machineTypes,
                    partsName: record.partName,
                    specs: record.specsList,
                }).then(res => {
                    this.supplierList = res.data.data;
                })
            },
            onclickOrgPrice(record) {
                this.orgPriceList = [];
                request(COMBO_PRICE, METHOD.POST, {
                    year: this.policyForm.calcYear + '',
                    customer:this.policyForm.customerNo,
                    plate:this.policyForm.plate,
                    blowoff: record.blowoff,
                    series: record.seriesArray,
                    productModel: record.productModels,
                    machineType: record.machineTypes,
                    partsName: record.partName,
                    specs: record.specsList,
                    supplier: record.suppliers,
                }).then(res => {
                    this.orgPriceList = res.data.data;
                })
            },
            handleEdit(record){
                this.form = record;
                this.dialog = true;
            },
            handleDelete(record) {
                //删除
                const newData = [...this.funcData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    let that = this;
                    this.$confirm({
                        content: `是否删除？`,
                        onOk: () => {
                            const newDatas = newData.filter(item => item.id !== target.id)
                            that.funcData = newDatas
                            this.loading = true
                            request(DELETE_XSGSPRICEPOLICYDETAIL + record.id, METHOD.DELETE)
                                .then(() => {
                                    this.$message.info('删除成功')
                                    this.loading = false
                                }).catch(() => {
                                this.loading = false
                            })
                        },
                        onCancel() {

                        }
                    });
                }
            },
            closeForm(){
                this.form={};
                this.dialog = false;
            },
            handleRowSubmit(){
                let that = this;
                this.$refs.form.validate(valid => {
                    if (!valid) {
                        return;
                    }
                    // 编辑
                    if (that.form.id) {
                        that.dialog = false;
                        return;
                    }

                    request(getUuid, METHOD.GET).then(res => {
                        const length = this.funcPagination.total;
                        const newData = [...this.funcData]
                        newData.push({
                            id: length === 0 ? res.data.data : res.data.data,
                            policyId: that.policyForm.id,
                            funcType: that.funcType,
                            blowoff: that.form.blowoff,
                            seriesArray: that.form.seriesArray,
                            productModels: that.form.productModels,
                            machineTypes: that.form.machineTypes,
                            partName: that.form.partName,
                            specsList: that.form.specsList,
                            suppliers: that.form.suppliers,
                            orgPrices: that.form.orgPrices,
                            price: that.form.price,
                            calState: 'WAIT',
                        })
                        //默认进入编辑状态

                        newData.forEach(item => {
                            item.editable = true;
                        })
                        const target = newData.filter(item => length === 0 ? res.data.data : res.data.data === item.id)[0];
                        if (target) {
                            target.editable = true;
                            that.funcData = newData;
                        }
                        that.funcPagination.total = parseInt(newData.length);
                        that.dialog = false;
                    })
                })
            },
            //新增
            newMember() {
                this.form ={};
                this.dialog = true;
            },
            handleCancel() {
                let that = this;
                this.$confirm({
                    content: `是否确认撤销上次计算数据？`,
                    onOk: () => {
                        that.loading = true;
                        that.funcState = 'REVOKEING';
                        that. setDisabled();
                        that.$emit('cancelCalc', this.funcType)
                    },
                    onCancel() {

                    }
                });
            },
            handleSaveAndCalc() {
                let that = this;
                this.$confirm({
                    content: `是否确认保存计算本次数据？`,
                    onOk: () => {
                        if (!that.checkPrice()) {
                            return;
                        }
                        let realData = this.funcData;
                        if (realData && realData.length > 0) {
                            that.policyForm.funcData = realData;
                            that.policyForm.funcType = that.funcType
                            that.$emit('saveAndCalc', that.policyForm)
                        } else {
                            that.$message.error("请添加有效的计算条件")
                        }
                    },
                    onCancel() {

                    }
                });


            },
            checkPrice() {
                if (this.funcType == 'F2') {
                    return true;
                }
                // 选配件名称验空
                let errData = this.funcData.filter((item, index) => {
                    if (!item.partName || item.partName.length == 0) {
                        item.sort = (index + 1);
                        return true;
                    }

                });
                if (errData && errData.length > 0) {
                    let mess = "";
                    errData.forEach((item, index) => {
                        mess += "第" + (item.sort) + "行\n\t";
                    });
                    this.$message.error("存在选配件为空的数据：\n\t" + mess)
                    return false;
                }
                // 售价验空
                errData = this.funcData.filter((item, index) => {
                    if (!item.price || item.price == 0) {
                        item.sort = (index + 1);
                        return true;
                    }

                });
                if (errData && errData.length > 0) {
                    let mess = "";
                    errData.forEach((item, index) => {
                        mess += "第" + (item.sort) + "行\n\t";
                    });
                    this.$message.error("存在降价值为空的数据：\n\t" + mess)
                    return false;
                }
                return true;
            },
            showLog() {
                this.$emit('showLogs', this.funcType)
            },
            setFuncState(funcState, lastLog) {
                this.funcState = funcState;
                if (lastLog) {
                    if (funcState == 'RUNNING' || funcState == 'IMPORTING' || funcState == 'REVOKEING') {
                        this.lastLog = '（'+lastLog+'）';
                    } else {
                        this.lastLog = '';
                    }
                } else {
                    this.lastLog = '';
                }
                this. setDisabled();
            },
            showLoading() {
                this.loading = true;
            },
            closeLoading() {
                this.loading = false;
            },
            setDisabled(){
                this.editAbled = this.funcState != 'WAIT' && this.funcState != 'ERROR' &&  this.funcState != 'FINISH';
            },
            refreshForm(policyForm){
                this.policyForm = policyForm;
                this.policyForm.policyId = this.policyForm.id;
            },
            caverState(state) {
                if (state == 'WAIT') {
                    return '待计算';
                } else if (state == 'RUNNING') {
                    return '计算中...';
                } else if (state == 'IMPORTING') {
                    return '导入中...';
                } else if (state == 'REVOKEING') {
                    return '撤销中...';
                } else if (state == 'FINISH') {
                    return '计算完成';
                } else if (state == 'ERROR'){
                    return '计算错误';
                } else if (state == 'REVERROR'){
                    return '撤销错误';
                } else {
                    return '未知';
                }
            },
        }
    }
</script>

<style scoped>

    /*/deep/.ant-table-thead >tr >th{*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

    /*/deep/.ant-table-tbody >tr >td{*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

    /*/deep/.ant-form >table >tbody >tr >td {*/
    /*  border: 1px solid rgb(240, 240, 240);*/
    /*}*/

</style>
