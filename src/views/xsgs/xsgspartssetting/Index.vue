<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 21}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>

            <td><a-form-model-item label="板块" /></td>
            <td>
              <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="排放" /></td>
            <td>
              <a-select v-model="blowoff" mode="multiple"  style="min-width: 150px">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="系列" /></td>
            <td>
              <a-select v-model="series"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zxl_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="产品型号[简化]" /></td>
            <td>
              <a-select v-model="productModel"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>

          </tr>
          <tr>
            <td><a-form-model-item label="客户" /></td>
            <td><a-input v-model="searchForm.customer"/></td>

            <td><a-form-model-item label="配置名称" /></td>
            <td><a-input v-model="searchForm.partsName"/></td>

            <td><a-form-model-item label="规格" /></td>
            <td><a-input v-model="searchForm.specs"/></td>
            <td><a-form-model-item label="本体/后处理"/></td>
            <td><a-select v-model="searchForm.mainType" >
              <a-select-option v-for="mty in mainType" :key="mty">
                {{ mty }}
              </a-select-option>
            </a-select></td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
        <a-row>
          <a-col :span="24">
            <a-form-model-item>
              <a-button v-if="checkPf('PARTS_SETTING_ADD')" type="primary" style="margin: 0 8px" @click="dialog=true"><a-icon type="plus"/>新增</a-button>
              <a-button v-if="checkPf('PARTS_SETTING_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"> <a-icon type="delete"/>删除</a-button>
              <a-upload  v-if="checkPf('PARTS_SETTING_IMPORT')"
                      name="file"
                      accept=".xlsx"
                      :action="UPLOAD_XSGSPARTSSETTING"
                      :headers="{'Authorization':Cookie.get('Authorization')}"
                      :showUploadList="false"
                      style="margin: 0 8px"
                      @change="handleUpload">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  导入
                </a-button>
              </a-upload>
              <a-button  v-if="checkPf('PARTS_SETTING_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon type="download"/>导出</a-button>

            </a-form-model-item>
          </a-col>
        </a-row>


      </a-form-model>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :row-selection="{selectedRowKeys:selectedRowKeys,onChange:onSelectChange}"
          :scroll="{ x: 1000 }"
          size="small"
          bordered
          :customRow="onRowClick"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('PARTS_SETTING_EDIT')" @click="handleEdit(record)">编辑</a>
<!--                  <a-divider v-if="checkPf('PARTS_SETTING_ADD')" type="vertical"/>-->
<!--                  <a v-if="checkPf('PARTS_SETTING_ADD')" @click="handleRef(record)">参考</a>-->
                  <a-divider v-if="checkPf('PARTS_SETTING_DELETE')" type="vertical"/>
                  <a v-if="checkPf('PARTS_SETTING_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        width="50%"
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 8}" :wrapperCol="{span: 16}">

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="客户号" prop="customer">
              <a-input v-model.trim="form.customer"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="板块" prop="platformName">
              <a-select v-model="form.platformName"  style="width: 100%">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>

        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="排放" prop="blowoff">
              <a-select v-model="form.blowoff"  style="width: 100%">
                <a-select-option value="国6" >国6</a-select-option>
                <a-select-option value="国5" >国5</a-select-option>
                <a-select-option value="国4" >国4</a-select-option>
                <a-select-option value="国3" >国3</a-select-option>
                <a-select-option value="国2" >国2</a-select-option>
                <a-select-option value="国1" >国1</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="系列" prop="series">
              <a-input v-model="form.series"/>
            </a-form-model-item>
          </a-col>

        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="产品型号[简化]" prop="productModel">
              <a-input v-model="form.productModel" @change="changes"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="品系" prop="type">
              <a-input v-model="form.type"/>
            </a-form-model-item>
          </a-col>

        </a-row>

        <a-row>
          <a-col :span="12">
            <a-form-model-item label="零部件" prop="partsName">
              <a-input v-model="form.partsName"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="12">
            <a-form-model-item label="规格" prop="specs">
              <a-input v-model="form.specs"/>
            </a-form-model-item>
          </a-col>

        </a-row>
        <a-row>
          <a-col :span="12">
            <a-form-model-item label="本体/后处理" prop="mainType">
              <a-select v-model="form.mainType" >
                <a-select-option v-for="mty in mainType" :key="mty">
                  {{ mty }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </a-modal>
    <a-modal
            width="60%"
            title="参考"
            :visible="refDialog"
            :confirm-loading="refLoading"
            @ok="handleSubmitRef"
            @cancel="refDialog = false">
      <a-form-model ref="refForm" :model="refForm" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
          <a-row>
            <a-col :span="8">
              <a-form-model-item label="客户号">
                <a-input v-model="refForm.customer" @change="refChanged"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="板块">
                <a-select v-model="refForm.platformName"  @change="refChanged" style="width: 100%">
                  <a-select-option value="卡车" >卡车</a-select-option>
                  <a-select-option value="客车" >客车</a-select-option>
                  <a-select-option value="新能源" >新能源</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="排放">
                <a-select v-model="refForm.blowoff" @change="refChanged">
                  <a-select-option value="国6" >国6</a-select-option>
                  <a-select-option value="国5" >国5</a-select-option>
                  <a-select-option value="国4" >国4</a-select-option>
                  <a-select-option value="国3" >国3</a-select-option>
                  <a-select-option value="国2" >国2</a-select-option>
                  <a-select-option value="国1" >国1</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="系列">
                <a-input v-model="refForm.series" @change="refChanged"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="产品型号[简化]" :labelCol="{span:10}" :wrapperCol="{span:14}">
                <a-input v-model="refForm.productModel" @change="refChanged"/>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="品系">
                <a-input v-model="refForm.type" @change="refChanged"/>
              </a-form-model-item>
            </a-col>
          </a-row>
        <a-row>
          <a-table
                  :columns="columns2"
                  rowKey="id"
                  :pagination="false"
                  :data-source="data2"
                  size="small"
                  v-if="!adding"
                  bordered
                  :customRow="onRowClick"
                  :loading="loading">
            <!-- 序号 -->
            <template slot="num" slot-scope="text, record, index">
              {{index+1}}
            </template>
            <template slot="partsName" slot-scope="text, record">
              <a-input v-model="record.partsName"/>
            </template>
            <template slot="specs" slot-scope="text, record">
              <a-input v-model="record.specs"/>
            </template>
            <template slot="mainType" slot-scope="text, record">
              <a-select v-model="record.mainType" >
                <a-select-option v-for="mty in mainType" :key="mty">
                  {{ mty }}
                </a-select-option>
              </a-select>
            </template>
            <span slot="action" slot-scope="record">
                  <a @click="handleRefDelete(record)">删除</a>
            </span>
          </a-table>
        </a-row>
      </a-form-model>
      <div>
        <a-button style="width: 100%; margin-top: 5px; margin-bottom: 5px" type="dashed" icon="plus"
                  @click="newMember">新增
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSPARTSSETTING_PAGE,GETSEACHLIST_XSGSPARTSSETTING,DELETE_XSGSPARTSSETTING,SUBMIT_XSGSPARTSSETTING,UPLOAD_XSGSPARTSSETTING,DOWNLOAD_XSGSPARTSSETTING,DELETE_XSGSPARTSSETTING_LIST,LIST_XSGSPARTSSETTING_REF,SUBMIT_XSGSPARTSSETTING_REF} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
import ARow from "ant-design-vue/es/grid/Row";
export default {
  name: "xsgspartssetting.vue",
  components: {ARow},
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSPARTSSETTING,
      Cookie,
      loading: false,
      dialog: false,
      refLoading:false,
      refDialog: false,
      confirmLoading: false,
      adding:false,
      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,PARTS_SETTING",
      PF_LIST:[],
      mainType:['本体配置','后处理配置'],
      subType:['选型件','选装件'],
      searchForm: {},

      productModel:[],
      blowoff:[],
      plate:[],
      series:[],
      zcpxh_list:[], //产品型号
      zxl_list:[],//系列


      form: {},
      refForm: {
        changed:false,
      },
      data2: [],
      columns2: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '配置名称', dataIndex: 'partsName', scopedSlots: {customRender: 'partsName'}},
        {title: '规格', dataIndex: 'specs', scopedSlots: {customRender: 'specs'}},
        {title: '本体/后处理', dataIndex: 'mainType', width: 140, scopedSlots: {customRender: 'mainType'}},
        {title: '操作', align:"center", width: 80, scopedSlots: {customRender: 'action'}}
      ],
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '客户号', dataIndex: 'customer'},
        {title: '板块', dataIndex: 'platformName'},
        {title: '排放', dataIndex: 'blowoff'},
        {title: '系列', dataIndex: 'series'},
        {title: '产品型号[简化]', dataIndex: 'productModel', width: 120},
        {title: '品系', dataIndex: 'type'},
        {title: '配置名称', dataIndex: 'partsName', width: 150},
        {title: '规格', dataIndex: 'specs'},
        {title: '本体/后处理', dataIndex: 'mainType', width: 120},
        {title: '操作', fixed: 'right', width: 140, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        plate: [{required: false, message: '板块不能为空', trigger: 'blur'}],
        platformName: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
        type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
        partsName: [{required: true, message: '零部件不能为空', trigger: 'blur'}],
        specs: [{required: false, message: '规格不能为空', trigger: 'blur'}],
        mainType: [{required: false, message: '本体/后处理不能为空', trigger: 'blur'}],
        subType: [{required: true, message: '类型不能为空', trigger: 'blur'}],
      },


    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
    this.getSearchList()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getSearchList(){
        request(GETSEACHLIST_XSGSPARTSSETTING, METHOD.GET)
                .then(res => {
                  if(res.data.data.cpxh!=null){
                    this.zcpxh_list=res.data.data.cpxh
                  }
                  if(res.data.data.xl!=null){
                    this.zxl_list=res.data.data.xl
                  }
                })
      },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      this.searchForm.productModels = this.productModel.join(",")
      this.searchForm.blowoffs = this.blowoff.join(",")
      this.searchForm.plates = this.plate.join(",")
      this.searchForm.seriess = this.series.join(",")


      request(LIST_XSGSPARTSSETTING_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    getRefData: function () {
      this.refLoading = true
      request(LIST_XSGSPARTSSETTING_REF, METHOD.GET, {...this.refForm})
              .then(res => {
                this.refLoading = false
                this.data2 = res.data.data
              })
    },
    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.productModel} ？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTING + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleRef(record){
      this.refForm = {};
      this.refForm.data=[];
      let ref = this.copy(record);
      this.refForm.customer = ref.customer;
      this.refForm.platformName = ref.platformName;
      this.refForm.blowoff = ref.blowoff;
      this.refForm.series = ref.series;
      this.refForm.productModel = ref.productModel;
      this.refForm.type = ref.type;
      this.getRefData();
      this.refDialog = true;
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSPARTSSETTING_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                  .then(() => {
                    this.$message.info('删除成功')
                    this.getData()
                  }).catch(() => {
            this.loading = false
          })
        }
      });

    },
    handleSubmit() {
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`系列不能为空`);
        return;
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSPARTSSETTING, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    newMember(){
      let record = {
        id:this.refForm.data.length + Math.round(Math.random() * 1000) + 1,
        platformName:null,
        specs:null,
        mainType:null,
      }
      this.$nextTick(()=>{
        this.data2.push(record);

      });

    },
    refChanged(){
      if (!this.refForm.changed) {
        this.refForm.changed = true;
      }
    },
    /** 新增 产品型号[简化] 输入回调 */
    changes(){
      if((this.form.series === undefined || this.form.series === '') &&
          this.form.productModel !== undefined){
        this.$message.error(`请先填写系列`);
        this.form.productModel = undefined
      }
    },
    handleRefDelete(record){
      let newData = this.data2.filter(s=>s.id != record.id);
      this.data2 = newData;
    },
    handleSubmitRef(){
      if (this.refForm.changed == true) {
        this.refLoading = true
        this.refForm['settingList'] = this.data2;
        request(SUBMIT_XSGSPARTSSETTING_REF, METHOD.POST, this.refForm)
                .then(() => {
                  this.getData()
                  this.$message.info('提交成功')
                  this.refLoading=false;
                  this.refDialog = false;

                })
      } else {
         this.$message.info("请修改基本信息");
      }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.productModel=[],
      this.blowoff=[],
      this.plate=[],
      this.series=[],

      this.searchForm = {}
      this.handleSearch();
    },
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onPageChange2(page, size) {
      this.pagination2.current = page;
      this.pagination2.size = size.toString();
      this.getRefData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange2(current, size) {
      this.pagination2.current = 1;
      this.pagination2.size = size.toString();
      this.getRefData();
    },
    handleUpload(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.searchForm.productModels = this.productModel
      this.searchForm.blowoffs = this.blowoff
      this.searchForm.plates = this.plate
      this.searchForm.seriess = this.series
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk:() => {
          // let option = ""
          // if (searchForm.platformName != null) {
          //   option = "?platformName=" + searchForm.platformName
          // }
          //
          // if (searchForm.blowoff != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "blowoff=" + searchForm.blowoff
          // }
          // if (searchForm.customer != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customer=" + searchForm.customer
          // }
          // if (searchForm.series != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "series=" + searchForm.series
          // }
          // if (searchForm.productModel != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "productModel=" + searchForm.productModel
          // }
          // if (searchForm.type != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "type=" + searchForm.type
          // }
          // if (searchForm.partsName != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "partsName=" + searchForm.partsName
          // }
          // if (searchForm.specs != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "specs=" + searchForm.specs
          // }
          // if (searchForm.mainType != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "mainType=" + searchForm.mainType
          // }
          //
          // window.open(decodeURI(DOWNLOAD_XSGSPARTSSETTING + option))

          exportExcel(DOWNLOAD_XSGSPARTSSETTING, {...searchForm}, "基本型配置.xlsx");
        }
      })
    },
  }
}
</script>

<style scoped>
.ant-input-number{
  width: 100%;
}
.ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}
/deep/.ant-form-item {
  margin: 0;
}
/deep/.ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}
</style>
