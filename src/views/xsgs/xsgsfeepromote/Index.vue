<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
          <tbody class="ant-table">
          <tr>
            <td><a-form-model-item label="年度"/></td>
            <td><a-input v-model="searchForm.year"/></td>

            <td><a-form-model-item label="客户名称"/></td>
            <td colspan="2">
              <a-select
                      style="width: 100%"
                      v-model="searchForm.customerName"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.name">
                  {{ d.name }}
                </a-select-option>
              </a-select>
<!--              <a-input v-model="searchForm.customerName"/>-->
            </td>

            <td><a-form-model-item label="客户"/></td>
            <td><a-input v-model="searchForm.customer"   @blur="customerBlurs"  /></td>
          </tr>
          <tr>
            <td><a-form-model-item label="板块" /></td>
            <td>
              <a-select v-model="plate" mode="multiple"  style="min-width: 150px">
                <a-select-option value="卡车" >卡车</a-select-option>
                <a-select-option value="客车" >客车</a-select-option>
                <a-select-option value="新能源" >新能源</a-select-option>
              </a-select>
            </td>

            <td><a-form-model-item label="产品型号"/></td>
            <td>
              <a-select v-model="productModel"  mode="multiple"  style="min-width: 150px">
                <a-select-option v-for="sty in zcpxh_list" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
            </td>
            <td>
              <a-button type="primary" style="margin: 0 8px" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </td>
          </tr>
          </tbody>
        </table>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('FEE_PROMOTE_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('FEE_PROMOTE_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('FEE_PROMOTE_IMPORT')"
                    name="file"
                    :action="UPLOAD_XSGSFEEPROMOTE"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('FEE_PROMOTE_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <vxe-grid  highlight-hover-row
                 row-id="id"
                 ref="xTable"
                 :checkbox-config="{ highlight: true,reserve: true, range: true}"
                 stripe
                v-bind="gridOptionsTG"
                @page-change="handlePageChangeTG"
      >
        <template   #action="{ row  }">
          <a v-if="checkPf('FEE_PROMOTE_EDIT')" @click="handleEdit(row )">编辑</a>
          <a-divider v-if="checkPf('FEE_PROMOTE_DELETE')" type="vertical"   />
          <a v-if="checkPf('FEE_PROMOTE_DELETE')" @click="handleDelete(row )" >删除</a>
        </template >

      </vxe-grid>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="60%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 7}" :wrapperCol="{span: 15}">
          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="年度" prop="year">
                <a-input v-model="form.year"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="板块" prop="plate">
                <a-select  style="width: 100%" v-model="form.plate">
                  <a-select-option value="卡车" >卡车</a-select-option>
                  <a-select-option value="客车" >客车</a-select-option>
                  <a-select-option value="新能源" >新能源</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="客户号" prop="customer">
                <a-input v-model="form.customer"  @blur="customerBlursFrom" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="客户名称" prop="customerName">
<!--                <a-input v-model="form.customerName"/>-->
                <a-select
                        style="width: 100%"
                        v-model="form.customerName"
                        show-search
                        :default-active-first-option="false"
                        :show-arrow="false"
                        :filter-option="false"
                        :not-found-content="null"
                        @search="handleCustomerSearchFrom"
                        @change="handleCustomerChangeFrom"
                >
                  <a-select-option v-for="d in customerListFrom" :key="d.name">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="产品归属" prop="gsk6">
                <a-input v-model="form.gsk6"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="燃料" prop="fuel">
                <a-input v-model="form.fuel"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系列" prop="series">
                <a-input v-model="form.series"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="排放" prop="blowoff">
                <a-select    style="width: 100%" v-model="form.blowoff"  >
                  <a-select-option value="国1">国1</a-select-option>
                  <a-select-option value="国2">国2</a-select-option>
                  <a-select-option value="国3">国3</a-select-option>
                  <a-select-option value="国4">国4</a-select-option>
                  <a-select-option value="国5">国5</a-select-option>
                  <a-select-option value="国6">国6</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="车型" prop="cx">
                <a-input v-model="form.cx"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="产品型号" prop="productModel">
                <a-input v-model="form.productModel"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="状态机" prop="platformName">
                <a-input v-model="form.platformName"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="功率" prop="power">
                <a-input v-model="form.power"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="推广区域" prop="area">
                <a-input v-model="form.area"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="品系" prop="type">
                <a-input v-model="form.type"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="单价" prop="limitPrice">
                <a-input-number v-model="form.limitPrice" @change="totalBlurs" style="width: 100%"
                                :step="0.01"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="数量" prop="limitNumber">
                <a-input v-model="form.limitNumber" @change="totalBlurs" />
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="金额" prop="limitAmount">
                <a-input style="width: 100%" v-model="form.limitAmount"
                         :step="0.01" disabled/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="备注" prop="remarks">
                <textarea class="xsgs-textarea" v-model="form.remarks" style="height: 33px;min-height: 33px"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="打包推广总额" prop="total">
                <a-input-number v-model="form.total" style="width: 100%" :step="0.01"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="打包推广条件" prop="condition">
                <textarea class="xsgs-textarea" v-model="form.condition" style="height: 33px;min-height: 33px"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="推广时间">
                <a-form-item  :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                  <a-date-picker style="width: 100%" value-format="YYYY-MM-DD"  format="YYYY-MM-DD" placeholder="" v-model="form.startTime" />
                </a-form-item>
                <span :style="{ display: 'inline-block', width: '12px', textAlign: 'center' }">~</span>
                <a-form-item :style="{ display: 'inline-block', width: 'calc(50% - 6px)' }">
                  <a-date-picker style="width: 100%"  value-format="YYYY-MM-DD" format="YYYY-MM-DD" placeholder=""  v-model="form.endTime" />
                </a-form-item>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="编制协议" prop="isBz">
                <a-select style="width: 100%" v-model="form.isBz"  >
                  <a-select-option value="已编制">已编制</a-select-option>
                  <a-select-option value="待编制">待编制</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="协议送审" prop="isSs">
                <a-select style="width: 100%" v-model="form.isSs"  >
                  <a-select-option value="已送审">已送审</a-select-option>
                  <a-select-option value="待送审">待送审</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="推广名称" prop="tgName">
                <a-input v-model="form.tgName"/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="实结数量" prop="realNumber">
                <a-input v-model="form.realNumber" @change="totalBlurs" />
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="实结金额" prop="realAmount">
                <a-input v-model="form.realAmount" :step="0.01" disabled/>
              </a-form-model-item>
            </a-col>
          </a-row>

          <a-row class="form-row">
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="系列开票数量" prop="number">
                <a-input v-model="form.number" @change="totalBlurs"/>
              </a-form-model-item>
            </a-col>
            <a-col :lg="12" :md="12" :sm="24">
              <a-form-model-item label="单台推广分摊" prop="dttgft">
                <a-input v-model="form.dttgft" :step="0.01" disabled/>
              </a-form-model-item>
            </a-col>
          </a-row>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSFEEPROMOTE_PAGE,GETSEACHLIST_XSGSFEEPROMOTE,LIST_XSGSCUSTOMER,DELETE_XSGSFEEPROMOTE,SUBMIT_XSGSFEEPROMOTE,DOWNLOAD_XSGSFEEPROMOTE,UPLOAD_XSGSFEEPROMOTE,DELETE_XSGS_FEE_PROMOTE_LIST} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
import {Ride,Divide} from "@/utils/util";
export default {
  name: "xsgsfeepromote.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSFEEPROMOTE,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      // 功能权限关键字
      PF_FIELD:"BUSINESS_POLICY_MANAGER,FEE_PROMOTE",
      PF_LIST:[],
      searchForm: {
        customerName:null,
        customer:null,
      },
      plate:[],
      productModel:[],
      zcpxh_list:[],

      customerList:[],
      customerListFrom:[],
      form: {
        customerName:null,
        customer:null,
      },
      gridOptionsTG:{
        border: true,
        resizable: true,
        keepSource: true,
        showOverflow: true,
        loading: false,
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50],
          layouts: ['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total'],
          perfect: true
        },
        columns: [
          { type: 'checkbox', title: '', width: 40,fixed:'left' },
          { title: '序号', align: 'center',type: 'seq', width: 50 ,fixed:'left'},
          {title: '年度', width: 60, field: 'year'},
          {title: '推广名称', width: 120, field: 'tgName'},
          {title: '板块',width: 80, field: 'plate'},
          {title: '客户号', width: 95,field: 'customer'},
          {title: '客户名称',width: 200, field: 'customerName'},
          {title: '客户简称',width: 100, field: 'customerJc'},
          {title: '产品归属',width: 95, field: 'gsk6'},
          {title: '燃料',width: 95, field: 'fuel'},
          {title: '系列', width: 95,field: 'series'},
          {title: '排放', width: 65,field: 'blowoff'},
          {title: '产品型号', width: 95,field: 'productModel'},
          {title: '状态机', width: 95,field: 'platformName'},
          {title: '功率', width: 95,field: 'power'},
          {title: '推广区域', width: 95,field: 'area'},
          {title: '品系', width: 95,field: 'type'},
          {title: '车型', width: 95,field: 'cx'},
          {title: '单价',width: 95, field: 'limitPrice'},
          {title: '数量',width: 110, field: 'limitNumber'},
          {title: '金额',width: 140, field: 'limitAmount'},
          {title: '其他推广备注', width: 120,field: 'remarks'},
          {title: '打包推广总额',width: 120, field: 'total'},
          {title: '打包推广条件',width: 140, field: 'condition'},
          {title: '推广开始时间',width: 140, field: 'startTime'},
          {title: '推广结束时间',width: 140, field: 'endTime'},
          {title: '实结数量', width: 125,field: 'realNumber'},
          {title: '实结金额', width: 95,field: 'realAmount'},
          {title: '系列开票数量', width: 120,field: 'number'},
          {title: '单台推广分摊', width: 120,field: 'dttgft'},
          {title: '是否已编制协议', width: 120,field: 'isBz'},
          {title: '协议是否已送审', width: 120,field: 'isSs'},
          { title: '操作', width: 110, slots: { default: 'action' },fixed:'right' },
        ],
        data: []
      },
      selectedRowKeys: [],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },

        {title: '操作', fixed: 'right', width: 100, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
        customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
        gsk6: [{required: true, message: '产品归属不能为空', trigger: 'blur'}],
        fuel: [{required: true, message: '燃料不能为空', trigger: 'blur'}],
        series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
        blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
        tgName: [{required: true, message: '推广名称不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getData()
    this.checkName()
    this.getSearchList()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getSearchList(){
      request(GETSEACHLIST_XSGSFEEPROMOTE, METHOD.GET)
              .then(res => {
                if(res.data.data!=null){
                  this.zcpxh_list=res.data.data
                }
              })
    },

    customerBlurs(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.searchForm.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.searchForm.customer!=null){
            if(!this.searchForm.customer.split(" ").join("").length == 0){
              this.searchForm.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },
    customerBlursFrom(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer:this.form.customer
      }).then(res => {
        if(res.data.data.length>0){
          if(this.form.customer!=null){
            if(!this.form.customer.split(" ").join("").length == 0){
              this.form.customerName = res.data.data[0].name
            }
          }
        }else{
          this.$message.info('此客户号没找到信息')
        }
      })
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerList = res.data.data
      })
    },
    handleCustomerChange(value) {
      this.customerList.forEach(item =>{
        if(item.name == value){
          this.searchForm.customer = item.customer
        }
      })
    },
    handleCustomerSearchFrom(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name:value
      }).then(res => {
        this.customerListFrom = res.data.data
      })
    },
    handleCustomerChangeFrom(value) {
      this.customerListFrom.forEach(item =>{
        if(item.name == value){
          this.form.customer = item.customer
        }
      })
    },




    getData: function () {
      this.gridOptionsTG.loading = true
      let current= this.gridOptionsTG.pagerConfig.currentPage
      let size = this.gridOptionsTG.pagerConfig.pageSize
      this.searchForm.productModels = this.productModel.join(",")
      this.searchForm.plates = this.plate.join(",")
      request(LIST_XSGSFEEPROMOTE_PAGE, METHOD.GET, {...this.searchForm, current, size})
              .then(res => {
                const {records, total} = res.data.data
                this.gridOptionsTG.loading = false
                this.gridOptionsTG.data = records
                this.gridOptionsTG.pagerConfig.total = parseInt(total)
              })
    },

    deleteSelectedIds(){
      this.selectedRowKeys=[]
      const $table = this.$refs.xTable
      //当前行选中
      const selectRecords = $table.getCheckboxRecords()
      //已保留选中行
      const selectReserveRecords = $table.getCheckboxReserveRecords()
      if(selectRecords.length<=0&&selectReserveRecords.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      selectRecords.forEach(item=>{
        this.selectedRowKeys.push(item.id)
      })
      selectReserveRecords.forEach(item=>{
        this.selectedRowKeys.push(item.id)
      })

      this.$confirm({
        content: `是否确认删除选择的`+this.selectedRowKeys.length+`条数据？`,
        onOk: () => {
          this.gridOptionsTG.loading = true
          request(DELETE_XSGS_FEE_PROMOTE_LIST, METHOD.POST,{ids:this.selectedRowKeys})
                  .then(() => {
                    this.$message.info('删除成功')
                    this.gridOptionsTG.pagerConfig.currentPage=1
                    this.$refs.xTable.clearCheckboxReserve();
                    this.getData()
                  }).catch(() => {
            this.gridOptionsTG.loading = false
          })
        }
      });

    },

    handleDelete(record) {
      this.$confirm({
        content: `是否确认删除 ${record.productModel} ？`,
        onOk: () => {
          request(DELETE_XSGSFEEPROMOTE + record.id, METHOD.DELETE)
                  .then(() => {
            this.$message.info('删除成功')
            this.getData()
          }).catch(() => {
              this.gridOptionsTG.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          request(SUBMIT_XSGSFEEPROMOTE, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.plate=[],
      this.productModel=[],

      this.searchForm = {
        customerName:null,
        customer:null,
      }
      this.handleSearch();
      this.$refs.xTable.clearCheckboxReserve();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {
        customerName:null,
        customer:null,
      }
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      this.searchForm.productModels = this.productModel
      this.searchForm.plates = this.plate
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          // let option = ""
          // if (searchForm.year != null) {
          //   option = "?year=" + searchForm.year
          // }
          //
          // if (searchForm.customerName != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customerName=" + searchForm.customerName
          // }
          // if (searchForm.customer != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "customer=" + searchForm.customer
          // }
          // if (searchForm.plate != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "plate=" + searchForm.plate
          // }
          // if (searchForm.productModel != null) {
          //   if (option != "") {
          //     option += "&"
          //   } else {
          //     option += "?"
          //   }
          //   option += "productModel=" + searchForm.productModel
          // }
          // window.open(decodeURI(DOWNLOAD_XSGSFEEPROMOTE+option))
          exportExcel(DOWNLOAD_XSGSFEEPROMOTE, {...searchForm}, "费用推广.xlsx")
        },
        onCancel() {},
      });
    },

    handlePageChangeTG ({ currentPage, pageSize }) {
      this.gridOptionsTG.pagerConfig.currentPage = currentPage
      this.gridOptionsTG.pagerConfig.pageSize = pageSize;
      this.getData()
    },
    /** 自动计算价格 **/
    totalBlurs(){
      let f = this.form;
      //自动计算金额
      if(f.limitPrice !== undefined && f.limitNumber !== undefined) {
        f.limitAmount = Ride(f.limitPrice,f.limitNumber);
      }
      //自动计算实结金额
      if(f.limitPrice !== undefined && f.realNumber !== undefined) {
        f.realAmount = Ride(f.limitPrice,f.realNumber);
      }
      //自动计算单台推广分摊
      if(f.realAmount !== undefined && f.number !== undefined && f.number !== '' && f.number !== null) {
        f.dttgft = Divide(f.realAmount,f.number);
      }else{
        f.dttgft = 0;
      }
    },
  }
}
</script>

<style scoped>
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /*/deep/  样式穿透
  */
  /deep/.ant-col-8{
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }

  /deep/.col-blue {
    background-color: #2db7f5;
    color: #fff;
  }

  /deep/.vxe-table--render-default .vxe-body--column.col--ellipsis, .vxe-table--render-default.vxe-editable .vxe-body--column, .vxe-table--render-default .vxe-footer--column.col--ellipsis, .vxe-table--render-default .vxe-header--column.col--ellipsis {
    height: 0px;
  }
  /deep/.vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
    padding: 0px 0;
  }
  /deep/.vxe-table--render-default.border--full .vxe-body--column,/deep/ .vxe-table--render-default.border--full .vxe-footer--column, /deep/.vxe-table--render-default.border--full .vxe-header--column {
    background-image: -webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646)),-webkit-gradient(linear,left top, left bottom,from(#464646),to(#464646));
    background-image: linear-gradient(#464646,#464646),linear-gradient(#464646,#464646);
    background-repeat: no-repeat;
    background-size: 1px 100%,100% 1px;
    background-position: 100% 0,100% 100%;
  }

  /deep/.vxe-table--render-default.border--default
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--full
  .vxe-table--header-wrapper,
  .vxe-table--render-default.border--outer .vxe-table--header-wrapper {
    background-color: #f8f8f9;
  }

  /deep/ .vxe-pager.is--perfect {
    border: 0px solid #464646;
    border-top-width: 0;
    background-color: #fff;
  }
  /deep/ .vxe-header--column{
    background-color: rgba(0, 0, 0, 0.2);
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--stripe{
    background-color: #e6fdff;
  }
  /deep/  .vxe-table--render-default .vxe-body--row.row--checked{
    background-color: #fff3e0;
  }
  /deep/.ant-col-16 {
    display: block;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 100%;
  }
  /deep/.vxe-table--render-default .vxe-body--row.row--hover{
    background-color: #fcce10;
  }
</style>

