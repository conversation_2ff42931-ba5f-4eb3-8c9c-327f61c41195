<template>
  <div>
    <a-card>
      <a-form-model class="contractForm" ref="searchForm" :model="searchForm" :labelCol="{span: 8}" :wrapperCol="{span: 16}">
        <a-row>
          <a-col span="4">
            <a-form-model-item label="年度">
              <a-input v-model="searchForm.year"/>
            </a-form-model-item>
          </a-col>
          <a-col span="6">
            <a-form-model-item label="客户">
              <a-select
                      v-model="searchForm.customer"
                      show-search
                      :default-active-first-option="false"
                      :show-arrow="false"
                      :allowClear="true"
                      :filter-option="false"
                      :not-found-content="null"
                      @search="handleCustomerSearch"
                      @change="handleCustomerChange"
              >
                <a-select-option v-for="d in customerList" :key="d.customer">
                  {{ d.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
          <a-col span="4">
            <a-form-model-item label="状态机">
              <a-input v-model="searchForm.machineType"/>
            </a-form-model-item>
          </a-col>
          <a-col span="4">
            <a-form-model-item label="合同号">
              <a-input v-model="searchForm.contractNo"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="4">
            <a-form-model-item style="text-align: left" :wrapperCol="{span:24}">
              <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon type="search"/>查询</a-button>
              <a-button type="primary" style="margin: 0" @click="resetQuery"><a-icon type="reload"/>重置</a-button>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
      <a-row>
        <a-col :span="24">
          <a-form-model-item>
            <a-button v-if="checkPf('CONTRACT_NO_ADD')" type="primary" style="margin: 0 8px" @click="handleNew"><a-icon type="plus"/>新增</a-button>
            <a-button v-if="checkPf('CONTRACT_NO_DELETE')" type="danger" style="margin: 0 8px" @click="deleteSelectedIds"><a-icon type="delete"/>删除</a-button>
            <a-upload v-if="checkPf('CONTRACT_NO_IMPORT')"
                    name="file"
                    :action="UPLOAD_XSGSCONTRACTNO"
                    :headers="{'Authorization':Cookie.get('Authorization')}"
                    :showUploadList="false"
                      :data="searchForm"
                    style="margin: 0 8px"
                    @change="handleUploadExcel">
              <a-button type="primary">
                <a-icon type="upload"/>
                导入
              </a-button>
            </a-upload>
            <a-button v-if="checkPf('CONTRACT_NO_EXPORT')" type="primary" style="margin: 0 8px" @click="handleExportExcel"> <a-icon type="download"/>导出</a-button>

          </a-form-model-item>
        </a-col>
      </a-row>
      <a-table
          :columns="columns"
          rowKey="id"
          :pagination="pagination"
          :data-source="data"
          :scroll="{ x: 1000 }"
          size="small"
          :customRow="onRowClick"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
          :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{index+1}}
        </template>
                 <span slot="action" slot-scope="record">
                  <a v-if="checkPf('CONTRACT_NO_EDIT')" @click="handleEdit(record)">编辑</a>
                  <a-divider v-if="checkPf('CONTRACT_NO_DELETE')" type="vertical"/>
                  <a v-if="checkPf('CONTRACT_NO_DELETE')" @click="handleDelete(record)">删除</a>
                </span>
      </a-table>
    </a-card>
    <a-modal
        :title="form.id?'编辑':'新增'"
        :visible="dialog"
        :confirm-loading="confirmLoading"
        width="30%"
        @ok="handleSubmit"
        @cancel="closeForm">
      <a-form-model ref="form" :model="form" :rules="rules" :labelCol="{span: 6}" :wrapperCol="{span: 18}">
        <a-form-model-item label="年度" prop="year">
          <a-input v-model="form.year" @blur="changeYear"/>
        </a-form-model-item>
        <a-form-model-item label="客户号" prop="customer">
          <a-input v-model="form.customer" @blur="customerBlurs2"/>
        </a-form-model-item>
        <a-form-model-item label="客户名称" prop="customerName">
          <a-select
                  v-model="form.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :allowClear="true"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch2"
                  @change="handleCustomerChange2"
          >
            <a-select-option v-for="d in customerList2" :key="d.name">
              {{ d.name }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="状态机" prop="machineType">
          <a-select v-model="form.machineType"
                    @dropdownVisibleChange="onclickMachineType(form)"
                    :allowClear="true"
                    showSearch
                    @search="searchMachineType"
                    @change="changedMachineType">
            <a-select-option v-for="sty in machineTypeList" :key="sty">
              {{ sty }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="合同号" prop="contractNo">
          <a-input v-model="form.contractNo"/>
        </a-form-model-item>
        <a-form-model-item label="关联价格" prop="mainId">
        </a-form-model-item>
        <a-form-model-item :wrapperCol="{span:24}">
          <a-table
                  :columns="columns2"
                  rowKey="id"
                  :data-source="priceList"
                  size="small"
                  :pagination="false"
                  :customRow="onRowClick"
                  :row-selection="{ selectedRowKeys: selectedRowKeys2, onChange: onSelectChange2 }"
                  :loading="loading">
              <span slot="action" slot-scope="record">
                  <a @click="showMain(record.id)">查看</a>
                </span>
          </a-table>
        </a-form-model-item>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import {hasAuth} from "@/utils/authority-utils";
import {LIST_XSGSCONTRACTNO_PAGE,DELETE_XSGSCONTRACTNO_IDS,SUBMIT_XSGSCONTRACTNO,DOWNLOAD_XSGSCONTRACTNO,UPLOAD_XSGSCONTRACTNO,LIST_XSGSCUSTOMER,COMBO_MACHINETYPE,COMBO_MAIN_PRICE} from "@/services/api/xsgs";
import {METHOD, request,exportExcel} from "@/utils/request";
import Cookie from 'js-cookie'
export default {
  name: "xsgscontractno.vue",
  data() {
    return {
      hasAuth,
      UPLOAD_XSGSCONTRACTNO,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      // 功能权限关键字
      PF_FIELD:"BASE_DATA_MANAGER,CONTRACT_NO",
      PF_LIST:[],
      searchForm: {},
      form: {
        year:null,
        customer:null,
        customerName:null,
        machineType:null,
        contractNo:null,
        mainId:null,
      },
      selectedRowKeys: [],
      selectedRowKeys2: [],
      customerList:[],
      customerList2:[],
      allCustomerList:[],
      priceList:[],
      machineTypeList:[],
      allMachineTypeList:[],
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
            `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
                total / this.pagination.size
            )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
            this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {title: '序号', dataIndex: 'num', key: 'num', width: 50, align: 'center', fixed: true, scopedSlots: {
            customRender: 'num'
          }
        },
        {title: '年度', dataIndex: 'year', width: 60},
        {title: '客户号', dataIndex: 'customer', width: 90},
        {title: '客户名称', dataIndex: 'customerName', width: 280},
        {title: '状态机', dataIndex: 'machineType', width: 100},
        {title: '合同号', dataIndex: 'contractNo', width: 100},
        {title: '商务核算编号', dataIndex: 'mainId', width: 130},
        {title: '整机开票价', dataIndex: 'unitAmount', width: 120},
        {title: '基本型开票价', dataIndex: 'basicAmount', width: 130},
        {title: '选配件合计', dataIndex: 'chooseAmount', width: 120},
        {title: '操作', fixed: 'right', width: 135, scopedSlots: {customRender: 'action'}}
      ],
      data2: [],
      columns2: [
        {title: '整机开票价',align:"center", dataIndex: 'unit_amount', width: 80},
        {title: '基本型开票价',align:"center", dataIndex: 'basic_amount'},
        {title: '选配件合计',align:"center", dataIndex: 'choose_amount', width: 80},
        {title: '功能',align:"center",  width: 50, scopedSlots: {customRender: 'action'}}
      ],
      rules: {
        year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
        customer: [{required: true, message: '客户号不能为空', trigger: 'blur'}],
        customerName: [{required: false, message: '客户名称不能为空', trigger: 'blur'}],
        machineType: [{required: true, message: '状态机不能为空', trigger: 'blur'}],
        contractNo: [{required: true, message: '合同号不能为空', trigger: 'blur'}],
      }
    }
  },
  async created() {
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
  },
  mounted() {
    this.getCustomerList();
    this.getData()
    this.checkName()
  },
  methods: {
    checkPf(field) {
      return this.PF_LIST.findIndex(s=>s == field) != -1;
    },
    getData: function () {
      this.loading = true
      let {current, size} = this.pagination
      request(LIST_XSGSCONTRACTNO_PAGE, METHOD.POST, {...this.searchForm, current, size})
              .then(res => {
        const {records, total} = res.data.data
        this.loading = false
        this.data = records
        this.pagination.total = parseInt(total)
      })
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys;
    },
    onSelectChange2(selectedRowKeys){
      if (!this.isEmpty(selectedRowKeys)) {
        let selectKey = selectedRowKeys[selectedRowKeys.length - 1];
        let selectKeys = [];
        selectKeys.push(selectKey)
        this.selectedRowKeys2 = selectKeys;
        this.form.mainId = selectKey;
      } else {
        this.selectedRowKeys2 = selectedRowKeys;
        this.form.mainId = null;
      }

    },
    customerBlurs(){
      let customer = this.searchForm.customer;
      if (!customer) {
        this.searchForm.customerName = null;
      }
      let newData = this.customerList.filter(item => item.customer == customer);
      if (newData.length > 0) {
        this.searchForm.customerName = newData[0].name;
      } else {
        newData = this.allCustomerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.searchForm.customerName = newData[0].name;
        }
      }
    },

    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.allCustomerList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList = this.allCustomerList;
      }

    },
    handleCustomerChange(value) {
      if (value) {
        this.customerList.forEach(item =>{
          if(item.name == value){
            this.searchForm.customer = item.customer
            this.searchForm.customerName = item.name
          }
        })
      } else {
        this.searchForm.customer = null
      }

    },
    changeYear(){
      this.getPriceList();
    },
    customerBlurs2(){
      let customer = this.form.customer;
      if (!customer) {
        this.form.customerName = null;
      }
      let newData = this.customerList2.filter(item => item.customer == customer);
      if (newData.length > 0) {
        this.form.customerName = newData[0].name;
      } else {
        newData = this.allCustomerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          this.form.customerName = newData[0].name;
        }
      }
      this.getPriceList();
    },

    handleCustomerSearch2(value) {
      if (value) {
        this.customerList2 = this.allCustomerList.filter(s=>s.name.indexOf(value) != -1 || s.customer.indexOf(value) != -1);
      } else {
        this.customerList2 = this.allCustomerList;
      }

    },
    handleCustomerChange2(value) {
      if (value) {
        this.customerList.forEach(item =>{
          if(item.name == value){
            this.form.customer = item.customer
            this.form.customerName = item.name
          }
        })
      } else {
        this.form.customer = null
      }
      this.getPriceList();
    },
    getCustomerList(){
      request(LIST_XSGSCUSTOMER, METHOD.POST, this.searchForm).then(res => {
        this.allCustomerList = res.data.data
        this.customerList = res.data.data
        this.customerList2 = res.data.data
      })
    },
    onclickMachineType() {
      if (!this.form.year || !this.form.customer) {
        return;
      }
      this.machineTypeList = [];
      request(COMBO_MACHINETYPE, METHOD.POST, {
        year: this.form.year + '',
        customer:this.form.customer
      }).then(res => {
        this.machineTypeList = res.data.data;
        this.allMachineTypeList= res.data.data;
      })
    },
    searchMachineType(value) {
      if (value) {
        this.machineTypeList = this.allMachineTypeList.filter(s=>s.indexOf(value) != -1);
      } else {
        this.machineTypeList = this.allMachineTypeList;
      }
    },
    changedMachineType(value){
      this.getPriceList();
    },
    getPriceList() {
      this.priceList = [];
      if (!this.form.year || !this.form.customer || !this.form.machineType) {
        return;
      }
      let machineTypes = [];
      machineTypes.push(this.form.machineType)
      request(COMBO_MAIN_PRICE, METHOD.POST, {
        year: this.form.year + '',
        customer:this.form.customer,
        machineType:machineTypes
      }).then(res => {
        this.priceList = res.data.data;
      })
    },
    deleteSelectedIds(){
      if(this.selectedRowKeys.length<=0){
        this.$message.info('请至少选择一条记录！')
        return
      }
      this.deleteByIds(this.selectedRowKeys);
    },
    handleDelete(record) {
      let ids =[];
      ids.push(record.id);
      this.deleteByIds(ids)
    },
    deleteByIds(ids) {
      this.$confirm({
        content: `是否确认删除选择的数据？`,
        onOk: () => {
          this.loading = true
          request(DELETE_XSGSCONTRACTNO_IDS, METHOD.POST,[...ids])
                  .then(() => {
                    this.$message.info('删除成功')
                    this.getData()
                  }).catch(() => {
            this.loading = false
          })
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.loading = true
          request(SUBMIT_XSGSCONTRACTNO, METHOD.POST, this.form)
                  .then(() => {
            this.getData()
            this.$message.info('提交成功')
            this.closeForm()
          })
        }
      })
    },
    /** 新增，打开对话框 */
    handleNew() {
      this.form = {};
      this.selectedRowKeys2 = [];
      this.getPriceList();
      this.dialog = true
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      this.dialog = true
      this.form = {...record}
      this.priceList = [];
      this.selectedRowKeys2 = [];
      this.getPriceList();
      if(this.form.mainId) {
        this.selectedRowKeys2.push(this.form.mainId)
      }
    },
    showMain(id) {
      this.dialog = false;
      this.$router.push({path:'/xsgs_allowance_mainSee/'+id, props: true})
    },
    /** 处理查询 */
    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {}
      this.handleSearch();
    },
    /** 关闭对话框，表单重置 */
    closeForm() {
      this.dialog = false
      this.$refs.form.resetFields()
      this.form = {}
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.loading = true
      if (info.file.status !== 'uploading') {
        this.getData()
      }
      if (info.file.status === 'done') {
        this.$message.success(`${info.file.name} 导入成功`);
      } else if (info.file.status === 'error') {
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      const searchForm =this.searchForm
      this.$confirm({
        title: '提示',
        content: '是否确认导出所有信息数据项?',
        onOk() {
          exportExcel(DOWNLOAD_XSGSCONTRACTNO, {...searchForm}, "合同编号.xlsx")
        },
        onCancel() {},
      });
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size;
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size;
      this.getData();
    },
  }
}
</script>

<style scoped>
  .ant-input-number{
    width: 100%;
  }
  .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
    padding: 1px 1px;
    overflow-wrap: break-word;
  }
  /deep/.ant-form-item {
    margin: 0;
  }
  /deep/.ant-form > table > tbody > tr > td {
    border: 1px solid #f0f0f0;
  }
</style>


