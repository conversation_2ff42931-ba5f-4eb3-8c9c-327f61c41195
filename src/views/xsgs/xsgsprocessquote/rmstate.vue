<template>
  <div>
    <a-card>
      <a-form-model ref="searchForm" :model="searchForm" :labelCol="{ span: 8 }" :wrapperCol="{ span: 16 }">
        <table style="width: 100%; border: 1px solid #f0f0f0;">
          <tbody class="ant-table">
            <tr>
              <td style="width: 70px"><a-form-model-item label="状态号" /></td>
              <td style="min-width: 100px">
                <a-select v-model="searchForm.stateCode" show-search :default-active-first-option="false"
                  :show-arrow="false" :allowClear="true" :filter-option="false" :not-found-content="null"
                  @search="handleStateCodeSearch">
                  <a-select-option v-for="d in stateCodeList" :key="d">
                    {{ d }}
                  </a-select-option>
                </a-select>
              </td>
              <td style="width: 70px"><a-form-model-item label="客户" /></td>
              <td style="min-width: 200px;max-width: 200px">
                <a-select v-model="searchForm.customerList" show-search :default-active-first-option="false"
                  :show-arrow="false" mode="multiple" :allowClear="true" :filter-option="false"
                  @search="handleCustomerSearch" @change="handleCustomerChange" :not-found-content="null">
                  <a-select-option v-for="d in customerList" :key="d">
                    {{ d }}
                  </a-select-option>
                </a-select>
              </td>

              <td style="width: 90px"><a-form-model-item label="TC是否插旗" /></td>
              <td style="min-width: 80px">
                <a-select v-model="searchForm.isFlage">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="1">是</a-select-option>
                  <a-select-option value="0">否</a-select-option>
                </a-select>

              </td>

              <td style="width: 80px"><a-form-model-item label="是否报价" /></td>
              <td style="min-width: 80px">
                <a-select v-model="searchForm.isQuote">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="1">是</a-select-option>
                  <a-select-option value="0">否</a-select-option>
                </a-select>
              </td>
              <td style="width: 80px"><a-form-model-item label="是否闭环" /></td>
              <td style="min-width: 80px">
                <a-select v-model="searchForm.isClosed">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="1">是</a-select-option>
                  <a-select-option value="0">否</a-select-option>
                </a-select>
              </td>
              <td>
                <a-button type="primary" style="margin: 0 5px" @click="handleSearch"><a-icon
                    type="search" />查询</a-button>
                <a-button type="primary" @click="resetQuery"><a-icon type="reload" />重置</a-button>
              </td>
            </tr>
          </tbody>
        </table>
        <a-row style="margin-top: 5px">
          <a-col :span="24">
            <a-button type="primary" style="margin: 0 8px" @click="handleExportExcel"><a-icon
                type="download" />导出</a-button>
            <a-button type="primary" style="margin: 0 8px" @click="seriesManaga">状态机配置</a-button>
          </a-col>
        </a-row>
      </a-form-model>
      <a-table :columns="columns" rowKey="id" :pagination="pagination" :data-source="data" :scroll="{ x: 1000 }"
        size="small" bordered :rowClassName="rowStyle" :customRow="onRowClick" :loading="loading">
        <!-- 序号 -->
        <template slot="num" slot-scope="text, record, index">
          {{ index + 1 }}
        </template>
        <template slot="isFlage" slot-scope="text, record">
          {{ (record.isFlage && record.isFlage == 1) ? '是' : '否' }}
        </template>
        <template slot="isQuote" slot-scope="text, record">
          {{ (record.isQuote && record.isQuote == 1) ? '是' : '否' }}
        </template>
        <template slot="quoteTime" slot-scope="text, record">
          {{ dateFormat(record.quoteTime) }}
        </template>
        <template slot="isClosed" slot-scope="text, record">
          {{ (record.isClosed && record.isClosed == 1) ? '是' : '否' }}
        </template>
        <template slot="completeTime" slot-scope="text, record">
          {{ dateFormat(record.completeTime) }}
        </template>
        <template slot="receiveTime" slot-scope="text, record">
          {{ dateFormat(record.receiveTime) }}
        </template>
        <template slot="flagTime" slot-scope="text, record">
          {{ dateFormat(record.flagTime) }}
        </template>
        <template slot="quoteTime" slot-scope="text, record">
          {{ dateFormat(record.quoteTime) }}
        </template>
        <template slot="closedTime" slot-scope="text, record">
          {{ dateFormat(record.closedTime) }}
        </template>
        <template slot="action" slot-scope="record">
          <span>
            <a v-if="!record.receiveUser" @click="receive(record)">领用</a>
            <a-divider v-if="!record.receiveUser" type="vertical" />

            <a @click="goRm(record)">RM系统</a>
            <a-divider type="vertical" />
            <a @click="goQuoteProcess(record)">商务报价审批流程</a>
          </span>
        </template>

      </a-table>
    </a-card>

    <a-modal title="状态机配置" :visible="seriesVisiable" :footer="null" @cancel="seriesVisiable = false">
      <a-form ref="seriesForm" v-if="seriesListData" :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }">
        <a-form-item label="股司产品" prop="gusi_List">
          <a-tag type="info" v-for="item in gusiSeries" :key="item.id">
            <span>
              {{ item.series }}
            </span>
            <a-icon type="close" @click="deleteId(item.id)" />
          </a-tag>
          <a-button size="small" type="primary" @click="addItem('1', 'gusi')">+</a-button>
        </a-form-item>

        <a-form-item label="动力公司产品" prop="dongli_List">
          <a-tag type="info" v-for="item in DongliSeries" :key="item.id">
            <span>
              {{ item.series }}
            </span>
            <a-icon type="close" @click="deleteId(item.id)" />
          </a-tag>
          <a-button size="small" type="primary" @click="addItem('2', 'dongli_List')">+</a-button>
        </a-form-item>

        <a-form-item label="联合动力公司产品" prop="lianhe_List">
          <a-tag type="info" v-for="item in lianheSeries" :key="item.id">
            <span>
              {{ item.series }}
            </span>
            <a-icon type="close" @click="deleteId(item.id)" />
          </a-tag>
          <a-button size="small" type="primary" @click="addItem('3', 'lianhe_List')">+</a-button>
        </a-form-item>
      </a-form>
    </a-modal>
    <a-modal title="添加项" :loading="addseriesLoading" :visible="addseriesVisiable" @ok="handleAddSeries"
      @cancel="addseriesVisiable = false">
      <a-input v-model="addSeries" placeholder="请输入添加项，中间使用英文 “,”间隔开 " />
    </a-modal>

  </div>
</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import { LIST_XSGSRMSTATEQUOTE_PAGE, LIST_STATE_CODE, UPDATE_RECEIVE_USER, GET_REMIND_INFO, DOWNLOAD_STATE_CODE, STATE_CODE_CUSTOMERLIST, XSGS_STATUS_LIST, XSGS_STATUS_ADD, XSGS_STATUS_DELETE } from "@/services/api/xsgs";
import { METHOD, request, exportExcel } from "@/utils/request";
import { dateFormat } from "@/utils/dateUtil";
import Cookie from 'js-cookie'
export default {
  name: "rmstate.vue",
  data() {
    return {
      hasAuth,
      Cookie,
      loading: false,
      dialog: false,
      confirmLoading: false,
      remindId: null,
      stateCode: null,
      searchForm: {
        stateCode: null,
        customerName: null,
        customerList: [],
        isFlage: null,
        isQuote: null,
        isClosed: null,
      },
      seriesForm: {},
      seriesVisiable: false,
      addseriesLoading: false,
      seriesListData: [],
      gusiSeries:[],
      DongliSeries:[],
      lianheSeries:[],
      addseriesVisiable: false,
      currentCid: '',
      addSeries: '',
      stateCodeList: [],
      allStateCodeList: [],
      customerList: [],
      allCustomerList: [],
      form: {},
      pagination: {
        total: 0,
        current: 1,
        size: '10',
        showSizeChanger: true, // 是否可以改变 size
        showQuickJumper: true, // 是否可以快速跳转至某页
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        showTotal: (total) =>
          `共 ${total} 条 第${this.pagination.current}/${Math.ceil(
            total / this.pagination.size
          )}页`, // 显示总数
        onChange: (page, size) => this.onPageChange(page, size), // 页码改变的回调
        onShowSizeChange: (current, size) =>
          this.onSizeChange(current, size) // 改变每页数量时更新显示
      },
      data: [],
      columns: [
        {
          title: '序号', dataIndex: 'num', key: 'num', width: 30, align: 'center', scopedSlots: {
            customRender: 'num'
          }
        },
        { title: '状态号', width: 130, dataIndex: 'stateCode' },
        { title: '产品归属',width: 130,  dataIndex: 'belongCompany' },
        { title: '系列', width: 65, dataIndex: 'series' },
        { title: '排放', width: 65, dataIndex: 'letOut' },
        { title: '客户', dataIndex: 'customerName', width: 220 },
        { title: '客户号', dataIndex: 'customer', width: 80 },
        { title: '订单数量',width: 100, dataIndex: 'orderNum', },
        { title: '订单客户',width: 130, dataIndex: 'orderCustomer', },
        { title: 'TC是否插旗', dataIndex: 'isFlage', width: 50, align: 'center', scopedSlots: { customRender: 'isFlage' } },
        { title: '是否报价', dataIndex: 'isQuote', width: 40, align: 'center', scopedSlots: { customRender: 'isQuote' } },
        { title: '报价是否闭环', dataIndex: 'isClosed', width: 50, align: 'center', scopedSlots: { customRender: 'isClosed' } },
        { title: '领用人', dataIndex: "receiveUserName", width: 50, align: 'center', scopedSlots: { customRender: 'receiveUserName' } },
        { title: '领用时间', dataIndex: "receiveTime", width: 120, align: 'center', scopedSlots: { customRender: 'receiveTime' } },
        { title: 'RM完成时间', dataIndex: "completeTime", width: 120, align: 'center', scopedSlots: { customRender: 'completeTime' } },
        { title: 'TC完成时间', dataIndex: "flageTime", width: 90, align: 'center' },
        { title: '报价时间', width: 120, align: 'center', scopedSlots: { customRender: 'quoteTime' } },
        { title: '闭环时间', width: 120, align: 'center', scopedSlots: { customRender: 'closedTime' } },
        { title: '功能', align: 'center', width: 230, fixed: "right", scopedSlots: { customRender: 'action' } }
      ],

    }
  },
  async mounted() {
    this.remindId = this.$route.query.remindId;
    if (this.remindId) {
      let res = await this.getRemindInfo();
      if (res && res.data && res.data.data) {
        this.stateCode = res.data.data;
      }
    }
    if (this.stateCode) {
      this.searchForm.stateCode = this.stateCode;
    }
    this.getData();
    this.getStateCodeList();
    this.getCustomerList();
    this.checkName()
  },
  methods: {
    getData: function () {
      this.loading = true
      let { current, size } = this.pagination
      request(LIST_XSGSRMSTATEQUOTE_PAGE, METHOD.POST, { ...this.searchForm, current, size })
        .then(res => {
          const { records, total } = res.data.data
          this.loading = false
          this.data = records
          this.pagination.total = parseInt(total)
        })
    },
    getStateCodeList() {
      request(LIST_STATE_CODE, METHOD.POST, {}).then(res => {
        this.allStateCodeList = res.data.data;
        this.stateCodeList = this.allStateCodeList;
      })
    },
    getCustomerList() {
      request(STATE_CODE_CUSTOMERLIST, METHOD.GET).then(res => {
        this.customerList = res.data.data;
        this.allCustomerList = res.data.data;
      })
    },
    handleCustomerSearch(value) {
      if (value) {
        this.customerList = this.allCustomerList.filter(s => s.indexOf(value) != -1);
      } else {
        this.customerList = this.allCustomerList;
      }

    },
    handleCustomerChange(value) {
      if (!value) {
        this.customerList = this.allCustomerList;
      }
    },
    async getRemindInfo() {
      return request(GET_REMIND_INFO, METHOD.GET, { remindId: this.remindId });
    },
    handleStateCodeSearch(value) {
      let newData = this.allStateCodeList.filter(item => {
        if (!value) {
          return true;
        } else {
          if (!item) {
            return false;
          } else {
            return item.indexOf(value) != -1;
          }
        }
      });
      this.stateCodeList = this.cutList(newData, 0, 20);
    },
    cutList(list, start, end) {
      if (!list || list.length == 0) {
        return [];
      }
      let len = end - start;
      if (list.length <= len) {
        return list;
      } else {
        return list.slice(start, end);
      }
    },
    goQuoteProcess() {
      this.$router.push({
        path: '/xsgs_process_manager/xsgs_quote_applove/quote',
        props: true
      })
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.searchForm = {
        stateCode: null,
        isFlage: null,
        isQuote: null,
        isClosed: null,
      }
      this.handleSearch();
    },

    handleSearch() {
      this.pagination.current = 1
      this.getData()
    },
    onPageChange(page, size) {
      this.pagination.current = page;
      this.pagination.size = size.toString();
      this.getData();
    },
    onSizeChange(current, size) {
      this.pagination.current = 1;
      this.pagination.size = size.toString();
      this.getData();
    },
    goRm(record) {
      let url = process.env.VUE_APP_RM_PROCESS
      url += record.formId + "?instanceId=" + record.instanceId;
      window.open(url);
    },
    receive(record) {
      let data = {};
      data['stateCode'] = record.stateCode;
      let that = this;
      this.$confirm({
        content: `是否确认领用 ${record.stateCode} ？`,
        onOk: () => {
          that.loading = true
          request(UPDATE_RECEIVE_USER, METHOD.POST, data)
            .then(() => {
              that.$message.info('领用成功')
              that.getData()
            }).catch(() => {
            })
        }
      });
    },
    /** 导出按钮操作 */
    handleExportExcel() {
      exportExcel(DOWNLOAD_STATE_CODE, {}, "状态机进度跟踪表.xlsx")
    },
    rowStyle(rowObj) {
      if (this.stateCode) {
        let data = rowObj;
        if (data.stateCode == this.stateCode) {
          return "rowBackgroundColor"
        } else {
          return ""
        }
      }
    },
    dateFormat(date) {
      if (date) {
        return dateFormat(date, 'YYYY-MM-DD HH:mm');
      }
      return "";
    },
    seriesManaga() {
      this.seriesVisiable = true;
      request(XSGS_STATUS_LIST, METHOD.GET).then(res => {
        if (res.data.code == 0 && res.data.data.length > 0) {
          this.seriesListData = res.data.data;
          this.gusiSeries = res.data.data[0].companyProductDetailList
          this.DongliSeries = res.data.data[1].companyProductDetailList
          this.lianheSeries = res.data.data[2].companyProductDetailList
        }

      });
    },
    handleAddSeries() {
      // 对addSeries进行字符串转数组处理，将其中的中文逗号转成英文逗号
      this.addSeries = this.addSeries.replace(/，/g, ",");
      // 如果最后一个英文逗号后没有字符，则去掉最后一个英文逗号,
      if (this.addSeries.charAt(this.addSeries.length - 1) == ",") {
        this.addSeries = this.addSeries.substring(0, this.addSeries.length - 1);
      }
      // console.log(this.addSeries);
      // 字符串转数组
      let arr = this.addSeries.split(",");
      // 删除空项
      arr = arr.filter(item => item);
      // map循环数组构建数组对象
      let newArr = arr.map(item => {
        return {
          series: item,
          cid: this.currentCid
        }
      });
      console.log(newArr);

      this.addseriesLoading = true
      request(XSGS_STATUS_ADD, METHOD.POST, newArr).then(res => {
        if (res.data.code == 0) {
          this.$message.success('添加成功')
          this.addseriesLoading = false
          this.addseriesVisiable = false
          this.seriesManaga()
        } else {
          if (res.data.message) {
            this.$message.error(res.data.message)
          } else {
            this.$message.error('添加失败')
          }
          this.addseriesLoading = false
          this.addseriesVisiable = false
        }

      });
    },
    seriesDefa(List) {
      // 将List中每项的id提取出来，放入数组中
      return List.map(item => item.id)
    },
    deleteId(id) {
      console.log(id);

      let newArr = [id]
      // 弹出确认框确认删除
      this.$confirm({
        content: `是否确认删除？`,
        onOk: () => {
          request(XSGS_STATUS_DELETE, METHOD.POST, newArr).then(res => {
            if (res.data.code == 0) {
              this.$message.success('删除成功')
              this.seriesManaga()
            } else {
              if (res.data.message) {
                this.$message.error(res.data.message)
              } else {
                this.$message.error('删除失败')
              }
            }

          });
        },
        onCancel: () => {
          this.seriesManaga()
        }
      });


    },
    addItem(cid) {
      console.log(cid);

      this.addseriesVisiable = true
      this.currentCid = cid
    },
  },

}
</script>

<style scoped>
.ant-input-number {
  width: 100%;
}

/deep/.rowBackgroundColor {
  background-color: #ffff00 !important;
}

.ant-table-thead>tr>th,
.ant-table-tbody>tr>/deep/ td {
  padding: 1px 1px;
  overflow-wrap: break-word;
}

/*/deep/  样式穿透
*/
/deep/.ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/.ant-form-item {
  margin: 0;
}

/deep/.ant-form>table>tbody>tr>td {
  border: 1px solid #f0f0f0;
}
</style>
