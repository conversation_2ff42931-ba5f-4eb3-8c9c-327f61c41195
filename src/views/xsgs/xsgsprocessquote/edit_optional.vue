<template>
   <div>

    <a-table
            :columns="optionalColumns"
            rowKey="id"
            class="`quotePartsList"
            :pagination="false"
            :data-source="optionalData.filter(s=>s.isDelete != 1)"
            slot="expandedRowRender"
            :scroll="{ x: 520 }"
            size="small"
            bordered
            :customRow="onRowClick"
            :loading="optionalloading">
            <template slot="isCheck" slot-scope="text, record">
                <a-checkbox v-if="!isView" :checked="record.isCheck === '1' ? true:false "  @change="cancel(record)">
                </a-checkbox>
            </template>
            <template slot="subType" slot-scope="text, record">
                <a-select v-if="!isView"  style="width: 100%" v-model="record.subType"  @change="e => handleChooseSubTypeChange(record)">
                    <a-select-option value="选型件">选型件</a-select-option>
                    <a-select-option value="选装件">选装件</a-select-option>
                </a-select>
                <span v-if="isView">{{record.subType}}</span>
            </template>
            <template slot="partsName" slot-scope="text, record" >
                <a-select  style="width: 100%" v-model="record.partsName" @dropdownVisibleChange="onclickPartsName()"  v-if="record.subType=='选型件' && !isView"
                           showSearch
                           :filterOption="filterOption"
                           @change="e => handleChoosePartChange(record)">
                    <a-select-option v-for="sty in xxListData" :key="sty"  >
                        {{ sty }}
                    </a-select-option>
                </a-select>
                <a-select  style="width: 100%" v-model="record.partsName" @dropdownVisibleChange="onclickPartsName()"  v-if="record.subType=='选装件' && !isView"
                           showSearch
                           :filterOption="filterOption"
                           @change="e => handleChoosePartChange(record)">
                    <a-select-option v-for="sty in xzListData" :key="sty"  >
                        {{ sty }}
                    </a-select-option>
                </a-select>
                <span v-if="isView">{{record.partsName}}</span>
            </template>

            <template slot="series" slot-scope="text, record">
              <a-select :style="`width: 100%;${record.hasNoBasic == 1?'color:red':''}`" v-model="record.series" v-if="record.partsName!='' && searchForm.dataType == '新版'"  @dropdownVisibleChange="onclickSeries(record)" @change="handleChooseSeriesChange(record)">
                <a-select-option v-for="sty in seriesData" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </template>

            <template slot="specs" slot-scope="text, record"  >
                    <a-select style="width: 100%" v-model="record.specs" v-if="record.partsName!='' && !isView"  @dropdownVisibleChange="onclickSpecs(record)" @change="handleChooseSpecsChange(record)">
                        <a-select-option v-for="sty in AllListData" :key="sty"  >
                            {{ sty }}
                        </a-select-option>
                    </a-select>
                <span v-if="isView">{{record.specs}}</span>
            </template>

            <template slot="xs" slot-scope="text, record"  >
              <a-select  :style="`width: 100%;${record.hasNoBasic == 1?'color:red':''}`" v-model="record.xs" v-if="record.partsName!=''"  @dropdownVisibleChange="onclickXs(record)" @change="handleChooseXsChange(record)">
                <a-select-option v-for="sty in xsData" :key="sty"  >
                  {{ sty }}
                </a-select-option>
              </a-select>
            </template>

            <template slot="supplierBak" slot-scope="text, record" >
                    <a-select style="width: 100%" v-model="record.supplierBak" v-if="record.partsName!='' && !isView" @dropdownVisibleChange="onclickSupplierBak(record)"   @change="e => handleChooseSupplierBakChange(record)">
                        <a-select-option v-for="sty in supplierBakListData" :key="sty.supplier"  >
                            {{ sty.supplier }}
                        </a-select-option>
                    </a-select>
                <span v-if="isView">{{record.supplierBak}}</span>
            </template>

            <template slot="budgetedPrice" slot-scope="text, record" >
                <a-input-number
                        v-model="record.budgetedPrice"
                        :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                        :precision="2"
                        :step="0.01"
                        v-if="!isView"
                        style="width: 100%"
                        @blur="blursBudgetedPrice(record)"
                        @change="handleChange(record.budgetedPrice, record.id,record.budgetedPrice)"

                />
                <span v-if="isView">{{record.budgetedPrice}}</span>
            </template>

            <template slot="actualPrice" slot-scope="text, record" >
                <a-input-number
                        v-model="record.actualPrice"
                        :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                        :precision="2"
                        :step="0.01"
                        v-if="!isView"
                        style="width: 100%"
                        @blur="blursActualPrice(record)"
                        @change="handleChange(record.actualPrice, record.id,record.actualPrice)"

                />
                <span v-if="isView">{{record.actualPrice}}</span>
            </template>
            <template slot="valuePrice" slot-scope="text, record" >
                <a-input-number
                        v-model="record.valuePrice"
                        :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                        :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                        :precision="2"
                        :step="0.01"
                        v-if="record.subType=='选型件'&&!isView"
                        style="width: 100%"
                        @blur="blursValuePrice()"
                        @change="handleChange(record.valuePrice, record.id,record.valuePrice)"
                />
                <span v-if="isView">{{record.valuePrice}}</span>
            </template>

    </a-table>

    <div v-if="!isView">
        <a-button style="width: 100%; margin-top: 16px; margin-bottom: 8px" type="dashed" icon="plus" @click="newMember">新增</a-button>
    </div>

   </div>

</template>

<script>
    import {
        // LIST_XSGSALLOWANCEPARTS_PAGE,
        LIST_XSGSPARTSPRICE,
        LIST_XSGSALLOWANCEPARTS,
        getUuid,
        LIST_XSGSPARTSSETTING,
        LIST_XSGSPARTSOPTION} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "edit_optional",

        props:{
            masterId:String,
            searchForm:Object,
            year:Number,
            basicSubtotal:Number,
            productModelJh:String,
            optionList:Array,
            isView:Boolean
        },
        data() {
            return {
                isBlurs:false,
                //选型选装配置
                optionalData: [],
                optionalloading: false,
                optionalForm: {
                    masterId:null,
                },
                subType:null,
                subType2:null,
                subType3:null,
                subType4:null,
                subType5:null,
                subType6:null,
                subType7:null,
                subType8:null,
                partsName:null,
                partsName2:null,
                partsName3:null,
                partsName4:null,
                partsName5:null,
                partsName6:null,
                partsName7:null,
                partsName8:null,
                ids:[],
                isJump:true,
                isJump2:true,
                noInList:[],
                noPartsList:[],
                delPartsList:[],// 删除的选装件
                // linshiList:[],
                form:{},
                allForm:{},
                optionalPagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.optionalPagination.current}/${Math.ceil(
                            total / this.optionalPagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onOptionalcPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onOptionalcSizeChange(current, size) // 改变每页数量时更新显示
                },
                optionalColumns: [
                    {title: '选中', dataIndex: 'isCheck',width: 50, scopedSlots: { customRender: 'isCheck' }},
                    {title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }},
                    {title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }},
                    {title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }},
                    {title: '供应商', dataIndex: 'supplierBak',scopedSlots: { customRender: 'supplierBak' }},
                    {title: '指导售价', dataIndex: 'budgetedPrice',scopedSlots: { customRender: 'budgetedPrice' }},
                    {title: '实际售价', dataIndex: 'actualPrice',scopedSlots: { customRender: 'actualPrice' }},
                    {title: '价差', dataIndex: 'valuePrice',scopedSlots: { customRender: 'valuePrice' }},
                    // {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
                ],
                optionalsColumns: [
                {title: '选中', dataIndex: 'isCheck',width: 50, scopedSlots: { customRender: 'isCheck' }},
                {title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }, width: 90},
                {title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }, width: 200},
                {title: '系列', dataIndex: 'series', scopedSlots: { customRender: 'series' }, width: 90},
                {title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }, width: 120},
                {title: '形式', dataIndex: 'xs', scopedSlots: { customRender: 'xs' }, width: 90},
                {title: '供应商', dataIndex: 'supplierBak',scopedSlots: { customRender: 'supplierBak' }, width: 120},
                {title: '指导售价', dataIndex: 'budgetedPrice',scopedSlots: { customRender: 'budgetedPrice' }, width: 90},
                {title: '实际售价', dataIndex: 'actualPrice',scopedSlots: { customRender: 'actualPrice' }, width: 90},
                {title: '价差', dataIndex: 'valuePrice',scopedSlots: { customRender: 'valuePrice' }, width: 90},
                // {title: '操作', fixed: 'right', width: 130,dataIndex: 'operation', scopedSlots: {customRender: 'action'}}
              ],
                xzListData:[],
                xxListData:[],
                specsListData:[],
                AllListData:[],
                supplierBakListData:[],
                seriesData:[],
                xsData:[],
            }
        },
        mounted() {
            this.checkName();
        },
        watch: {
          "searchForm": {
            handler(val) {
              if (val.dataType == '新版') {
                this.optionalColumns = this.optionalsColumns

              } else if (val.dataType == '旧版') {
                this.optionalColumns = this.optionalsColumns.filter(x => x.title != '系列' && x.title != '形式');
                this.optionalData.forEach(item => {
                  item.series = null, item.xs = null
                })
                this.subType = null, this.subType2 = null, this.partsName = null, this.partsName2 = null
                this.subType5 = null, this.subType6 = null, this.partsName5 = null, this.partsName6 = null
              }
            },
            deep: true
          },
        },
        methods: {
            initDataList(){
                request(LIST_XSGSPARTSOPTION, METHOD.POST, {
                    platformName:this.searchForm.plate,
                    series:this.searchForm.series,
                    // productModel:this.productModelJh,
                    mainType:"选装件"
                }).then(res =>  {
                    var temp = [];
                    for(var i=0;i<res.data.data.length;i++){
                        if(temp.indexOf(res.data.data[i].partsName)==-1){
                            temp.push(res.data.data[i].partsName);
                        }
                    }
                    this.xzListData =temp

                })
                request(LIST_XSGSPARTSOPTION, METHOD.POST, {
                    platformName:this.searchForm.plate,
                    series:this.searchForm.series,
                    mainType:"选型件"
                }).then(res =>  {
                    var temp = [];
                    for(var i=0;i<res.data.data.length;i++){
                        if(temp.indexOf(res.data.data[i].partsName)==-1){
                            temp.push(res.data.data[i].partsName);
                        }
                    }
                    this.xxListData =temp
                })
            },
            getOptionalcDataList(){
                return  this.optionalData;
            },
            getOptionalcDelDataList(){
                return  this.delPartsList;
            },
            getIsBlurs(){
                return  this.isBlurs;
            },
            setIsBlurs(){
                this.isBlurs=false;
            },
            //选型选装配置
            getOptionalcData: function () {
                if(this.masterId!=null){
                    this.optionalloading = true
                    this.optionalForm.subType="subType"
                    this.optionalForm.isSubtraction="0";
                    this.optionalForm.masterId=this.masterId;

                    request(LIST_XSGSALLOWANCEPARTS, METHOD.POST, {...this.optionalForm})
                        .then(res => {
                            this.optionalloading = false
                            if(this.searchForm.id===null){
                                res.data.data.forEach(item =>{
                                    // request(getUuid, METHOD.GET).then(res => {
                                    //     item.id=res.data.data
                                    // })
                                    item.masterId=null
                                })
                                this.optionalData = res.data.data
                            }else{
                                this.optionalData = res.data.data
                            }
                            this.optionalData.forEach(item=>{
                                item['sysId']=item.id;
                                item['sysBudgetedPrice']=item.budgetedPrice;
                                item['sysActualPrice']=item.actualPrice;
                                item['sysValuePrice']=item.valuePrice;
                                item['sysStandardPrice']=item.standardPrice;
                                item['sysStandardSpecs']=item.standardSpecs;
                                item['sysSpecs']=item.specs;
                                item['sysSupplierBak']=item.supplierBak;
                                item['sysPartsName']=item.partsName;
                                item['sysIsSubtraction']=item.isSubtraction;
                                item['sysSubType']=item.subType;
                                item['isDelete']=0;
                            });
                            this.delPartsList = [];
                        })
                }else{
                    this.optionalData = []
                    this.optionalPagination.total = parseInt(this.optionalData.length)
                }

            },
            //暂时list
            getoptionList(data){
                this.optionalloading = false
                this.optionalData = data;
                this.optionalPagination.total = parseInt(this.optionList.length)

                //默认进入编辑状态
                let newData = [...this.optionalData];
                let newData1 = newData.filter(s=>s.isDelete != 1);
                newData1.forEach(item=>{
                    item['isCheck'] = '1';
                });
                let delData = newData.filter(s=>s.isDelete == 1);
                this.optionalData = newData1;
                this.delPartsList = delData;
                this.blursValuePrice();
            },
            //获取改变值
            handleChange (value, id, column) {
                const newData = [...this.optionalData]
                const target = newData.find(item => id === item.id)
                if (target) {
                    target[column] = value
                    this.optionalData = newData
                }

            },

            //选型选装配置
            onOptionalcPageChange(page, size) {
                this.optionalPagination.current = page;
                this.optionalPagination.size = size.toString();
            },
            onOptionalcSizeChange(current, size) {
                this.optionalPagination.current = 1;
                this.optionalPagination.size = size.toString();

            },
            //新增
            newMember () {
                request(getUuid, METHOD.GET).then(res => {
                      const length =  this.optionalPagination.total;
                      const newData = [...this.optionalData]
                         newData.push({
                            id: length === 0 ? res.data.data : res.data.data ,
                            masterId: null,
                            isCheck: '1',
                            subType: '',
                            partsName: '',
                            specs: '',
                            supplierBak: '',
                            budgetedPrice: '',
                            standardPrice: '',
                            actualPrice: '',
                            valuePrice: '',
                             newRecord: 1,
                            isSubtraction:'0',
                            series: '',
                            xs: '',
                        })
                        //默认进入编辑状态

                         newData.forEach(item =>{
                              item.editable=true;
                        })
                        const target = newData.filter(item => length === 0 ? res.data.data : res.data.data  === item.id)[0];
                        if (target) {
                            target.editable = true;
                            this.optionalData = newData;
                        }
                        this.optionalPagination.total = parseInt(newData.length);
                        this.isBlurs=true;
                    })
            },
            blursBudgetedPrice(record){
                this.isBlurs=true;
                const newData = [...this.optionalData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    target.standardPrice =record.budgetedPrice;
                    this.optionalData = newData
                }
            },
            //更改价差
            blursValuePrice(){
                //当前页码的选装实际价格设置为0
                let optionalTotal =0;
                //当前页码的选型件总价差设置为0
                let lectotypeDifTotal =0;
                this.optionalData.forEach(item =>{
                    if(item.subType==='选型件'){
                        lectotypeDifTotal=this.NumberAdd(lectotypeDifTotal,item.valuePrice)
                    }
                    if(item.subType==='选装件'){
                        optionalTotal=this.NumberAdd(optionalTotal,item.actualPrice)
                    }
                })
                //选型选装小计
                let xxxzTotal=this.NumberAdd(lectotypeDifTotal,optionalTotal);
                //选型选装件小计 = 选型件价差+选装件实际售价
                this.$emit('optionTotal',xxxzTotal)
                //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
                this.$emit('chooseAmountToal',this.NumberAdd(xxxzTotal,this.basicSubtotal))
                //不允许跳转
                this.isJump2=false;
                this.isBlurs=true;
            },
            blursActualPrice(record){
                this.isBlurs=true;
                const newData = [...this.optionalData]
                newData.forEach(item =>{
                    item.editable=true;
                })
                const target = newData.find(item => record.id === item.id)
                //价差 =  实际价格-标准价格
                if (target) {
                    if(record.standardPrice!=null){
                        target.valuePrice =this.Subtr(record.actualPrice,record.standardPrice);
                    }
                    this.optionalData = newData
                }

                //当前页码的选装实际价格设置为0
                let optionalTotal =0;
                //当前页码的选型件总价差设置为0
                let lectotypeDifTotal =0;
                newData.forEach(item =>{
                    if(item.subType==='选型件'){
                        lectotypeDifTotal=this.NumberAdd(lectotypeDifTotal,item.valuePrice)
                    }
                    if(item.subType==='选装件'){
                        optionalTotal=this.NumberAdd(optionalTotal,item.actualPrice)
                    }
                })
                //选型选装小计
                let xxxzTotal=this.NumberAdd(lectotypeDifTotal,optionalTotal);
                //选型选装件小计 = 选型件价差+选装件实际售价
                this.$emit('optionTotal',xxxzTotal)
                //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
                this.$emit('chooseAmountToal',this.NumberAdd(xxxzTotal,this.basicSubtotal))
                //不允许跳转
                this.isJump2=false;
            },

            //重新计算()
            Recalculate(newData,noPartData){
                this.isBlurs=true;
                //当前页码的选装实际价格设置为0
                let optionalTotal =0;
                //当前页码的选型件总价差设置为0
                let lectotypeDifTotal =0;
                newData.forEach(item =>{
                    if(item.subType==='选型件'){
                        lectotypeDifTotal=this.NumberAdd(lectotypeDifTotal,item.valuePrice)
                    }
                    if(item.subType==='选装件'){
                        optionalTotal=this.NumberAdd(optionalTotal,item.actualPrice)
                    }
                })

                //选型减配小计设置为0
                let lectotypeAcualTotal =0;
                noPartData.forEach(item =>{
                    if(item.subType==='选型件'){
                        lectotypeAcualTotal=this.NumberAdd(lectotypeAcualTotal,item.actualPrice)
                    }
                })
                //选型选装小计
                let xxxzTotal=this.NumberAdd(lectotypeDifTotal,optionalTotal);
                //选型选装件小计 = 选型件价差+选装件实际售价
                this.$emit('optionTotal',xxxzTotal)
                //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
                this.$emit('chooseAmountToal',this.NumberAdd(xxxzTotal,lectotypeAcualTotal))
                //选型减配小计
                this.$emit('lectotypeSubtotal',lectotypeAcualTotal)
            },
               //取消
            cancel(record){
                this.isBlurs=true;
                let newData = [...this.optionalData]
                newData.forEach(item =>{
                    item.editable=true;
                });
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    let  that = this;
                    this.$confirm({
                        content: `是否取消？`,
                        onOk: () => {
                            // 新数据，直接删除
                            if (record.newRecord == 1) {
                                let newDatas = newData.filter(item => item.id !== target.id);

                                that.optionalData = newDatas
                                //重新计算值
                                that.Recalculate(newDatas,that.noPartsList);
                                return;
                            }
                            if(target.subType==="选型件"){
                                // that.noPartsList=that.$parent.$parent.$parent.$parent.$parent.getNoPart();
                                that.$emit('getNoParts');
                                //选型减配=1
                                target.isSubtraction="1";
                                // 减配还原标准价格
                                let standardPrice = target.standardPrice;
                                target.budgetedPrice = standardPrice;
                                target.actualPrice = standardPrice;
                                target.specs = target.standardSpecs;
                                target.valuePrice = null;
                                //指导售价不为空
                                if(target.budgetedPrice!=null){
                                    if(target.budgetedPrice!=0){
                                        target.budgetedPrice="-"+target.budgetedPrice;
                                    }
                                }
                                //实际售价不为空
                                if(target.actualPrice!=null){
                                    if(target.actualPrice!=0){
                                        target.actualPrice="-"+target.actualPrice;
                                    }
                                }
                                //在数组末端添加一组数据
                                that.noPartsList.push(target)
                                //传递list返回父组件
                                that.$emit('noPartsListNew',that.noPartsList)
                                // that.$parent.$parent.$parent.$parent.$parent.getChildrenPart();
                                //删除
                                const newDatas = newData.filter(item => item.id !== target.id)
                                that.optionalData = newDatas
                                //重新计算值
                                that.Recalculate(newDatas,that.noPartsList);
                            }else {
                                //直接删除
                                target['isDelete'] = 1;
                                this.delPartsList.push(target);
                                const newData1 = newData.filter(item => item.id !== target.id)
                                that.optionalData = newData1
                                that.$emit('getNoParts');
                                //重新计算值
                                that.Recalculate(newData1,that.noPartsList);
                            }
                            //更新total
                            that.optionalPagination.total = parseInt(that.Subtr(this.optionalPagination.total,"1"))


                        },
                        onCancel() {
                            target.isCheck = '1'
                        }
                    });
                }

            },
            // 将输入的内容与显示的内容进行匹配
            filterOption (value, option) {
                return option.componentOptions.children[0].text.indexOf(value) >= 0
            },
            /** 点击获取系列 **/
            onclickSeries(record) {
              //重新获取系列时清空规格、形式、供应商集合
              this.AllListData = []
              this.xs = []
              this.supplierBakListData = []
              this.isBlurs = true;
              //首次
              this.subType = record.subType
              this.partsName = record.partsName
              if (this.subType != this.subType2 || this.partsName != this.partsName2) {
                //点击当前获取相对应系列
                this.seriesData = []
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                  partsName: record.partsName,
                  year: this.year,
                  version: this.searchForm.dataType == '新版' ? 1 : ''
                }).then(res => {
                  if (res.data.data.length > 0) {
                    var temp = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                      if (res.data.data[i].series != null) {
                        if (temp.indexOf(res.data.data[i].series) == -1) {
                          temp.push(res.data.data[i].series);
                        }
                      }
                    }
                    this.seriesData = temp
                  } else {
                    this.seriesData = []
                  }
                  this.subType2 = record.subType
                  this.partsName2 = record.partsName
                })
              }
            },
            //点击获取规格
            onclickSpecs(record){
                //重新获取规格时清空供应商集合
                this.supplierBakListData = []

                this.isBlurs=true;
                    //点击当前获取相对应规格
                    this.AllListData = []
                    request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                        partsName: record.partsName,
                        year: this.year,
                        version: this.searchForm.dataType == '新版' ? 1 : ''
                    }).then(res => {
                        if (res.data.data.length > 0) {
                            /*  if(res.data.data[0].specs===null){
                                  this.AllListData=[]
                              }else{

                              }*/
                            var temp = [];
                            for (var i = 0; i < res.data.data.length; i++) {
                                if (res.data.data[i].specs != null) {
                                    if (temp.indexOf(res.data.data[i].specs) == -1) {
                                        temp.push(res.data.data[i].specs);
                                    }
                                }
                            }
                            this.AllListData = temp
                        } else {
                            this.AllListData = []
                        }
                        this.subType4=record.subType
                        this.partsName4=record.partsName

                    })
            },
            /** 点击获取形式 **/
            onclickXs(record) {
              //重新获取形式时清空供应商集合
              this.supplierBakListData = []
              this.isBlurs = true;
              //首次
              this.subType5 = record.subType
              this.partsName5 = record.partsName
              if (this.subType5 != this.subType6 || this.partsName5 != this.partsName6) {
                //点击当前获取相对应系列
                this.seriesData = []
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                  partsName: record.partsName,
                  year: this.year,
                  series: record.series,
                  specs: record.specs,
                  version: this.searchForm.dataType == '新版' ? 1 : ''
                }).then(res => {
                  if (res.data.data.length > 0) {
                    var temp = [];
                    for (var i = 0; i < res.data.data.length; i++) {
                      if (res.data.data[i].xs != null) {
                        if (temp.indexOf(res.data.data[i].xs) == -1) {
                          temp.push(res.data.data[i].xs);
                        }
                      }
                    }
                    this.xsData = temp
                  } else {
                    this.xsData = []
                  }
                  this.subType6 = record.subType
                  this.partsName6 = record.partsName
                })
              }
            },
            //点击获取供应商
            onclickSupplierBak(record){

                this.isBlurs=true;
                //首次
                this.subType7=record.subType
                this.partsName7=record.partsName
                if(this.subType7!=this.subType8||this.partsName7!=this.partsName8) {
                    this.supplierBakListData = []
                    //点击当前获取相对应供应商
                    request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                        partsName: record.partsName,
                        specs: record.specs,
                        year: this.year,
                        version: this.searchForm.dataType == '新版' ? 1 : ''
                    }).then(res => {

                        if (res.data.data.length === 1) {
                            if (res.data.data[0].supplier === null) {
                                this.supplierBakListData = []
                            } else {
                                this.supplierBakListData = res.data.data
                            }
                        } else  if(res.data.data.length>1){
                            this.supplierBakListData = []
                            res.data.data.forEach(item=>{

                                if(item.supplier!=null){
                                    this.supplierBakListData.push(item)
                                }
                            })
                        }
                        this.subType8=record.subType
                        this.partsName8=record.partsName

                    })
                }
            },
            //零部件点击事件
            onclickPartsName(){
                if(this.searchForm.machineType==null){
                    this.$message.info('请先输入新增状态机！')
                    return
                }
                if(this.searchForm.plate==null){
                    this.$message.info('请先选择板块！')
                    return
                }
                if(this.isJump){
                    this.initDataList()
                    this.isJump=false;
                    this.isBlurs=true;
                }

            },
            handleChooseSubTypeChange(record){
                this.isBlurs=true;
                //清空后面
                const newData = [...this.optionalData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    target.partsName=''
                    target.specs = ''
                    target.standardSpecs= ''
                    target.supplierBak = ''
                    target.budgetedPrice = ''
                    target.actualPrice =''
                    target.standardPrice =''
                    target.valuePrice =''
                    this.optionalData = newData
                }

            },
            /** 选择系列 **/
            handleChooseSeriesChange(record) {
              this.isBlurs = true;
              const newData = [...this.optionalData]
              const target = newData.find(item => record.id === item.id)
              if (target) {
                target.specs = ''
                target.standardSpecs = ''
                target.supplierBak = ''
                target.budgetedPrice = ''
                target.actualPrice = ''
                target.standardPrice = ''
                target.valuePrice = ''
                target.xs = ''
                this.optionalData = newData
              }
            },
            //选规格
            handleChoosePartChange(record){
                this.isBlurs=true;
                const newData = [...this.optionalData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    target.specs = ''
                    target.standardSpecs= ''
                    target.supplierBak = ''
                    target.budgetedPrice = ''
                    target.actualPrice =''
                    target.standardPrice =''
                    target.valuePrice =''
                    target.series = ''
                    target.xs = ''
                    this.optionalData = newData
                }
                //临时标准规格list
                if (record.subType == '选型件') {
                    //根据当前选型配件去本体查询标准规格
                    request(LIST_XSGSPARTSSETTING, METHOD.POST, {
                        platformName: this.searchForm.plate,
                        // blowoff:this.searchForm.blowoff,
                        series:this.searchForm.series,
                        productModel: this.searchForm.productModelJh,
                        partsName: target.partsName,
                    }).then(result => {

                        if (result.data.data.length > 0) {
                            //标准规格
                            target.specs = result.data.data[0].specs;
                            target.standardSpecs = result.data.data[0].specs;
                            target.series = result.data.data[0].series;
                            target.xs = result.data.data[0].xs;
                            target.supplierBak = result.data.data[0].supplierBak;
                            //大于0说明有标准规格，
                                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                                        partsName:record.partsName,
                                        specs:target.specs,
                                        year:this.year,
                                        series: target.series,
                                        xs: target.xs,
                                        supplierBak: target.supplierBak,
                                        version: this.searchForm.dataType == '新版' ? 1 : ''
                                    }).then(result1 =>  {

                                        if(result1.data.data.length>0){
                                            target.budgetedPrice = result1.data.data[0].actualPrice
                                            target.actualPrice = result1.data.data[0].actualPrice
                                            target.standardPrice = result1.data.data[0].actualPrice
                                            target.valuePrice =this.Subtr(target.actualPrice ,target.standardPrice);
                                            this.blursActualPrice(target);
                                        }else{
                                            target.budgetedPrice = 0
                                            target.actualPrice = 0
                                            target.standardPrice = 0
                                            target.valuePrice =this.Subtr(target.actualPrice ,target.standardPrice);
                                            this.blursActualPrice(target);
                                        }

                                })
                        }else{
                            //没有标准规格
                            request(LIST_XSGSPARTSPRICE, METHOD.POST, {partsName:record.partsName,year:this.year,version: this.searchForm.dataType == '新版' ? 1 : ''}).then(res =>  {
                                if(res.data.data.length===1&&res.data.data[0].specs===null&&res.data.data[0].supplier===null){
                                    target.budgetedPrice = res.data.data[0].actualPrice
                                    target.actualPrice = res.data.data[0].actualPrice
                                    target.standardPrice = res.data.data[0].actualPrice
                                    target.valuePrice =this.Subtr(target.actualPrice ,target.standardPrice);
                                    this.blursActualPrice(target);
                                }else if(res.data.data.length===0){
                                    target.budgetedPrice = 0
                                    target.actualPrice = 0
                                    target.standardPrice = 0
                                    target.valuePrice =this.Subtr(target.actualPrice ,target.standardPrice);
                                    this.blursActualPrice(target);
                                }
                            })
                        }

                        this.$emit('deleteDoubleXx',target)
                    })
                }else if (record.subType == '选装件') {
                    // 先检查是否是之前删除的数据
                    let delData = this.delPartsList.filter(s=>s.partsName == target.partsName);
                    if (!this.isEmpty(delData)) {
                        let item = delData[0];
                        target['sysId']=item.sysId;
                        target['sysBudgetedPrice']=item.sysBudgetedPrice;
                        target['sysActualPrice']=item.sysActualPrice;
                        target['sysValuePrice']=item.sysValuePrice;
                        target['sysStandardPrice']=item.sysStandardPrice;
                        target['sysStandardSpecs']=item.sysStandardSpecs;
                        target['sysSpecs']=item.sysSpecs;
                        target['sysSupplierBak']=item.sysSupplierBak;
                        target['sysPartsName']=item.sysPartsName;
                        target['isDelete']=0;
                    }
                    request(LIST_XSGSPARTSPRICE, METHOD.POST, {partsName:target.partsName,year:this.year,version: this.searchForm.dataType == '新版' ? 1 : ''}).then(res =>  {
                        if(res.data.data.length===1&&res.data.data[0].specs===null&&res.data.data[0].supplier===null){
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            this.blursActualPrice(target);
                        }else if(res.data.data.length===0){
                            target.budgetedPrice = 0
                            target.actualPrice = 0
                            target.standardPrice = 0
                            this.blursActualPrice(target);
                        }
                    })

                }
            },
            /** 选择系列 **/
            handleChooseXsChange(record) {
              this.isBlurs = true;
              const newData = [...this.optionalData]
              const target = newData.find(item => record.id === item.id)
              if (target) {
                target.standardSpecs = ''
                target.supplierBak = ''
                target.budgetedPrice = ''
                target.actualPrice = ''
                target.standardPrice = ''
                target.valuePrice = ''
                this.optionalData = newData
              }
            },
            //选供应商
            handleChooseSpecsChange(record){
                this.isBlurs=true;
                const newData = [...this.optionalData]
                const target = newData.find(item => record.id === item.id)
                if (target) {
                    target.supplierBak = ''
                    target.budgetedPrice = ''
                    target.actualPrice =''
                    target.valuePrice =''
                    this.optionalData = newData;
                }
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                    partsName:target.partsName,
                    specs:target.specs,
                    year:this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : ''
                }).then(res =>  {
                    if(res.data.data.length>0){
                        if(res.data.data.length===1&&res.data.data[0].supplier===null){
                            if (target.subType == '选装件') {
                                if (target) {
                                    target.budgetedPrice = res.data.data[0].actualPrice
                                    target.actualPrice = res.data.data[0].actualPrice
                                    this.blursActualPrice(target);
                                }
                            } else if (target.subType == '选型件') {
                                if (target) {
                                    target.budgetedPrice = res.data.data[0].actualPrice
                                    target.actualPrice = res.data.data[0].actualPrice
                                    if(target.standardPrice===''&& target.masterId===null) {
                                        target.standardPrice = res.data.data[0].actualPrice;
                                    }
                                    this.blursActualPrice(target);
                                }
                            }
                        }
                        else if(res.data.data.length>1){
                            if (target.subType == '选装件') {
                                if (target) {
                                    res.data.data.forEach(item=>{
                                        if(item.supplier==null){
                                            target.budgetedPrice = item.actualPrice
                                            target.actualPrice = item.actualPrice
                                        }
                                    })
                                    this.blursActualPrice(target);
                                }
                            } else if (target.subType == '选型件') {
                                if (target) {
                                    res.data.data.forEach(item=>{
                                        if(item.supplier==null){
                                            target.budgetedPrice = item.actualPrice
                                            target.actualPrice = item.actualPrice
                                            if(target.standardPrice===''&& target.masterId===null) {
                                                target.standardPrice = item.actualPrice;
                                            }
                                        }
                                    })

                                    this.blursActualPrice(target);
                                }
                            }
                        }
                    }

                    //重新选取规格时供应商重新读取
                    this.subType4=null
                    this.partsName4=null
                })
            },
            //获取价格
            handleChooseSupplierBakChange(record){
                this.isBlurs=true;
                request(LIST_XSGSPARTSPRICE, METHOD.POST, {
                    partsName:record.partsName,
                    specs:record.specs,
                    supplier:record.supplierBak,
                    year:this.year,
                    version: this.searchForm.dataType == '新版' ? 1 : ''
                }).then(res =>  {

                    if(res.data.data.length===1){
                        const newData = [...this.optionalData]
                        const target = newData.find(item => record.id === item.id)
                        this.optionalData = newData;
                        if(target.subType =='选装件') {
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            this.blursActualPrice(target);
                        }else if(target.subType =='选型件'){
                            target.budgetedPrice = res.data.data[0].actualPrice
                            target.actualPrice = res.data.data[0].actualPrice
                            if(target.standardPrice===''&& target.masterId===null) {
                                target.standardPrice = res.data.data[0].actualPrice;
                            }
                            this.blursActualPrice(target);
                        }
                    }

                })
            },
            resetId(){
                this.optionalData.forEach(item =>{
                    request(getUuid, METHOD.GET).then(res => {
                        item.id=res.data.data
                    })
                    item.masterId=null
                })
            },
            setNoparts(noPartsList) {
                this.noPartsList = noPartsList;
            }
        }
    }
</script>

<style scoped>
    /deep/ .quotePartsList input[disabled=disabled],textarea[disabled=disabled],.ant-select-disabled {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;;
    }
    /deep/ .quotePartsList .disabelTd {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;
    }
    /deep/ .quotePartsListDisabeld td {
        width: 100%;
        height: 100%;
        background-color: #f5f5f5 !important;
    }
/*/deep/.ant-table-thead >tr >th{*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/

/*/deep/.ant-table-tbody >tr >td{*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/

/*/deep/.ant-form >table >tbody >tr >td {*/
/*  border: 1px solid rgb(240, 240, 240);*/
/*}*/

</style>
