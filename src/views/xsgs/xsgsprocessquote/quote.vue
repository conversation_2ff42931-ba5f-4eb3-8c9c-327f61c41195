<template>
    <div>
        <a-card>
            <a-spin :spinning="loading">
                <a-form-model class="quoteInfo" ref="searchForm" :model="searchForm" :rules="searchRules" :labelCol="{span: 8}"
                              :wrapperCol="{span: 16}">

                    <table style="width: 100%; border: 1px solid #f0f0f0;">
                        <tbody class="ant-table">
                        <tr>
                            <td>
                                <a-form-model-item label="年份" prop="year"/>
                            </td>
                            <td width="120px">
                                <a-input-number v-if="!isView" :min="0" step="1" style="width: 100%" v-model="searchForm.year"
                                               />
                                <span v-if="isView">{{searchForm.year}}</span>
                            </td>
                            <td style="width: 90px">
                                <a-form-model-item label="板块" prop="plate"/>
                            </td>
                            <td width="120px">
                                <a-select v-if="!isView" style="width: 100%" v-model="searchForm.plate" @blur="platechanged">
                                    <a-select-option value="卡车">卡车</a-select-option>
                                    <a-select-option value="客车">客车</a-select-option>
                                    <a-select-option value="新能源">新能源</a-select-option>
                                </a-select>
                                <span v-if="isView">{{searchForm.plate}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="客户" prop="customer"/>
                            </td>
                            <td width="120px">
                                <span >{{searchForm.customer}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="客户名称" prop="customerName"/>
                            </td>
                            <td colspan="3">
                                <span>{{searchForm.customerName}}</span>
                            </td>
                        </tr>

                        <tr>
                            <td colspan="10" style="text-align: center">
                                <strong>基本信息</strong>
                            </td>
                        </tr>
                        <tr v-if="quoteType=='REF'">
                            <td>
                                <a-form-model-item label="参考状态机" prop="refMachineType"/>
                            </td>
                            <td>
                                <a-input  v-if="!isView" v-model.trim="searchForm.refMachineType" @blur="blurs"/>
                                <span v-if="isView">{{searchForm.refMachineType}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="整机开票价" prop="unitAmountRefer"/>
                            </td>
                            <td>
                                <a-input-number v-model="searchForm.refUnitAmount"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                />
                                <span v-if="isView">{{searchForm.refUnitAmount}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="基本型开票价" prop="basicAmountRefer"/>
                            </td>
                            <td>
                                <a-input-number v-model="searchForm.refBasicAmount"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                />
                                <span v-if="isView">{{searchForm.refBasicAmount}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="产品型号" prop="refProductModel"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" style="width: 120px" v-model="searchForm.refProductModel"/>
                                <span v-if="isView">{{searchForm.refProductModel}}</span>
                            </td>
                        </tr>

                        <tr v-if="quoteType=='REF'">
                            <td colspan="8">
                                <a-divider></a-divider>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <a-form-model-item label="新增状态机" prop="machineType"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model.trim="searchForm.machineType" @blur="blursMachineType"/>
                                <span v-if="isView">{{searchForm.machineType}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="基本型指导价" prop="basicGuidanceAmount"/>
                            </td>
                            <td>
                                <a-input-number v-model="searchForm.basicGuidanceAmount" disabled
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                />
                                <span v-if="isView">{{searchForm.basicGuidanceAmount}}</span>
                            </td>
                            <td>
                                <a-checkbox style="margin-left: 5px;color: rgba(0, 0, 0, 0.85);" :disabled="isView" :checked="artificialVer"   @change="onAtificialVerChange" >人工定版</a-checkbox>
                            </td>
                            <td></td>
                            <td>
                              <a-form-model-item label="数据版本" prop="dataType"/>
                            </td>
                            <td>
                              <a-select v-if="!isView" style="width: 50%" v-model="searchForm.dataType" @blur="dataTypeChanged">
                                <a-select-option value="旧版">旧版</a-select-option>
                                <a-select-option value="新版">新版</a-select-option>
                              </a-select>
                              <span v-if="isView">{{searchForm.dataType}}</span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <a-form-model-item label="排放" prop="blowoff"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.blowoff" @blur="blowoffchanged"/>
                                <span v-if="isView">{{searchForm.blowoff}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="系列" prop="series"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.series" @blur="serieschanged"/>
                                <span v-if="isView">{{searchForm.series}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="产品型号" prop="productModel"/>
                                <a-input :disabled="isView" v-show="false" v-model="searchForm.productModelJh"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.productModel" @blur="productModelchanged"/>
                                <span v-if="isView">{{searchForm.productModel}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="功率(PS)" prop="ps"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" style="width: 120px" v-model="searchForm.ps"/>
                                <span v-if="isView">{{searchForm.ps}}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <!-- <a-form-model-item label="品系1" prop="type"/> -->
                                <a-form-model-item label="BU业务" prop="businessUnit"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.businessUnit"/>
                                <span v-if="isView">{{searchForm.businessUnit}}</span>
                            </td>
                            <td>
                                <!-- <a-form-model-item label="品系2" prop="strain1"/> -->
                                <a-form-model-item label="品系" prop="productLine "/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.productLine"/>
                                <span v-if="isView">{{searchForm.productLine}}</span>
                            </td>
                            <td>
                                <!-- <a-form-model-item label="品系3" prop="strain2"/> -->
                                <a-form-model-item label="新场景1" prop="newScenario1"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" v-model="searchForm.newScenario1"/>
                                <span v-if="isView">{{searchForm.newScenario1}}</span>
                            </td>
                            <td>
                                <!-- <a-form-model-item label="品系4" prop="strain3"/> -->
                                <a-form-model-item label="新场景2" prop="newScenario2"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" style="width: 120px" v-model="searchForm.newScenario2"/>
                                <span v-if="isView">{{searchForm.newScenario2}}</span>
                            </td>
                            <td>
                                <!-- <a-form-model-item label="品系4" prop="strain3"/> -->
                                <a-form-model-item label="新场景3" prop="newScenario3"/>
                            </td>
                            <td>
                                <a-input v-if="!isView" style="width: 120px" v-model="searchForm.newScenario3"/>
                                <span v-if="isView">{{searchForm.newScenario3}}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <a-form-model-item label="基本型开票价" prop="basicAmount"/>
                            </td>
                            <td>
                                <a-input-number v-model="searchForm.basicAmount"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                                @blur="basicAmountBlurs"
                                />
                                <span v-if="isView">{{searchForm.basicAmount}}</span>
                            </td>
                            <td style="width: 120px">
                                <a-checkbox  :checked="checked" :disabled="isView"  @change="onCheckChange" >特</a-checkbox>
                                <span v-if="quoteType=='REF'" style="float: right;">功率差：</span>
                            </td>
                            <td v-if="quoteType=='REF'">
                                <a-input-number v-model="searchForm.powerDiff"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                                @blur="powerDiffBlurs"
                                />
                                <span v-if="isView">{{searchForm.powerDiff}}</span>
                            </td>
                            <td>
                                <a-form-model-item label="选配件合计" prop="chooseAmount"/>
                            </td>
                            <td>
                                <a-input-number v-model="searchForm.chooseAmount"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                                @blur="chooseAmountBlurs"
                                />
                                <span v-if="isView">{{searchForm.chooseAmount}}</span>
                            <td>
                                <a-form-model-item label="整机开票价" prop="unitAmount"/>
                            </td>
                            <td colspan="3">
                                <a-input-number v-model="searchForm.unitAmount"
                                                :step="0.01"
                                                v-if="!isView"
                                                style="width: 120px"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                                @blur="unitAmountBlurs"
                                />
                                <span v-if="isView">{{searchForm.unitAmount}}</span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <a-form-model-item label="选型选装件小计" prop="optionSubtotal"/>
                            </td>
                            <td colspan="2">
                                <a-input-number v-model="searchForm.optionSubtotal"
                                                @blur="optionSubtotalBlurs"
                                                :step="0.01"
                                                v-if="!isView"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
                                <span v-if="isView">{{searchForm.optionSubtotal}}</span>

                            <td colspan="7">
                                <a-button type="primary" style="margin: 0 8px" @click="handleBasicView">
                                    <a-icon type="eye"/>
                                    查看基本型清单
                                </a-button>
                            </td>
                        </tr>


                        </tbody>
                    </table>


                    <a-row v-if="!isView" style="margin: 24px;">
                        <a-col :span="24">
                            <a-form-model-item>
                                <a-button :disabled="loading" :loading="loading" type="primary" style="margin: 0 8px"
                                          @click="handleSave">
                                    <a-icon type="save"/>
                                    保存
                                </a-button>
                            </a-form-model-item>
                        </a-col>
                    </a-row>
                    <a-row>
                        <a-col :span="12">

                        </a-col>
                        <a-col :span="12">

                        </a-col>
                    </a-row>

                    <a-card title="选型选装清单">
                        <a-row>
                            <editOptional :masterId="masterId" :basicSubtotal="searchForm.basicSubtotal"
                                          :searchForm="searchForm"
                                          :isView="isView"
                                          :year="searchForm.year" @searchUpdate="getSearchUpdate"
                                          @chooseAmountToal="getChooseAmoutToal"
                                          @lectotypeSubtotal="getlectotypeSubtotal"
                                          @optionTotal='getOptionTotal'
                                          @deleteDoubleXx='deleteDoubleXx'
                                          @noPartsListNew="setNoPartsListNew"
                                          @getNoParts="getNoParts"
                                          :optionList="optionList"
                                          :productModelJh="searchForm.productModelJh" ref="editOptional"></editOptional>
                        </a-row>
                    </a-card>
                    <a-card title="选型减配小计">
                        <tr slot="extra" style="float: left">
                            <td colspan="3" class="tdnumber">
                                <a-input-number v-model="searchForm.basicSubtotal" disabled
                                                :step="0.01"
                                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"/>
                            </td>
                        </tr>
                        <a-row>
                            <selectNoOptional :masterId="masterId" :searchForm="searchForm"
                                              :optionSubtotal="searchForm.optionSubtotal"
                                              :year="searchForm.year"
                                              :isView="isView"
                                              @searchNoOptionUpdate="getSearchNoOptionUpdate"
                                              :productModelJh="searchForm.productModelJh"
                                              @chooseBasicAmountToal="getchooseBasicAmountToal"
                                              @lectotypeSubtotal="getlectotypeSubtotal"
                                              ref="selectNoOptional"></selectNoOptional>
                        </a-row>
                    </a-card>
                    <table style="border: 1px solid #f0f0f0; width: 100%">
                        <tbody class="ant-table-tbody">
                        <tr>
                            <td style="width: 10%"><a-form-model-item label="备注信息" prop="remarks"/></td>
                            <td colspan="9"><a-input type="textarea" v-if="!isView" :autoSize="{minRows: 3 }" aria-placeholder="请输入备注信息" v-model="searchForm.remark1"/></td>
                        </tr>
                        </tbody>
                    </table>
                </a-form-model>
            </a-spin>
        </a-card>
        <a-modal
                :title="'查看基本型清单'"
                :visible="lookBasicDialog"
                :confirm-loading="confirmLoading"
                @ok="handlelookBasicSubmit"
                @cancel="closelookBasicrForm">
            <a-form-model>
                <selectOptionList :childOption="childOption" :masterId="masterId"
                                  ref="selectOptionList"></selectOptionList>
            </a-form-model>
        </a-modal>
    </div>
</template>

<script>
    import {hasAuth} from "@/utils/authority-utils";
    import {
        GET_XSGSPROCESSQUOTE_ID,
        SElECTLIST_XSGSALLOWANCEMAIN,
        SELECTOPTIONLIST_XSGSALLOWANCEMAIN,
        LIST_XSGSBASICPRICE,
        ztjxx_action,
        LIST_XSGSCUSTOMER,
        LIST_SAVETOCACHE,
        LIST_GETFROMCACHE,
        CHECK_STATE_CODE,
        getUuid
    } from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    import Cookie from 'js-cookie'
    import ARow from "ant-design-vue/es/grid/Row";
    import selectOptionList from "./select_optionList";
    import editOptional from "./edit_optional";
    import selectNoOptional from "./select_noOptional";
    import {b64_md5} from "@/utils/md5";
    import $ from 'jquery';

    export default {
        name: "xsgsallowancemain.vue",
        components: {
            ARow,
            editOptional,
            selectOptionList,
            selectNoOptional,
        },
        data() {
            return {
                hasAuth,
                orgBackground: "",
                Cookie,
                masterId: null,
                loading: false,
                machineType: null,
                lookBasicDialog: false,
                confirmLoading: false,
                fullPath: null,
                parentPath:null,
                artificialVer:false,
                subType: ['标准选型', '选型'],
                key: "1",
                quoteStoreId:null,
                option:null,
                quoteType:null,
                id:null,
                isView:false,
                checked:false,
                customer:null,
                searchForm: {
                    quoteStoreId:null,
                    quoteType:null,
                    option:null,
                    customer: null,
                    customerName: null,
                    year: null,
                    refMachineType: null,
                    machineType: null,
                    productModel: null,
                    productModelJh: null,
                    blowoff: null,
                    plate: null,
                    series: null,
                    basicAmount: null,
                    powerDiff:null,
                    isCheck: 0,
                    hasChange:null,
                    chooseAmount: null,
                    optionSubtotal: null,
                    basicSubtotal: null,
                    unitAmount: null,
                    createTime: null,
                    updateTime: null,

                    refUnitAmount: null,
                    refBasicAmount: null,
                    refProductModel: null,
                    refProductModelJh: null,
                    refBlowoff: null,
                    refPlate: null,
                    refSeries: null,
                    refPs: null,
                    refType: null,
                    refStrain1: null,
                    refStrain2: null,
                    refStrain3: null,
                    basicGuidanceAmount: null,
                    isArtificialVer: null,
                    strain1: null,
                    strain2: null,
                    strain3: null,
                    materialNo: null,
                    productLine:null,
                    businessUnit:null,
                    newScenario1:null,
                    newScenario2:null,
                    newScenario3:null,
                    dataType: null

                },
                searchPartForm: {
                    masterId: null,
                },
                noContainForm: {
                    masterId: null,
                },
                //子组件传值
                childOption: [],
                list: [],
                //选型选装清单
                optionList: [],
                //选型减配清单
                noPartsList: [],
                optionXXList: [],
                searchSettingForm: {},
                form: {},
                searchFormAll: {},
                settingForm: {
                    temId: null,
                    masterId: null,
                    year: null,
                    productModelJh: null,
                },

                basicAmount: null,
                data: [],
                customerList: [],
                searchRules: {
                    customer: [{required: true, message: '客户不能为空', trigger: 'blur'}],
                    customerName: [{required: true, message: '客户名称不能为空', trigger: 'blur'}],
                    year: [{required: true, message: '年度不能为空', trigger: 'blur'}],
                    refMachineType: [{required: false, message: '参考机型不能为空', trigger: 'blur'}],
                    machineType: [{required: true, message: '新增状态机型不能为空', trigger: 'blur'}],
                    productModel: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
                    productModelJh: [{required: true, message: '产品型号不能为空', trigger: 'blur'}],
                    blowoff: [{required: true, message: '排放不能为空', trigger: 'blur'}],
                    plate: [{required: true, message: '板块不能为空', trigger: 'blur'}],
                    series: [{required: true, message: '系列不能为空', trigger: 'blur'}],
                    type: [{required: false, message: '品系不能为空', trigger: 'blur'}],
                    ps: [{required: false, message: '功率(PS)不能为空', trigger: 'blur'}],
                    basicAmount: [{required: true, message: '基本型开票价不能为空', trigger: 'blur'}],
                    chooseAmount: [{required: true, message: '选配件合计不能为空', trigger: 'blur'}],
                    unitAmount: [{required: true, message: '整机开票价不能为空', trigger: 'blur'}],
                    optionSubtotal: [{required: false, message: '选型选装件小计不能为空', trigger: 'blur'}],
                    basicSubtotal: [{required: false, message: '基本型减配小计不能为空', trigger: 'blur'}],
                },

                itemRules: {
                    masterId: [{required: false, message: '价格主表ID不能为空', trigger: 'blur'}],
                    code: [{required: false, message: '核算单号不能为空', trigger: 'blur'}],
                    year: [{required: false, message: '年度不能为空', trigger: 'blur'}],
                    contract: [{required: false, message: '合同号不能为空', trigger: 'blur'}],
                    platformName: [{required: false, message: '状态机不能为空', trigger: 'blur'}],
                    yearExpectVolume: [{required: false, message: '年度销售预计不能为空', trigger: 'blur'}],
                    yearSalesVolume: [{required: false, message: '年度实际销售不能为空', trigger: 'blur'}],
                    basicTicketPrice: [{required: false, message: '基本型开票价不能为空', trigger: 'blur'}],
                    unitTicketPrice: [{required: false, message: '整机开票价不能为空', trigger: 'blur'}],
                    basicNetPrice: [{required: false, message: '基本型净价不能为空', trigger: 'blur'}],
                    unitNetPrice: [{required: false, message: '整机净价不能为空', trigger: 'blur'}],
                    basicBudgetPrice: [{required: false, message: '基本型预算不能为空', trigger: 'blur'}],
                    zbChooseBudget: [{required: false, message: '总部-选配件预算不能为空', trigger: 'blur'}],
                    zbBudgetActualPrice: [{required: false, message: '总部-整机预算净价不能为空', trigger: 'blur'}],
                    zbBudgetSpace: [{required: false, message: '总部-预算空间不能为空', trigger: 'blur'}],
                    zbBasicBalance: [{required: false, message: '总部-基本型预算结余不能为空', trigger: 'blur'}],
                    zbUnitBalance: [{required: false, message: '总部-整机预算结余不能为空', trigger: 'blur'}],
                    zbBasicBudgetBalance: [{required: false, message: '总部-基本型预算不能为空', trigger: 'blur'}],
                    sysChooseBudget: [{required: false, message: '系统-选配件预算不能为空', trigger: 'blur'}],
                    sysBudgetActualPrice: [{required: false, message: '系统-整机预算净价不能为空', trigger: 'blur'}],
                    sysBudgetSapce: [{required: false, message: '系统-预算空间不能为空', trigger: 'blur'}],
                    sysBasicBalance: [{required: false, message: '系统-基本型预算结余不能为空', trigger: 'blur'}],
                    sysUnitBalance: [{required: false, message: '系统-整机预算结余不能为空', trigger: 'blur'}],
                    costOfMeterial: [{required: false, message: '材料成本不能为空', trigger: 'blur'}],
                    contribution: [{required: false, message: '边际贡献不能为空', trigger: 'blur'}],
                    contributionPercent: [{required: false, message: '边际贡献率不能为空', trigger: 'blur'}],
                    sequenceNumber: [{required: false, message: '序号不能为空', trigger: 'blur'}],
                    settlementPrice: [{required: false, message: '结算价不能为空', trigger: 'blur'}],
                    balanceAmount: [{required: false, message: '折让金额不能为空', trigger: 'blur'}],
                    createUser: [{required: false, message: '创建人不能为空', trigger: 'blur'}],
                    createTime: [{required: false, message: '创建时间不能为空', trigger: 'blur'}],
                    updateUser: [{required: false, message: '更新人不能为空', trigger: 'blur'}],
                    updateTime: [{required: false, message: '更新时间不能为空', trigger: 'blur'}],
                },
                childOptionalData: [],
                textarea: ''

            }
        },

        mounted() {
            this.loading = false;
            this.quoteType = this.$route.query.quoteType;
            this.option = this.$route.query.option;
            this.quoteStoreId = this.$route.query.quoteStoreId;
            this.parentPath = this.$route.query.parentPath;
            this.fullPath = this.$route.fullPath;
            this.customer = this.$route.query.customer;
            this.searchForm.customer = this.$route.query.customer;
            this.isView = this.$route.query.option == 3;
            this.searchForm.dataType = this.searchForm.dataType !== "新版" ? "旧版" : "新版"
            if (":id" === this.$route.params.id) {
                request(getUuid, METHOD.GET).then(res => {
                    this.masterId = res.data.data
                    this.id = res.data.data;
                });
                this.searchForm.id = null;
                this.searchForm.masterId = null;
                this.$setPageTitle(this.fullPath, "新建报价");
            } else {
                this.id = this.$route.params.id
                this.masterId = this.$route.params.id
                this.searchForm.id = this.$route.params.id;
                this.getMainData(this.searchForm.id,this.quoteStoreId);
                this.$setPageTitle(this.fullPath, "编辑报价");
            }
            if (this.isView) {
                this.$setPageTitle(this.fullPath, "查看报价");
            }
            this.getCustomercName();
            this.created();
            this.checkName();
        },
        watch: {
          "searchForm.dataType": {
            deep: true,
            handler(val) {
              if(val == undefined || val == null){
                this.searchForm.dataType = '旧版'
              }
            }
          }
        },
        methods: {
            platechanged() {
                this.checkField('plate');
            },
            getCustomercName() {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {
                    customer: this.customer
                }).then(res => {
                    if (res.data.data.length > 0) {
                        this.searchForm.customerName = res.data.data[0].name
                    } else {
                        this.$message.info('此客户号没找到信息')
                    }
                })

            },
            blowoffchanged() {
                this.checkField('blowoff');
            },
            serieschanged() {
                this.checkField('series');
            },
            productModelchanged() {
                this.checkField("productModel");
            },
            unitAmountBlurs() {
                this.checkField("unitAmount");
            },
            dataTypeChanged(){
              this.checkField("dataType");
            },

            onCheckChange(e) {
                this.checked = e.target.checked;
                if (e.target.checked) {
                    this.searchForm.isCheck = 1;
                } else {
                    this.searchForm.isCheck = 0;
                }
            },

            //获取当前年份
            created: function () {
                var aData = new Date();
                this.searchForm.year = Number(this.$moment(aData).format('YYYY'));
            },
            getMainData: function (id,quoteStoreId) {
                this.loading = true;
                // 先从缓存拿，拿不到从数据库拿
                request(LIST_GETFROMCACHE, METHOD.GET, {id: id, quoteStoreId:quoteStoreId})
                    .then(res => {
                        if (res.data.data != null) {
                            this.searchForm = res.data.data;
                            if (this.searchForm.isCheck == 1) {
                                this.checked = true;
                            } else {
                                this.checked = false;
                            }
                            if(res.data.data.isArtificialVer===1){
                                this.artificialVer =true
                            }else{
                                this.artificialVer =false
                            }
                            let partsList = res.data.data.partsList;
                            //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                            this.$nextTick(() => {
                                let noPartsList = partsList.filter(item=>item.isSubtraction == '1');
                                this.optionList = partsList.filter(item=>!item.isSubtraction || item.isSubtraction == '0');
                                this.$refs.selectNoOptional.setNoOptionalcDataList(noPartsList);
                                this.$refs.editOptional.getoptionList(this.optionList);
                            });
                            this.loading = false;
                        } else {
                            request(GET_XSGSPROCESSQUOTE_ID, METHOD.GET, {id: id})
                                .then(res => {
                                    this.loading = false;
                                    if (res.data.data != null) {
                                        this.searchForm = res.data.data
                                        this.searchForm.id = this.id;
                                        if (this.searchForm.isCheck == 1) {
                                            this.checked = true;
                                        } else {
                                            this.checked = false;
                                        }
                                        let partsList = res.data.data.partsList;
                                        //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                                        this.$nextTick(() => {
                                            let noPartsList = partsList.filter(item=>item.isSubtraction == '1');
                                            this.optionList = partsList.filter(item=>!item.isSubtraction || item.isSubtraction == '0');
                                            this.$refs.selectNoOptional.setNoOptionalcDataList(noPartsList);
                                            this.$refs.editOptional.getoptionList(this.optionList);
                                        })
                                    }

                                })
                        }
                    });

            },
            handleSaveAction() {
                let partsList = this.$refs.editOptional.getOptionalcDataList();
                let noPartsList = this.$refs.selectNoOptional.getNoOptionalcDataList();
                if (!this.isEmpty(noPartsList)) {
                    noPartsList.forEach(item=>{
                        partsList.push(item);
                    })
                }

                let delPartsList = this.$refs.editOptional.getOptionalcDelDataList();
                if (!this.isEmpty(delPartsList)) {
                    delPartsList.forEach(item=>{
                        partsList.push(item);
                    })
                }

                this.searchForm.partsList = partsList;
                this.searchForm.quoteStoreId = this.quoteStoreId;
                this.searchForm.option = this.option;
                this.searchForm.quoteType = this.quoteType;
                this.searchForm.hasChange = 1;
                this.searchForm.hasChangeParts = 1;
                if (!this.searchForm.id) {
                    this.searchForm.id = this.id;
                }
                let that = this;
                this.loading = true;
                // 保存到redis
                request(LIST_SAVETOCACHE, METHOD.POST, this.searchForm)
                    .then(res => {
                        this.loading = false;
                        if (res.data.code == 0) {
                            let storeQuotes = this.$store.state.account.quotes;
                            if (!storeQuotes) {
                                storeQuotes = [];
                            }
                            // 检查已否已经存在，存在则更新
                            // 删除原有的
                            if (that.searchForm.id) {
                                let newData = storeQuotes.filter(s=>s.id != that.searchForm.id && s.quoteStoreId != that.searchForm.quoteStoreId);
                                storeQuotes = newData;
                            }
                            storeQuotes.push(this.searchForm);
                            that.$store.commit("account/setQuotes",storeQuotes);
                            that.$closePage(this.fullPath,this.parentPath);
                        }
                    })
            },
            //保存
            handleSave() {
                var that =this
                if (!this.checkCustomer()) {
                    return;
                }
                this.$refs.searchForm.validate(valid => {
                    if (valid) {
                        that.$confirm({
                            content: `是否保存当前数据？`,
                            onOk: () => {
                                that.handleSaveAction()
                            },
                        });
                    }
                })
            },

            /** 打开基本型配置清单 */
            handleBasicView() {
                this.lookBasicDialog = true;

                if (this.searchForm.plate != null && this.searchForm.series != null) {
                    this.childOption[0] = this.searchForm.plate
                    this.childOption[1] = this.searchForm.series
                    this.childOption[2] = this.searchForm.customer
                    this.childOption[3] = this.searchForm.productModelJh
                    this.childOption[4] = this.searchForm.dataType == '新版' ? "新版" : null
                    //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                    this.$nextTick(() => {
                        this.$refs.selectOptionList.getLinshiBasicData();
                    })
                }
            },
            onAtificialVerChange(e) {
                this.artificialVer = e.target.checked;
                if(this.artificialVer){
                    this.searchForm.isArtificialVer=1;
                }else{
                    this.searchForm.isArtificialVer=0;
                }
            },
            //子组件调用子组件方法,中间
            getchildren() {
                this.$nextTick(() => {
                    this.$refs.selectNoOptional.getNoOptionData();
                })
            },
            getchildrenOption() {
                this.$nextTick(() => {
                    this.$refs.editOptional.getOptionalcData();
                })

            },
            //获取选型减配集合
            getNoPart() {
                return this.$refs.selectNoOptional.getNoOptionalclinshiList();
            },
            //更新页面计算信息
            getSearchUpdate(data) {
                this.searchForm = data
            },
            getSearchNoOptionUpdate(data) {
                this.searchForm = data
            },

            //计算选型选装小计
            getOptionTotal(data) {
                this.searchForm.optionSubtotal = Number(data);
            },
            getNoPartsListNew(data) {
                this.noPartsList = data;
            },

            //触发子组件重新获取list
            getNewList() {
                return this.noPartsList;
            },
            getChildrenPart() {
                this.$refs.selectNoOptional.getNoPartsListNewAll();
            },
            // 选型组件获取减配列表
            getNoParts() {
                this.noPartsList = this.$refs.selectNoOptional.getNoOptionalcDataList();
                this.$refs.editOptional.setNoparts(this.noPartsList);
            },

            // 取消选型件后重新设置减配列表
            setNoPartsListNew(data) {
                this.noPartsList = data;
                this.$refs.selectNoOptional.getNoPartsListNewAll(this.noPartsList);
            },

            //选配件合计=选型选装件小计+选型减配小计
            getChooseAmoutToal(data) {
                this.searchForm.chooseAmount = Number(data);
                this.optionSubtotalBlurs();


            },
            //计算选型减配小计
            getlectotypeSubtotal(data) {
                this.searchForm.basicSubtotal = Number(data);
                this.optionSubtotalBlurs();

            },
            //选配件合计=选型选装件小计+选型减配小计
            getchooseBasicAmountToal(data) {
                this.searchForm.chooseAmount = Number(data);
                this.optionSubtotalBlurs();
            },

            //新增客户调用子类方法
            handleCustomerSubmit() {
                this.$refs.selectCustomer.choose();
            },

            //关闭基本型清单对话框
            handlelookBasicSubmit() {
                this.lookBasicDialog = false;
            },
            //关闭基本型清单对话框
            closelookBasicrForm() {
                this.lookBasicDialog = false;
            },
            /**选择参考*/
            blurs() {
                this.machineType = this.searchForm.machineType
                this.basicAmount = this.searchForm.basicAmount

                this.searchFormAll.year = this.searchForm.year
                this.searchFormAll.reference = this.searchForm.refMachineType
                this.searchFormAll.customer = this.searchForm.customer
                this.searchFormAll.plate = this.searchForm.plate
                if (this.searchForm.customer == null) {
                    this.$message.info('请先选择客户')
                    return
                }
                if (this.searchForm.plate == null) {
                    this.$message.info('请先选择板块')
                    return
                }
                this.$nextTick(() => {
                    this.loadReferenceData();
                })
            },
            //加载参考机型数据
            loadReferenceData() {
                if (this.searchForm.refMachineType === null || this.searchForm.refMachineType === '') {
                    if (this.searchForm.machineType != null && this.searchForm.machineType != '') {
                        console.log(111111);
                        
                        this.blursMachineType();
                    } else {
                        console.log(222222);
                        
                        this.clearPrice();
                    }
                } else {
                    request(SElECTLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.searchFormAll)
                        .then(res => {
                            if (res.data.msg == 'false') {
                                this.$message.info('暂无参考机型')
                                this.clearPrice();
                            } else {
                                this.searchPartForm.masterId = res.data.data[0].id;
                                this.masterId = res.data.data[0].id;
                                this.noContainForm.masterId = res.data.data[0].id;

                                this.searchForm.refUnitAmount = res.data.data[0].unitAmount;
                                this.searchForm.refBasicAmount = res.data.data[0].basicAmount;
                                this.searchForm.refProductModel = res.data.data[0].productModel;
                                this.searchForm.refProductModelJh = res.data.data[0].productModelJh;
                                this.searchForm.refBlowoff = res.data.data[0].blowoff;
                                this.searchForm.refSeries = res.data.data[0].series;
                                this.searchForm.refType = res.data.data[0].type;
                                this.searchForm.refPs = res.data.data[0].ps;
                                this.searchForm.refStrain1 = res.data.data[0].strain1;
                                this.searchForm.refStrain2 = res.data.data[0].strain2;
                                this.searchForm.refStrain3 = res.data.data[0].strain3;
                                this.searchForm.basicAmount = res.data.data[0].basicAmount;
                                this.searchForm.unitAmount = res.data.data[0].unitAmount;
                                this.searchForm.optionSubtotal = res.data.data[0].optionSubtotal;
                                this.searchForm.chooseAmount = res.data.data[0].chooseAmount;
                                this.searchForm.basicSubtotal = res.data.data[0].basicSubtotal;
                                this.searchForm.powerDiff = 0;
                                this.searchForm.id = this.id;
                                this.searchForm.refMachineType = this.searchFormAll.reference;

                                if(res.data.data[0].isArtificialVer===1){
                                    this.artificialVer =true
                                    this.searchForm.isArtificialVer=1;
                                }else{
                                    this.artificialVer =false
                                    this.searchForm.isArtificialVer=0;
                                }

                                //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                                this.$nextTick(() => {
                                    this.$refs.selectNoOptional.getNoOptionData();
                                    this.$refs.editOptional.getOptionalcData();

                                })
                                this.basicAmountBlurs();
                            }
                        })
                }
            },
          /**  状态机变更加载数据 */
          blursMachineType() {
            if (this.searchForm.customer == null) {
              this.$message.info('请先输入客户名称！')
              return
            }
            if (this.searchForm.plate == null) {
              this.$message.info('请先输入板块！')
              return
            }
            // 检查状态机
              let that= this;
              request(CHECK_STATE_CODE, METHOD.POST, this.searchForm)
                  .then(res => {
                      if (res.data.data) {
                          that.$message.error(res.data.data);
                      } else {
                          this.$nextTick(() => {
                              that.loadData();
                          })
                      }

                  })
          },
          //加载数据
          loadData() {
            let data = {
              "P_ZTJ": this.searchForm.machineType
            }
            request(ztjxx_action, METHOD.POST, data).then(res => {
              let returnData = res.data.data
              let RETURN = returnData.RETURN
              if (RETURN && RETURN.ZTYPE == 'S') {
                let T_OUT = returnData.T_OUT[0];
                if (!T_OUT) {
                  this.$message.error("状态机信息不存在");
                  return;
                }

                this.searchForm.type = T_OUT.ZZPX;
                this.searchForm.productModel = T_OUT.ZCPXH;
                this.searchForm.productModelJh = T_OUT.ZCPXHJ;
                this.searchForm.ps = T_OUT.ZGL;
                this.searchForm.series = T_OUT.ZCPZXL;
                this.searchForm.blowoff = T_OUT.ZPF_TEXT;
                this.searchForm.strain1 = T_OUT.ZYT1_TEXT;
                this.searchForm.strain2 = T_OUT.ZYT2_TEXT;
                this.searchForm.strain3 = T_OUT.ZYT3_TEXT;
                this.checkField('blowoff');
                this.checkField('series');
                this.checkField('productModel');
                if (this.searchForm.refMachineType != null && this.searchForm.refMachineType != '') {
                  this.selectBasicPrice2(this.searchForm.year, this.searchForm.customer, T_OUT.ZCPXH, this.searchForm.plate,T_OUT.ZCPXHJ,this.searchForm.dataType)
                } else {
                  this.selectBasicPrice()
                  if (this.searchForm.refMachineType == null || this.searchForm.refMachineType === '') {
                    //如果参考机型为空的时候,带出基本选型配置list
                    request(SELECTOPTIONLIST_XSGSALLOWANCEMAIN, METHOD.POST, {
                      customer: this.searchForm.customer,
                      year: this.searchForm.year,
                      plate: this.searchForm.plate,
                      blowoff: this.searchForm.blowoff,
                      productModelJh: this.searchForm.productModelJh,
                      series: this.searchForm.series,
                      dataType: this.searchForm.dataType == '新版' ? 'new' : null,
                    }).then(res => {
                      this.clearPrice();
                      this.optionList = res.data.data;
                        this.optionList.forEach(item=>{
                            item['sysBudgetedPrice']=item.budgetedPrice;
                            item['sysActualPrice']=item.actualPrice;
                            item['sysValuePrice']=item.valuePrice;
                            item['sysStandardPrice']=item.standardPrice;
                            item['sysStandardSpecs']=item.standardSpecs;
                            item['sysSpecs']=item.specs;
                            item['sysSupplierBak']=item.supplierBak;
                            item['sysPartsName']=item.partsName;
                            item['sysIsSubtraction']=item.isSubtraction;
                            item['sysSubType']=item.subType;
                            item['isDelete']=0;
                        })
                      this.$nextTick(() => {
                        this.$refs.editOptional.getoptionList(this.optionList);
                        this.$refs.selectNoOptional.getNoOptionList();
                      })
                    })
                  }
                }
              } else {
                this.$message.info('暂无该机型信息')
                this.clearPrice();
                if (this.searchForm.refMachineType != null && this.searchForm.refMachineType != '') {
                  this.searchForm.blowoff = null;
                  this.searchForm.series = null;
                  this.searchForm.productModel = null;
                  this.searchForm.ps = null;
                  this.searchForm.basicGuidanceAmount = 0;
                  this.searchForm.productModelJh = null;
                  this.$nextTick(() => {
                    this.$refs.selectNoOptional.getNoOptionData();
                    this.$refs.editOptional.getOptionalcData();
                  })
                } else {
                  this.optionList = [];
                  this.searchForm.blowoff = null;
                  this.searchForm.series = null;
                  this.searchForm.productModel = null;
                  this.searchForm.ps = null;
                  this.searchForm.productModelJh = null;
                  this.masterId = null;
                  this.$nextTick(() => {
                    this.$refs.selectNoOptional.getNoOptionData();
                    this.$refs.editOptional.getOptionalcData();
                  })
                }
              }
            })
          },
            powerDiffBlurs(){
                //基本型开票价=参考基本型开票价+功率差
                this.searchForm.basicAmount = this.NumberAdd(this.searchForm.refBasicAmount, this.searchForm.powerDiff);
                this.optionSubtotalBlurs();
            },
            //计算整机开票价
            basicAmountBlurs() {
                this.optionSubtotalBlurs();
                this.checkField('basicAmount');
            },
            //计算整机开票价
            chooseAmountBlurs() {
                this.optionSubtotalBlurs();
                this.checkField('chooseAmount');
            },
            //计算整机开票价
            optionSubtotalBlurs() {
                if (this.searchForm.basicAmount != null && this.searchForm.chooseAmount != null) {
                    //选配件合计=选型选装件小计+选型减配小计
                    this.searchForm.chooseAmount = this.NumberAdd(this.searchForm.optionSubtotal, this.searchForm.basicSubtotal);
                    //整机开票价=基本型开票价+选配件合计
                    this.searchForm.unitAmount = this.NumberAdd(this.searchForm.basicAmount, this.searchForm.chooseAmount);
                }
            },
            //基本型开票价
            selectBasicPrice() {
                if (!(this.searchForm.year == null || this.searchForm.customer == null || this.searchForm.productModel == null)) {
                  //判断新旧数据 调整param
                  let param = {
                    year: this.searchForm.year,
                    customer: this.searchForm.customer,
                    productModel: this.searchForm.productModel,
                    plate: this.searchForm.plate
                  }
                  if(this.searchForm.dataType == "新版"){
                    // param.push({
                    //   productModelJh: this.searchForm.productModelJh,
                    //   isRead: 1
                    // })
                    param.productModelJh = this.searchForm.productModelJh;
                    param.isRead = 1;
                  }
                  request(LIST_XSGSBASICPRICE, METHOD.POST, {param}).then(res => {
                        let data = res.data.data
                        this.searchForm.basicAmount = null;
                        if (data.length > 0) {
                            this.searchForm.basicGuidanceAmount = data[0].actualPrice
                        } else {
                            this.searchForm.basicGuidanceAmount = null;
                        }
                    })
                }
            },
            //基本型开票价
            selectBasicPrice2(year, customer, productModel, plate, productModelJh, dataType) {

                if (!(year == null || customer == null || productModel == null)) {
                    //判断新旧数据 调整param
                    let param = {
                      year: year,
                      customer: customer,
                      productModel: productModel,
                      plate: plate,
                    }
                    if (dataType == "新版") {
                    //   param.push({
                    //     productModelJh: productModelJh,
                    //     isRead: 1
                    //   })
                        param.productModelJh = productModelJh
                        param.isRead = 1
                    }
                    request(LIST_XSGSBASICPRICE, METHOD.POST, {param}).then(res => {

                        let data = res.data.data
                        if (data.length > 0) {
                            this.searchForm.basicGuidanceAmount = data[0].actualPrice
                        } else {
                            this.searchForm.basicGuidanceAmount = null;
                        }
                        this.checked = false;
                        this.basicAmountBlurs()

                    })
                }
            },

            /** 处理查询 */
            handleSearch() {
                this.pagination.current = 1
                this.getData()
            },
            /** 关闭对话框，表单重置 */
            closeForm() {
                this.dialog = false
                this.$refs.form.resetFields()
                this.form = {}
            },
            checkCustomer(){
                if (this.customer && this.searchForm.customer && this.searchForm.customer != this.customer) {
                    this.$message.error("客户与现有报价信息客户不一致")
                    return false;
                }
                return true;
            },
            optionalChanged(xxColumns, xzColumns, optionalData) {
                if (optionalData === null) {
                    return
                }
                optionalData.forEach(item => {
                    if (item.subType == '选型件') {
                        if (!this.isDoubleColumns(xxColumns, item.partsName)) {
                            xxColumns.push({
                                title: item.partsName,
                                dataIndex: b64_md5(b64_md5(item.id)),
                                width: 165,
                                align: 'center'
                            })
                        }
                    } else if (item.subType == '选装件') {
                        if (!this.isDoubleColumns(xzColumns, item.partsName)) {
                            xzColumns.push({
                                title: item.partsName,
                                dataIndex: b64_md5(b64_md5(item.id)),
                                width: 165,
                                align: 'center'
                            })
                        }
                    }

                })


            },

            clearPrice() {
                this.optionList = [];
                this.searchForm.basicAmount = null;
                this.searchForm.chooseAmount = null;
                this.searchForm.unitAmount = null;
                this.searchForm.optionSubtotal = null;
                this.searchForm.basicSubtotal = null;

                this.searchForm.refUnitAmount = null;
                this.searchForm.refBasicAmount = null;
                this.searchForm.powerDiff = null;
                this.searchForm.refProductModel = null;
                this.searchForm.refProductModelJh = null;
                this.searchForm.refBlowoff = null;
                this.searchForm.refSeries = null;
                this.searchForm.refType = null;
                this.searchForm.refPs = null;
                this.searchForm.refStrain1 = null;
                this.searchForm.refStrain2 = null;
                this.searchForm.refStrain3 = null;
                this.masterId = null;
                //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
                this.$nextTick(() => {
                    this.$refs.selectNoOptional.getNoOptionData();
                    this.$refs.editOptional.getOptionalcData();
                })
            },
            isDoubleColumns(columns, name) {
                let flag = false;
                columns.forEach(item => {
                    if (item.dataIndex == b64_md5(b64_md5(name))) {
                        flag = true;
                    }
                })
                return flag
            },

            formatTime(row, column) {
                const date = new Date(row[column.property])
                return date.getFullYear() + '年' +
                    date.getMonth() + '月' +
                    date.getDate() + '日 ' +
                    date.getHours()
                    + ':' + date.getMinutes()
            },
            handleCustomerSearch(value) {
                request(LIST_XSGSCUSTOMER, METHOD.POST, {
                    name: value
                }).then(res => {
                    this.customerList = res.data.data
                })

            },
            handleCustomerChange(value) {
                this.customerList.forEach(item => {
                    if (item.name == value) {
                        this.searchForm.customer = item.customer;
                        return false;
                    }
                })
                if (!this.checkCustomer()) {
                    return;
                }
                this.selectBasicPrice()
            },

            deleteDoubleXx(records) {
                let newList = []
                let dataList = this.$refs.selectNoOptional.getNoOptionalcDataList();
                let flag = false
                let noParts = {};
                dataList.forEach(item => {
                    if (records.partsName == item.partsName) {
                        flag = true
                        noParts = {...item};
                    } else {
                        newList.push(item)
                    }
                })
                let that = this;
                if (flag) {
                    this.$confirm({
                        content: `减配里面有相同的[` + records.partsName + "],是否删除掉",
                        onOk: () => {
                            records['sysId']=noParts.sysId;
                            records['sysBudgetedPrice']=noParts.sysBudgetedPrice;
                            records['sysActualPrice']=noParts.sysActualPrice;
                            records['sysValuePrice']=noParts.sysValuePrice;
                            records['sysStandardPrice']=noParts.sysStandardPrice;
                            records['sysStandardSpecs']=noParts.sysStandardSpecs;
                            records['sysSpecs']=noParts.sysSpecs;
                            records['sysSupplierBak']=noParts.sysSupplierBak;
                            records['sysPartsName']=noParts.sysPartsName;
                            records['sysIsSubtraction']=noParts.sysIsSubtraction;
                            records['sysSubType']=noParts.sysSubType;
                            that.$refs.selectNoOptional.setNoOptionalcDataList(newList)
                            that.$refs.selectNoOptional.blursActualPrice()
                        },
                    });
                }
            },
            checkField(field){
                this.$refs.searchForm.validateField(field, valid => {})
            }
        }
    }
</script>

<style scoped>
    .ant-input-number {
        width: 100%;
    }

    td {
        border: 1px solid #f0f0f0;
    }

    .ant-table-thead > tr > th, .ant-table-tbody > tr > /deep/ td {
        padding: 1px 1px;
        overflow-wrap: break-word;
    }

    /*/deep/  样式穿透
    */
    /deep/ .ant-col-8 {
        display: block;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 100%;
    }

    /deep/ .ant-card-head-title {
        flex: auto;
        width: 120px;
        overflow: inherit;
    }

    /deep/ .ant-form-item {
        margin: 0;
    }

    /deep/ .ant-card-extra {
        margin-right: 75%;
        padding: 0px 0;
    }

    .tdnumber > /deep/ .ant-input-number {
        width: 180px;
    }

    /deep/ .ant-form > table > tbody > tr > td {
        border: 1px solid #f0f0f0;
    }

    .ant-divider-horizontal {
        height: 0.5px;
        margin: 0px 0;
    }

    .ant-divider {
        background: #000;
    }

    /deep/ .quoteInfo input[disabled=disabled],textarea[disabled=disabled],.ant-select-disabled {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;;
    }
    /deep/ .quoteInfo .disabelTd {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;
    }
</style>
