<template>
  <div>
    <a-spin :spinning="loading">
      <table style="width: 100%" class="policyInfo">
        <tbody class="ant-table">
        <tr class="annexRow">
          <th style="width: 9%">附件</th>
          <td style="width: 100%;" colspan="11" class="annexTd">
            <FileUpload v-model="quoteForm.annexList" path="xsgs/" field="quote" :multiple="true"
                        :disabled="isNotDrafted()"/>
          </td>
        </tr>
        <tr>
          <th colspan="12">状态机信息区</th>
        </tr>
        <tr>
          <th style="width: 10%">
            客户号
          </th>
          <td colspan="2" style="width: 17%">
            <a-input v-model="quoteForm.customer"
                     :style="`border:${formRule.customer.border}`"
                     @blur="customerBlurs" placeholder="起草部门必填"/>
          </td>
          <th style="width: 9%">客户名称</th>
          <td colspan="7" style="width: 36%">
            <a-select
                :style="`min-width: 200px;border:${formRule.customerName.border}`"
                placeholder="起草部门必填"
                v-model="quoteForm.customerName"
                show-search
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                @search="handleCustomerSearch"
            >
              <a-select-option v-for="d in customerList" @click="selectCustomer(d)" :key="d.name">
                {{ d.name }}
              </a-select-option>
            </a-select>
          </td>
        </tr>
        <tr>
          <th style="width: 5%">
            序号
          </th>
          <th style="width: 10%">
            报价形式
          </th>
          <th style="width: 15%">
            报价状态机
          </th>
          <th style="width: 10%">
            整机价格
          </th>
          <th style="width: 25%">
            备注
          </th>
          <th style="width: 10%">
            核算报告
          </th>
          <th v-if="isArchive()" style="width: 10%;">是否发布</th>
          <th style="width: 20%">操作</th>
        </tr>
        <tr v-for="(d,index) in quoteForm.quoteList.filter(s=>s.option != 2)" :key="d.id" :id="d.id">
          <td style="width: 5%;text-align: center">{{index+1}}</td>
          <td style="width: 10%;text-align: center">{{coverQuoteType(d.quoteType)}}</td>
          <td style="width: 15%">{{d.machineType}}</td>
          <td style="width: 10%;text-align: right">{{d.unitAmount}}</td>
          <td style="width: 25%">
                    <textarea row="1"
                              v-model="d.remark2"
                              class="xsgs-textarea"
                              style="margin: 0;padding: 0 0 0 2px;height: 33px;min-height:33px;max-height:150px;font-size: 14px"
                              :placeholder="isNotDrafted()?'':'请输入备注'"
                              @change="changeRemark(d)"
                              :title="d.remark2"></textarea>
          </td>
          <td style="width: 10%;text-align: center">
            <a @click="editQuotes(d,1)">编辑</a>
            <a @click="editQuotes(d,2)">查看</a>
          </td>
          <td v-if="isArchive()" style="width: 10%;text-align: center">{{d.isRelease == 1?"已发布":"未发布"}}</td>

          <td style="width: 20%;text-align: center">
            <a @click="editQuote(d)">编辑</a>
            <a-divider type="vertical"/>
            <a @click="editQuote(d,3)">查看</a>
            <a-divider v-if="isArchive() && d.isRelease != 1" type="vertical"/>
            <a v-if="isArchive() && d.isRelease != 1" @click="releaseQuote(d)">发布</a>
            <a-divider v-if="!isNotDrafted()" type="vertical"/>
            <a v-if="!isNotDrafted()" @click="deleteQuote(d)">删除</a>
          </td>
        </tr>
        <tr v-if="!isNotDrafted() || !quoteForm.quoteList.size > 0">
          <td colspan="12">
            <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newQuote">新增
            </a-button>
          </td>
        </tr>
        </tbody>
      </table>
    </a-spin>
    <a-modal
        width="30%"
        title="新增报价"
        :footer="false"
        @cancel="isChooseType = false"
        :visible="isChooseType">
      <div style="text-align: center;">
        <a @click="newPriceQuote('REF')">参考原机型报价</a>、
        <a @click="newPriceQuote('BASE')">参考基本型报价</a>、
        <a @click="newChangeConfig">更改配置报价</a>
      </div>
    </a-modal>
    <!--状态机配置变更报价-->
    <a-modal
        width="80%"
        title="状态机配置变更报价"
        @cancel="isChangeConfig = false"
        @ok="saveChangeConfig"
        :visible="isChangeConfig">
      <a-spin :spinning="isConfigLoading">
        <table style="width: 100%" class="changeConfigTitle">
          <tbody class="ant-table">
          <!--                    <tr style="border: 0.5px solid #464646">-->
          <!--                        <td colspan="2" style="text-align: center">-->
          <!--                            <h3 style="margin: 5px">广西玉柴机器股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;Guangxi Yuchai Machinery-->
          <!--                                Co.,Ltd.</h3>-->
          <!--                        </td>-->
          <!--                    </tr>-->
          <!--                    <tr style="border: 0.5px solid #464646">-->
          <!--                        <td colspan="2" style="text-align: center">-->
          <!--                            <h3 style="margin: 5px">发动机报价审批表</h3>-->
          <!--                        </td>-->
          <!--                    </tr>-->
          <!--                    <tr>-->
          <!--                        <td>&nbsp;</td>-->
          <!--                    </tr>-->
          <tr>
            <td>
              <a-button type="primary" @click="handleDownloadTmp">
                <a-icon type="download"/>
                下载导入模板
              </a-button>
              <a-upload
                  name="file"
                  :action="UPLOAD_CONF"
                  :headers="{'Authorization':Cookie.get('Authorization')}"
                  :showUploadList="false"
                  style="margin-left: 8px"
                  @change="handleUploadExcel">
                <a-button type="primary">
                  <a-icon type="upload"/>
                  批量导入
                </a-button>
              </a-upload>
            </td>
            <td style="text-align: right;font-size: 16px">无税/(元/台)</td>
          </tr>
          </tbody>
        </table>
        <table style="width: 100%;margin-top:5px"
               class="changeConfigInfo">
          <tbody class="ant-table">
          <tr>
            <th colspan="11" style="text-align: center">
              状态机配置变更报价
            </th>
          </tr>
          <tr>
            <th>配套客户</th>
            <td colspan="3">
              <span>{{changeConfigForm.customerName}}</span>
            </td>
            <th>更改通知单日期</th>
            <td colspan="2">
              <a-date-picker :style="`width: 100%;border:${formRule.noticeDate.border}`"
                             value-format="YYYY-MM-DD" format="YYYY-MM-DD"
                             placeholder="请选择日期"
                             @blur="checkRequired(changeConfigForm,'noticeDate',true)"
                             v-model="changeConfigForm.noticeDate"/>
            </td>
            <th>更改通知单号</th>
            <td colspan="3">
              <a-input :style="`border:${formRule.noticeNo.border}`"
                       placeholder="请输入变更通知单号"
                       @blur="checkRequired(changeConfigForm,'noticeNo')"
                       v-model="changeConfigForm.noticeNo"/>
            </td>
          </tr>
          <tr>
            <th style="width:7%;text-align: center">序号</th>
            <th style="width:8%;text-align: center">
              产品型号
            </th>
            <th style="width:8%;text-align: center">
              状态机
            </th>
            <th style="width:15%;text-align: center">
              变更零部件名称
            </th>
            <th style="width:8%;text-align: center">
              <div class="tipRow">
                <div><span>更改规格</span><span class="takeIconImg takeTriangleWhite tipIcon"/></div>
              </div>
              <div class="tipRow">
                <div><span>新增配置</span><span class="takeIconImg takeCircleBlack tipIcon"/></div>
              </div>
              <div class="tipRow">
                <div><span>减少配置</span><span class="takeIconImg takeCircleWhite tipIcon"/></div>
              </div>
            </th>
            <th style="width:7%;text-align: center">
              成本变动
            </th>
            <th style="width:7%;text-align: center">
              价格变动
            </th>
            <th style="width:10%;text-align: center">
              原价格
            </th>
            <th style="width:7%;text-align: center">
              现价格
            </th>
            <th style="width:6%;text-align: center">
              操作
            </th>
            <th style="width:18%;text-align: center">
              备注
            </th>
          </tr>
          <tr v-for="(d,index) in changeConfigForm.partsList" :key="d.id">
            <td  style="text-align: center">{{index+1}}</td>
            <td>
              <a-input v-model="d.series" :title="d.series" v-if="!changeConfigForm.isView"/>
            </td>
            <td>
              <a-input v-model="d.machineType" :title="d.machineType"/>
            </td>
            <td>
              <a-input v-model="d.partsName" :title="d.partsName" v-if="!changeConfigForm.isView"/>
            </td>
            <td style="text-align: center;position: relative">
              <div class="isTaskSelect istake" :id="d.id">
                <div class="isTaskSelectValue istake">
                  <span v-if="d.isTake==2" style="" class="takeIconImg takeTriangleWhite istake"/>
                  <span v-if="d.isTake==3" style="" class="takeIconImg takeCircleBlack istake"/>
                  <span v-if="d.isTake==4" style="" class="takeIconImg takeCircleWhite istake"/>
                </div>
                <ul v-show="d.showIsTake" class="istake">
                  <li @click="d['isTake']=2"><span class="takeIconImg takeTriangleWhite"/></li>
                  <li @click="d['isTake']=3"><span class="takeIconImg takeCircleBlack"/></li>
                  <li @click="d['isTake']=4"><span class="takeIconImg takeCircleWhite"/></li>
                </ul>
              </div>

            </td>
            <!-- 成本变动 -->
            <td>
              <a-input v-model="d.budgetedPrice"
                       :style="`text-align: right;${coverRed(d.budgetedPrice)}`"
                       @input="inputMoney(d,'budgetedPrice')"
                       @blur="coverMoney(d,'budgetedPrice')"/>
            </td>
            <!-- 价格变动 -->
            <td>
              <a-input v-model="d.actualPrice"
                       :style="`text-align: right;${coverRed(d.actualPrice)}`"
                       @input="inputMoney(d,'actualPrice')"
                       @blur="coverMoney(d,'actualPrice')"/>
            </td>
            <!-- 原价格 -->
            <td>
              <a-input v-model="d.sysValuePrice"
                       :style="`text-align: right;${coverRed(d.sysValuePrice)}`"
                       @input="inputMoney(d,'sysValuePrice')"/>
            </td>
            <!-- 现价格 -->
            <td>
              <a-input v-model="d.valuePrice"
                       :style="`text-align: right;${coverRed(d.valuePrice)}`"
                       @input="inputMoney(d,'valuePrice')"/>
            </td>
            <td style="text-align: center;">
              <a v-if="!isNotDrafted()" @click="deleteDetail(d)">删除</a>
            </td>
            <td v-if="index == 0" :rowspan="changeConfigForm.partsList.length">
                            <textarea min-row="1"
                                      class="xsgs-textarea"
                                      :style="`height:${changeConfigForm.partsList.length * 33}px;`"
                                      v-model="changeConfigForm.remark2"
                                      :title="changeConfigForm.remark2"></textarea>
            </td>
          </tr>
          <tr v-if="!isNotDrafted()">
            <td colspan="11">
              <a-button style="width: 100%;margin: 10px 0;" type="dashed" icon="plus" @click="newDetail">新增</a-button>
            </td>
          </tr>
          </tbody>
        </table>
      </a-spin>
    </a-modal>
    <!--状态机配置变更报价-->
    <!--核算报告-->
    <div class="quoteReportModelStyle" ref="quoteReportModel">
      <a-modal
          width="80%"
          title="核算报告"
          :footer="false"
          :header="false"
          :getContainer="()=>$refs.quoteReportModel"
          @cancel="isReport1 = false"
          :visible="isReport1">
        <a-spin :spinning="isReportLoading">
          <table style="width: 100%" class="quoteReportInfo">
            <tbody class="ant-table">
            <tr>
              <td colspan="12" style="text-align: center;padding: 0">
                <h3 style="margin: 0">广西玉柴机器股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;Guangxi Yuchai Machinery
                  Co.,Ltd.</h3>
              </td>
            </tr>
            <tr>
              <td colspan="12" style="text-align: center;padding: 0">
                <h3 style="margin: 0">发动机报价审批表</h3>
              </td>
            </tr>
            <tr>
              <td colspan="12" style="text-align: center;position: relative;;padding: 0">
                <h3 style="margin: 0">{{reportTitle}}</h3>
                <h3 style="margin: 0;position: absolute;right: 0;top: 0;">无税/(元/台)</h3>
              </td>
            </tr>
            <tr>
              <th style="width: 46%" colspan="6">参考机型</th>
              <th style="width: 41%" colspan="5">报价机型</th>
              <td rowspan="4" style="width: 13%">
                <div class="tipRow">
                  <div><span>基本配置</span><span class="takeIconImg takeTriangleBlack tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>更改规格</span><span class="takeIconImg takeTriangleWhite tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>新增配置</span><span class="takeIconImg takeCircleBlack tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>减少配置</span><span class="takeIconImg takeCircleWhite tipIcon"/></div>
                </div>
              </td>
            </tr>
            <tr>
              <th colspan="2" style="width: 18%">配套客户</th>
              <td style="width: 28%" colspan="4">{{reportForm.customerName}}</td>
              <th style="width: 13%">配套客户</th>
              <td style="width: 28%" colspan="4">{{reportForm.customerName}}</td>
            </tr>
            <tr>
              <th colspan="2" style="width: 18%">产品型号</th>
              <th style="width: 6%">功率(PS)</th>
              <th style="width: 16%" colspan="2">状态机</th>
              <th style="width: 6%">整机价格</th>
              <th style="width: 13%">产品型号</th>
              <th style="width: 6%">功率(PS)</th>
              <th style="width: 16%" colspan="2">状态机</th>
              <th style="width: 6%">整机价格</th>
            </tr>
            <tr>
              <td colspan="2" style="width: 18%">{{reportForm.refProductModel}}</td>
              <td style="width: 6%">{{reportForm.refPs?reportForm.refPs.split('.')[0]:""}}</td>
              <td style="width: 16%" colspan="2">{{reportForm.refMachineType}}</td>
              <td style="width: 7%;text-align: right">
                <span :style="`${coverRed(reportForm.refUnitAmount)}`">{{reportForm.refUnitAmount}}</span>
              </td>
              <td style="width: 13%">{{reportForm.productModel}}</td>
              <td style="width: 6%">{{reportForm.ps?reportForm.ps.split('.')[0]:""}}</td>
              <td style="width: 16%" colspan="2">{{reportForm.machineType}}</td>
              <td style="width: 7%;text-align: right">
                <span :style="`${coverRed(reportForm.unitAmount)}`">{{reportForm.unitAmount}}</span>
              </td>
            </tr>
            <tr>
              <th style="width: 5%">类型</th>
              <th style="width: 13%">零部件名称</th>
              <th style="width: 6%">零件状态</th>
              <th style="width: 9%">规格</th>
              <th style="width: 7%">供应商</th>
              <th style="width: 6%">价格/价差</th>
              <th style="width: 13%">零部件名称</th>
              <th style="width: 6%">零件状态</th>
              <th style="width: 9%">规格</th>
              <th style="width: 7%">供应商</th>
              <th style="width: 6%">价格/价差</th>
              <th style="width: 13%">差异(报价减参考)</th>
            </tr>
            <tr v-for="(d) in reportForm.partsList" :key="d.id">
              <td style="width: 5%;text-align: center">{{d.subType}}</td>
              <td style="width: 13%">{{d.sysPartsName}}</td>

              <td style="width: 6%;text-align: center">
                <span v-if="d.sysPartsName" class="takeIconImg takeTriangleBlack"/>
              </td>

              <td style="width: 9%;">{{d.sysSpecs}}</td>
              <td style="width: 7%;">{{d.sysSupplierBak}}</td>
              <td style="width: 6%;text-align: right">
                <span :style="`${coverRed(d.sysActualPrice)}`">{{d.sysActualPrice}}</span>
              </td>
              <td style="width: 13%;">{{d.partsName}}</td>
              <td style="width: 6%;text-align: center">
                <span v-if="d.isTake==1" style="" class="takeIconImg takeTriangleBlack"/>
                <span v-if="d.isTake==2" style="" class="takeIconImg takeTriangleWhite"/>
                <span v-if="d.isTake==3" style="" class="takeIconImg takeCircleBlack"/>
                <span v-if="d.isTake==4" style="" class="takeIconImg takeCircleWhite"/>
              </td>
              <td style="width: 9%;">{{d.specs}}</td>
              <td style="width: 7%;">{{d.supplierBak}}</td>
              <td style="width: 6%;text-align: right">
                <span :style="`${coverRed(d.actualPrice)}`">{{d.actualPrice}}</span>
              </td>
              <td style="width:13%;text-align: right">
                <span v-if="isLook" :style="`${coverRed(d.valuePrice)}`">{{d.revisePrice}}</span>
                <a-input-number v-if="isEdit" v-model="d.revisePrice" :style="`${coverRed(d.valuePrice)}`"
                                :step="0.01"
                                :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                @blur="unitAmountBlurs"
                />
              </td>
            </tr>
            <tr>
              <td colspan="11" style="text-align: center">功率差异价差：</td>
              <td style="text-align: right">
                <span :style="`${coverRed(reportForm.powerDiff)}`">{{reportForm.powerDiff}}</span>
              </td>
            </tr>
            <tr>
              <td colspan="11" style="text-align: center">小计：</td>
              <td style="text-align: right">
                <span  :style="`${coverRed(reportForm.total)}`">{{sum}}</span>
              </td>
            </tr>
            <tr style="height: 75px !important;">
              <th colspan="2">备注</th>
              <td colspan="10">
                <a-textarea readonly style="margin: 0;padding: 0 0 0 3px;height: 70px;min-height: 70px"
                            v-model="reportForm.remark2"></a-textarea>
              </td>
            </tr>
            </tbody>
          </table>
          <div v-if="isEdit" style="margin:10px 5px 5px 5px;text-align: center">
            <a-button :disabled="loading" :loading="loading" type="primary" style="margin: 0 8px"
                      @click="handleSave">
              保存
            </a-button>
          </div>
        </a-spin>
      </a-modal>
      <a-modal
          width="80%"
          title="核算报告"
          :footer="false"
          :getContainer="()=>$refs.quoteReportModel"
          @cancel="isReport2 = false"
          :visible="isReport2">
        <a-spin :spinning="isReportLoading">
          <table style="width: 100%" class="quoteReportInfo">
            <tbody class="ant-table">
            <tr>
              <td colspan="11" style="text-align: center;padding: 0">
                <h3 style="margin: 0">广西玉柴机器股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;Guangxi Yuchai Machinery
                  Co.,Ltd.</h3>
              </td>
            </tr>
            <tr>
              <td colspan="11" style="text-align: center;padding: 0">
                <h3 style="margin: 0">发动机报价审批表</h3>
              </td>
            </tr>
            <tr>
              <td colspan="11" style="text-align: center;position: relative;;padding: 0">
                <h3 style="margin: 0">{{reportTitle}}</h3>
                <h3 style="margin: 0;position: absolute;right: 0;top: 0;">无税/(元/台)</h3>
              </td>
            </tr>
            <tr>
              <th style="width: 47%" colspan="5" rowspan="4">基本型配置</th>
              <th style="width: 42%" colspan="5">报价机型</th>
              <td rowspan="4" style="width: 11%">
                <div class="tipRow">
                  <div><span>基本配置</span><span class="takeIconImg takeTriangleBlack tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>更改规格</span><span class="takeIconImg takeTriangleWhite tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>新增配置</span><span class="takeIconImg takeCircleBlack tipIcon"/></div>
                </div>
                <div class="tipRow">
                  <div><span>减少配置</span><span class="takeIconImg takeCircleWhite tipIcon"/></div>
                </div>
              </td>
            </tr>
            <tr>
              <th style="width: 15%">配套客户</th>
              <td style="width: 29%" colspan="4">{{reportForm.customerName}}</td>
            </tr>
            <tr>
              <th style="width: 15%">产品型号</th>
              <th style="width: 6%">功率(PS)</th>
              <th style="width: 16%" colspan="2">状态机</th>
              <th style="width: 7%">整机价格</th>
            </tr>
            <tr>
              <td style="width: 13%">{{reportForm.productModel}}</td>
              <td style="width: 6%">{{reportForm.ps?reportForm.ps.split('.')[0]:""}}</td>
              <td style="width: 16%" colspan="2">{{reportForm.machineType}}</td>
              <td style="width: 7%;text-align: right">
                <span :style="`${coverRed(reportForm.unitAmount)}`">{{reportForm.unitAmount}}</span>
              </td>
            </tr>
            <tr>
              <th style="width: 5%">类型</th>
              <th style="width: 15%">零部件名称</th>
              <th style="width: 6%">零件状态</th>
              <th style="width: 9%">规格</th>
              <th style="width: 7%">价格</th>
              <th style="width: 15%">零部件名称</th>
              <th style="width: 6%">零件状态</th>
              <th style="width: 9%">规格</th>
              <th style="width: 7%">供应商</th>
              <th style="width: 7%">价格</th>
              <th style="width: 14%">差异(报价减基本型)</th>
            </tr>
            <tr v-for="(d) in reportForm.partsList" :key="d.id">
              <td style="width: 5%;text-align: center">{{d.subType}}</td>
              <td style="width: 15%">{{d.sysPartsName}}</td>

              <td style="width: 6%;text-align: center">
                <span v-if="d.sysPartsName" class="takeIconImg takeTriangleBlack"/>
              </td>

              <td style="width: 9%;">{{d.sysSpecs}}</td>
              <td style="width: 7%;text-align: right">
                <span :style="`${coverRed(d.sysActualPrice)}`">{{d.sysActualPrice}}</span>
              </td>
              <td style="width: 15%;">{{d.partsName}}</td>
              <td style="width: 6%;text-align: center">
                <span v-if="d.isTake==1" class="takeIconImg takeTriangleBlack"/>
                <span v-if="d.isTake==2" class="takeIconImg takeTriangleWhite"/>
                <span v-if="d.isTake==3" class="takeIconImg takeCircleBlack"/>
                <span v-if="d.isTake==4" class="takeIconImg takeCircleWhite"/>
              </td>
              <td style="width: 9%;">{{d.specs}}</td>
              <td style="width: 7%;">{{d.supplierBak}}</td>
              <td style="width: 7%;text-align: right">
                <span :style="`${coverRed(d.actualPrice)}`">{{d.actualPrice}}</span>
              </td>
              <td style="width:14%;text-align: right">
                <span :style="`${coverRed(d.valuePrice)}`">{{d.valuePrice}}</span>
              </td>
            </tr>
            <tr>
              <td colspan="10" style="text-align: center">小计：</td>
              <td style="text-align: right">
                <span :style="`${coverRed(reportForm.total)}`">{{reportForm.total}}</span>
              </td>
            </tr>
            <tr style="height: 55px !important;">
              <th colspan="2">备注</th>
              <td colspan="9">
                <a-textarea readonly
                            :style="`margin: 0;padding: 0 0 0 3px;height: 70px;min-height: 70px`"
                            v-model="reportForm.remark2"></a-textarea>
              </td>
            </tr>
            </tbody>
          </table>
        </a-spin>
      </a-modal>
      <a-modal
          width="80%"
          :title="changeConfigForm.title"
          :footer="false"
          :getContainer="()=>$refs.quoteReportModel"
          @cancel="isReport3 = false"
          :visible="isReport3">
        <a-spin :spinning="isConfigLoading">
          <table style="width: 100%" class="quoteReportInfo">
            <tbody class="ant-table">
            <tr>
              <td colspan="10" style="text-align: center;padding: 0">
                <h3 style="margin: 0">广西玉柴机器股份有限公司&nbsp;&nbsp;&nbsp;&nbsp;Guangxi Yuchai Machinery
                  Co.,Ltd.</h3>
              </td>
            </tr>
            <tr>
              <td colspan="10" style="text-align: center;padding: 0">
                <h3 style="margin: 0">发动机报价审批表</h3>
              </td>
            </tr>
            <tr>
              <td colspan="10" style="text-align: center;position: relative;;padding: 0">
                <h3 style="margin: 0">{{reportTitle}}</h3>
                <h3 style="margin: 0;position: absolute;right: 0;top: 0;">无税/(元/台)</h3>
              </td>
            </tr>
            <tr>
              <th style="width:7%;">配套客户</th>
              <td style="width:20%;" colspan="2">
                <span>{{changeConfigForm.customerName}}</span>
              </td>
              <th style="width:15%;">更改通知单日期</th>
              <td style="width:17%;" colspan="2">
                {{changeConfigForm.noticeDate}}
              </td>
              <th style="width:17%;" colspan="2">更改通知单号</th>
              <td style="width:25%;" colspan="2">
                {{changeConfigForm.noticeNo}}
              </td>
            </tr>
            <tr>
              <th style="width:7%;text-align: center">序号</th>
              <th style="width:10%;text-align: center">
                产品型号
              </th>
              <th style="width:10%;text-align: center">
                状态机
              </th>
              <th style="width:15%;text-align: center">
                变更零部件名称
              </th>
              <th style="width:10%;text-align: center">
                <div class="tipRow"><span>更改规格</span><span style="margin-top: 3px;margin-left: 2px" class="takeIconImg takeTriangleWhite"/></div>
                <div class="tipRow"><span>新增配置</span><span style="margin-top: 3px;margin-left: 2px" class="takeIconImg takeCircleBlack"/></div>
                <div class="tipRow"><span>减少配置</span><span style="margin-top: 3px;margin-left: 2px" class="takeIconImg takeCircleWhite"/></div>
              </th>
              <th style="width:7%;text-align: center">
                成本变动
              </th>
              <th style="width:7%;text-align: center">
                价格变动
              </th>
              <th style="width:10%;text-align: center">
                原价格
              </th>
              <th style="width:7%;text-align: center">
                现价格
              </th>
              <th style="width:18%;text-align: center">
                备注
              </th>
            </tr>
            <tr v-for="(d,index) in changeConfigForm.partsList" :key="d.id">
              <td  style="width:7%;text-align: center">{{index+1}}</td>
              <td style="width:10%;">
                {{d.series}}
              </td>
              <td style="width:10%;">
                {{d.machineType}}
              </td>
              <td style="width:15%;">
                {{d.partsName}}
              </td>
              <td style="width:10%;text-align: center" class="disabelTd">
                <span v-if="d.isTake==1" style="" class="takeIconImg takeTriangleBlack"/>
                <span v-if="d.isTake==2" style="" class="takeIconImg takeTriangleWhite"/>
                <span v-if="d.isTake==3" style="" class="takeIconImg takeCircleBlack"/>
                <span v-if="d.isTake==4" style="" class="takeIconImg takeCircleWhite"/>
              </td>
              <!-- 成本变动 -->
              <td style="width:7%;">
                {{d.budgetedPrice}}
              </td>
              <!-- 价格变动 -->
              <td style="width:7%;">
                {{d.actualPrice}}
              </td>
              <!-- 原价格 -->
              <td style="width:10%;">
                {{d.sysValuePrice}}
              </td>
              <!-- 现价格 -->
              <td style="width:7%;">
                {{d.valuePrice}}
              </td>
              <td style="width:18%;" v-if="index == 0" :rowspan="changeConfigForm.partsList.length">
                <a-textarea readonly :style="`margin: 0;padding: 0 0 0 3px;height: ${changeConfigForm.partsList.length*25}px;min-height: ${changeConfigForm.partsList.length*25}px`"
                            v-model="changeConfigForm.remark2"></a-textarea>
              </td>
            </tr>
            </tbody>
          </table>
        </a-spin>
      </a-modal>
    </div>
    <!--核算报告-->
  </div>

</template>

<script>
import {
  LIST_XSGSPROCESS_QUOTE,
  LIST_XSGSCUSTOMER,
  UPLOAD_CONF,
  DOWNLOAD_TEMPLATE,
  getUuid,
  GET_XSGSPROCESSQUOTE_ID,
  LIST_GETFROMCACHE,
  CHECK_RELEASE,
  DO_QUOTE_RELEASE,
  CLEAR_QUOTE_CACHE,
  UPDATE_QUOTE_MESSAGE,
  UPDATE_QUOTE_MESSAGES
} from "@/services/api/xsgs";
import {METHOD, request, exportExcel} from "@/utils/request";
import Cookie from 'js-cookie';
import FileUpload from "../../../components/upload/FileUpload";
import Fingerprint2 from 'fingerprintjs2';

export default {
  name: "quote_list",
  props: {
    form: Object,
    userInfo: Object,
    isView: {type:Boolean,default:false},
  },
  components: {
    FileUpload
  },
  data() {
    return {
      Cookie,
      UPLOAD_CONF,
      loading:false,
      showIsTake: false,
      isConfigLoading: false,
      isChooseType: false,
      isChangeConfig: false,
      isReportLoading: false,
      isReport1: false,
      isReport2: false,
      isReport3: false,
      isEdit: false,
      isLook: false,
      reportTitle: null,
      requiredBorder: "1px solid red !important",
      normalBorder: "1px solid #d9d9d9 !important",
      detailFontSize: 14,
      quoteStoreId: null,
      fullPath: null,
      quoteForm: {
        customer:null,
        customerName:undefined,
        annexList: [],
        quoteList: []
      },
      customerList: [],
      allCustomerList: [],
      changeConfigForm: {},
      reportForm: {},
      quote: {
        id: null,
        quoteType: null,
        machineType: null,
        unitAmount: null,
        remark1: null,
        isView: false
      },
      formRule: {
        customer: {border: "1px solid #d9d9d9 !important", nodeCode: "drafted", msg: "请输入客户号"},
        customerName: {
          border: "1px solid #ffffff !important",
          nodeCode: "drafted",
          msg: "请选择客户"
        },
        noticeNo: {border: "1px solid #d9d9d9 !important", nodeCode: "drafted", msg: "请输入变更通知单号"},
        noticeDate: {border: "1px solid #d9d9d9 !important", nodeCode: "drafted", msg: "请选择变更通知单日期"},
      },
      sum:0,
      customer:undefined,
      customerName:undefined,
    }
  },
  created() {

  },
  mounted() {
    this.fullPath = this.$route.fullPath;
    document.addEventListener('click', this.showIsTakeSelect)
  },
  beforeDestroy() {
    window.removeEventListener('click', this.showIsTakeSelect)
  },
  watch: {
    "$store.state.account.quotes": {
      deep: true,
      handler(val) {
        let newList = val.filter(item => item.quoteStoreId == this.quoteStoreId);
        if (!this.isEmpty(newList)) {
          let that = this;
          let newData = [...this.quoteForm.quoteList];
          newList.forEach(item => {
            if (!item.id) {
              item.id = newData.length + Math.round(Math.random() * 1000) + 1;
              item.option = 0;
              newData.push(item);
            } else {
              let len = newData.filter(s => s.id == item.id);
              if (!that.isEmpty(len)) {
                newData.forEach((s, index) => {
                  if (s.id == item.id) {
                    newData[index] = {...item};
                  }
                })
              } else {
                newData.push(item);
              }

            }
          });
          this.$set(this.quoteForm, "quoteList", newData);
          // this.quoteForm["quoteList"] = newData;
          if (!this.quoteForm.customer) {
            this.quoteForm.customer = newData[0].customer;
          }
          let list = val.filter(item => item.quoteStoreId != this.quoteStoreId);
          this.$store.commit("account/setQuotes", list);
        }
      }
    }
  },
  methods: {
    async initData(processId) {
      if (!processId) {
        let that = this;
        const fingerprint = await Fingerprint2.get((components) => {
          // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
          const values = components.map((component) => component.value) // 配置的值的数组
          const murmur = Fingerprint2.x64hash128(values.join(''), 31) // 生成浏览器指纹
          //murmur就是浏览器指纹啦
          that.quoteStoreId = that.$store.state.account.user.code + murmur
        })
      } else {
        this.quoteStoreId = this.$store.state.account.user.code + processId;
        request(LIST_XSGSPROCESS_QUOTE, METHOD.GET, {"processId": processId}).then(res => {
          if (res.data.data) {
            let list = res.data.data
            this.quoteForm.quoteList = res.data.data.quoteList ? res.data.data.quoteList : [];
            this.quoteForm.customer = list.quoteList ? this.quoteForm.quoteList[0].customer:this.customer;
            this.quoteForm.customerName = list.quoteList ?this.quoteForm.quoteList[0].customerName:this.customerName;
            this.quoteForm.annexList = res.data.data.annexList ? res.data.data.annexList : [];
            // 清楚缓存数据
            request(CLEAR_QUOTE_CACHE, METHOD.POST, {"quoteStoreId": this.quoteStoreId,quoteList:this.quoteForm.quoteList}).then(res => {

            });

          }
        });
      }
      this.getCustomerList();
    },

    newQuote() {
      // 校验客户号
      if (this.checkRequired(this.quoteForm,'customer')) {
        this.isChooseType = true;
      }
    },
    newPriceQuote(quoteType) {
      let query = {
        quoteStoreId: this.quoteStoreId,
        quoteType: quoteType,
        option: 0,
        customer:this.quoteForm.customer,
        parentPath: this.fullPath
      }
      this.$router.push({
        path: '/xsgs_process_manager/xsgs_quote_applove/quoteInfo/:id',
        props: true,
        query: {...query}
      })
      this.isChooseType = false;
    },
    newChangeConfig() {
      this.isChooseType = false;
      this.initChangeConfigForm();
      this.changeConfigForm.customer = this.quoteForm.customer;
      this.changeConfigForm.customerName = this.quoteForm.customerName;
      // 获取id
      request(getUuid, METHOD.GET).then(res => {
        this.changeConfigForm.id = res.data.data;
        this.isChangeConfig = true;
      });

    },
    /** 获取客户列表 **/
    getCustomerList() {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {}).then(res => {
        this.allCustomerList = res.data.data;
        this.customerList = this.allCustomerList;
      })
    },
    /** 客户号查询 **/
    customerBlurs() {
      this.$nextTick(() => {
        let customer = this.quoteForm.customer;
        if (!customer) {
          this.quoteForm.customerName = undefined;
        }
        let newData = this.customerList.filter(item => item.customer == customer);
        if (newData.length > 0) {
          request(LIST_XSGSCUSTOMER, METHOD.POST, {
            customer: this.quoteForm.customer
          }).then(res => {
            if (res.data.data.length > 0) {
              this.quoteForm.customerName = res.data.data[0].name
              this.checkRequired(this.quoteForm, "customer");
            } else {
              this.$message.info('此客户号没找到信息')
            }
          })
        } else {
          this.checkRequired(this.quoteForm, "customer");
        }
      })
    },
    /** 客户名称查找 **/
    handleCustomerSearch(value) {
      let newData = this.allCustomerList.filter(item => {
        if (!value) {
          return true;
        } else {
          if (!item.name) {
            return false;
          } else {
            return (item.customer.indexOf(value) != -1 || item.name.indexOf(value) != -1);
          }
        }
      });
      this.customerList = this.cutList(newData, 0, 20);
    },
    /** 客户查找 **/
    selectCustomer(user) {
      this.$set( this.quoteForm, "customer", user.customer)
      this.$set( this.quoteForm, "customerName", user.name)
      // this.changeConfigForm.customer = user.customer;
      // this.changeConfigForm.customerName = user.name;
    },
    /** 查看报价 **/
    editQuote(d, opt) {
      let option = opt?opt:1;
      if (d.quoteType == "CONFIG") {
        this.changeConfigForm = {...d};
        if (!this.changeConfigForm.hasChange || this.changeConfigForm.hasChange == 0) {
          // 数据库获取
          this.isConfigLoading = true;
          request(GET_XSGSPROCESSQUOTE_ID, METHOD.GET, {id: this.changeConfigForm.id})
              .then(res => {
                this.isConfigLoading = false;
                if (res.data.data != null) {
                  this.changeConfigForm = res.data.data
                  this.changeConfigForm['option'] = option;
                  if (option == 3) {
                    this.changeConfigForm['title'] = '查看更改配置报价信息';
                    this.isReport3 = true;
                  } else if (option == 4) {
                    this.changeConfigForm['title'] = '核算报告';
                    this.isReport3 = true;
                  }
                  else {
                    this.isChangeConfig = true;
                  }

                }
              })
        } else {
          if (option == 3) {
            this.changeConfigForm['title'] = '查看更改配置报价信息';
            this.isReport3 = true;
          } else if (option == 4) {
            this.changeConfigForm['title'] = '核算报告';
            this.isReport3 = true;
          }
          else {
            this.isChangeConfig = true;
          }
        }

      } else {
        let query = {
          quoteStoreId: this.quoteStoreId,
          quoteType: d.quoteType,
          option: option,
          customer:this.quoteForm.customer,
          parentPath: this.fullPath
        }
        this.$router.push({
          path: '/xsgs_process_manager/xsgs_quote_applove/quoteInfo/' + d.id,
          props: true,
          query: {...query}
        })
      }

    },
    /** 删除报价 **/
    deleteQuote(d) {
      let that = this;
      this.$confirm({
        content: `是否确认删除？`,
        onOk: () => {
          let newData = [...that.quoteForm.quoteList];
          if (d.option == 0) {
            newData = newData.filter(s => s.id != d.id);
            that.quoteForm.quoteList = newData;
          } else {
            newData.forEach(s => {
              if (s.id == d.id) {
                s["option"] = 2;
                s["hasChange"] = 1;
              }
            });
            that.quoteForm.quoteList = newData;
          }
        },
      });
    },
    /** 发布报价 **/
    async releaseQuote(d) {
      this.loading = true;
      let res = await request(CHECK_RELEASE, METHOD.POST, {id: d.id});
      this.loading = false;
      let tip = "请确认是否已经将新的报价信息全部同步到价格库？";
      let okTxt = "已同步";
      let cnlTxt = "未同步";
      if (d.quoteType == 'CONFIG') {
        tip = "请确认是否已经将需要更改的配置报价信息全部同步到价格库？";
      }

      let isNew = false;
      if (res.data.code == 9999) {
        isNew = true;
        tip = "年度：" + d.year + "，状态机：" + d.machineType + "是新增状态机价格，是否需要系统自动同步到价格库？";
        okTxt = "需要";
        cnlTxt = "不需要";
      }
      let releaseType = 0;
      this.$confirm({
        content: `${tip}`,
        okText:`${okTxt}`,
        cancelText:`${cnlTxt}`,
        onOk: () => {
          this.loading = true;
          if (isNew) {
            releaseType = 1;
          }
          request(DO_QUOTE_RELEASE, METHOD.POST, {id: d.id, releaseType:releaseType})
              .then(res => {
                this.loading = false;
                if (res.data.code == 0) {
                  this.$message.info("发布成功");
                  d["isRelease"] = 1;
                } else {
                  this.$message.error(res.data.msg);
                }
              })
        },
        onCancel:()=>{
          if (isNew) {
            this.$confirm({
              content: "请确认是否已经将新的报价信息全部同步到价格库",
              okText:"已同步",
              cancelText:"未同步",
              onOk: () => {
                this.loading = true;
                request(DO_QUOTE_RELEASE, METHOD.POST, {id: d.id, releaseType:0})
                    .then(res => {
                      this.loading = false;
                      if (res.data.code == 0) {
                        this.$message.info("发布成功");
                        d["isRelease"] = 1;
                      } else {
                        this.$message.error(res.data.msg);
                      }
                    })
              },
              onCancel:()=>{
              }
            });
          }
        }
      });
    },
    /** 查看核算报告 **/
    reportQuote(d) {
      this.sum = 0;
      if (d.quoteType == 'REF') {
        this.reportTitle = "报价机型较参考机型配置差异加减清单";
        this.isReport1 = true;
      } else if (d.quoteType == 'BASE') {
        this.reportTitle = "报价机型较基本型配置差异加减清单";
        this.isReport2 = true;
      } else if (d.quoteType == 'CONFIG') {
        this.reportTitle = "报价机型较基本型配置差异加减价";
        this.editQuote(d, 4);
        return;
      }
      this.reportForm = {...d};

      let remark2 = this.reportForm.remark2;
      if (!this.reportForm.hasChangeParts || this.reportForm.hasChangeParts == 0) {
        // 数据库获取
        this.isReportLoading = true;
        request(GET_XSGSPROCESSQUOTE_ID, METHOD.GET, {id: this.reportForm.id})
            .then(res => {
              this.isReportLoading = false;
              if (res.data.data != null) {
                this.reportForm = res.data.data;
                let newData = [];
                let total = 0;
                newData = this.coverReportData(d.quoteType,this.reportForm.partsList);
                newData.forEach(item => {
                  total += Number(item['revisePrice']?item['revisePrice']:item['valuePrice']);
                });
                this.reportForm["total"] = total + this.coverNumber(this.reportForm.powerDiff);
                this.sum = total + this.coverNumber(this.reportForm.powerDiff);
                this.reportForm.partsList = newData;
              }
            })
      } else {
        // 缓存拿
        this.isReportLoading = true;
        request(LIST_GETFROMCACHE, METHOD.GET, {id: this.reportForm.id, quoteStoreId: this.quoteStoreId})
            .then(res => {
              this.isReportLoading = false;
              if (res.data.data != null) {
                this.reportForm = res.data.data;
                let newData = [];
                let total = 0;
                newData = this.coverReportData(d.quoteType, this.reportForm.partsList);
                newData.forEach(item => {
                  total += Number(item['revisePrice']?item['revisePrice']:item['valuePrice']);
                });
                this.reportForm["remark2"] = remark2;
                this.reportForm["total"] = total + this.coverNumber(this.reportForm.powerDiff);
                this.sum = total + this.coverNumber(this.reportForm.powerDiff);
                this.reportForm.partsList = newData;
              }
            });
      }
    },
    /** 覆盖报表数据 **/
    coverReportData(quoteType, data) {
      let newData = [];
      let that = this;
      let delData = [];
      let editData =[];
      let addData = [];
      data.forEach(item => {
        if (item.isDelete == 1 || (item.subType=='选型件' && (item.sysIsSubtraction !='1' &&item.isSubtraction == '1'))) {
          // 删除 4-减少配置，已删除+新减配
          item['isTake'] = 4;
          delData.push({...item});
        } else if (that.isEmpty(item.sysPartsName)
            || (item.subType=='选型件' && item.sysIsSubtraction == '1' && item.isSubtraction != '1')) {
          // 新增 3-新增配置，新增+选型件减配转正常
          item['isTake'] = 3;
          addData.push({...item});
        } else if (item.sysSpecs != item.specs
            || (item.subType=='选型件' && item.sysValuePrice != item.valuePrice)
            || (item.subType=='选装件' && item.sysActualPrice != item.actualPrice)) {
          // 更改 2-更改配置 规格不一致+选型价差不一致+选装实际售价不一致
          item['isTake'] = 2;
          editData.push({...item});
        }
      });
      if (!that.isEmpty(editData)) {
        editData.forEach(s=>newData.push(s));
      }
      if (!that.isEmpty(delData)) {
        delData.forEach(s=>newData.push(s));
      }
      if (!that.isEmpty(addData)) {
        addData.forEach(s=>newData.push(s));
      }
      if (quoteType == 'REF') {
        newData.forEach(item => {
          if (item.isSubtraction != '1' && item.subType == '选型件') {
            item['actualPrice'] = item.valuePrice;
          }
          if (item.isDelete == 1) {
            item['actualPrice'] = 0;
          }

          if (item.sysIsSubtraction != '1' && item.subType == '选型件') {
            item['sysActualPrice'] = item.sysValuePrice;
          }

          item['valuePrice'] = this.coverNumber(item['actualPrice']) - this.coverNumber(item['sysActualPrice']);
          item['revisePrice'] = item.revisePrice?item.revisePrice:(this.coverNumber(item['actualPrice']) - this.coverNumber(item['sysActualPrice']));
        });
      } else if (quoteType == 'BASE') {
        newData.forEach(item => {
          if (item.subType == '选装件') {
            item['valuePrice'] = item.actualPrice;
            item['revisePrice'] = item.revisePrice?item.revisePrice:item.actualPrice;
          }

          if (item.isSubtraction == '1' && item.subType == '选型件') {
            item['valuePrice'] = item.actualPrice;
            item['revisePrice'] = item.revisePrice?item.revisePrice:item.actualPrice;
          }
        });
      }
      return newData;
    },
    /** 修改备注 **/
    changeRemark(d) {
      let newData = [...this.quoteForm.quoteList];
      newData.forEach(s => {
        if (s.id == d.id) {
          s['hasChange'] = 1;
          s['option'] = 1;
        }
      });
      this.quoteForm.quoteList = newData;
    },
    /** 新增信息 **/
    newDetail() {
      const newDataList = [...this.changeConfigForm.partsList]
      newDataList.push({
        id: this.changeConfigForm.partsList.length + Math.round(Math.random() * 1000) + 1,
        series: null,
        productModel: null,
        machineType: null,
        partsName: null,
        isTake: null,
        budgetedPrice: null,
        actualPrice: null,
        sysValuePrice: null,
        valuePrice: null
      });
      this.changeConfigForm.partsList = newDataList;
    },
    /** 删除信息 **/
    deleteDetail(d){
      let list = [...this.changeConfigForm.partsList];
      let newData = list.filter(item=>item.id != d.id);
      this.changeConfigForm.partsList = newData;
    },
    /** 上传 */
    handleUploadExcel(info) {
      this.isConfigLoading = true
      if (info.file.status !== 'uploading') {
        // this.getData()
      }
      if (info.file.status === 'done') {
        this.isConfigLoading = false
        this.$message.success(`${info.file.name} 导入成功`);
        let data = info.file.response.data.data;
        this.changeConfigForm.partsList = data;
        let errors = info.file.response.data.errors;
      } else if (info.file.status === 'error') {
        this.isConfigLoading = false
        this.$message.error(`${info.file.name} 导入失败`);
      }
    },
    /** 导出按钮操作 */
    handleDownloadTmp() {
      exportExcel(DOWNLOAD_TEMPLATE, {}, "更改配置批量导入模板.xlsx")
    },
    /** 客户校验 **/
    checkCustomer(isNotRemind){
      if (this.quoteForm.customer && this.changeConfigForm.customer && this.changeConfigForm.customer != this.quoteForm.customer) {
        if (!isNotRemind) {
          this.$message.error("客户与现有报价信息客户不一致")
        }
        return false;
      }
      return true;
    },
    /** 校验报价信息 **/
    checkQuoteForm() {
      if (this.isEmpty(this.quoteForm.quoteList)) {
        this.$success({
          title:`未维护报价状态机信息`,
          content: null,
          onOk: () => {
            return false;
          }
        });
      }
      return true;
    },
    /** 校验是否已全部发布 **/
    checkRelease(){
      if (this.form.nodeInfo.nodeCode == 'archive') {
        // 报价流程，归档节点提交校验是否已全部发布
        let quoteList = this.quoteForm.quoteList;
        let len = quoteList.length;
        let newList = quoteList.filter(s=>s.isRelease != 1);
        if (newList.length > 0) {
          this.$message.error("请完成所有报价的发布后，再进行提交完成");
          return false;
        }
      }
      return true;
    },
    /** 获取报价信息 **/
    getQuoteInfo() {
      if(this.reportForm.partsList != undefined && this.quoteForm.quoteList[0].partsList != undefined){
        this.reportForm.partsList.forEach(y => {
          this.quoteForm.quoteList[0].partsList.forEach(item => {
            if(item.id == y.id){
              item['revisePrice'] = y.revisePrice;
            }
          });
        });
      }
      return this.quoteForm;
    },
    /** 验证不是草稿 **/
    isNotDrafted() {
      return this.isView || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'drafted';
    },
    /** 是否发布 **/
    isArchive() {
      return !this.isView && this.form.nodeInfo.nodeCode && this.form.nodeInfo.nodeCode == 'archive';
    },
    /** 状态机配置报价保存 **/
    saveChangeConfig() {
      let msg = this.checkConfigForm();
      if (msg) {
        this.$message.error(msg);
        return;
      }
      // 计算整机开票价
      let partsList = this.changeConfigForm.partsList.filter(s=>s.machineType);
      let unitAmount = 0;
      let machineType = "";
      partsList.forEach(item => {
        unitAmount += Number((item.valuePrice ? item.valuePrice : 0));
        if (machineType.length == 0) {
          machineType = item.machineType;
        } else {
          machineType += ";" + item.machineType;
        }
      });

      if (this.changeConfigForm.option == 1) {
        let id = this.changeConfigForm.id;
        this.quoteForm.quoteList.forEach(item => {
          if (item.id == id) {
            item["unitAmount"] = unitAmount;
            item["machineType"] = machineType;
            item["partsList"] = partsList;
            item["customer"] = this.changeConfigForm.customer;
            item["customerName"] = this.changeConfigForm.customerName;
            item["hasChange"] = 1;
            item["hasChangeParts"] = 1;
            item["noticeDate"] = this.changeConfigForm.noticeDate;
            item["noticeNo"] = this.changeConfigForm.noticeNo;
            item["remark2"] = this.changeConfigForm.remark2;
          }
        })
      } else {
        this.changeConfigForm["quoteType"] = "CONFIG";
        this.changeConfigForm["unitAmount"] = unitAmount;
        this.changeConfigForm["machineType"] = machineType;
        this.changeConfigForm["partsList"] = partsList;
        this.changeConfigForm["hasChange"] = 1;
        this.changeConfigForm["hasChangeParts"] = 1;
        this.quoteForm.quoteList.push({...this.changeConfigForm});
      }
      if (!this.quoteForm.customer) {
        this.quoteForm.customer = this.changeConfigForm.customer;
      }
      // this.initChangeConfigForm();
      this.isChangeConfig = false;
    },
    /** 覆盖报价类型 **/
    coverQuoteType(quoteType) {
      if (quoteType == 'REF') {
        return "参考原机型报价";
      } else if (quoteType == 'BASE') {
        return "参考基本型报价";
      } else if (quoteType == 'CONFIG') {
        return "更改配置报价";
      } else {
        return "未知";
      }
    },
    /** 客户列表 **/
    cutList(list, start, end) {
      if (!list || list.length == 0) {
        return [];
      }
      let len = end - start;
      if (list.length <= len) {
        return list;
      } else {
        return list.slice(start, end);
      }
    },
    /** 初始化配置 **/
    initChangeConfigForm() {
      this.changeConfigForm = {
        customer: undefined,
        customerName: undefined,
        noticeDate: undefined,
        option: 0,
        title:null,
        hasChange: 1,
        partsList: []
      };
    },
    /** 校验请求 **/
    checkRequired(form, field, isNotRemind) {
      if (this.formRule[field] && this.formRule[field].nodeCode == this.form.nodeInfo.nodeCode) {
        let value = form[field];
        if (!value) {
          this.formRule[field].border = this.requiredBorder;
          if (!isNotRemind) {
            this.$message.error(this.formRule[field].msg);
          }
          return false;
        }
      }
      this.formRule[field].border = this.normalBorder;
      return true
    },
    /** 金额输入 **/
    inputMoney(d, field) {
      this.$nextTick(() => {
        let val = event.target.value ? event.target.value.toString() : "";
        if (val == ".") {
          val = '';
        } else {
          val = val.replace(/[^\d.]/g, ""); //清除"数字"和"."以外的字符
          val = val.replace(/\.{2,}/g, ""); //只保留第⼀个. 清除多余的
          val = val.replace(/^0+\./g, '0.');
          val = val.match(/^0+[1-9]+/) ? val = val.replace(/^0+/g, '') : val
          val = (val.match(/^\d*(\.?\d{0,2})/g)[0]) || ''
        }
        d[field] = val;
      });
    },
    /** 金额覆盖 **/
    coverMoney(d, field) {
      let val = event.target.value ? event.target.value.toString() : "";
      if (val) {
        if (val.includes(".")) {
          if (val.endsWith(".")) {
            val = val + "00";
          } else {
            let point = val.split(".")[1];
            let intValue = val.split(".")[0];
            point += "000";
            val = intValue + "." + point.substr(0, 2);
          }
        } else {
          val = val + ".00";
        }
        d[field] = val;
      }
    },
    /** 金额覆盖 **/
    coverNumber(num) {
      return !num ? 0 : Number(num);
    },
    /** 颜色覆盖 **/
    coverRed(num) {
      return this.coverNumber(num) < 0 ? 'color:red !important' : '';
    },
    /** 校验配置 **/
    checkConfigForm() {
      let msg = null;
      // 客户
      if (!this.checkRequired(this.changeConfigForm, "customer", true)) {
        msg = this.formRule['customer'].msg;
      }
      if (!msg && !this.checkCustomer(true)) {
        msg = "客户与现有报价信息客户不一致";
      }
      // 通知日期
      if (!msg && !this.checkRequired(this.changeConfigForm, "noticeDate", true)) {
        msg = this.formRule['noticeDate'].msg;
      }
      // 通知单号
      if (!msg && !this.checkRequired(this.changeConfigForm, "noticeNo", true)) {
        msg = this.formRule['noticeDate'].msg;
      }
      if (!msg && this.changeConfigForm.partsList.length == 0) {
        msg = "请添加配置信息";
      }
      return msg;
    },
    showIsTakeSelect(e) {
      // 先隐藏
      let takeSelects = document.getElementsByClassName("isTaskSelect");
      if (takeSelects && takeSelects.length > 0) {
        for (let i = 0; i < takeSelects.length; i++) {
          let item = takeSelects[i];
          item.getElementsByTagName('ul')[0].style.display = "none";
        }
      }
      if (e.target.classList.contains('istake')) {
        let target = e.srcElement;
        while (!target.classList.contains('isTaskSelect')) {
          target = target.parentNode;
        }
        if (target) {
          target.getElementsByTagName('ul')[0].style.display = "block";
        }
      }
    },
    isNotVE() {
      return !this.form.nodeInfo || !this.form.nodeInfo.nodeCode || this.form.nodeInfo.nodeCode != 'value_engine_approval';
    },
    /** 价格自动求和 **/
    unitAmountBlurs(){
      let price = 0
      this.reportForm.partsList.forEach(y => {
        price += Number(y['revisePrice']);
      })
      this.sum = price + this.coverNumber(this.reportForm.powerDiff);
    },
    /** 编辑保存 **/
    handleSave() {
      var that =this
      if (!this.reportForm.hasChangeParts || this.reportForm.hasChangeParts == 0) {
        that.save(UPDATE_QUOTE_MESSAGE);
      }else {
        that.save(UPDATE_QUOTE_MESSAGES)
      }
    },
    /** 核算报告报价保存弹窗 **/
    save(url){
      var that =this
      that.$confirm({
        content: `是否保存当前数据？`,
        onOk: () => {
          request(url, METHOD.POST, {...this.reportForm})
              .then(res => {
                if (res.data.code == 0) {
                  that.isReport1 = false;
                  this.$message.info("保存成功");
                } else {
                  this.$message.error(res.data.msg);
                }
              })
        },
      });
    },
    /** 编辑核算报告报价 **/
    editQuotes(d,type){
      if(type == 1){
        this.isEdit = true;
        this.isLook = false;
      }else {
        this.isEdit = false;
        this.isLook = true;
      }
      this.reportQuote(d);
    }
  }
}
</script>

<style scoped>
/deep/ .fontSmall td,
.fontSmall th,
.fontSmall input,
.fontSmall textarea,
.fontSmall select,
.fontSmall a,
.fontSmall button {
  font-size: 12px !important;
}

/deep/ .fontSmall tr,
.fontSmall td,
.fontSmall th {
  height: 22px !important;
}
/deep/ .fontMiddle td,
.fontMiddle th,
.fontMiddle input,
.fontMiddle textarea,
.fontMiddle select,
.fontMiddle a,
.fontMiddle button {
  font-size: 14px !important;
}

/deep/ .fontMiddle tr,
.fontMiddle td,
.fontMiddle th {
  height: 28px !important;
}

/deep/ .fontLarge td,
.fontLarge th,
.fontLarge input,
.fontLarge textarea,
.fontLarge select,
.fontLarge a,
.fontLarge button {
  font-size: 18px !important;
}

/deep/ .fontLarge tr,
.fontLarge td,
.fontLarge th {
  height: 33px !important;
}
/deep/ .ant-select-selection__placeholder {
  padding-left: 3px;
  width: 100%;
}

/deep/ .ant-table .ant-select-selection-selected-value {
  padding-left: 3px;
}

/deep/ .ant-table .ant-select-selection__rendered {
  margin: 0 !important;
}

/deep/ .ant-table input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

/deep/ .ant-table input[type="number"] {
  -moz-appearance: textfield !important;
}

/deep/ .annexTd > div {
  float: left !important;
}

.changeConfigInfo td, th {
  text-align: left;
  border: 0.5px solid #464646;
  /*padding: 0 !important;*/
  background-clip: padding-box;
}

.changeConfigInfo th {
  font-weight: normal;
  background-color: #bdd7ee;
}

/deep/ .changeConfigInfo td > input, textarea {
  /*font-size: 16px !important;*/
}

/deep/ .ant-table > tr {
  height: 33px;
}

/deep/ .changeConfigInfo .ant-table > tr > td input {
  /*border-radius: 0px;*/
  /*padding: 3px;*/
  height: 33px;
  /*border: none !important;*/
}

/deep/ .changeConfigInfo .ant-table > tr > td a {
  margin-left: 5px;
}

/deep/ .changeConfigInfo .ant-table > tr > th {
  text-align: center;
  font-weight: bold;
  background-color: #bdd7ee;
  border: 0.5px solid #464646;
  background-clip: padding-box;
}

/deep/ .changeConfigInfo .ant-table > tr > td {
  text-align: left;
  border: 0.5px solid #464646;
  /*padding: 0 !important;*/
  background-clip: padding-box;
}

/deep/ .changeConfigInfo .ant-table .ant-select-selection {
  /*border-radius: 0px !important;*/
  /*border: none !important;*/
}

/deep/ .changeConfigInfo .ant-table .ant-select-selection__rendered {
  margin-left: 0px !important;
}

/deep/ .changeConfigInfo .ant-select-selection__placeholder {
  padding-left: 3px;
  width: 100%;
}

/deep/ .changeConfigInfo .ant-table .ant-select-selection-selected-value {
  padding-left: 3px;
}

/deep/ .changeConfigInfo .ant-table .ant-select-selection__rendered {
  margin: 0 !important;
}

/deep/ .changeConfigInfo .ant-table textarea {
  margin: 0 !important;
  padding: 0 !important;
}

/deep/ .changeConfigInfo .ant-table .ant-calendar-picker-input ant-input {
  /*font-size: 16px !important;*/
}

/deep/ .isTaskSelectValue {
  height: 33px;
  width: 100%;
  line-height: 30px;
}

/deep/ .isTaskSelectValue:hover {
  border: 1px solid lightblue;
  border-radius: 3px;
}
/deep/ .takeIconImg{
  display: inline-block;
  width: 14px;
  height: 14px;
  background-size: 100% 100% !important;
  background-position: center center !important;
}
/deep/ .takeTriangleBlack {
  background: url("~@/assets/img/triangleBlack.png") no-repeat;
}
/deep/ .takeTriangleWhite {
  background: url("~@/assets/img/triangleWhite.png") no-repeat;
}

/deep/ .takeCircleBlack {
  background: url("~@/assets/img/circleBlack.png") no-repeat;
}

/deep/ .takeCircleWhite {
  background: url("~@/assets/img/circleWhite.png") no-repeat;
}

/deep/ .changeConfigInfo input[disabled=disabled], textarea[disabled=disabled], .ant-select-disabled {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.65) !important;;
}

/deep/ .changeConfigInfoDisabled td {
  background-color: #f5f5f5 !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

/deep/ .quoteReportModelStyle .ant-modal-body {
  padding: 10px 16px
}
/deep/ .quoteReportModelStyle .quoteReportInfo tr {
  height: 25px;
}
/deep/ .quoteReportModelStyle .quoteReportInfo td, th {
  border: 0.5px solid #464646;
  padding: 0 3px !important;
  text-align: center !important;
}
/deep/ .quoteReportModelStyle .quoteReportInfo td > .tipRow {
  text-align: center;
  float: left;
  width: 100%;
  height: 20px;
}

/deep/ .quoteReportModelStyle .quoteReportInfo td > .tipRow > div {
  display: inline-block;
}
/deep/ .quoteReportModelStyle .quoteReportInfo td > .tipRow > div > .tipIcon {
  margin-left: 2px;
  margin-top: 4px;
}
/deep/ .quoteReportModelStyle .quoteReportInfo td > .tipRow > div > span {
  float: left;
}
/deep/ .quoteReportModelStyle .quoteReportInfo .ant-table > tr > th {
  text-align: center;
  font-weight: bold;
  background-color: #bdd7ee;
}

/deep/ .isTaskSelect {
  height: 33px;
  line-height: 33px;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
}

/deep/ .isTaskSelect ul {
  width: 100%;
  margin: 0px;
  padding: 0px;
  position: absolute;
  background-color: white;
  border-radius: 3px;
  z-index: 999;
  border: 1px solid #d9d9d9;
}

/deep/ .isTaskSelect li {
  list-style: none;
  background-color: white;
  border-bottom: 1px solid #d9d9d9
}

/deep/ .quoteReportModelStyle .ant-modal-header {
  padding: 5px 24px !important;
}
/deep/ .quoteReportModelStyle .ant-modal-close-x {
  width: 33px !important;
  height: 33px !important;
  line-height: 33px !important;
}
</style>

