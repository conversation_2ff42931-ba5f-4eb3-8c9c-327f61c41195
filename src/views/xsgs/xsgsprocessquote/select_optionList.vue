<template>
        <table style="width: 100%; border: 1px solid #f0f0f0;" >
            <tbody class="ant-table-tbody">
            <tr>
                <td colspan="4">本体配置</td>
            </tr>
            <tr>
                <td>名称</td>
                <td>规格</td>
                <td>名称</td>
                <td>规格</td>
            </tr>
            <tr v-for="item in mainList"  v-bind:key="item.id" >
                <td>{{item.partsName}}</td>
                <td>{{item.specs}}</td>
                <td>{{item.secondPartsName}}</td>
                <td>{{item.secondSpecs}}</td>
            </tr>
            <tr>
                <td colspan="4">后处理配置</td>
            </tr>
            <tr>
                <td>名称</td>
                <td>规格</td>
                <td>名称</td>
                <td>规格</td>
            </tr>
            <tr v-for="item in proceList"  v-bind:key="item.id" >
                <td>{{item.partsName}}</td>
                <td>{{item.specs}}</td>
                <td>{{item.secondPartsName}}</td>
                <td>{{item.secondSpecs}}</td>
            </tr>
            <tr v-if="this.childOption[4]">
              <td colspan="4">整机配置</td>
            </tr>
            <tr v-if="this.childOption[4]">
              <td>名称</td>
              <td>规格</td>
              <td>名称</td>
              <td>规格</td>
            </tr>
            <tr v-for="item in zjList" v-bind:key="item.id">
              <td>{{ item.partsName }}</td>
              <td>{{ item.specs }}</td>
              <td>{{ item.secondPartsName }}</td>
              <td>{{ item.secondSpecs }}</td>
            </tr>
            </tbody>
        </table>

    
</template>

<script>
    import {LISTTYPE_XSGSPARTSSETTING,SELECTBASICLIST_XSGSALLOWANCEMAIN} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "select_optionList",
        props:{
            childOption:Array,
            masterId:String,
        },
        data() {
            return {
                mainList:[],
                proceList:[],
                zjList:[],
                searchForm:{},
                proceForm:{},
            }
        },
        mounted() {
            this.checkName();
        },
        methods: {
            getLinshiBasicData: function () {
                this.searchForm.platformName=this.childOption[0]
                this.searchForm.series=this.childOption[1]
                this.searchForm.customer=this.childOption[2]
                this.searchForm.productModel=this.childOption[3]
                this.searchForm.version = this.childOption[4]
                this.searchForm.mainType="本体配置";
                request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.searchForm)
                    .then(res => {
                        this.mainList=res.data.data
                        // 结果为空时添加提示
                        if (!this.mainList.length) this.$message.warning('本体配置数据为空');
                    })
                //后处理配置
                this.proceForm.platformName=this.childOption[0]
                this.proceForm.series=this.childOption[1]
                this.proceForm.customer=this.childOption[2]
                this.proceForm.productModel=this.childOption[3]
                this.proceForm.version = this.childOption[4]
                this.proceForm.mainType="后处理配置";
                request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.proceForm)
                    .then(res => {
                        this.proceList=res.data.data
                        if (!this.proceList.length) this.$message.warning('后处理配置数据为空');
                      this.getZjList()
                    })
            },
          /** 获取整机配置数据 **/
          getZjList() {
            //整机配置
            this.proceForm.platformName = this.childOption[0]
            this.proceForm.series = this.childOption[1]
            this.proceForm.customer = this.childOption[2]
            this.proceForm.productModel = this.childOption[3]
            this.proceForm.version = this.childOption[4]
            this.proceForm.mainType = "整机配置";
            request(SELECTBASICLIST_XSGSALLOWANCEMAIN, METHOD.POST, this.proceForm)
                .then(res => {
                  this.zjList = res.data.data
                })
          }
        }
    }
</script>

<style scoped>
    td{
        border: 1px solid #f0f0f0;
        text-align: center;
    }
    .ant-table-thead > tr > th, .ant-table-tbody > tr >/deep/ td {
        padding: 1px 1px !important;
        overflow-wrap: break-word !important;
    }

</style>