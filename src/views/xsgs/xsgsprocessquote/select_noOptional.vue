<template>
    <div>
        <a-table
                :columns="noOptionColumns"
                rowKey="id"
                class="quoteNoPartsList"
                :pagination="false"
                :data-source="noOptionData"
                :scroll="{ x: 1000 }"
                size="small"
                bordered
                :customRow="onRowClick"
                :loading="loading">
            <template slot-scope="text">
                <div class="textTd">{{text}}</div>
            </template>

            <template slot="partsName" slot-scope="text" >
                {{text}}
            </template>

            <template slot="specs" slot-scope="text" >
                {{text}}
            </template>

            <template slot="supplierBak" slot-scope="text" >
                {{text}}
            </template>

            <template slot="budgetedPrice" slot-scope="text" >
                {{text}}
            </template>

            <template slot="actualPrice" slot-scope="text, record" >
                <a-input-number   ref="actualchick"
                                  v-model="record.actualPrice"
                                  :formatter="value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                                  :parser="value => `${value}`.replace(/\$\s?|(,*)/g, '')"
                                  :precision="2"
                                  :step="0.01"
                                  v-if="!isView"
                                  style="width: 100%"
                                  @blur="blursActualPrice(record)"
                                  @change="handleChange(record.actualPrice, record.id,record.actualPrice)"

                />
                <span v-if="isView">{{text}}</span>
            </template>

        </a-table>
    </div>
</template>

<script>
    import {
        LIST_XSGSALLOWANCEPARTS,
        getUuid,} from "@/services/api/xsgs";
    import {METHOD, request} from "@/utils/request";
    export default {
        name: "select_noOptional",
        props:{
            masterId:String,
            optionSubtotal:Number,
            searchForm:Object,
            year:Number,
            productModelJh:String,
            isView:Boolean
        },
        data() {
            return {
                loading: false,
                noOptionForm: {
                    masterId:null,
                },
                selectedRowKeys: [],
                editingKey: '',
                form:{},
                count:0,
                noInList:[],
                ids:[],
                allForm:{},
                //减配
                noOptionData: [],
                //减配配置
                noOptionPagination: {
                    total: 0,
                    current: 1,
                    size: '10',
                    showSizeChanger: true, // 是否可以改变 size
                    showQuickJumper: true, // 是否可以快速跳转至某页
                    pageSizeOptions: ['10', '20', '30', '40', '50'],
                    showTotal: (total) =>
                        `共 ${total} 条 第${this.noOptionPagination.current}/${Math.ceil(
                            total / this.noOptionPagination.size
                        )}页`, // 显示总数
                    onChange: (page, size) => this.onNoOptionPageChange(page, size), // 页码改变的回调
                    onShowSizeChange: (current, size) =>
                        this.onNoOptionSizeChange(current, size) // 改变每页数量时更新显示
                },
                noOptionColumns: [
                    {title: '类型', dataIndex: 'subType', scopedSlots: { customRender: 'subType' }},
                    {title: '部件名称', dataIndex: 'partsName', scopedSlots: { customRender: 'partsName' }},
                    {title: '规格', dataIndex: 'specs', scopedSlots: { customRender: 'specs' }},
                    {title: '供应商', dataIndex: 'supplierBak',scopedSlots: { customRender: 'supplierBak' }},
                    {title: '指导售价', dataIndex: 'budgetedPrice',scopedSlots: { customRender: 'budgetedPrice' }},
                    {title: '实际售价', dataIndex: 'actualPrice',scopedSlots: { customRender: 'actualPrice' }},
                ],
                xxListData:[],
                supplierBakListData:[],
                AllListData:[],


            }
        },
        mounted() {
            this.checkName();
        },
        methods: {
            getNoOptionalcDataList(){
                return this.noOptionData;
            },
            getNoOptionalclinshiList(){
                return this.noOptionData;
            },
            //暂时list
            getNoOptionList(){
                this.loading = false
                this.noOptionData = [];
            },

            setNoOptionalcDataList(data){
                 this.noOptionData = data
                 this.noOptionPagination.total = parseInt(this.noOptionData.length)
            },
            getNoPartsListNewAll(data){
                // this.noOptionData=this.$parent.$parent.$parent.$parent.$parent.getNewList();
                this.noOptionData=data;
                this.noOptionData.forEach(item =>{
                    item.editable=true;
                })
                this.noOptionPagination.total = parseInt(this.noOptionData.length)

            },
            //选型减配
            getNoOptionData(){
                if(this.masterId!=null) {
                    this.loading = true
                    //条件
                    this.noOptionForm.isSubtraction = "1";
                    this.noOptionForm.masterId = this.masterId;
                    request(LIST_XSGSALLOWANCEPARTS, METHOD.POST, {...this.noOptionForm})
                        .then(res => {
                            this.loading = false
                            //为空为参考机型
                            if(this.searchForm.id===null){
                                res.data.data.forEach(item =>{
                                    request(getUuid, METHOD.GET).then(res => {
                                        item.id=res.data.data
                                    })
                                    item.masterId=null
                                })
                                this.noOptionData = res.data.data
                            }else{
                                this.noOptionData = res.data.data
                            }
                            //默认进入编辑状态
                            const newData = [...this.noOptionData];
                            this.noOptionData.forEach(item=>{
                                item['sysId']=item.id;
                                item['sysBudgetedPrice']=item.budgetedPrice;
                                item['sysActualPrice']=item.actualPrice;
                                item['sysValuePrice']=item.valuePrice;
                                item['sysStandardPrice']=item.standardPrice;
                                item['sysStandardSpecs']=item.standardSpecs;
                                item['sysSpecs']=item.specs;
                                item['sysSupplierBak']=item.supplierBak;
                                item['sysPartsName']=item.partsName;
                                item['sysIsSubtraction']=item.isSubtraction;
                                item['sysSubType']=item.subType;
                                item['isDelete']=0;
                            });
                            this.noOptionData = newData;
                        })
                }
            },
            //基本型减配
            onNoOptionPageChange(page, size) {
                this.noOptionPagination.current = page;
                this.noOptionPagination.size = size.toString();
                this.getNoOptionData();

            },
            onNoOptionSizeChange(current, size) {
                this.noOptionPagination.current = 1;
                this.noOptionPagination.size = size.toString();
                this.getNoOptionData();
            },
            //获取改变值
            handleChange (value, id, column) {
                let newData=[];
                newData = [...this.noOptionData]
                const target = newData.find(item => id === item.id)
                if (target) {
                    target[column] = value
                    this.noOptionData = newData
                }
            },
            //计算减配信息
            blursActualPrice(){
                let newData=[];
                newData = [...this.noOptionData]
                //当前页码的选型件总实际价格设置为0
                let lectotypeDifTotal =0;
                newData.forEach(item =>{
                    if(item.subType==='选型件'){
                        lectotypeDifTotal=this.NumberAdd(lectotypeDifTotal,item.actualPrice)
                    }
                    item.editable=true;
                    this.noOptionData = newData
                })
                //选型选装小计
                let xxxzTotal=this.optionSubtotal;

                //计算选配件合计=选型选装件小计+选型减配小计(刚开始无选型减配)
                this.$emit('chooseBasicAmountToal',this.NumberAdd(xxxzTotal,lectotypeDifTotal))
                //选型减配小计
                this.$emit('lectotypeSubtotal',lectotypeDifTotal)

            },
            resetId(){
                this.optionalData.forEach(item =>{
                    request(getUuid, METHOD.GET).then(res => {
                        item.id=res.data.data
                    })
                    item.masterId=null
                })
            }
        }
    }
</script>

<style scoped>
    /deep/ .quotePartsList input[disabled=disabled],textarea[disabled=disabled],.ant-select-disabled {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;;
    }
    /deep/ .quotePartsList .disabelTd {
        background-color: #f5f5f5 !important;
        color: rgba(0, 0, 0, 0.65) !important;
    }
    /deep/ .quoteNoPartsListDisabeld td {
        width: 100%;
        height: 100%;
        background-color: #f5f5f5 !important;
    }
</style>
