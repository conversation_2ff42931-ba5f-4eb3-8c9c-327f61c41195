import Mock from 'mockjs'
import {SSO_LOGIN} from "@/services/api/user";
import {METHOD} from "@/utils/request";

if (process.env.NODE_ENV != 'production') {
    Mock.mock(SSO_LOGIN, METHOD.GET, () => {
        return {"code": "200", "message": "成功", "data": "yc90036062"}
    })

    // Mock.mock(GET_MENUS, METHOD.POST, () => {
    //     let result = {}
    //     result.code = 0
    //     result.data = [{
    //         router: 'root',
    //         children: [
    //             {
    //                 router: 'sys',
    //                 children: ['sys_client', 'sys_dict', 'sys_dict_item'],
    //             }, {
    //                 router: 'mtu',
    //                 children: ['mtu_salary']
    //             }, {
    //                 router: 'fire_check',
    //                 children: ['fc_equipment', 'fc_log']
    //             }
    //         ]
    //     }]
    //     return result
    // })
// 设置全局延时
    Mock.setup({
        timeout: '200-400'
    })
}
