import enquireJs from 'enquire.js'

export function isDef (v){
  return v !== undefined && v !== null
}

/**
 * Remove an item from an array.
 */
export function remove (arr, item) {
  if (arr.length) {
    const index = arr.indexOf(item)
    if (index > -1) {
      return arr.splice(index, 1)
    }
  }
}
export function getObsFileUrl(path, name) {
  return `https://${process.env.VUE_APP_BUCKET_NAME}.${process.env.VUE_APP_OBS_ENDPOINT}/${path}${name}`
}

export function isRegExp (v) {
  return _toString.call(v) === '[object RegExp]'
}

export function enquireScreen(call) {
  const handler = {
    match: function () {
      call && call(true)
    },
    unmatch: function () {
      call && call(false)
    }
  }
  enquireJs.register('only screen and (max-width: 767.99px)', handler)
}

const _toString = Object.prototype.toString

//加法
export function  NumberAdd(arg1, arg2){
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length
  } catch (e) {
    r1 = 0
  }
  try {
    r2 = arg2.toString().split(".")[1].length
  } catch (e) {
    r2 = 0
  }
  m = Math.pow(10, Math.max(r1, r2))
  n = (r1 >= r2) ? r1 : r2;
  return ((arg1 * m + arg2 * m) / m).toFixed(n);
}

//减法
export function  Subtr(arg1, arg2) {
  var r1, r2, m, n;
  try { r1 = arg1.toString().split(".")[1].length } catch (e) { r1 = 0 }
  try { r2 = arg2.toString().split(".")[1].length } catch (e) { r2 = 0 }
  m = Math.pow(10, Math.max(r1, r2));
  n = (r1 >= r2) ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

//乘法
export function Ride(arg1, arg2) {
  return (arg1 * arg2).toFixed(2);
}

//除法
export function Divide(arg1, arg2) {
  return (arg1 / arg2).toFixed(2);
}

export function onRowClick() {
  return{
    on:{
      click:(e)=>{
        let oldLists = document.querySelectorAll(".row--current");
        if (oldLists) {
          for (let i = 0; i < oldLists.length; i++) {
            oldLists[i].classList.remove("row--current")
          }
        }
        let currTr = e.target.parentNode;
        let keyId = currTr.getAttribute("data-row-key");
        if(keyId) {
          let trs = document.querySelectorAll('tr[data-row-key="'+ keyId +'"]');
          for (let i = 0; i < trs.length; i++) {
            let tds = trs[i].querySelectorAll("td");
            for (let i = 0; i < tds.length; i++) {
              tds[i].classList.add("row--current")
            }
          }
        }
      }
    }
  }
}

/**
 * 创建blob对象，并利用浏览器打开url进行下载
 * @param data 文件流数据
 * @param fileName  文件名称
 * @param contentType 文件类型
 */
export function downloadFile(data, fileName, contentType) {
  let blob = new Blob([data], { type: contentType });
  let url = window.URL.createObjectURL(blob);
  // 打开新窗口方式进行下载
  // 以动态创建a标签进行下载
  if (window.navigator.msSaveBlob) {
    // for ie 10 and later
    try {
      window.navigator.msSaveBlob(blob, fileName);
    } catch (e) {
      this.message.showErrorMessage(e);
    }
  } else {
    // 其他浏览器
    let a = document.createElement('a');
    a.href = url;
    a.download = fileName; // 设置行为为下载而不是预览
    let evt = document.createEvent('MouseEvents'); // 解决firefox手动触发点击事件无效
    evt.initEvent('click', true, true);
    a.dispatchEvent(evt);
    window.URL.revokeObjectURL(url);
  }
}

export function isEmpty(obj) {
  return !obj||obj.length == 0;
}


export function cutList(list, start, end) {
  if (!list || list.length == 0) {
    return [];
  }
  let len = end - start;
  if (list.length <= len) {
    return list;
  } else {
    return list.slice(start, end);
  }
}

export function prefixZero(val, len) {
  if (!val || !len || val.length > len) {
    return val;
  }
  return ('000000000000000000' + val).substr((0-len));
}
export function copy(obj) {
  return JSON.parse(JSON.stringify(obj));
}

