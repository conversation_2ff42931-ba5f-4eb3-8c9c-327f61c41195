import store from "@/store";
import {METHOD, request} from "@/utils/request";
import {GET_USER_AUTH} from "@/services/api/xsgs";

export async function getUserAuth(field) {
  let auth = store.getters['account/userAuth'];
  // 权限不存在，接口重新获取
  if (!auth || !auth[field]) {
    let res = await getByApi(field);
    let authList = res.data.data;
    auth[field] = authList;
    store.commit('account/setUserAuth', auth);
  }
}

/**
 * 覆盖权限表格字段
 * @param field 权限关键字
 * @param columms 表格字段
 * @param keyField 表格绑定字段 dataIndex或者field
 * @returns {Promise<[]>}
 */
export function coverAuthColumn(field, columms, keyField='dataIndex') {
  let auth = store.getters['account/userAuth'];
  if (!auth) {
    return false;
  }
  let list = auth[field];
  if (!list) {
    list = [];
  }
  return coverTableColumn(columms, list, keyField);
}

export async function getByApi(field) {
  return request(GET_USER_AUTH, METHOD.GET, {field})
}

export function coverTableColumn(columns, authList, keyField) {
  if (!columns || columns.length == 0) {
    return columns;
  }
  if (!authList) {
    authList = [];
  }
  let newData = [];
  columns.forEach(s=>{
    if (s['exclude'] == true) {
      newData.push(s);
      return true;
    }
    if (s.children && s.children.length > 0) {
      let child = coverTableColumn(s.children, authList, keyField);
      if (child && child.length > 0) {
        s['children'] = child;
        newData.push(s);
      }
    } else {
      if (authList.findIndex(k=>k===s[keyField]) != -1) {
        newData.push(s);
      }
    }
  });
  return newData;

}


/**
 * 校验页面功能
 * @param field
 * @param columms
 * @param keyField
 * @returns {Promise<[]>}
 */
export function getPfList(field) {
  let auth = store.getters['account/userAuth'];
  if (!auth) {
    return [];
  }
  return auth[field];
}

