import Cookie from 'js-cookie'
import {GET_TOKEN} from "@/services/api/user";
import {refreshToken} from "../services/user";
import {setAuthorization} from "./request";
// 401拦截
const resp401 = {
    /**
     * 响应数据之前做点什么
     * @param response 响应对象
     * @param options 应用配置 包含: {router, i18n, store, message}
     * @returns {*}
     */
    onFulfilled(response, options) {
        const {message} = options
        if (response.code === 401) {
            message.error('无此权限')
        }
        return response
    },
    /**
     * 响应出错时执行
     * @param error 错误对象
     * @param options 应用配置 包含: {router, i18n, store, message}
     * @returns {Promise<never>}
     */
    onRejected(error, options) {
        const {message} = options
        const {response} = error
        if (response.status === 401) {
            message.error('无此权限')
        }
        return Promise.reject(error)
    }
}

const resp403 = {
    onFulfilled(response, options) {
        const {message} = options
        if (response.code === 403) {
            message.error('请求被拒绝')
        }
        return response
    },
    onRejected(error, options) {
        const {message} = options
        const {response} = error
        if (response.status === 403) {
            message.error('请求被拒绝')
        }
        return Promise.reject(error)
    }
}
/**
 * 统一异常处理
 * @type {{onFulfilled(*): *, onRejected(*=, *): Promise<never>}}
 */
const resCommon = {
    onFulfilled(response) {
        return response
    },
    onRejected(error, options) {
        const {message} = options
        const {response} = error
        //如果返回的异常，直接弹出错误提示
        if (response.data.code && response.data.code != 0) {
            if (response.data.msg) {
                message.error(response.data.msg)
            } else {
                message.error('未知错误')
            }
        }
        return Promise.reject(error)
    }
}
const reqCommon = {
    /**
     * 发送请求之前做些什么
     * @param config axios config
     * @param options 应用配置 包含: {router, i18n, store, message}
     * @returns {*}
     */
    async onFulfilled(config, options) {
        const {message} = options
        const {url, xsrfCookieName} = config

        if (url.indexOf('ssologin') === -1 && url.indexOf(GET_TOKEN) === -1 && xsrfCookieName && !Cookie.get(xsrfCookieName)) {
            let res
            try {
                res = await refreshToken(localStorage.getItem('refresh_token'))
            } catch (e) {
                message('刷新 token 失败：' + e)
            }
            setAuthorization({
                token: res.data.access_token,
                refresh_token: res.data.refresh_token,
                expireAt: res.data.expires_in
            })
        }
        if (Cookie.get(xsrfCookieName) && config.url.indexOf('myhuaweicloud.com') == -1) {
            config.headers.Authorization = Cookie.get(xsrfCookieName)
        }
        return config
    },
    /**
     * 请求出错时做点什么
     * @param error 错误对象
     * @param options 应用配置 包含: {router, i18n, store, message}
     * @returns {Promise<never>}
     */
    onRejected(error, options) {
        const {message} = options
        message.error(error.message)
        return Promise.reject(error)
    }
}

export default {
    request: [reqCommon], // 请求拦截
    response: [resp401, resp403, resCommon] // 响应拦截
}
