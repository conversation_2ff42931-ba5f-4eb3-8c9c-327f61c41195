import JSEncrypt from 'jsencrypt'

const app =
    "-----BEGIN PUBLIC KEY-----\n" +
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtvRCw7jYs/Ee+mkYz78fqNN9A2AaukFY\n" +
    "0UuHI4Whwm97GTBHYiqjG09XzXON5jG8oacUFPETSasU35/Nq+TOZ52I/7EA33HDUuJGn0EPIA1z\n" +
    "E2yalnqOfHS9stg/hXpAZm/LuVgq7wInNPnah/vXwdnzX+V5XHSDFFVgVvwyYAjgf5LMttTd8TAv\n" +
    "yg0aFspt+uEMskXRvDXpep45tYbpXg/JxDqioKWvFylGzlJAQwC58di9FoIIOg+ytDjPE+r9HXQq\n" +
    "EH9RvqjNQMnMPWelAK3N8+3YyALvh7NUVR1jczA3A060XWgO49ycz4goL5UQeq0aiInpPct2Gips\n" +
    "Fh5huQIDAQAB\n" +
    "-----END PUBLIC KEY-----"
const qas =
    "-----BEGIN PUBLIC KEY-----\n" +
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsjTxmOIcOwyqK57HUpWILl3oiYQPiuGR\n" +
    "42ZxuMqRM45M57py5fy6kN1YcOI27/oxyUAV8HmCMLZsUxXnn3d5Sem3i+NGkPICi4gAWD4CJ+mG\n" +
    "nQuuNxZEKmuf2guxjChDJeDx+J2piS/BMAZY9T4XFQhxFe44j+P/XBxJ82A9liPRWxZ0AbrqGWRY\n" +
    "UBvmZhU2vvxycNPN1iKFyU+LhuKBgVlNJnlKcyH1OIQQlD7HvRgEYkgrV9QtHYv53q4z5pAxgLIF\n" +
    "WNXngLlGj6YE+w3qsyxBRmwwtirLf4HcYB51A3l0KVQIClLX4p2CkEeZBdRsFDlPeqVCGJkncNtN\n" +
    "D9+9jQIDAQAB\n" +
    "-----END PUBLIC KEY-----"


export function genFrUrl(url, username) {
  let encrypt = new JSEncrypt();
  if (url.indexOf('qas.yuchai.com') != -1) {
    encrypt.setPublicKey(qas);
  } else {
    encrypt.setPublicKey(app);
  }
  let encrypted = encrypt.encrypt(username);
  return url + '&ssoToken=' + encodeURIComponent(encrypted)
}
