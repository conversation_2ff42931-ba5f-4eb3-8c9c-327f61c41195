/**
 * 添加水印
 * @param str 水印内容
 * @param left 水印距离
 */
export function setWatermark(str,left=150) {
  let id = '1.23452384164.123412416';
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id));
  }
  //创建一个画布
  let can = document.createElement('canvas');
  //设置画布的长宽
  can.width = 340;
  can.height = 140;
  let cans = can.getContext('2d');
  //旋转角度
  cans.rotate(-10 * Math.PI / 180);
  cans.font = '18px Vedana';
  //设置填充绘画的颜色、渐变或者模式
  cans.fillStyle = 'rgba(183, 176, 176, 0.4)';
  //设置文本内容的当前对齐方式
  cans.textAlign = 'left';
  //设置在绘制文本时使用的当前文本基线
  cans.textBaseline = 'Middle';
  //在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）
  cans.fillText(str, can.width / 8, can.height / 2);
  let div = document.createElement('div');
  div.id = id;
  div.style.pointerEvents = 'none';
  div.style.top = '0px';
  div.style.left = left+'px';
  div.style.position = 'fixed';
  div.style.zIndex = '2';
  div.style.width = '100%';
  div.style.height = '100%';
  div.style.zIndex = '1100';
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat';
  document.body.appendChild(div);
}

/**
 * 添加水印
 * @param str 水印内容
 * @param left 水印距离
 */
export function addWaterMark() {
    let current_date = new Date();
    let year = current_date.getFullYear();
    let mouth = current_date.getMonth() + 1;
    let day = current_date.getDate();
    let hours = current_date.getHours();
    let minutes = current_date.getMinutes();
    let seconds = current_date.getSeconds();
    if(this.$store.getters['account/user'].code){
        let person_name = this.$store.getters['account/user'].code;
        let ctx = document.createElement("canvas");
        ctx.width = 340;
        ctx.height = 100;
        ctx.style.display = "none";
        let cans = ctx.getContext("2d");
        //旋转角度
        cans.rotate(-10 * Math.PI / 180);
        cans.font = '18px Vedana';
        //设置填充绘画的颜色、渐变或者模式
        cans.fillStyle = 'rgba(183, 176, 176, 0.4)';
        //设置文本内容的当前对齐方式
        cans.textAlign = 'left';
        //设置在绘制文本时使用的当前文本基线
        cans.textBaseline = 'Middle';
        //在画布上绘制填色的文本（输出的文本，开始绘制文本的X坐标位置，开始绘制文本的Y坐标位置）
        cans.fillText(`${person_name}  ${year}-${mouth}-${day} ${hours}:${minutes}:${seconds}`, ctx.width / 8, ctx.height / 2);
        cans.save();
        return  ctx.toDataURL();
    }else{
        clearTimeout(this.timeCount);
        this.timeCount = setTimeout(() => {
            this.addWaterMark();
        }, 1000);
    }




}


export function  checkName(){

  let current_date = new Date();
  let year = current_date.getFullYear();
  let mouth = current_date.getMonth() + 1;
  let day = current_date.getDate();
  let hours = current_date.getHours();
  let minutes = current_date.getMinutes();
  let seconds = current_date.getSeconds();

  if(this.$store.getters['account/user'].code){
    let person_name = this.$store.getters['account/user'].code;
    // 读取环境变量 如果是生产环境就设置水印，开发环境不显示水印
    if(process.env.NODE_ENV === 'production'){
      setWatermark(`${person_name}  ${year}-${mouth}-${day} ${hours}:${minutes}:${seconds}`);
    }
  }else{
    clearTimeout(this.timeCount);
    this.timeCount = setTimeout(() => {
      this.checkName();
    }, 1000);
  }
}
