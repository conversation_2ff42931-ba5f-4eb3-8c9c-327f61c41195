// 合并表格行内容
let columnsGroup = {};
let columnsGroupField = [];
//用来获取 调用此 js的vue 组件 实例 （this）
let vm = null;

const sendThis = ( _this )=> {
    vm = _this;
};

const SetCustomFields = function (fields) {
    columnsGroupField = fields;
}

const ColumnCustomRender = function (value, record, dataIndex, field) {
    // 找出当前行，所在的集合
    let idx = -1;
    columnsGroup[field][value].forEach((item, index) => {
        let found = item.find(value => {
            return value == dataIndex;
        });

        if (typeof found != 'undefined') {
            idx = index;
        }
    });
    var child = vm.$createElement("a", {
        domProps: {
            innerHTML: value
        },
        on: {
            click: function () {
                vm.onclick(record);
            }
        }
    });

    const cell = {
        children: child,
        attrs: {},
    };

    if (columnsGroup[field][value][idx].length > 1) {
        cell["attrs"]["rowSpan"] = 0;
        if (dataIndex == columnsGroup[field][value][idx][0]) {
            cell["attrs"]["rowSpan"] = columnsGroup[field][value][idx].length;
        }
    }

    return cell;
};

const groupDataByFieldName = function (records) {
    columnsGroupField.forEach(field => {
        columnsGroup[field] = {};
    });

    records.forEach((item, index) => {
        columnsGroupField.forEach(field => {
            if (typeof columnsGroup[field][item[field]] == "undefined") {
                columnsGroup[field][item[field]] = [index];
            } else {
                columnsGroup[field][item[field]].push(index);
            }
        });
    });

    columnsGroupField.forEach(field => {
        for (let item in columnsGroup[field]) {
            let result = [];
            let idx = 0;
            columnsGroup[field][item].forEach(item => {
                if (typeof result[idx] == "undefined") {
                    result[idx] = [item];
                } else if (item == (result[idx][result[idx].length - 1] + 1)) {
                    result[idx].push(item);
                } else {
                    result[++idx] = [item];
                }
            });
            columnsGroup[field][item] = result;
        }
    });
}

export {SetCustomFields, ColumnCustomRender, groupDataByFieldName,sendThis}