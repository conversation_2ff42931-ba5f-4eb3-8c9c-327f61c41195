import cloneDeep from 'lodash.clonedeep'

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} expandRecord 要展开的id
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, expandRecord, id, parentId, children, ) {
    return _handleTree(true, data, expandRecord, id, parentId, children)
}

export function handleOptionsTree(data, expandRecord, id, parentId, children) {
    return _handleTree(false, data, expandRecord, id, parentId, children)
}


function _handleTree(lazy, data, expandRecord, id, parentId, children) {
    let config = {
        id: id || 'id',
        parentId: parentId || 'parentId',
        childrenList: children || 'children'
    };
    
    data = cloneDeep(data)
    let sortFunc = (a, b) => {
            
            let checkA = "0"
            let checkB = "0"
            
            if (typeof a.meunNum == "string") {
                checkA = a.meunNum.replace(/\D/g,'')
            }
            
            if (typeof b.meunNum == "string") {
                checkB = b.meunNum.replace(/\D/g,'')
            }
            
            return checkA - checkB
        }
        
    let checkIn = (id) => {
        for (let k in expandRecord) {
            let v = expandRecord[k]
            if (v == id) {
                return true
            }
        }
        
        return false
    }

    let childrenListMap = {};
    let nodeIds = {};
    let tree = [];
    
    for (let d of data) {
        let parentId = d[config.parentId];
        if (childrenListMap[parentId] == null) {
            childrenListMap[parentId] = [];
        }
        nodeIds[d[config.id]] = d;
        childrenListMap[parentId].push(d);
    }

    for (let d of data) {
        let parentId = d[config.parentId];
        if (nodeIds[parentId] == null) {
            tree.push(d);
        }
    }
    
    tree.sort(sortFunc)
    for (let t of tree) {
        adaptToChildrenList(t);
    }

    function adaptToChildrenList(o) {
        let childList = childrenListMap[o[config.id]];
        if (childList != null) {
            childList.sort(sortFunc);
            
            o[config.childrenList] = [];
            
            if (!lazy || checkIn(o[config.id])) {
                o[config.childrenList] = childList;
            }
            
            o["childList"] = childList;
            
            for (let c of childList) {
                adaptToChildrenList(c);
            }
            
        } else {
            delete o[config.childrenList]
        }
    }
        
    return tree;
}