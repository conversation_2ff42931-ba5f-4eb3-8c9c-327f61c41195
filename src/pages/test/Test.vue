<template>
  <div>
    <div>
      <a-form-model
          ref="searchForm"
          :model="searchForm"
          :rules="rules"
          :labelCol="{ span: 8 }"
          :wrapperCol="{ span: 16 }"
      >
        <a-tabs default-active-key="1">
          <a-tab-pane :tab="'Tab ' + i" v-for="i in 10" :key="i">
            <div>
              <a-form-model-item
                  :label="'字段: ' + index"
                  v-for="index in arr[i]"
                  :key="index"
                  prop="field"
              >
                <a-input v-model="searchForm[i.toString()+index.toString()]"/>
              </a-form-model-item>
            </div>
          </a-tab-pane>
        </a-tabs>
      </a-form-model>
    </div>
  </div>
</template>

<script>
export default {
  name: 'test',
  data() {
    return {
      searchForm: [],
      arr: [],
      rules: [],
    };
  },
  created() {
    for (let index = 0; index <= 10; index++) {

      this.arr[index] = Array.from(new Array(20).keys());
      this.rules = {field: [{required: true, message: '12341234', trigger: 'blur'}]};

    }
  },
};
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 0px !important;
}
</style>
