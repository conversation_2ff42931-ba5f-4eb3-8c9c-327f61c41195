import routerMap from './router.map'
import {parseRoutes} from '@/utils/routerUtil'

// 异步路由配置
const routesConfig = [
    'root',
    {
        router: 'exp404',
        path: '*',
        name: '404',
        component: () => import('@/pages/exception/404')
    },
    {
        router: 'exp403',
        path: '/403',
        name: '403',
        component: () => import('@/pages/exception/403')
    },
    {
        router: 'exp500',
        path: '/500',
        name: '500',
        component: () => import('@/pages/exception/500')
    },
    {
        router: 'phoneView',
        path: '/phoneView',
        name: '预览',
        component: () => import('@/views/xsgs/xsgsprocess/phoneView')
    },
]

const options = {
    routes: parseRoutes(routesConfig, routerMap)
}

export default options
