// 视图组件
const view = {
    tabs: () => import('@/layouts/tabs'),
    blank: () => import('@/layouts/BlankView'),
    page: () => import('@/layouts/PageView')
}

// 路由组件注册
const routerMap = {
    root: {
        path: '/',
        name: '首页',
        component: view.tabs,
        redirect:'xsgs_home'
        // meta: {
        //     page: {
        //         breadcrumb: ['首页']            //页面面包屑
        //     }
        // },

    },
    sys: {
        name: '系统设置',
        component: view.blank
    },
    sys_client: {
        name: '客户端',
        path: 'client',
        component: () => import('@/views/sys/client/Index')
    },
    sys_dict: {
        name: '数据字典',
        path: 'dict',
        component: () => import('@/views/sys/dict/Index')
    },
    sys_dict_item: {
        name: '数据字典项',
        path: 'dict_item/:type',
        invisible: true,
        component: () => import('@/views/sys/dict/Item')
    },


    xsgs: {
        name: '商用车商务管理平台',
        component: view.blank
    },
    quick_quote: {
        name: '快速报价',
        component: view.blank
    },
    xsgs_quick_quote: {
        name: '预报价台账',
        path: 'xsgs_quick_quote',
        component: () => import('@/views/xsgs/xsgsquickquote/Index')
    },
    xsgs_quick_quote_order: {
        name: '预报价单',
        path: 'xsgs_quick_quote_order/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsquickquote/order')
    },
    xsgs_quick_quote_detail: {
        name: '预报价单明细',
        path: 'xsgs_quick_quote_detail/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsquickquote/detail')
    },
    xsgs_quick_quote_parts_allocation_rule: {
        name: '专用件分摊规则',
        path: 'xsgs_quick_quote_parts_allocation_rule',
        component: () => import('../../views/xsgs/xsgsquickquotepartsallocationrule')
    },
    xsgs_quick_quote_parts_allocation_rule_edit: {
        name: '专用件分摊规则编辑',
        path: 'xsgs_quick_quote_parts_allocation_rule_edit',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsquickquotepartsallocationrule/edit.vue')
    },
    xsgs_quick_quote_oa_push: {
      name: '报价单推送用户',
      path: 'xsgs_quick_quote_oa_push',
      component: () => import('@/views/xsgs/xsgsquickquoteoapush')
    },
    xsgs_quick_quote_bu_biz_rule: {
        name: 'BU业务划分规则',
        path: 'xsgs_quick_quote_bu_biz_rule',
        component: () => import('@/views/xsgs/xsgsquickquotebubizrule')
    },
    costs: {
        name: '成本管理',
        component: view.blank
    },
    xsgs_material_cost: {
        name: '整机成本(MBOM)',
        path: 'xsgs_material_cost',
        component: () => import('@/views/xsgs/xsgsmaterialcost/Index')
    },
    machine_cost_detail: {
        name: '整机成本明细(MBOM)',
        path: 'machine_cost_detail',
        component: () => import('@/views/xsgs/xsgsmachinecostdetail/Index')
    },
    setting: {
        name: '基础数据',
        component: view.blank
    },
    xsgs_parts_price: {
        name: '选配件价格表(旧)',
        path: 'xsgs_parts_price',
        component: () => import('@/views/xsgs/xsgspartsprice/Index')
    },
    xsgs_parts_prices: {
        name: '零部件价格管理(新)',
        path: 'xsgs_parts_prices',
        component: () => import('@/views/xsgs/xsgspartsprices/Index')
    },
    xsgs_parts_setting: {
        name: '基本型配置清单(旧)',
        path: 'xsgs_parts_setting',
        component: () => import('@/views/xsgs/xsgspartssetting/Index')
    },
    xsgs_parts_settings: {
        name: '基本型配置管理(新)',
        path: 'xsgs_parts_settings',
        component: () => import('@/views/xsgs/xsgspartssettings/Index')
    },
    xsgs_parts_settings_theme: {
        name: '基本型配置清单(新)-主题',
        path: 'xsgs_parts_settings_theme/:id/:customerName',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartssettings/theme')
    },
    xsgs_parts_settings_partSee: {
        name: '查看',
        path: 'xsgs_parts_settings_partSee/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartssettings/partSee')
    },
    xsgs_parts_settings_part: {
        name: '零件配置',
        path: 'xsgs_parts_settings_part/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartssettings/part')
    },
    xsgs_parts_settings_code: {
        name: '代号配置',
        path: 'xsgs_parts_settings_code/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartssettings/code')
    },
    xsgs_customer_manage: {
        name: '客户资料管理',
        component: view.blank
    },
    xsgs_customer: {
        name: '客户组管理',
        path: 'xsgs_customer',
        component: () => import('@/views/xsgs/xsgscustomer/Index')
    },
    xsgs_budget: {
        name: '基本预算表',
        path: 'xsgs_budget',
        component: () => import('@/views/xsgs/xsgsbudget/Index')
    },

    xsgs_allowance_set: {
        name: '折让政策',
        path: 'xsgs_allowance_set',
        component: () => import('@/views/xsgs/xsgsallowanceset/Index'),
        meta: {
            page: {
                breadcrumb: ['商用车商务管理平台','折让条件']            //页面面包屑
            }
        },
    },
    xsgs_allowance_set_detail: {
        name: '折让条件明细表',
        path: 'xsgs_allowance_set_detail/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsallowancesetdetail/Index'),
        meta: {
            page: {
                breadcrumb: ['商用车商务管理平台','折让条件明细表']            //页面面包屑
            }
        },
    },
    xsgs_allowance_main: {
        name: '新建价格',
        path: 'xsgs_allowance_main/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsallowancemain/Index'),
    },
    xsgs_allowance_compare: {
        name: '选配件价格对比',
        invisible: true,
        path: 'xsgs_allowance_compare/:id',
        component: () => import('@/views/xsgs/xsgsallowancemain/compare')
    },
    xsgs_allowance_mainSee: {
        name: '新建价格查看',
        path: 'xsgs_allowance_mainSee/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsallowancemain/IndexSee'),
    },


    xsgs_allowance_detail: {
        name: '价格折让明细表',
        path: 'xsgs_allowance_detail/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsallowancedetail/Index')
    },
    xsgs_manger: {
        name: '价格管理',
        component: view.blank
    },
    xsgs_mangers: {
        name: '价格管理',
        path: 'xsgs_manger',
        component: () => import('@/views/xsgs/xsgsallowancemain/Manger')
    },
    xsgs_reject_set: {
        name: '剔除政策',
        path: 'xsgs_reject_set',
        component: () => import('@/views/xsgs/xsgsrejectset/Index')
    },
    xsgs_allowance_parts: {
        name: '价格主配件表',
        path: 'xsgs_allowance_parts/:id',
        component: () => import('@/views/xsgs/xsgsallowanceparts/Index')
    },
    xsgs_allowance_item: {
        name: '价格主表-行数据',
        path: 'xsgs_allowance_item',
        component: () => import('@/views/xsgs/xsgsallowanceitem/Index')
    },
    xsgs_allowance_price_query: {
        name: '价格库查询',
        component: view.blank
    },
    xsgs_allowance_query: {
        name: '价格查询',
        path: 'xsgs_allowance_query',
        component: () => import('@/views/xsgs/xsgsallowancemain/Query')
    },
    xsgs_parts_option: {
        name: '选型选装清单(旧)',
        path: 'xsgs_parts_option',
        component: () => import('@/views/xsgs/xsgspartsoption/Index')
    },
    xsgs_parts_options: {
        name: '选型选装清单(新)',
        path: 'xsgs_parts_options',
        component: () => import('@/views/xsgs/xsgspartsoptions/Index')
     },
    xsgs_parts_options_theme: {
        name: '选型选装清单(新)-主题',
        path: 'xsgs_parts_options_theme/:customerName/',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartsoptions/theme/Index')
    },
        xsgs_parts_options_part: {
        name: '零件配置',
        path: 'xsgs_parts_options_part',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartsoptions/theme/edit_optional')
    },
        xsgs_parts_options_code: {
        name: '代号配置',
        path: 'xsgs_parts_options_code',
        invisible: true,
        component: () => import('@/views/xsgs/xsgspartsoptions/theme/edit_code')
    },
    xsgs_settle_price: {
        name: '结算价',
        path: 'xsgs_settle_price',
        invisible: true,
        component: () => import('@/views/xsgs/xsgssettleprice/Index')
    },

    xsgs_fee_promote: {
        name: '主机厂费用推广',
        path: 'xsgs_fee_promote',
        component: () => import('@/views/xsgs/xsgsfeepromote/Index')
    },

    xsgs_parts_basic: {
        name: '基本型中间表',
        path: 'xsgs_parts_basic',
        component: () => import('@/views/xsgs/xsgspartsbasic/Index')
    },
    basic_price: {
        name: '基本型参考价',
        path: 'xsgs_basic_price',
        component: () => import('@/views/xsgs/xsgsbasicprice/Index')
    },

    xsgs_series_order: {
        name: '产品系列排序',
        path: 'xsgs_series_order',
        component: () => import('@/views/xsgs/xsgsseriesorder/Index')
    },
    xsgs_parts: {
        name: '选型选装列表排序',
        path: 'xsgs_parts',
        component: () => import('@/views/xsgs/xsgsparts/Index')
    },
    xsgs_report_columns: {
        name: '报表字段权限',
        path: 'xsgs_report_columns',
        component: () => import('@/views/xsgs/xsgsreportcolumns/Index')
    },
    xsgs_report_theme: {
        name: '报表主题',
        path: 'xsgs_report_theme',
        component: () => import('@/views/xsgs/xsgsreporttheme/Index')
    },

    xsgs_plate_order: {
        name: '业务板块排序',
        path: 'xsgs_plate_order',
        component: () => import('@/views/xsgs/xsgsplateorder/Index')
    },
    xsgs_batch_manager: {
        name: '跑批管理',
        path: 'xsgs_batch',
        component: () => import('@/views/xsgs/xsgsbatch/Index')
    },
    policy: {
        name: '商务政策管理',
        component: view.blank
    },
    xsgs_price_policy: {
        name: '价格政策',
        path: 'xsgs_price_policy',
        component: () => import('@/views/xsgs/xsgspricepolicy/xsgsPricePolicy')
    },
    xsgs_price_policy_detail: {
        name: '价格政策管理',
        invisible: true,
        path: 'xsgs_price_policy_detail/:id',
        component: () => import('@/views/xsgs/xsgspricepolicy/xsgsPricePolicyDetail')
    },
    xsgs_part_policy: {
        name: '选配件政策',
        path: 'xsgs_part_policy',
        component: () => import('@/views/xsgs/xsgspartpolicy/xsgsPartPolicy')
    },
    xsgs_part_policy_detail: {
        name: '选配件政策管理',
        invisible: true,
        path: 'xsgs_part_policy_detail/:id',
        component: () => import('@/views/xsgs/xsgspartpolicy/xsgsPrartPolicyDetail')
    },
    xsgs_budget_manager: {
        name: '商务预算管理',
        path: 'xsgs_budget_manager',
        component: () => import('@/views/xsgs/xsgsbudgetmanager/xsgsBudgetManager')
    },
    xsgs_sales: {
        name: '销量查询',
        path: 'xsgs_sales',
        component: () => import('@/views/xsgs/xsgssales/xsgsSales')
    },
    xsgs_sales_com: {
        name: '基本型水平对比',
        path: 'xsgs_sales_com',
        component: () => import('@/views/xsgs/xsgssales/xsgsSalesCompare')
    },
    R_xsgs_main_save:{
        name: '保存按钮',
        path: 'R_xsgs_main_save',
        invisible: true,
    },
    b:{
        name: 'b',
        path: 'b',
        invisible: true,
    },
    xsgs_process_manager: {
        name: '商务流程管理',
        component: view.blank
    },
    xsgs_process_router: {
        name: '流程跳转',
        path: 'xsgs_process/router/:processCode/:action/:formId',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/router')
    },
    xsgs_process_pending: {
        name: '流程台账',
        path: 'bussPolicyLedger',
        component: () => import('@/views/xsgs/xsgsprocesspolicy/bussPolicyLedger')
    },
    xsgs_process_print: {
        name: '流程打印',
        path: 'processPrint/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/processPrint')
    },
    xsgs_business_applove: {
        name: '商务政策审批流程',
        component: view.blank
    },
    xsgs_buss_appl_add: {
        name: '新建流程',
        path: 'bussPolicy',
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_buss_appl_edit: {
        name: '新建流程',
        path: 'bussPolicy/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_quote_applove: {
        name: '商务报价审批流程',
        component: view.blank
    },
    xsgs_quote_add: {
        name: '新建流程',
        path: 'quote',
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_quote_appl_edit: {
        name: '新建流程',
        path: 'quote/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_process_pending_quote: {
        name: '流程台账',
        path: 'quoteLedger',
        component: () => import('@/views/xsgs/xsgsprocessquote/quoteLedger')
    },
    xsgs_process_quote_info: {
        name: '新建报价',
        invisible: true,
        path: 'quoteInfo/:id',
        component: () => import('@/views/xsgs/xsgsprocessquote/quote')
    },
    xsgs_process_rmstate: {
        name: '状态机进度跟踪表',
        path: 'rmstate',
        component: () => import('@/views/xsgs/xsgsprocessquote/rmstate')
    },
    xsgs_intcont_applove: {
        name: '商务合同内部审批流程',
        component: view.blank
    },
    xsgs_intcont_add: {
        name: '新建流程',
        path: 'intCont',
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_intcont_appl_edit: {
        name: '新建流程',
        path: 'intCont/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_process_pending_intcont: {
        name: '流程台账',
        path: 'intcontLedger',
        component: () => import('@/views/xsgs/xsgsProcessIntCont/intContLedger')
    },
    xsgs_process_cont_ledger: {
        name: '合同台账',
        path: 'contractLedger',
        component: () => import('@/views/xsgs/xsgsProcessIntCont/contractLedger')
    },
    xsgs_legal_cont_ledger: {
        name: '合同台账信息',
        path: 'legalInfo/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsProcessIntCont/legalInfo')
    },

    xsgs_quote_letter_applove: {
        name: '商务报价函审批流程',
        component: view.blank
    },
    xsgs_quote_letter_add: {
        name: '新建流程',
        path: 'quoteLetter',
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_quote_letter_appl_edit: {
        name: '新建流程',
        path: 'quoteLetter/:id',
        invisible: true,
        component: () => import('@/views/xsgs/xsgsprocess/Index')
    },
    xsgs_process_pending_quote_letter: {
        name: '流程台账',
        path: 'quoteLetterLedger',
        component: () => import('@/views/xsgs/xsgsprocessquoteletter/quoteLetterLedger')
    },
    xsgs_process_quote_letter_info: {
        name: '新建报价函',
        invisible: true,
        path: 'quoteLetterInfo/:id',
        component: () => import('@/views/xsgs/xsgsprocessquoteletter/quoteLetter')
    },
    xsgs_process_quote_letter_view: {
        name: '预览报价函',
        invisible: true,
        path: 'quoteLetterView/:id',
        component: () => import('@/views/xsgs/xsgsprocessquoteletter/quoteLetter_view')
    },

    xsgs_auth_manager: {
        name: '权限管理',
        component: view.blank
    },
    xsgs_auth_approval: {
        name: '审批权限',
        component: view.blank
    },
    xsgs_auth_bussPolicy_approval: {
        name: '商务政策审批流程',
        component: view.blank
    },
    xsgs_auth_bussPolicy_approval_user: {
        name: '环节审批人维护',
        path: '/bussPolicy',
        component: () => import('@/views/xsgs/xsgsauth/nodeAppAuth')
    },
    xsgs_auth_quote_approval: {
        name: '商务报价审批流程',
        component: view.blank
    },
    xsgs_auth_quote_approval_user: {
        name: '环节审批人维护',
        path: '/quote',
        component: () => import('@/views/xsgs/xsgsauth/nodeAppAuth')
    },
    xsgs_auth_intcont_approval: {
        name: '商务合同内部审批流程',
        component: view.blank
    },
    xsgs_auth_intcont_approval_user: {
        name: '环节审批人维护',
        path: '/intCont',
        component: () => import('@/views/xsgs/xsgsauth/nodeAppAuth')
    },
    xsgs_auth_quote_letter_approval: {
        name: '商务报价函审批流程',
        component: view.blank
    },
    xsgs_auth_quote_letter_approval_user: {
        name: '环节审批人维护',
        path: '/quoteLetter',
        component: () => import('@/views/xsgs/xsgsauth/nodeAppAuth')
    },
    xsgs_ledger_auth: {
        name: '台账权限配置',
        path: '/ledgerAuth',
        component: () => import('@/views/xsgs/xsgsauth/legerQueryAuth')
    },
    xsgs_customer_material: {
        name: '客户物料管理',
        path: 'xsgs_customer_material',
        component: () => import('@/views/xsgs/xsgscustomermaterial/Index')
    },
    xsgs_quote_letter_temp: {
        name: '报价函模板信息',
        path: 'xsgs_quote_letter_temp',
        component: () => import('@/views/xsgs/xsgsquotelettertemp/Index')
    },
    xsgs_contract_no: {
        name: '合同编号',
        path: 'xsgs_contract_no',
        component: () => import('@/views/xsgs/xsgscontractno/Index')
    },
    xsgs_auth_page: {
        name: '页面权限',
        component: view.blank
    },
    xsgs_auth_role: {
        name: '角色管理',
        path: 'xsgs_auth_role',
        component: () => import('@/views/xsgs/xsgsauth/roleList')
    },
    xsgs_auth_list: {
        name: '权限列表',
        path: 'xsgs_auth_list',
        component: () => import('@/views/xsgs/xsgsauth/authList')
    },
    xsgs_office_line: {
        name: '客户权限管理',
        path: 'xsgs_office_line',
        component: () => import('@/views/xsgs/xsgsofficeline/Index')
    },
    xsgs_user_config: {
        name: '用户配置',
        invisible: true,
        path: 'xsgs_user_config',
        component: () => import('@/views/xsgs/xsgsuserconfig/Index')
    },
    xsgs_home: {
        name: '首页',
        invisible: true,
        path: 'xsgs_home',
        component: () => import('@/views/xsgs/xsgshome/Index'),
        meta: {
            page: {
                breadcrumb: ['首页']            //页面面包屑
            },
            frIdDev: 'https://frrc.qas.yuchai.com/frbi/decision/v10/entry/access/cd19b976-c87d-4cbb-8f67-d5d78171ed6d?preview=true',
            frIdprod: 'https://frrc.app.yuchai.com/frbi/decision/v10/entry/access/110512e9-c98d-4880-9b29-0b859cd508ba?preview=true'
        },
    },
}
export default routerMap

