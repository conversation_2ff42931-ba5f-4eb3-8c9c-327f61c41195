<template>
  <div>
    <a-tooltip v-for="tag in value" :key="tag[nameField]" :title="tag[codeField]">
      <a-tag closable @close="() => handleClose(tag)">
        {{ tag[nameField] }}
      </a-tag>
    </a-tooltip>
    <a-select
        v-if="inputVisible"
        ref="input"
        autoFocus
        :disabled="disabled"
        show-search
        placeholder="请输入姓名"
        :filter-option="false"
        :not-found-content="fetching ? undefined : null"
        option-label-prop="label"
        @search="fetchUser"
        @change="handleChange"
        @blur="inputVisible=false">
      <a-spin v-if="fetching" slot="notFoundContent" size="small"/>
      <a-select-option v-for="d in users" :key="d.employeeCode" :label="d.employeeName">
        {{ d.displayName }}
      </a-select-option>
    </a-select>
    <a-tag v-if="!inputVisible&&value.length<num"
           style="background: #fff; borderStyle: dashed;" @click="inputVisible=true">
      <a-icon type="plus"/>
      增 加
    </a-tag>
  </div>
</template>

<script>
import {METHOD, request} from "../../utils/request";
import debounce from "../../utils/debounce";
import {GET_YC_USER} from "../../services/api/user";


export default {
  name: "YcUserSelect.vue",
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: new Array()
    },
    codeField: {
      type: String,
      default: 'code'
    },
    nameField: {
      type: String,
      default: 'name'
    },
    num: {
      type: Number,
      default: 1000
    }
  },
  data() {
    this.lastFetchId = 0;
    this.fetchUser = debounce(this.fetchUser, 800);
    return {
      fetching: false,
      inputVisible: false,
      users: [],
      data: []
    }
  },
  methods: {
    handleClose(removedTag) {
      let tmpValue = this.value.filter(tag => tag[this.nameField] !== removedTag[this.nameField])
      this.$emit('selectChange', tmpValue)
      this.$emit('input', tmpValue)
    },
    fetchUser(value) {
      this.lastFetchId += 1;
      const fetchId = this.lastFetchId;
      this.users = [];
      this.fetching = true;
      request(GET_YC_USER, METHOD.POST, {employeeName: value})
          .then(res => {
            if (fetchId !== this.lastFetchId) {
              // for fetch callback order
              return;
            }
            if (res.data.code === 'ok') {
              this.users = res.data.data.slice(0, 20)
              this.fetching = false
            }
          })
    },
    handleChange(value, node) {
      this.users = []
      this.inputVisible = false
      let tmpValue = this.value
      let tmpUser = {}
      tmpUser[this.codeField] = node.key
      tmpUser[this.nameField] = node.componentOptions.propsData.label
      tmpValue.push(tmpUser)
      this.$emit('selectChange', tmpValue)
      this.$emit('input', tmpValue)
    }
  }
}
</script>

<style scoped>

</style>
