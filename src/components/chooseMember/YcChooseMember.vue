editRecipient<template>
    <div>
        <a-modal
                :title="chooseTitle"
                :visible="isShow"
                :maskClosable="false"
                closable
                :width="'60%'"
                @cancel="close"
                @ok="chooseOk"
                class="choose-user-modal"
        >
            <a-layout>
                <a-layout-content>
                    <a-row>

<!--                        <a-col :span="8" class="left">-->
<!--                            <a-spin :spinning="treeLoading">-->
<!--                            <a-tabs v-model="currentKey">-->
<!--                                <a-tab-pane :key="1" tab="组织">-->

<!--                                    <a-tree-->
<!--                                            :tree-data="treeOrgData"-->
<!--                                            :selectedKeys="orgSelectedKeys"-->
<!--                                            @select="treeDataSelect"-->
<!--                                    >-->
<!--                                        <a-icon slot="switcherIcon" type="down"/>-->
<!--                                    </a-tree>-->
<!--                                </a-tab-pane>-->
<!--                            </a-tabs>-->
<!--                            </a-spin>-->
<!--                        </a-col>-->
                        <a-col :span="24" class="right">
                            <a-row>
                                <a-input-search
                                        placeholder="请输入姓名查询"
                                        :allowClear="false"
                                        v-model="searchValue"
                                        @change="searchMemberOptions"
                                        @search="searchMemberOptions"
                                >
                                    <a-icon
                                            v-if="searchValue"
                                            slot="suffix"
                                            theme="filled"
                                            type="close-circle"
                                            class="search-clear-icon"
                                            @click="clearSearch"
                                    />
                                </a-input-search>
                                <span v-if="loading"
                                ><a-spin>
                                  <a-icon slot="indicator" type="loading" spin/>
                                </a-spin
                                >加载数据中</span
                                >
                                <span v-else>共{{ memberOptions.length }}条数据</span>
                            </a-row>
                            <p v-if="memberOptions.length > 0" style="margin-bottom: 15px; text-align: left">
                                <a-tag color="blue" @click="FutureData(BtnName)" style="cursor: pointer;">
                                    {{ BtnName }}
                                </a-tag>
                            </p>
                            <a-row class="groups" v-if="!getting">
                                <a-radio-group
                                        v-if="!isMultiple"
                                        v-model="memberRadio"
                                        @change="radioChange"
                                >
                                    <a-radio
                                            v-for="(item, i) in memberOptions"
                                            :key="i"
                                            :value="item.employeeCode"
                                    >
                                        {{ item.orgFullName }}
                                    </a-radio>
                                </a-radio-group>
                                <a-checkbox-group
                                        v-else
                                        v-model="memberChecks"
                                        @change="checkboxChange"
                                >
                                    <a-checkbox
                                            v-for="(item, i) in memberOptions"
                                            :key="i"
                                            :value="item.employeeCode"
                                            v-model="item.checked"
                                    >
                                        {{ item.orgFullName }}
                                    </a-checkbox>
                                </a-checkbox-group>
                            </a-row>
                        </a-col>
                    </a-row>
                </a-layout-content>
                <a-layout-footer>
                    <div class="chosed">
                        <div class="title">已选择：</div>
                        <div class="list">
                            <a-tag
                                    closable
                                    v-for="tag in memberTags"
                                    :key="tag.employeeCode"
                                    @close="() => tagClose(tag)"
                            >
                                {{ tag.employeeName }}
                            </a-tag>
                        </div>
                    </div>
                </a-layout-footer>
            </a-layout>
        </a-modal>
    </div>
</template>

<script>
    import {queryAllOrgApi, getFndEmployeesApi} from "@/services/common.api.js";
    export default {
        name: "YcChooseMember.vue",
        props: {
            chooseTitle: {type: String, default: '人员选择'},
            isMultiple: {type: Boolean, default: true},
            dataList: {type: Array, default: () => []}
        },
        data() {
            return {
                loading: false,
                treeLoading: false,
                getting:false,
                isShow: false,
                rowId:null,
                isSpecial:false,
                currentKey: 1, // 当前激活tab面板的key
                treeOrgData: [], // 组织树的数据
                orgSelectedKeys: [], // 组织树勾选
                searchValue: null, // 搜索框数据
                memberRange:[],
                memberOptions: [], // 展示选项的数据
                memberRadio: null, // 单选框数据
                memberChecks: [], // 多选框被勾选数据
                memberTags: [], // 被勾选数据的标签
                defaultChoose: [], // 默认选中
            }
        },
        created() {
        },
        computed: {
            BtnName() {
                let list1 = [];
                let dataList = [];
                const {memberOptions, memberTags} = this;
                memberOptions.forEach((v) => {
                    if (list1.indexOf(v.employeeCode) === -1) {
                        list1.push(v.employeeCode);
                        dataList.push(v);
                    }
                });
                if (dataList.length === memberTags.length) {
                    return '反选';
                } else {
                    return '全选';
                }
            },
        },
        watch: {
            /**
             * 弹窗显示标记
             */
            isShow: {
                handler() {
                    if (this.isShow) {
                        this.initData();
                        if (!this.isSpecial) {
                            this.getTreeData();
                        }
                    }
                },
                immediate: true,
                deep: true,
            },

        },
        methods: {
            initData() {
                this.currentKey = 1;
                this.treeOrgData = []; // 组织树的数据
                this.orgSelectedKeys = []; // 组织树勾选
                this.searchValue = null; // 搜索框数据
                this.memberOptions = []; // 展示选项的数据
            },
            /**
             * 点击树节点
             */
            treeDataSelect(selectedKeys, e) {
                this.orgSelectedKeys = [e.node.eventKey];
                const nodeData = {...e.node.dataRef};
                this.requestMemberApi(nodeData.organizationCode);
                this.searchValue = null;
            },
            /**
             * 请求查询人员
             */
            requestMemberApi(params) {
                if (!params) {
                    this.memberOptions = this.memberRange;
                    return;
                }
                this.loading = true;
                this.memberOptions = [];
                getFndEmployeesApi(params).then(
                    (res) => {
                        console.log(res)
                        setTimeout(() => {
                            this.loading = false;
                        }, 300);
                        if (res.data.code == 'ok') {
                            let list = res.data.data;
                            if (list && list.length > 0) {
                                list = this.cutList(list, 0, 20);
                                list.forEach(s=>{
                                    let item = {};
                                    item['employeeName'] = s.employeeName;
                                    item['employeeCode'] = s.employeeCode;
                                    item['orgFullName'] = s.displayName;
                                    this.memberOptions.push(item);
                                })
                            }
                            // this.memberOptions = [...res.data.data];
                        } else {
                            this.memberOptions = [];
                        }
                        this.memberChecks = this.memberTags.map((tag) => {
                            return tag.employeeCode;
                        });
                    },
                    (error) => {
                        console.log(error);
                        this.loading = false;
                    }
                );
            },
            /**
             * 获取树的数据
             */
            getTreeData() {
                // 组织树接口
                const params = {orgCode: 'YC01'};
                this.treeLoading = true;
                queryAllOrgApi(params).then((res) => {
                    this.treeLoading = false;
                    this.treeOrgData = this.handleTreeData(res.data.table); // 处理组织树的数据
                });
            },
            /**
             * 数据全选按钮
             */
            FutureData(name) {
                let list1 = [];
                let dataList = [];
                const {memberOptions} = this;
                if (this.isMultiple) {
                    memberOptions.forEach((v) => {
                        if (list1.indexOf(v.employeeCode) === -1) {
                            list1.push(v.employeeCode);
                            dataList.push(v);
                        }
                    });
                    if (name == '全选') {
                        this.memberTags = dataList;
                        this.memberChecks = dataList.map((tag) => {
                            return tag.employeeCode;
                        });
                    } else {
                        this.memberTags = [];
                        this.memberChecks = [];
                    }
                }
            },
            /**
             * 单选框改变时触发
             * data 被选中的数据
             */
            radioChange() {
                let list1 = [];
                let dataList = [];
                const list = this.memberOptions.filter(
                    (item) => item.employeeCode == this.memberRadio
                );
                list.forEach((v) => {
                    if (list1.indexOf(v.employeeCode) === -1) {
                        list1.push(v.employeeCode);
                        dataList.push(v);
                    }
                });
                this.memberTags = [...dataList];
            },
            /**
             * 多选框改变时触发
             * data 被选中的数据
             */
            checkboxChange() {
                let list1 = [];
                let dataList = [];
                const list = this.memberOptions.filter((item) =>
                    this.memberChecks.includes(item.employeeCode)
                );
                let data = this.memberTags
                    .filter((item) =>
                        this.memberOptions.every(
                            (item1) => item1.employeeCode != item.employeeCode
                        )
                    )
                    .concat(list);
                data.forEach((v) => {
                    if (list1.indexOf(v.employeeCode) === -1) {
                        list1.push(v.employeeCode);
                        dataList.push(v);
                    }
                });
                this.memberTags = dataList;
            },
            handleTreeData (data){
                for (let i = 0; i < data.length; i++) {
                    const node = data[i];
                    node.key = node.organizationCode;
                    node.title = node.orgName;
                    if (node.child) {
                        node.children = this.handleTreeData(node.child);
                    }
                }
                return data;
            },
            searchMemberOptions() {
                if (this.isSpecial) {
                    this.memberOptions = this.searchValue
                        ? this.memberOptions.filter(
                            (item) => item.orgFullName.indexOf(this.searchValue) > -1
                        )
                        : [...this.memberRange];
                } else {
                    this.requestMemberApi(this.searchValue);
                }
            },
            clearSearch() {
                this.searchValue = '';
                this.searchMemberOptions();
            },
            chooseOk() {
                let memberData = [];
                this.memberTags.forEach((item) => {
                    let positionName = null;
                    let data = item.orgFullName ? item.orgFullName.split('/') : [];
                    if (data[1]) {
                        positionName = data[1];
                    }
                    memberData.push({
                        employeeName: item.employeeName,
                        employeeCode: item.employeeCode,
                        orgSectionCode: item.orgCode,
                        orgSectionName: item.orgName,
                        orgFullName:item.orgFullName,
                        phone: item.phone,
                        sectionName: item.orgName,
                        positionName: positionName,
                        orgCodeSecond: item.orgCodeSecond,
                        orgNameSecond: item.orgNameSecond,
                    });
                });
                if (memberData.length == 0) {
                    memberData=[];
                }
                this.$emit('member-modal-ok', { memberData: memberData,rowId:this.rowId});
                this.close();
            },
            /**
             * 标签关闭时触发
             * removedTag 被移除的数据
             */
            tagClose(removedTag) {
                const tags = this.memberTags.filter(
                    (tag) => tag.employeeCode !== removedTag.employeeCode
                );
                this.memberTags = [...tags];
                if (!this.isMultiple) {
                    this.memberRadio = null;
                } else {
                    this.memberChecks = this.memberTags.map((tag) => {
                        return tag.employeeCode;
                    });
                }
            },
            show(rowId, val, userList, isSpecial) {
                this.rowId = rowId;
                this.getting = true;
                this.$nextTick(()=>{
                    this.isSpecial = isSpecial?true:false;
                    if (userList && userList.length) {
                        this.memberRange = userList;
                        this.memberOptions = userList;
                    }
                    if (val && val.length) {
                        this.memberTags = [...val];
                        if (!this.isMultiple) {
                            this.memberRadio = this.memberTags[0].employeeCode;
                        } else {
                            this.memberChecks = this.memberTags.map((tag) => {
                                return tag.employeeCode;
                            });
                        }
                    } else {
                        this.memberTags = [];
                        this.memberChecks = [];
                        this.memberRadio = [];
                    }
                    this.getting = false;
                });

                this.isShow = true;
            },
            close() {
                this.isShow = false;
            },
            showSpecial(dataList, val){
                this.isSpecial = true;
                this.memberOptions = dataList;
                if (val && val.length) {
                    this.memberTags = [...val];
                    if (!this.isMultiple) {
                        this.memberRadio = this.memberTags[0].employeeCode;
                    } else {
                        this.memberChecks = this.memberTags.map((tag) => {
                            return tag.employeeCode;
                        });
                    }
                } else {
                    this.memberTags = [];
                }
                this.isShow = true;
            },
        }
    }
</script>

<style scoped>
    /deep/.ant-layout>.ant-layout-content>.ant-row{
        background-color: #ffffff;
    }
    /deep/.ant-modal .ant-layout .ant-layout-content .ant-row .left {
        padding-right: 10px;
        border-right: 1px solid #e8e8e8;
        height: 400px !important;
        overflow: auto;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right .ant-row:nth-child(1) {
        border: none;
        height: 45px;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right{
        padding-left: 10px;
        text-align: center;
        height: 400px !important;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right .ant-row:nth-child(1) .ant-input-search {
        max-width: 300px;
        margin-right: 10px;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row {
        border-bottom: 1px solid #e8e8e8;
    }
    .ant-modal .ant-layout .ant-layout-footer {
        background-color: #fff;
        padding: 10px 0;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups {
        border: none;
        height: calc(100% - 95px);
        overflow-x: hidden;
        overflow-y: auto;
        text-align: left;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-checkbox-group .ant-checkbox-wrapper, .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-checkbox-group .ant-radio-wrapper, .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-radio-group .ant-checkbox-wrapper, .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-radio-group .ant-radio-wrapper {
        display: block;
        margin-left: 0;
        margin-bottom: 8px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-checkbox-group, .ant-modal .ant-layout .ant-layout-content .ant-row .right .groups .ant-radio-group {
        width: 100%;
    }
    .ant-layout-footer {
        padding: 24px 50px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        background: #f0f2f5;
    }
</style>
