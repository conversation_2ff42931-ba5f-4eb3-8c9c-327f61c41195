<template>
  <div :style="customerstyle">
    <a-button v-if="!disabled" @click="handleAdd">
      <a-icon type="plus"/>
      点击添加
    </a-button>
    <div class="link-list">
      <div v-for="(l,i) in linkList" :key="i">
        <span>
          <div class="link-list-item">
              <div class="link-list-item-info" :style="`${disabled?'padding-right: 20px':''}`">
                <span>
                  <a-icon type="link" class="anticon-paper-clip"/>
                  <a target="_blank" :href="l.link">{{l.linkDesc?l.linkDesc:l.link}}</a>
                  <span class="link-list-item-actions">
                      <a><a-icon type="search" class="search" @click="handlePreview(l)" :style="`${disabled?'right: 5px':''}`"/></a>
                      <a @click="removeFile(i)"><a-icon type="delete" v-if="!disabled" class="remove"/></a>
                  </span>
                </span>
              </div>
          </div>
        </span>
      </div>
    </div>
    <a-modal title="添加链接"
             width="30%"
             :visible="showForm"
             @cancel="showForm = false"
             @ok="saveInfo">
      <a-form-model class="linkUpload" ref="linkForm" :model="linkForm" :labelCol="{span: 4}" :wrapperCol="{span: 20}">
        <a-row>
          <a-form-model-item label="描述" :wrapperCol="{span: 19}">
            <a-input v-if="!linkForm.isView" v-model="linkForm.linkDesc"/>
            <span v-if="linkForm.isView">{{linkForm.linkDesc}}</span>
          </a-form-model-item>
        </a-row>
        <a-row>
          <a-form-model-item label="链接" :wrapperCol="{span: 19}">
            <a-input v-if="!linkForm.isView" v-model="linkForm.link"/>
            <span v-if="linkForm.isView">{{linkForm.link}}</span>
          </a-form-model-item>
        </a-row>
      </a-form-model>
    </a-modal>
  </div>

</template>

<script>
export default {
  name: "LinkUpload",
  props: ['value','fieldList', 'field','multiple','disabled','customerstyle'],
  data() {
    return {
      showForm:false,
      linkForm:{
        isView:false,
        link:null,
        linkDesc:null
      }
    }
  },
  computed: {
    linkList: function () {
      return this.value
    }
  },
  methods: {
    handlePreview(l) {
      this.linkForm.link = l.link;
      this.linkForm.linkDesc = l.linkDesc;
      this.linkForm.isView = true;
      this.showForm=true;
    },
    handleAdd(){
      this.linkForm.link = null;
      this.linkForm.linkDesc = null;
      this.linkForm.isView = false;
      this.showForm=true;
    },
    removeFile(i) {
      this.value.splice(i, 1)
      this.$emit('input', this.value)
    },
    saveInfo(){
      if (!this.linkForm.link) {
        this.$message.error("请输入链接");
        return;
      }
      this.value.push({...this.linkForm});
      this.$emit('input', this.value)
      this.showForm=false;
    }
  }
}
</script>

<style scoped>
  .link-list-item{
    position: relative;
    height: 22px;
    line-height:22px;
  }
  .link-list-item-info {
    padding: 0;
    padding-right: 33px;
    padding-left: 5px;
    height: 100%;
  }
  .link-list-item-info > span {
    display: block;
    width: 100%;
    height: 100%;
  }
  .anticon-paper-clip {
    position: absolute;
    top: 5px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
  .link-list-item:hover .link-list-item-info {
    background-color: #e6f7ff;
  }
  .link-list-item-info > span>a {
    display: inline-block;
    width: 100%;
    padding-left: 22px;
    padding-right: 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .link-list-item:hover .link-list-item-info > span > span > a > i {
    opacity: 1;
  }
  .link-list-item-info > span > span > a > i {
    color: rgba(0, 0, 0, 0.45);
    position: absolute;
    top: 5px;
    opacity: 0;
  }
  .link-list-item-info > span > span > a > .remove {
    right: 5px;
  }
  .link-list-item-info > span > span > a>.search {
    right: 20px;
  }
  /deep/ .linkUpload .ant-modal-body {
    padding: 16px 24px;
  }
  /deep/ .linkUpload .ant-form-item {
    margin-bottom: 5px !important;

  }
</style>
