<template>
  <div>
    <a-upload
        list-type="picture-card"
        :file-list="fileList"
        :before-upload="beforeUpload"
        :customRequest="customRequest"
        :remove="removeFile"
        @preview="handlePreview"
    >
      <div v-if="value.length < 1">
        <a-icon :type="loading ? 'loading' : 'plus'"/>
        <div class="ant-upload-text">
          上传
        </div>
      </div>
    </a-upload>
    <a-modal :visible="previewVisible" :footer="null" @cancel="previewVisible=false" style="z-index: 99999">
      <img style="width: 100%" :src="previewImage"/>
    </a-modal>
  </div>
</template>

<script>
import {uploadObsFile} from "@/services/file";
import {getObsDownloadUrl} from "../../services/file";

export default {
  name: "ImgUpload",
  props: ['value', 'path', 'field'],
  data() {
    return {
      previewVisible: false,
      previewImage: '',
      loading: false
    }
  },
  computed: {
    fileList: function () {
      let files = []
      this.value.map(async (file, i) => {
        let res = await getObsDownloadUrl(file.path + file.finalName)
        let f = {...file, uid: file.id || file.uid, url: res.data.data}
        files.push(f)
        this.value[i] = f
      })
      return files
    }
  },
  methods: {
    handlePreview(file) {
      this.previewImage = file.url
      this.previewVisible = true
    },
    customRequest(data) {
      this.loading = true
      uploadObsFile(data.file, this.path, this.field).then(res => {
        this.value.push(res)
        this.$emit('input', this.value)
        this.$message.success(`${data.file.name} 上传成功！`)
        data.onSuccess()
      }).catch(() => {
        this.$message.error(`${data.file.name} 上传失败！`);
        data.onError()
      }).finally(() => {
        this.loading = false
      })
    },
    beforeUpload(file) {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        this.$message.error('必须上传图片文件');
      }
      const isLt2M = file.size / 1024 / 1024 < 20;
      if (!isLt2M) {
        this.$message.error('附件不能大于 20MB!');
      }
      return isLt2M && isJpgOrPng;
    },
    removeFile(file) {
      this.value.map((f, i) => {
        if (f.uid == file.uid) {
          this.value.splice(i, 1)
        }
      })
      this.$emit('input', this.value)
    }
  }
}
</script>

<style scoped>

</style>
