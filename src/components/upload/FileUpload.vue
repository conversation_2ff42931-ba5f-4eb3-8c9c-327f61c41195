<template>
  <div :style="customerstyle">
    <a-upload
        :file-list="fileList"
        :before-upload="beforeUpload"
        :customRequest="customRequest"
        :remove="removeFile"
        :multiple="multiple"
        :disabled="disabled"
        @preview="handlePreview"
    >
      <a-button v-if="!disabled">
        <a-icon type="upload"/>
        点击上传
      </a-button>
    </a-upload>
  </div>
</template>

<script>
import {getObsDownloadUrl, uploadObsFile} from "@/services/file";
import {exportFile} from "@/utils/request";
export default {
  name: "FileUpload",
  props: ['value', 'path', 'field','multiple','disabled','customerstyle'],
  data() {
    return {}
  },
  computed: {
    fileList: function () {
      let files = []
      this.value.map(async (file, i) => {
        let res = await getObsDownloadUrl(file.path + file.finalName)
        let f = {...file, uid: file.id || file.uid, url: res.data.data}
        files.push(f)
        this.value[i] = f
      })
      return files
    }
  },
  methods: {
    handlePreview(file) {
      getObsDownloadUrl(file.path + file.finalName)
          .then(res => {
            exportFile(res.data.data, file.finalName)
            // window.open(res.data.data)
          })
    },
    customRequest(data) {
      this.loading = true
      uploadObsFile(data.file, this.path, this.field).then(res => {
        this.value.push(res)
        this.$emit('input', this.value)
        this.$message.success(`${data.file.name} 上传成功！`)
        data.onSuccess()
      }).catch(() => {
        this.$message.error(`${data.file.name} 上传失败！`);
        data.onError()
      }).finally(() => {
        this.loading = false
      })
    },
    beforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 100;
      if (!isLt2M) {
        this.$message.error('附件不能大于 100MB!');
      }
      return isLt2M;
    },
    removeFile(file) {
      this.value.map((f, i) => {
        if (f.uid == file.uid) {
          this.value.splice(i, 1)
        }
      })
      this.$emit('input', this.value)
    }
  }
}
</script>

<style scoped>
  /deep/ .fontSmall .ant-upload-list-item-name
 {
    font-size: 12px !important;
  }

  /deep/ .fontSmall tr,
  .fontSmall td,
  .fontSmall th {
    height: 25px !important;
  }
  /deep/ .fontMiddle td,
  .fontMiddle th,
  .fontMiddle input,
  .fontMiddle textarea,
  .fontMiddle select,
  .fontMiddle a,
  .fontMiddle button {
    font-size: 14px !important;
  }

  /deep/ .fontMiddle tr,
  .fontMiddle td,
  .fontMiddle th {
    height: 30px !important;
  }

  /deep/ .fontLarge td,
  .fontLarge th,
  .fontLarge input,
  .fontLarge textarea,
  .fontLarge select,
  .fontLarge a,
  .fontLarge button {
    font-size: 18px !important;
  }

  /deep/ .fontLarge tr,
  .fontLarge td,
  .fontLarge th {
    height: 35px !important;
  }
 /deep/ .ant-upload-list-item:hover .ant-upload-list-item-card-actions{
   top:0 !important
 }
 /deep/ .ant-upload-list-item {
   margin-top: 0px !important;
 }
</style>
