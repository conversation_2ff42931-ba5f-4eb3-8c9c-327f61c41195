<template>
	<div ref="treeContainer" class="floattree" :style="style" v-show="visible">
		<a-spin class="treeLoading" :spinning="treeLoading">
		<div class="floattreeHeader"></div>
		<div class="floattreeContent" :style="`height:${this.height - 45}px`">
			<a-tree v-model="checkedKeys"
					checkable
					:selectable="false"
					:blockNode="true"
					:defaultExpandAll="true"
					@check="onCheck"
					:tree-data="treeData"/>
		</div>
		<div class="floattreeFooter">
			<a-button class="floattreeOk" type="primary" @click="selectOk">确认</a-button>
			<a-button class="floattreeGoback" type="normal" @click="reduction">还原</a-button>
		</div>
		</a-spin>
	</div>
</template>

<script>
	import {XSGS_CLUMNS,LIST_CLUMNS} from "@/services/api/xsgs";
	import {METHOD, request} from "@/utils/request";
	export default {
		name: 'ColumnTree',
		props: {
			pageName: {
				type: String,
				required: true,
			},
			dataList: {
				type: Array,
				required: true,
				default: () => []
			},
			keyField: {
				type: String,
				required: false,
				default: 'key'
			},
			allCheck:{
				type: Boolean,
				required: false,
				default: true
			},
			titleField: {
				type: String,
				required: false,
				default: 'title'
			},
			childrenField:{
				type: String,
				required: false,
				default: 'children'
			},
			width: {
				type: String,
				required: false,
				default: "250"
			},
			height: {
				type: String,
				required: false,
				default: "345"
			},
		},
		data() {
			return {
				treeLoading:false,
				left: 0,
				top: 0,
				visible:false,
				target: null,
				meta: null,
				checkedKeys:[]
			}
		},
		computed: {
			style() {
				return {
					left: this.left + 'px',
					top: this.top + 'px',
					width: this.width + 'px',
					height: this.height + 'px',
				}
			},
			treeData(){
				let tree = [];
				let list = this.copy(this.dataList);
				let newData = this.coverAntTreeData(list);
				if (this.allCheck) {
					let allItem = {key:'all',title:'全部', children:[]};
					allItem.children = newData;
					tree.push(allItem);
				} else {
					tree = newData;
				}
				return tree;
			}
		},
		created () {
			window.addEventListener('click', this.closeTree);
		},
		beforeDestroy() {
			window.removeEventListener('click', this.closeTree)
		},
		async mounted() {
			let res = await this.initPageColumn();
			this.checkedKeys = res.data.data;
			this.setPageColumns();
		},
		methods: {
			closeTree(e) {
					if (!this.isTreeElement(e)) {
					this.visible = false;
				}
			},
			isTreeElement(e){
				if (!e.target) {
					return false;
				}
				let target = e.target;
				let i = 0;
				let yes = false;

				while (i < 15) {
					if (target.targetName == 'BODY') {
						break;
					}
					if (target.className && target.className.indexOf && target.className.indexOf('floattree') != -1) {
						yes = true;
						break
					}
					target = target.parentElement;
					if (!target) {
						break;
					}
					i++;
				}
				return yes;
			},
			showTree(e){
				this.visible = true;
				this.setPosition(e);
			},
			setPosition(e) {
				this.left = e.clientX - this.width;
				this.top = e.clientY
				this.target = e.target
				this.meta = e.meta
			},
			selectOk(){
				let that = this;
				this.treeLoading = true;
				request(XSGS_CLUMNS, METHOD.POST, {pageName: this.pageName, columns: this.checkedKeys}).then(res => {
					that.setPageColumns();
					that.visible = false;
					this.treeLoading = false;
				});
			},
			setPageColumns(){
				let newData = this.copy(this.dataList);
				if (this.checkedKeys.length > 0 && this.checkedKeys.findIndex(s=>s=='all') == -1) {
					newData = this.coverTableData(newData);
				}
				this.$emit('selectOk', newData)
			},
			reduction(){
				this.checkedKeys = [];
				let that = this;
				this.treeLoading = true;
				request(XSGS_CLUMNS, METHOD.POST, {pageName: this.pageName, columns: this.checkedKeys}).then(res => {
					that.$emit('reduction');
					that.visible = false;
					this.treeLoading = false;
				});
			},
			onCheck(checkedKeys){
				this.checkedKeys = checkedKeys;
			},
			initPageColumn(){
				let pageName = this.pageName;
				return request(LIST_CLUMNS, METHOD.POST, {pageName});
			},

			coverAntTreeData(list) {
				let that = this;
				list.forEach(s=>{
					s['key'] = s[that.keyField];
					s['title'] = s[that.titleField];
					s['children'] = s[that.childrenField];
					if (!that.isEmpty(s['children'])) {
						s['children'] = that.coverAntTreeData(s['children']);
					}
				});
				return list;
			},
			coverTableData(list){
				if (this.isEmpty(list)) {
					null;
				}
				let that = this;
				let newData = [];
				let keys = this.checkedKeys;
				list.forEach(s=>{
					if (s.children && s.children.length > 0) {
						let child = that.coverTableData(s.children);
						if (child && child.length > 0) {
							s['children'] = child;
							newData.push(s);
						}
					} else {
						if (keys.findIndex(k=>k===s.dataIndex) != -1) {
							newData.push(s);
						}
					}
				});
				return newData;
			}
		}
	}
</script>

<style>
	.floattree {
		position: fixed;
		z-index: 10000;
		border-radius: 4px;
		background-color: #ffffff;
		box-shadow: rgb(189 189 189) -4px 4px 16px 1px !important;
	}
	.floattree .header {
		height: 10px;
		/*padding-left: 26px;*/
		/*line-height: 40px;*/
	}
	.floattree .floattreeContent {
		overflow: hidden;
		overflow-y: auto;
	}
	.floattree .floattreeFooter {
		border-top: 1px solid rgb(240 240 240);
		height: 40px;
		padding-top: 5px;
		line-height: 40px;
	}
	.floattree .floattreeGoback {
		float: right;
		margin-right: 16px;
	}
	.floattree .floattreeOk {
		float: left;
		margin-left: 16px;
	}
</style>
