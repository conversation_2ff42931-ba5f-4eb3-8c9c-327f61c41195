<template>
  <div>
    <iframe width="100%" height="900px" v-if="env === 'production'"
            :src="'https://frrc.app.yuchai.com/frbi/decision/v10/entry/access/'+$route.meta.frIdprod+'?preview=true'"
            frameborder="0"></iframe>
    <iframe width="100%" height="900px" v-else
            :src="'http://frrc.qas.yuchai.com/frbi/decision/v10/entry/access/'+$route.meta.frIdDev+'?preview=true'"
            frameborder="0"></iframe>

  </div>
</template>

<script>
export default {
  name: "<PERSON><PERSON><PERSON>",
  data() {
    return {
      env: process.env.NODE_ENV
    }
  }
}
</script>

<style scoped>

</style>
