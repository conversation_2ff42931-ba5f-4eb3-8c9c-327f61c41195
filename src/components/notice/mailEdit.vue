<template>
    <div>
        <a-modal
                :title="formTitle"
                :visible="isShow"
                :maskClosable="false"
                :width="'50%'"
                @cancel="close"
                @ok="editOk"
                class="mailEditModal"
        >
            <a-spin :spinning="loading">
            <a-row>
                <table class="mailTable">
                    <tbody>
                        <tr>
                            <th>
                                <a @click="editRecipient">收件人</a>
                            </th>
                            <td>
                                <a-textarea readOnly :value="userStr" style="min-height: 80px"/>
                            </td>
                        </tr>
                        <tr>
                            <th>主题</th>
                            <td>
                                <a-input v-model="noticeForm.noticeTitle"></a-input>
                            </td>
                        </tr>
                        <tr>
                            <th>意见</th>
                            <td>
                                <a-textarea style="min-height: 150px" v-model="noticeForm.noticeContent"/>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </a-row>
            </a-spin>
        </a-modal>
        <!-- 开始选人组件 -->
        <YcChooseMember
                ref="chooseMember"
                @member-modal-ok="memberModalOkTo($event)"
                @member-modal-cancel="canCel"
        ></YcChooseMember>
    </div>
</template>

<script>
    import YcChooseMember from "@/components/chooseMember/YcChooseMember";
    export default {
        name: "mailEdit.vue",
        props: {
            formTitle: {type: String, default: '邮件编辑'},
        },
        components:{
            YcChooseMember
        },
        data() {
            return {
                loading: false,
                isShow: false,
                userRange:[],
                isSpecial:false,
                noticeForm:{
                    refId:null,
                    refNode:null,
                    noticeTitle:null,
                    noticeContent:null,
                    noticeType:'OAMAIL',
                    noticeSubType:null,
                    userList:[]
                }
            }
        },
        created() {
        },
        computed:{
            userStr(){
                let userValue="";
                this.noticeForm.userList.forEach(s=>{
                    if (userValue.length == 0) {
                        userValue = s.userDisplay;
                    } else {
                        userValue = userValue + "；" +s.userDisplay;
                    }
                });
                return userValue;
            },
        },
        methods: {
            show(refId, refNode, noticeSubType, userList, isSpecial) {
                this.noticeForm = {
                    refId:null,
                    refNode:null,
                    noticeTitle:null,
                    noticeContent:null,
                    noticeType:'OAMAIL',
                    noticeSubType:null,
                    userList:[]
                }
                this.loading = false;
                this.noticeForm.refId = refId;
                this.noticeForm.refNode = refNode;
                this.noticeForm.noticeSubType = noticeSubType;
                this.userRange = userList;
                this.isSpecial = isSpecial?true:false;
                this.isShow = true;
            },
            close() {
                this.isShow = false;
            },
            editOk(){
                this.$emit("send", this.noticeForm);
            },
            showLoading(){
                this.loading = true;
            },
            closeLoading(){
                this.loading = true;
            },
            editRecipient() {
                let employeesData = [];
                this.$refs.chooseMember.defaultChoose = [];
                this.noticeForm.userList.forEach(item=>{
                    employeesData.push({
                        employeeName: item.userName,
                        employeeCode: item.userAccount,
                        orgFullName:item.userDisplay
                    });
                });
                this.$refs.chooseMember.show(null, [...employeesData], this.userRange, this.isSpecial);
            },
            canCel() {},
            memberModalOkTo(memberData) {
                let dataList = memberData.memberData;
                this.noticeForm.userList = [];
                dataList.forEach(item=>{
                    this.noticeForm.userList.push({
                        userName: item.employeeName,
                        userAccount: item.employeeCode,
                        userDisplay:item.orgFullName
                    });
                });
            },
        }
    }
</script>

<style scoped>
    /deep/ .ant-modal-content .ant-modal-body {
        padding: 16px !important;
    }
    /deep/ .mailTable {
        width: 100%;
    }
    /deep/ .mailTable td, .mailTable th {
        border: 0.5px solid;
    }
    /deep/ .mailTable th a {
        text-decoration: underline;
    }
    /deep/ .mailTable th {
        text-align: center;
        background-color: #bdd7ee;
    }
</style>
