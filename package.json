{"name": "vue-antd-admin", "version": "0.7.2", "homepage": "https://iczer.github.io/vue-antd-admin", "private": true, "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:uat": "vue-cli-service build --mode uat", "lint": "vue-cli-service lint", "predeploy": "yarn build", "deploy": "gh-pages -d dist -b pages -r https://gitee.com/iczer/vue-antd-admin.git", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "docs:deploy": "vuepress build docs && gh-pages -d docs/.vuepress/dist -b master -r https://gitee.com/iczer/vue-antd-admin-docs.git"}, "dependencies": {"@riophae/vue-treeselect": "^0.4.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.4.0", "animate.css": "^4.1.0", "ant-design-vue": "1.7.2", "axios": "^0.19.2", "clipboard": "^2.0.8", "core-js": "^3.6.5", "crypto-js": "^4.0.0", "date-fns": "^2.14.0", "enquire.js": "^2.1.6", "exceljs": "^4.3.0", "fingerprintjs2": "^2.1.4", "highlight.js": "^10.2.1", "jquery": "^3.6.0", "js-cookie": "^2.2.1", "jsencrypt": "^3.2.1", "lodash": "^4.17.21", "mockjs": "^1.1.0", "moment": "^2.30.1", "nprogress": "^0.2.0", "vant": "^2.12.53", "viser-vue": "^2.4.8", "vue": "^2.6.11", "vue-draggable-resizable": "^2.1.0", "vue-i18n": "^8.18.2", "vue-print-nb": "^1.7.5", "vue-router": "^3.3.4", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "vxe-table": "^3.3.11", "vxe-table-plugin-export-xlsx": "^2.2.1", "wangeditor": "^4.7.15", "xe-utils": "^3.3.1"}, "devDependencies": {"@ant-design/colors": "^4.0.1", "@vue/cli-plugin-babel": "^4.4.0", "@vue/cli-plugin-eslint": "^4.4.0", "@vue/cli-service": "^4.4.0", "@vuepress/plugin-back-to-top": "^1.5.2", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^2.0.0", "deepmerge": "^4.2.2", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "fast-deep-equal": "^3.1.3", "gh-pages": "^3.1.0", "less-loader": "^6.1.1", "sass": "1.32.0", "sass-loader": "10.1.0", "style-resources-loader": "^1.3.2", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-template-compiler": "^2.6.11", "vuepress": "^1.5.2", "webpack-theme-color-replacer": "1.3.12", "whatwg-fetch": "^3.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-debugger": 0, "no-console": "off", "no-unused-vars": "off", "no-mixed-spaces-and-tabs": "off", "no-unreachable": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"]}